# Bulk Payout Capitation Queue Implementation Plan

## Overview

This document outlines the implementation plan for converting the synchronous `payoutCapitation` operation into an asynchronous bulk processing system using Bull queues. The current implementation processes multiple payout requests sequentially, causing delays of up to 30 seconds due to external API calls.

## Current State Analysis

### Current Implementation Issues
- **Sequential Processing**: The `payoutCapitation` method processes each facility billing sequentially in a loop
- **External API Bottlenecks**: Multiple external calls per facility:
  - `bankService.getOperationToken()` - Authentication token retrieval
  - `bankService.getNIPBanks()` - Bank list retrieval
  - `bankService.verifyAccountNumber()` - Account verification per facility
  - `bankService.transferFund()` - Fund transfer per facility
- **Client Blocking**: Client waits for entire operation to complete (up to 30 seconds)
- **Additional Delays**: Email sending and WhatsApp notifications per successful transfer

### Current Data Structure
```typescript
class CapitationPayoutInput {
  hospitalId: string;
  enrolleeCount: number;
  totalCapitationAmount: number;
  totalAmountForPayout: number;
  transferFundId?: string;
  hmoPlanTypeId?: string;
  payoutDecreasePercentage?: number;
  perPlanPayoutDecreasePercentage?: PerPlanPayoutDecreasePercentage[];
}
```

## Proposed Solution Architecture

### 1. Queue Infrastructure

#### Queue Setup
- **Queue Name**: `CAPITATION_PAYOUT_QUEUE = 'capitation_payout_queue'`
- **Queue Prefix**: `capitation_payout_queue`
- **Redis Configuration**: Use existing Redis setup
- **Concurrency**: Configure appropriate concurrency level (e.g., 3-5 concurrent jobs)

#### Queue Module Structure
```
src/capitation-payouts/
├── constants/
│   └── queue.constants.ts
├── interfaces/
│   └── capitation-payout-job.interface.ts
├── producers/
│   └── capitation-payout.producer.ts
├── processors/
│   └── capitation-payout.processor.ts
├── models/
│   └── capitation-payout-job.model.ts
└── capitation-payout-queue.module.ts
```

### 2. Job Data Structure

#### Job Interface
```typescript
export interface ICapitationPayoutJob {
  jobId: string;
  mutatorId: string;
  inputs: CapitationPayoutInput[];
  origin: string;
  createdAt: Date;
  metadata: {
    totalFacilities: number;
    totalAmount: number;
    hmoProviderId: string;
  };
}

export interface ICapitationPayoutJobProgress {
  jobId: string;
  totalFacilities: number;
  processedFacilities: number;
  successfulPayouts: number;
  failedPayouts: number;
  currentStatus: 'queued' | 'processing' | 'completed' | 'failed';
  errors: Array<{
    facilityId: string;
    error: string;
    timestamp: Date;
  }>;
  results: TransferFundModel[];
}
```

### 3. Database Schema Changes

#### New Table: `capitation_payout_jobs`
```sql
CREATE TABLE capitation_payout_jobs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  job_id VARCHAR(255) UNIQUE NOT NULL,
  mutator_id UUID NOT NULL REFERENCES profiles(id),
  status VARCHAR(50) NOT NULL DEFAULT 'queued',
  total_facilities INTEGER NOT NULL,
  processed_facilities INTEGER DEFAULT 0,
  successful_payouts INTEGER DEFAULT 0,
  failed_payouts INTEGER DEFAULT 0,
  total_amount DECIMAL(15,2) NOT NULL,
  origin VARCHAR(255),
  hmo_provider_id UUID REFERENCES hmo_providers(id),
  created_at TIMESTAMP DEFAULT NOW(),
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  error_message TEXT,
  metadata JSONB
);

CREATE INDEX idx_capitation_payout_jobs_job_id ON capitation_payout_jobs(job_id);
CREATE INDEX idx_capitation_payout_jobs_mutator_id ON capitation_payout_jobs(mutator_id);
CREATE INDEX idx_capitation_payout_jobs_status ON capitation_payout_jobs(status);
```

#### New Table: `capitation_payout_job_results`
```sql
CREATE TABLE capitation_payout_job_results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  job_id VARCHAR(255) NOT NULL REFERENCES capitation_payout_jobs(job_id),
  facility_id UUID NOT NULL REFERENCES hospitals(id),
  transfer_fund_id UUID REFERENCES transfer_funds(id),
  status VARCHAR(50) NOT NULL,
  amount DECIMAL(15,2),
  error_message TEXT,
  processed_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_capitation_payout_job_results_job_id ON capitation_payout_job_results(job_id);
```

### 4. API Changes

#### Modified Mutation Response
```typescript
@ObjectType()
export class CapitationPayoutJobResponse {
  @Field()
  jobId: string;

  @Field()
  status: string;

  @Field()
  message: string;

  @Field()
  totalFacilities: number;

  @Field()
  estimatedCompletionTime: string; // e.g., "2-3 minutes"
}
```

#### New Query for Job Status
```typescript
@Query(() => CapitationPayoutJobStatus)
async getCapitationPayoutJobStatus(
  @Args('jobId') jobId: string,
  @CurrentProfile() profile: ProfileModel
): Promise<CapitationPayoutJobStatus> {
  return this.capitationPayoutService.getJobStatus(jobId, profile);
}
```

#### Job Status Response
```typescript
@ObjectType()
export class CapitationPayoutJobStatus {
  @Field()
  jobId: string;

  @Field()
  status: 'queued' | 'processing' | 'completed' | 'failed';

  @Field()
  totalFacilities: number;

  @Field()
  processedFacilities: number;

  @Field()
  successfulPayouts: number;

  @Field()
  failedPayouts: number;

  @Field()
  progressPercentage: number;

  @Field(() => [TransferFundModel], { nullable: true })
  results?: TransferFundModel[];

  @Field(() => [String], { nullable: true })
  errors?: string[];

  @Field()
  createdAt: Date;

  @Field({ nullable: true })
  completedAt?: Date;

  @Field({ nullable: true })
  estimatedTimeRemaining?: string;
}
```

### 5. Queue Processor Implementation

#### Core Processing Logic
```typescript
@Processor(CAPITATION_PAYOUT_QUEUE)
export class CapitationPayoutProcessor {
  
  @Process()
  async processCapitationPayout(job: Job<ICapitationPayoutJob>) {
    const { jobId, mutatorId, inputs, origin } = job.data;
    
    // Update job status to processing
    await this.updateJobStatus(jobId, 'processing');
    
    try {
      // Get shared resources once (optimization)
      const token = await this.bankService.getOperationToken();
      const banks = await this.bankService.getNIPBanks(VirtualAccountProvider.WEMA, token);
      
      const results: TransferFundModel[] = [];
      let processedCount = 0;
      let successCount = 0;
      let failedCount = 0;
      
      // Process each facility
      for (const input of inputs) {
        try {
          const result = await this.processSinglePayout(input, token, banks, mutatorId, origin);
          results.push(result);
          successCount++;
          
          // Publish individual success event
          this.pubSub.publish(HospitalCapitationDetailsUpdated, {
            [HospitalCapitationDetailsUpdated]: result,
          });
          
        } catch (error) {
          failedCount++;
          await this.recordPayoutError(jobId, input.hospitalId, error.message);
        }
        
        processedCount++;
        
        // Update progress
        await this.updateJobProgress(jobId, processedCount, successCount, failedCount);
        
        // Update Bull job progress
        const progressPercentage = (processedCount / inputs.length) * 100;
        await job.progress(progressPercentage);
      }
      
      // Mark job as completed
      await this.completeJob(jobId, results, successCount, failedCount);
      
      return results;
      
    } catch (error) {
      await this.failJob(jobId, error.message);
      throw error;
    }
  }
  
  private async processSinglePayout(
    input: CapitationPayoutInput,
    token: string,
    banks: any,
    mutatorId: string,
    origin: string
  ): Promise<TransferFundModel> {
    // Extract single payout logic from current implementation
    // This will be the core logic from the current payoutCapitation method
    // but optimized for single facility processing
  }
}
```

### 6. Producer Implementation

```typescript
@Injectable()
export class CapitationPayoutProducer {
  constructor(
    @InjectQueue(CAPITATION_PAYOUT_QUEUE) 
    private capitationPayoutQueue: Queue
  ) {}

  async addCapitationPayoutJob(
    mutator: ProfileModel,
    inputs: CapitationPayoutInput[],
    origin: string
  ): Promise<string> {
    const jobId = generateUUID();
    
    const jobData: ICapitationPayoutJob = {
      jobId,
      mutatorId: mutator.id,
      inputs,
      origin,
      createdAt: new Date(),
      metadata: {
        totalFacilities: inputs.length,
        totalAmount: inputs.reduce((sum, input) => sum + input.totalAmountForPayout, 0),
        hmoProviderId: mutator.hmoId,
      },
    };

    // Create job record in database
    await this.createJobRecord(jobData);

    // Add to queue
    await this.capitationPayoutQueue.add(jobData, {
      jobId,
      removeOnComplete: 10, // Keep last 10 completed jobs
      removeOnFail: 50,     // Keep last 50 failed jobs
      attempts: 3,          // Retry failed jobs 3 times
      backoff: {
        type: 'exponential',
        delay: 5000,        // Start with 5 second delay
      },
    });

    return jobId;
  }
}
```

### 7. Service Layer Changes

#### Modified Hospital Service
```typescript
// Replace current payoutCapitation method
async payoutCapitation(
  mutator: ProfileModel,
  inputs: CapitationPayoutInput[],
  origin: string,
): Promise<CapitationPayoutJobResponse> {
  // Validate inputs
  await this.validatePayoutInputs(inputs, mutator);
  
  // Queue the job
  const jobId = await this.capitationPayoutProducer.addCapitationPayoutJob(
    mutator,
    inputs,
    origin
  );
  
  return {
    jobId,
    status: 'queued',
    message: 'Capitation payout job has been queued for processing',
    totalFacilities: inputs.length,
    estimatedCompletionTime: this.calculateEstimatedTime(inputs.length),
  };
}

async getCapitationPayoutJobStatus(
  jobId: string,
  profile: ProfileModel
): Promise<CapitationPayoutJobStatus> {
  // Get job status from database
  const jobRecord = await this.getJobRecord(jobId, profile.id);
  
  if (!jobRecord) {
    throw new NotFoundException('Job not found');
  }
  
  // Get Bull job for real-time progress
  const bullJob = await this.capitationPayoutQueue.getJob(jobId);
  
  return {
    jobId: jobRecord.jobId,
    status: jobRecord.status,
    totalFacilities: jobRecord.totalFacilities,
    processedFacilities: jobRecord.processedFacilities,
    successfulPayouts: jobRecord.successfulPayouts,
    failedPayouts: jobRecord.failedPayouts,
    progressPercentage: (jobRecord.processedFacilities / jobRecord.totalFacilities) * 100,
    results: jobRecord.status === 'completed' ? await this.getJobResults(jobId) : null,
    errors: await this.getJobErrors(jobId),
    createdAt: jobRecord.createdAt,
    completedAt: jobRecord.completedAt,
    estimatedTimeRemaining: this.calculateRemainingTime(jobRecord, bullJob),
  };
}
```

### 8. Error Handling & Retry Strategy

#### Retry Configuration
- **Max Attempts**: 3 retries per job
- **Backoff Strategy**: Exponential backoff starting at 5 seconds
- **Individual Payout Failures**: Continue processing other payouts, record errors
- **Critical Failures**: Fail entire job for authentication or configuration issues

#### Error Categories
1. **Retryable Errors**: Network timeouts, temporary API failures
2. **Non-retryable Errors**: Invalid account numbers, insufficient funds
3. **Critical Errors**: Authentication failures, configuration issues

### 9. Monitoring & Observability

#### Queue Metrics
- Job completion rates
- Average processing time per facility
- Error rates by type
- Queue depth and processing lag

#### Logging Strategy
- Structured logging for each job phase
- Error tracking with context
- Performance metrics collection

### 10. Testing Strategy

#### Unit Tests
- Producer functionality
- Processor logic for single payouts
- Error handling scenarios
- Progress tracking accuracy

#### Integration Tests
- End-to-end job processing
- Database transaction integrity
- PubSub event publishing
- API endpoint functionality

#### Load Tests
- Queue performance under high load
- Concurrent job processing
- Memory usage patterns

### 11. Deployment Considerations

#### Environment Configuration
- Redis connection settings
- Queue concurrency limits
- Retry attempt configurations
- Job retention policies

#### Monitoring Setup
- Queue dashboard (Bull Board)
- Alert thresholds for failed jobs
- Performance monitoring

### 12. Migration Strategy

#### Phase 1: Infrastructure Setup
1. Create database tables
2. Set up queue infrastructure
3. Implement basic producer/processor

#### Phase 2: API Changes
1. Modify resolver to return job ID
2. Implement job status endpoint
3. Update client-side handling

#### Phase 3: Testing & Rollout
1. Comprehensive testing
2. Gradual rollout with feature flags
3. Monitor performance and adjust

#### Phase 4: Cleanup
1. Remove old synchronous code
2. Optimize based on production metrics
3. Documentation updates

### 13. Client-Side Integration

#### Polling Strategy
```typescript
// Client-side polling example
const pollJobStatus = async (jobId: string) => {
  const maxAttempts = 60; // 5 minutes with 5-second intervals
  let attempts = 0;
  
  const poll = async (): Promise<CapitationPayoutJobStatus> => {
    const status = await getCapitationPayoutJobStatus(jobId);
    
    if (status.status === 'completed' || status.status === 'failed') {
      return status;
    }
    
    if (attempts >= maxAttempts) {
      throw new Error('Job status polling timeout');
    }
    
    attempts++;
    await new Promise(resolve => setTimeout(resolve, 5000));
    return poll();
  };
  
  return poll();
};
```

#### WebSocket Integration (Optional)
- Real-time progress updates
- Immediate completion notifications
- Error alerts

### 14. Performance Optimizations

#### Batch Operations
- Batch database updates where possible
- Optimize external API calls
- Cache frequently accessed data

#### Resource Management
- Connection pooling for external APIs
- Memory-efficient job data structures
- Cleanup of completed job data

### 15. Security Considerations

#### Job Access Control
- Verify user permissions for job status queries
- Ensure job isolation between different HMO providers
- Audit trail for all payout operations

#### Data Protection
- Encrypt sensitive job data
- Secure Redis configuration
- Proper error message sanitization

## Implementation Timeline

### Week 1-2: Infrastructure & Database
- Set up queue infrastructure
- Create database schema
- Basic producer/processor implementation

### Week 3-4: Core Logic & API
- Implement payout processing logic
- Create API endpoints
- Error handling implementation

### Week 5-6: Testing & Integration
- Comprehensive testing
- Client-side integration
- Performance optimization

### Week 7-8: Deployment & Monitoring
- Production deployment
- Monitoring setup
- Documentation completion

## Success Metrics

1. **Performance**: Reduce client wait time from 30 seconds to < 2 seconds
2. **Reliability**: 99.9% job completion rate
3. **Scalability**: Handle 10x current payout volume
4. **User Experience**: Real-time progress tracking
5. **Maintainability**: Clear separation of concerns and testable code

## Conclusion

This implementation will transform the capitation payout process from a blocking synchronous operation to a scalable, asynchronous system. The use of Bull queues provides reliability, retry mechanisms, and monitoring capabilities while maintaining the existing business logic and integrations.

The key benefits include:
- **Immediate Response**: Clients get instant feedback with job ID
- **Progress Tracking**: Real-time status updates
- **Scalability**: Handle multiple concurrent payout requests
- **Reliability**: Built-in retry and error handling
- **Monitoring**: Comprehensive observability into the payout process

The implementation follows established patterns in the codebase and integrates seamlessly with existing infrastructure while providing a foundation for future enhancements.