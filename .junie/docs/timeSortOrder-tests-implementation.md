# TimeSortOrder Filter - Unit Tests Implementation

## Overview

This document describes the comprehensive unit tests implemented for the new `timeSortOrder` filter functionality in the pre-authorization repository. The `timeSortOrder` filter allows sorting pre-authorization records by time in ascending or descending order while maintaining date-based primary sorting.

## Implementation Details

### Test File Location
- **File**: `src/pre-authorisations/repositories/pre-authorization.repository.spec.ts`
- **Test Suite**: `PreauthorizationRepository - timeSortOrder Filter`

### Filter Functionality

The `timeSortOrder` filter was added to the `PreauthorizationFilterInput` with the following behavior:

```typescript
enum TimeSortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}
```

**Repository Implementation**:
- When `timeSortOrder` is provided: Sorts by date (DESC) first, then by time using the specified order
- When `timeSortOrder` is not provided: Uses default sorting by `updatedDate DESC`
- Uses SQL date/time extraction: `updated_date::date` and `updated_date::time`

## Test Coverage

### 1. Basic Functionality Tests

#### findByProfile with timeSortOrder
- ✅ **ASC Sorting**: Verifies records are sorted by time in ascending order (08:10, 09:30, 11:45, 14:15, 16:20)
- ✅ **DESC Sorting**: Verifies records are sorted by time in descending order (16:20, 14:15, 11:45, 09:30, 08:10)
- ✅ **Default Behavior**: Ensures default sorting by full timestamp DESC when `timeSortOrder` is not provided

#### findByHospital with timeSortOrder
- ✅ **ASC Sorting**: Tests time-based ascending sort for hospital-specific queries
- ✅ **DESC Sorting**: Tests time-based descending sort for hospital-specific queries
- ✅ **Default Behavior**: Verifies default sorting behavior for hospital queries

### 2. Integration Tests

#### Filter Combination
- ✅ **Keyword + TimeSortOrder**: Tests that `timeSortOrder` works correctly with other filters like keyword search
- ✅ **Pagination + TimeSortOrder**: Verifies pagination works correctly with time-based sorting

### 3. Edge Cases and Complex Scenarios

#### Same Date, Different Times
- ✅ **Time Precision**: Tests sorting of records with the same date but different times (10:00, 10:15, 10:30)

#### Cross-Date Sorting
- ✅ **Multi-Date Logic**: Verifies that records are sorted by date DESC first, then by time within each date
- ✅ **Date Priority**: Ensures newer dates appear first regardless of time

#### Boundary Conditions
- ✅ **Empty Results**: Tests behavior when no records match the criteria
- ✅ **Single Record**: Implicit testing through various scenarios

## Test Data Setup

### Fixtures Used
- `createPreauthorization`: Creates pre-authorization records with specific timestamps
- `createHmoProviderFixtures`: Creates HMO provider test data
- `createHospitals`: Creates hospital test data
- `createUsers`: Creates user and profile test data

### Test Data Strategy
- **Controlled Timestamps**: Uses specific dates and times for predictable sorting
- **Multiple Scenarios**: Creates records across different dates and times
- **Realistic Data**: Uses proper relationships between entities (users, hospitals, providers)

## Key Test Scenarios

### Scenario 1: Time-Only Sorting (Same Date)
```
Base Date: 2024-01-15
Times: 09:30, 14:15, 11:45, 16:20, 08:10
Expected ASC: 08:10, 09:30, 11:45, 14:15, 16:20
Expected DESC: 16:20, 14:15, 11:45, 09:30, 08:10
```

### Scenario 2: Cross-Date Sorting
```
Records:
- 2024-03-01 15:00
- 2024-03-02 09:00  
- 2024-03-01 10:00

Expected Order (DESC): 2024-03-02 09:00, 2024-03-01 15:00, 2024-03-01 10:00
```

### Scenario 3: Integration with Filters
```
Filter: keyword="SPECIAL-CODE" + timeSortOrder=ASC
Expected: Only matching records, sorted by time ASC
```

## Test Results

- **Total Tests**: 11
- **Passed**: 11 ✅
- **Failed**: 0
- **Coverage**: Complete coverage of all timeSortOrder scenarios

## Technical Implementation Notes

### Database Queries
The implementation uses PostgreSQL-specific date/time functions:
```sql
.addSelect('preauthorizations.updated_date::date', 'updated_date_only')
.addSelect('preauthorizations.updated_date::time', 'updated_time_only')
.orderBy('updated_date_only', 'DESC')
.addOrderBy('updated_time_only', timeSortOrder)
```

### Response Structure
Tests verify the correct response structure:
```typescript
{
  list: PreauthorisationModel[],
  totalCount: number
}
```

## Future Considerations

1. **Performance**: Monitor query performance with large datasets
2. **Timezone Handling**: Consider timezone implications for time-based sorting
3. **Additional Filters**: Ensure compatibility with future filter additions
4. **Database Compatibility**: Verify behavior across different database systems if needed

## Maintenance

- **Test Data**: Update test dates periodically to avoid issues with relative date logic
- **Fixtures**: Keep fixture functions updated with model changes
- **Assertions**: Update property names if response structure changes (e.g., `totalCount` vs `total`)

## Related Files

- **Repository**: `src/pre-authorisations/repositories/pre-authorization.repositories.ts`
- **Input Types**: `src/pre-authorisations/inputs/pre-authorisation-filter.input.ts`
- **Models**: `src/pre-authorisations/models/preauthorisation.model.ts`
- **Fixtures**: `src/utils/tests/preauthorization.fixtures.ts`