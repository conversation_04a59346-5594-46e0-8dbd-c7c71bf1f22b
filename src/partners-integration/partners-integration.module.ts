import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { config } from '@clinify/config';
import { JwtStrategy } from '@clinify/partners-integration/authorization/guards/jwt-strategy';
import { WemaBankController } from '@clinify/partners-integration/banks/wema/controllers/wema-bank.controller';
import { VirtualBankAccountModule } from '@clinify/virtual-bank-accounts/virtual-bank-account.module';

@Module({
  imports: [
    VirtualBankAccountModule,
    JwtModule.register({
      secret: config.generatedTokenSecret,
    }),
  ],
  controllers: [WemaBankController],
  providers: [JwtStrategy, Logger],
})
export class PartnersIntegrationModule {}
