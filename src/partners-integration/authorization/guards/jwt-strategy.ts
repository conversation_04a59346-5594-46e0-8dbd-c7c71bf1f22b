import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { config } from '@clinify/config';

export class JwtStrategy extends PassportStrategy(Strategy, 'partner') {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: config.generatedTokenSecret,
    });
  }

  validate(payload: Record<string, any>): Record<string, any> {
    return payload;
  }
}
