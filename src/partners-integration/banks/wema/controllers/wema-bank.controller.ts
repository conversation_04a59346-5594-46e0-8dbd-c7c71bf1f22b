import {
  Body,
  Controller,
  HttpStatus,
  Logger,
  Post,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Response } from 'express';
import { VirtualAccountProvider } from '@clinify/banks/enum/virtual-account.enum';
import type {
  WemaBlockAccountRequest,
  WemaBlockAccountResponse,
  WemaFetchMiniStatementRequest,
  WemaFetchMiniStatementResponse,
  WemaKYCDetailsRequest,
  WemaKYCDetailsResponse,
} from '@clinify/partners-integration/banks/wema/interfaces/wema.interface';
import {
  IWemaAccountLookupRequest,
  IWemaAccountLookupResponse,
  IWemaNotificationAPIRequest,
  IWemaNotificationAPIResponse,
} from '@clinify/partners-integration/banks/wema/interfaces/wema.interface';
import { VirtualBankAccountService } from '@clinify/virtual-bank-accounts/services/virtual-bank-account.service';

const LOGGER_CONTEXT = 'WemaBank';

@Controller('wema-bank')
export class WemaBankController {
  constructor(
    private readonly virtualBankAccountService: VirtualBankAccountService,
    private readonly logger: Logger,
  ) {}

  @UseGuards(AuthGuard('partner'))
  @Post('lookup')
  async virtualAccountLookup(
    @Req() req,
    @Res() res: Response<IWemaAccountLookupResponse>,
    @Body() reqBody: IWemaAccountLookupRequest,
  ): Promise<Response<IWemaAccountLookupResponse>> {
    this.logger.log(
      `Account Lookup for ${reqBody.accountnumber} requested by ${req.user?.bank}`,
      LOGGER_CONTEXT,
    );
    const virtualBankAccount = await this.virtualBankAccountService
      .lookupAccount(reqBody.accountnumber, VirtualAccountProvider.WEMA)
      .catch(() => {
        return null;
      });
    if (!virtualBankAccount) {
      return res.status(HttpStatus.NOT_FOUND).send({
        accountname: null,
        status: '07',
        status_desc: 'Account not found',
      });
    }

    return res.status(HttpStatus.OK).send({
      accountname: virtualBankAccount.accountName,
      status: '00',
      status_desc: 'success',
    });
  }

  @UseGuards(AuthGuard('partner'))
  @Post('transaction-notification')
  async transactionNotification(
    @Req() req,
    @Res() res: Response<IWemaNotificationAPIResponse>,
    @Body() reqBody: IWemaNotificationAPIRequest,
  ): Promise<Response<IWemaNotificationAPIResponse>> {
    this.logger.log(
      `Transaction with reference ${reqBody.paymentreference} performed by ${req.user?.bank}`,
      LOGGER_CONTEXT,
    );
    const bankAccountTransaction =
      await this.virtualBankAccountService.handleNotificationEvent(
        VirtualAccountProvider.WEMA,
        {
          originatorAccountNumber: reqBody.originatoraccountnumber,
          amount: reqBody.amount,
          originatorName: reqBody.originatorname,
          narration: reqBody.narration,
          creditAccountName: reqBody.craccountname,
          creditAccount: reqBody.craccount,
          paymentReference: reqBody.paymentreference,
          bankName: reqBody.bankname,
          sessionId: reqBody.sessionid,
          bankCode: reqBody.bankcode,
          transactionReference: reqBody.transactionreference,
        },
      );

    return res.status(HttpStatus.OK).send({
      transactionreference: bankAccountTransaction.id,
      status: '00-Okay',
      status_desc: 'success',
    });
  }

  @UseGuards(AuthGuard('partner'))
  @Post('kyc-details')
  async getKYCDetails(
    @Req() req,
    @Res() res: Response<WemaKYCDetailsResponse>,
    @Body() reqBody: WemaKYCDetailsRequest,
  ): Promise<Response<WemaKYCDetailsResponse>> {
    this.logger.log(
      `KYC Details for ${reqBody.accountnumber} requested by ${req.user?.bank}`,
      LOGGER_CONTEXT,
    );
    const details = await this.virtualBankAccountService.getKYCDetails(
      VirtualAccountProvider.WEMA,
      reqBody.accountnumber,
    );

    return res.status(HttpStatus.OK).send({
      accountname: details.accountName,
      BVN: details.bvn,
      mobilenumber: details.phoneNumber,
      walletbalance: details.walletBalance / 100,
      status_desc: details.status,
    });
  }

  @UseGuards(AuthGuard('partner'))
  @Post('fetch-mini-statement')
  async fetchMiniStatement(
    @Req() req,
    @Res() res: Response<WemaFetchMiniStatementResponse>,
    @Body() reqBody: WemaFetchMiniStatementRequest,
  ): Promise<Response<WemaFetchMiniStatementResponse>> {
    this.logger.log(
      `Mini statement for ${reqBody.accountnumber} requested by ${req.user?.bank}`,
      LOGGER_CONTEXT,
    );
    const transactions = await this.virtualBankAccountService.getMiniStatement(
      VirtualAccountProvider.WEMA,
      reqBody.accountnumber,
    );

    return res.status(HttpStatus.OK).send({
      transactions: transactions.map((transaction) => {
        return {
          amount: transaction.amount / 100,
          accountNo: transaction.sender.accountNumber,
          bankName: transaction.sender.bankName,
          direction: 'Credit',
          transactionDate: transaction.transactionDateTime,
        };
      }),
    });
  }

  @UseGuards(AuthGuard('partner'))
  @Post('block-account')
  async blockAccount(
    @Req() req,
    @Res() res: Response<WemaBlockAccountResponse>,
    @Body() reqBody: WemaBlockAccountRequest,
  ): Promise<Response<WemaBlockAccountResponse>> {
    this.logger.log(
      `Account restriction for ${reqBody.accountnumber} requested by ${req.user?.bank}`,
      LOGGER_CONTEXT,
    );
    const response = await this.virtualBankAccountService.deactivateAccount(
      VirtualAccountProvider.WEMA,
      reqBody.accountnumber,
      reqBody.blockreason,
    );
    if (response) {
      return res.status(HttpStatus.OK).send({
        message: 'Account Restricted Successfully',
      });
    } else {
      return res.status(HttpStatus.EXPECTATION_FAILED).send({
        message: 'Account Restriction Failed',
      });
    }
  }
}
