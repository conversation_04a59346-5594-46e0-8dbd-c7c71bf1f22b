export interface IWemaAccountLookupRequest {
  accountnumber: string;
}

export interface IWemaAccountLookupResponse {
  accountname: string;
  status: '00-Okay' | '07-Invalid Account' | '00' | '07';
  status_desc: string;
}

export interface IWemaNotificationAPIRequest {
  originatoraccountnumber: string;
  amount: string;
  originatorname: string;
  narration: string;
  craccountname: string;
  paymentreference: string;
  bankname: string;
  sessionid: string;
  craccount: string;
  bankcode: string;
  transactionreference?: string;
}

export interface IWemaNotificationAPIResponse {
  transactionreference: string;
  status: string; // 00-Okay
  status_desc: string;
}

export type WemaKYCDetailsRequest = {
  accountnumber: string;
};

export type WemaKYCDetailsResponse = {
  accountname: string;
  BVN: string;
  mobilenumber: string;
  walletbalance: number; // Float eg. 0.00
  status_desc: 'Active' | 'Inactive';
};

export type WemaFetchMiniStatementRequest = {
  accountnumber: string;
};

export type WemaFetchMiniStatementResponse = {
  transactions: Array<{
    accountNo: string;
    bankName: string;
    amount: number;
    direction: 'Credit' | 'Debit';
    transactionDate: Date;
  }>;
};

export type WemaBlockAccountRequest = {
  accountnumber: string;
  blockreason: string;
};

export type WemaBlockAccountResponse = {
  message: string;
};
