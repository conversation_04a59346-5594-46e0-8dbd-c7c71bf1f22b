import { HttpService } from '@nestjs/axios';
import { Provider } from '@nestjs/common';
import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken, TypeOrmModule } from '@nestjs/typeorm';
import MockDate from 'mockdate';
import { EntityManager } from 'typeorm';
import { PreauthorisationReferralService } from './pre-authorisation-referral.service';
import { AuthorizationModule } from '../../authorization/authorization.module';
import { TestDataSourceOptions } from '../../data-source';
import { extendModel } from '../../database/extendModel';
import { HmoProviderModule } from '../../hmo-providers/hmo-provider.module';
import { HmoProviderModel } from '../../hmo-providers/models/hmo-provider.model';
import { ProfileModel } from '../../users/models/profile.model';
import { PreauthorisationReferralModel } from '../models/preauthorisation-referral.model';
import { PreAuthReferralUtilisationsModel } from '../models/utilisations-referral.model';
import { IPreauthorizationReferralRepository } from '../repositories/pre-authorization-referral.repositories';
import { loggerMock } from '@clinify/__mocks__/logger';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { HmoProviderService } from '@clinify/hmo-providers/services/hmo-provider.service';
import {
  DefaultInvImage,
  InventoryUpload,
} from '@clinify/integrations/inventory-blk/entities/FileUpload.entity';
import { PriceUpload } from '@clinify/integrations/price-blk/entities/PriceFileUpload.entity';
import { RemindersService } from '@clinify/reminders/services/reminders.service';
import { mailDocService as MailDocService } from '@clinify/shared/services/mail-doc.service';
import { UserModel } from '@clinify/users/models/user.model';
import {
  preauthorizationFactory,
  utilizationFactory,
} from '@mocks/factories/preauthorization.factory';
import { mockUser } from '@mocks/factories/user.factory';

// Mock the queryDSWithSlave function and customDSSerializeInTransaction
jest.mock('@clinify/database', () => ({
  queryDSWithSlave: jest.fn().mockResolvedValue([
    {
      id: 'hospital-id',
      support_mail: '<EMAIL>',
      facility_logo: 'logo-url',
    },
  ]),
  customDSSerializeInTransaction: jest
    .fn()
    .mockImplementation((dataSource, callback) => {
      // Mock the manager object that gets passed to the callback
      const mockManager = {
        withRepository: jest.fn().mockImplementation((repo) => {
          // Return the actual mock repository methods
          return repo;
        }),
      };
      return callback(mockManager);
    }),
}));

jest.mock('@aws-sdk/client-bedrock-runtime', () => {
  const mockInvokeModelCommand = jest.fn();
  return {
    BedrockRuntimeClient: jest.fn(() => ({
      send: jest.fn(),
    })),
    InvokeModelCommand: mockInvokeModelCommand,
  };
});

const user: UserModel = mockUser;
const profile = user.defaultProfile;
const preAuthList = preauthorizationFactory.buildList(3);
const preAuthData = preAuthList[0];
const MockHmoProviderService = {
  createPreauthorizationReferralRequest: jest.fn(() => {
    return Promise.resolve(preAuthData);
  }),
  getHmoProviderModule: jest.fn(() => {
    return {
      synchronizePreauthorizations: jest.fn(() => {
        return Promise.resolve(preAuthData.utilizations);
      }),
      requestPreAuthorization: jest.fn(() => {
        return Promise.resolve(preAuthData.utilizations);
      }),
      createPreauthorizationReferralRequest: jest.fn(() => {
        return Promise.resolve(preAuthData);
      }),
    };
  }),
};
const MockPreauthorisationRepo = {
  deletePreauthorizationReferrals: jest.fn(() => [preAuthData]),
  getPreauthorizationReferral: jest.fn(() => preAuthData),
  findByHospital: jest.fn(() => preAuthList),
  findByProfile: jest.fn(() => preAuthList),
  archivePreauthorizationReferrals: jest.fn(() => preAuthList),
  save: jest.fn((v: any) => v),
  find: jest.fn(() => [preAuthList[0]]),
  getActivePreauthorizationReferral: jest.fn(() => preAuthData),
  getPreauthorizationReferralByCode: jest.fn(() => preAuthData),
  updatePreauthorizationReferral: jest.fn(() => preAuthData),
};

const MockProfileRepository = {
  findOne: jest.fn(() => profile),
};

const commonManagerMethods = {
  save: jest.fn(() => preAuthData),
  delete: jest.fn(),
};

const ManagerMock = {
  findOneOrFail: jest.fn(() => Promise.resolve(profile)) as jest.Mock<
    Promise<unknown>,
    []
  >,
  findOne: jest.fn(() => profile),
  find: jest.fn(() => [profile]),
  save: jest.fn(),
  getCustomRepository: jest.fn(() => ({
    save: jest.fn((v) => v),
  })),
  withRepository: jest.fn().mockReturnValue(commonManagerMethods),
};

const MockReminderService = {};

const MockMailDocService = {
  sendEmailOnReferralApprovalOrRejection: jest.fn().mockResolvedValue(true),
};

describe('PreAuthorizationService', () => {
  let service: PreauthorisationReferralService;
  let preAuthRepo: Provider<IPreauthorizationReferralRepository>;

  beforeEach(async () => {
    preAuthRepo = extendModel(
      PreauthorisationReferralModel,
      MockPreauthorisationRepo,
    );
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        AuthorizationModule,
        HmoProviderModule,
        TypeOrmModule.forFeature([
          PreAuthReferralUtilisationsModel,
          PreauthorisationReferralModel,
          HmoProviderModel,
        ]),
      ],
      providers: [
        PreauthorisationReferralService,
        preAuthRepo,
        extendModel(ProfileModel, MockProfileRepository),
        extendModel(HmoClaimModel, {}),
        {
          provide: HttpService,
          useValue: {},
        },
        {
          provide: RemindersService,
          useValue: MockReminderService,
        },
        {
          provide: MailDocService,
          useValue: MockMailDocService,
        },

        { ...loggerMock },
      ],
    })
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .overrideProvider(HmoProviderService)
      .useValue(MockHmoProviderService)
      .overrideProvider(getRepositoryToken(PreauthorisationReferralModel))
      .useValue(MockPreauthorisationRepo)
      .overrideProvider(getRepositoryToken(HmoClaimModel))
      .useValue({})
      .overrideProvider(EntityManager)
      .useValue(ManagerMock)
      .compile();

    service = module.get<PreauthorisationReferralService>(
      PreauthorisationReferralService,
    );
    jest.clearAllMocks();
  });

  afterAll(() => {
    MockDate.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(service).toBeTruthy();
  });

  it('addPreauthorization() should add preauthorization', async () => {
    const input = preAuthData;
    delete input.profile;

    const response = await service.addPreauthorizationReferral(profile, input);
    expect(response.creatorId).toEqual(profile.id);
  });

  it('addPreauthorization() should add preauthorization with Clinify HMO', async () => {
    const input = preAuthData;
    delete input.profile;
    MockHmoProviderService.getHmoProviderModule = jest.fn(() => {
      return {
        isClinify: true,
        synchronizePreauthorizations: jest.fn(() => {
          return Promise.resolve(preAuthData.utilizations);
        }),
        requestPreAuthorization: jest.fn(() => {
          return Promise.resolve(preAuthData.utilizations);
        }),
        createPreauthorizationReferralRequest: jest.fn(() => {
          return Promise.resolve(preAuthData);
        }),
      };
    });

    const response = await service.addPreauthorizationReferral(profile, input);
    expect(response.creatorId).toEqual(profile.id);
    expect(
      MockHmoProviderService.createPreauthorizationReferralRequest,
    ).toBeCalled();
  });
  it('updatePreauthorizationReferral(): should call the updatePreauthorizationReferral repository method', async () => {
    await service.updatePreauthorizationReferral(
      profile,
      'referral-id',
      preAuthData,
    );
    expect(
      MockPreauthorisationRepo.updatePreauthorizationReferral,
    ).toHaveBeenLastCalledWith(profile, 'referral-id', preAuthData);
  });
  it('deletePreauthorizations() should delete preauthorization', async () => {
    const response = await service.deletePreauthorizationReferrals(profile, [
      'id',
    ]);
    expect(
      MockPreauthorisationRepo.deletePreauthorizationReferrals,
    ).toHaveBeenCalledWith(profile, ['id']);
    expect(response).toEqual([preAuthData]);
  });

  it('getPreauthorization() should get Preauthorization', async () => {
    const response = await service.getPreauthorizationReferral('id');
    expect(
      MockPreauthorisationRepo.getPreauthorizationReferral,
    ).toHaveBeenCalledWith('id');
    expect(response).toEqual(preAuthData);
  });

  it('findByHospital() should findByHospital', async () => {
    const response = await service.findByHospital(
      'id',
      {},
      user.defaultProfile,
    );
    expect(MockPreauthorisationRepo.findByHospital).toHaveBeenCalledWith(
      'id',
      {},
      user.defaultProfile,
    );
    expect(response).toEqual(preAuthList);
  });

  it('findByProfile() should findByProfile', async () => {
    const response = await service.findByProfile('id', {}, profile);
    expect(MockPreauthorisationRepo.findByProfile).toHaveBeenCalledWith(
      'id',
      {},
      profile,
    );
    expect(response).toEqual(preAuthList);
  });

  it('archivePreauthorizations() should archivePreauthorizations', async () => {
    const profile = user.defaultProfile;
    const response = await service.archivePreauthorizationReferrals(
      profile,
      ['id'],
      true,
    );
    expect(
      MockPreauthorisationRepo.archivePreauthorizationReferrals,
    ).toHaveBeenCalledWith(profile, ['id'], true);
    expect(response).toEqual(preAuthList);
  });

  it('getHmoProvider should return hmoProvider entity by id', async () => {
    ManagerMock.findOneOrFail = jest.fn(() =>
      Promise.resolve({ id: 'hmo-provider-id' }),
    );
    const response = await service.getHmoProvider('id');
    expect(response).toEqual(
      expect.objectContaining({ id: 'hmo-provider-id' }),
    );
  });

  it('getActivePreauthorizationReferral should return active preauthorization referral', async () => {
    const response = await service.getActivePreauthorizationReferral(
      'profileId',
      profile,
    );
    expect(response).toEqual(preAuthData);
  });

  it('getPreauthorizationReferralByCode(): should call getPreauthorizationReferralByCode service method', async () => {
    await service.getPreauthorizationReferralByCode('Referral-code');
    expect(
      MockPreauthorisationRepo.getPreauthorizationReferralByCode,
    ).toHaveBeenCalledWith('Referral-code');
  });

  it('updateReferralUtilStatus(): should throw error when utilization is not found', async () => {
    ManagerMock.find = jest.fn().mockResolvedValue(null);

    await expect(
      service.updateReferralUtilStatus(profile, 'Referral-code', 'Approved'),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateReferralUtilStatus(): should update utilization status', async () => {
    const util = utilizationFactory.build({
      status: 'Pending',
      preAuthorizationReferral: {
        id: 'referral-id',
        hospital: { supportMail: '<EMAIL>' },
        profile: { user: { nonCorporateEmail: '<EMAIL>' } },
        referredProvider: { supportMail: '<EMAIL>' },
        utilizations: [],
      },
      preAuthorizationReferralId: 'referral-id',
    });
    ManagerMock.find = jest.fn().mockResolvedValue([util]);

    // Mock the manager.findOne method for the mailDocService call
    ManagerMock.findOne = jest.fn().mockImplementation((model, options) => {
      if (
        model === PreauthorisationReferralModel &&
        options?.where?.id === 'referral-id'
      ) {
        return Promise.resolve({
          id: 'referral-id',
          providerId: '550e8400-e29b-41d4-a716-************', // Valid UUID
          hospital: { supportMail: '<EMAIL>' },
          profile: { user: { nonCorporateEmail: '<EMAIL>' } },
          referredProvider: { supportMail: '<EMAIL>' },
          utilizations: [util],
        });
      }
      return Promise.resolve(profile);
    });

    expect(util.status).toBe('Pending');

    MockDate.set(new Date('2025-02-19T22:57:16.823Z'));
    await service.updateReferralUtilStatus(
      profile,
      'Referral-code',
      'Approved',
    );
    MockDate.reset();
    expect(ManagerMock.find).toHaveBeenLastCalledWith(
      PreAuthReferralUtilisationsModel,
      {
        where: { paCode: 'Referral-code' },
        relations: [
          'preAuthorizationReferral',
          'preAuthorizationReferral.hospital',
        ],
      },
    );
    expect(ManagerMock.save).toHaveBeenLastCalledWith(
      PreAuthReferralUtilisationsModel,
      [
        {
          ...util,
          status: 'Approved',
          statusHistory: [
            {
              status: 'Approved',
              creatorId: profile.id,
              creatorName: profile.fullName,
              createdDate: new Date('2025-02-19T22:57:16.823Z'),
            },
          ],
        },
      ],
    );
  });
});
