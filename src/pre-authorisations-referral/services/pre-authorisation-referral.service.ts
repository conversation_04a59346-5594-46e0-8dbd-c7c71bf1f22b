import {
  forwardRef,
  Inject,
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { PreauthorizationReferralFilterInput } from '../inputs/pre-authorisation-referral-filter.input';
import {
  PreauthorisationReferralInput,
  PreauthorisationReferralUpdateInput,
  UpdateReferralUtilisationStatusInput,
} from '../inputs/preauthorisation-referral.input';
import { PreauthorisationReferralModel } from '../models/preauthorisation-referral.model';
import { PreAuthReferralUtilisationsModel } from '../models/utilisations-referral.model';
import { IPreauthorizationReferralRepository } from '../repositories/pre-authorization-referral.repositories';
import { PreauthorisationReferralResponse } from '../responses/pre-authorisation.response';
import { customDSSerializeInTransaction } from '@clinify/database';
import { HmoHospitalModel } from '@clinify/hmo-providers/models/hmo-hospital.model';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { HmoProviderService } from '@clinify/hmo-providers/services/hmo-provider.service';
import { RemindersService } from '@clinify/reminders/services/reminders.service';
import { mailDocService as MailDocService } from '@clinify/shared/services/mail-doc.service';
import { ProfileModel } from '@clinify/users/models/profile.model';

@Injectable()
export class PreauthorisationReferralService {
  constructor(
    @InjectRepository(PreauthorisationReferralModel)
    private repo: IPreauthorizationReferralRepository,
    @Inject(forwardRef(() => HmoProviderService))
    private hmoProviderService: HmoProviderService,
    private readonly manager: EntityManager,
    private readonly reminderService: RemindersService,
    private readonly mailDocService: MailDocService,
    private dataSource: DataSource,
  ) {}

  async addPreauthorizationReferral(
    mutator: ProfileModel,
    input: PreauthorisationReferralInput,
  ): Promise<PreauthorisationReferralModel> {
    const provider = await this.hmoProviderService.getHmoProviderModule(
      input.providerId,
      mutator.hmoId ? input.referredProviderId : mutator.hospitalId,
      mutator.clinifyId,
    );
    const hmoHospital = await this.manager
      .findOneOrFail(HmoHospitalModel, {
        where: {
          hospitalId: input.referredProviderId,
          providerId: input.providerId,
        },
      })
      .catch(() => {
        throw new NotFoundException('Referred Facility Not Found');
      });
    let pre: PreauthorisationReferralModel;

    if (provider.isClinify) {
      pre = await this.hmoProviderService.createPreauthorizationReferralRequest(
        mutator,
        input,
      );
    } else {
      pre = await provider.createPreauthorizationReferralRequest({
        ...input,
        staffEmail: mutator.user.email,
        refferalProviderId: hmoHospital.hmoProviderUniqueId,
      });
    }
    const profile = await this.manager.findOne(ProfileModel, {
      where: {
        clinifyId: input?.clinifyId,
      },
    });
    if (!profile) {
      throw new NotFoundException('Patient Not Found');
    }
    const preAuth = new PreauthorisationReferralModel({
      ...pre,
      profile,
      profileId: profile.id,
      providerId: input.providerId,
      hospitalId: mutator.hmoId
        ? input.referringProviderId
        : mutator.hospitalId,
      creatorName: mutator.fullName,
      creatorId: mutator.id,
      createdBy: mutator,
      enrolleeNumber: input.enrolleeId,
    });
    return this.repo.save(preAuth);
  }

  async updatePreauthorizationReferral(
    mutator: ProfileModel,
    referralId: string,
    input: PreauthorisationReferralUpdateInput,
  ): Promise<PreauthorisationReferralModel> {
    return customDSSerializeInTransaction(this.dataSource, async (manager) => {
      const repo = manager.withRepository(this.repo);
      const updatedPreAuthorization = await repo.updatePreauthorizationReferral(
        mutator,
        referralId,
        input,
      );
      return updatedPreAuthorization;
    });
  }

  async getPreauthorizationReferral(
    id: string,
  ): Promise<PreauthorisationReferralModel> {
    const preAuthorisation = await this.repo.getPreauthorizationReferral(id);
    return preAuthorisation;
  }

  async getPreauthorizationReferralByCode(
    referralCode: string,
  ): Promise<PreauthorisationReferralModel> {
    return this.repo.getPreauthorizationReferralByCode(referralCode);
  }

  async findByHospital(
    hospitalId: string,
    options: PreauthorizationReferralFilterInput,
    mutator: ProfileModel,
  ): Promise<PreauthorisationReferralResponse> {
    return this.repo.findByHospital(hospitalId, options, mutator);
  }

  async findByProfile(
    profileId: string,
    options: PreauthorizationReferralFilterInput,
    mutator: ProfileModel,
  ): Promise<PreauthorisationReferralResponse> {
    return this.repo.findByProfile(profileId, options, mutator);
  }
  async deletePreauthorizationReferrals(
    profile: ProfileModel,
    ids: string[],
  ): Promise<PreauthorisationReferralModel[]> {
    const preAuthorisation = await this.repo.deletePreauthorizationReferrals(
      profile,
      ids,
    );
    return preAuthorisation;
  }
  async archivePreauthorizationReferrals(
    profile: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<PreauthorisationReferralModel[]> {
    const preAuthorisation = await this.repo.archivePreauthorizationReferrals(
      profile,
      ids,
      archive,
    );
    return preAuthorisation;
  }

  async getHmoProvider(providerId: string) {
    return this.manager.findOneOrFail(HmoProviderModel, {
      where: { id: providerId },
    });
  }
  getActivePreauthorizationReferral(profileId: string, mutator: ProfileModel) {
    return this.repo.getActivePreauthorizationReferral(profileId, mutator);
  }

  async updateReferralUtilStatus(
    mutator: ProfileModel,
    referralCode: string,
    status: string,
  ): Promise<PreAuthReferralUtilisationsModel[]> {
    const utilizations = await this.manager.find(
      PreAuthReferralUtilisationsModel,
      {
        where: { paCode: referralCode },
        relations: [
          'preAuthorizationReferral',
          'preAuthorizationReferral.hospital',
        ],
      },
    );
    if (!utilizations?.length) {
      throw new NotAcceptableException('Record Not Found');
    }

    utilizations.forEach((util) => {
      if (util.status !== status) {
        util.statusHistory = [
          ...(util.statusHistory || []),
          {
            status,
            creatorId: mutator.id,
            creatorName: mutator.fullName,
            createdDate: new Date(),
          },
        ];
      }

      util.status = status;
    });

    if (status === 'Rejected') {
      this.reminderService.handleReminderEvent(mutator, {
        icon: 'pre-authorisation-referral',
        action: 'rejected',
        item: utilizations[0].preAuthorizationReferral,
      });
    }

    await this.manager.save(PreAuthReferralUtilisationsModel, utilizations);
    const referralIds = Array.from(
      new Set(
        utilizations.map(
          ({ preAuthorizationReferralId }) => preAuthorizationReferralId,
        ),
      ),
    );
    referralIds.forEach((referralId) => {
      this.mailDocService.sendEmailOnReferralApprovalOrRejection(
        referralId,
        status as any,
      );
    });

    return utilizations;
  }

  async updateReferralUtilisationStatus(
    mutator: ProfileModel,
    input: UpdateReferralUtilisationStatusInput,
  ): Promise<PreAuthReferralUtilisationsModel> {
    const utilization = await this.manager.findOne(
      PreAuthReferralUtilisationsModel,
      {
        where: {
          id: input.id,
        },
        relations: [
          'preAuthorizationReferral',
          'preAuthorizationReferral.hospital',
        ],
      },
    );

    if (!utilization) {
      throw new NotAcceptableException('Utilization Record Not Found');
    }

    if (utilization.status !== input.status) {
      utilization.statusHistory = [
        ...(utilization.statusHistory || []),
        {
          status: input.status,
          creatorId: mutator.id,
          creatorName: mutator.fullName,
          createdDate: new Date(),
        },
      ];
    }

    utilization.status = input.status;
    utilization.rejectionReason = input.rejectionReason;
    utilization.specifyReasonForRejection = input.specifyReasonForRejection;
    await this.manager.save(PreAuthReferralUtilisationsModel, utilization);

    if (input.status === 'Rejected') {
      this.reminderService.handleReminderEvent(mutator, {
        icon: 'pre-authorisation-referral',
        action: 'rejected',
        item: utilization.preAuthorizationReferral,
      });
    }
    this.mailDocService.sendEmailOnReferralApprovalOrRejection(
      utilization.preAuthorizationReferralId,
      input.status as any,
    );

    return utilization;
  }
}
