/* eslint-disable @typescript-eslint/unbound-method */
import { NotAcceptableException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { PreauthorisationReferralModel } from '../../models/preauthorisation-referral.model';
import { PreAuthReferralUtilisationsModel } from '../../models/utilisations-referral.model';
import { PreauthorisationReferralService } from '../pre-authorisation-referral.service';
import { HmoProviderService } from '@clinify/hmo-providers/services/hmo-provider.service';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { RemindersService } from '@clinify/reminders/services/reminders.service';
import { mailDocService as MailDocService } from '@clinify/shared/services/mail-doc.service';
import { ProfileModel } from '@clinify/users/models/profile.model';

describe('PreauthorisationReferralService - Email Functionality', () => {
  let service: PreauthorisationReferralService;
  let entityManager: jest.Mocked<EntityManager>;
  let mailDocService: jest.Mocked<MailDocService>;
  let reminderService: jest.Mocked<RemindersService>;

  const mockProfile = {
    id: 'profile-id',
    fullName: 'John Doe',
    user: { nonCorporateEmail: '<EMAIL>' },
  } as ProfileModel;

  const mockHospital = {
    id: 'hospital-id',
    name: 'Test Hospital',
    supportMail: '<EMAIL>',
  } as HospitalModel;

  const mockReferral = {
    id: 'referral-id',
    code: 'REF123',
    serviceType: 'Cardiology',
    enrolleeNumber: 'ENR123',
    providerId: 'provider-id',
    profile: mockProfile,
    hospital: mockHospital,
  } as PreauthorisationReferralModel;

  const mockUtilization = {
    id: 'util-id',
    paCode: 'PA123',
    status: 'Pending',
    preAuthorizationReferralId: 'referral-id',
    preAuthorizationReferral: mockReferral,
    statusHistory: [],
  } as PreAuthReferralUtilisationsModel;

  beforeEach(async () => {
    const mockEntityManager = {
      find: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
    };

    const mockRepository = {
      getActivePreauthorizationReferral: jest.fn(),
    };

    const mockMailDocService = {
      sendEmailOnReferralApprovalOrRejection: jest.fn(),
    };

    const mockReminderService = {
      handleReminderEvent: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PreauthorisationReferralService,
        {
          provide: getRepositoryToken(PreauthorisationReferralModel),
          useValue: mockRepository,
        },
        {
          provide: HmoProviderService,
          useValue: {},
        },
        {
          provide: EntityManager,
          useValue: mockEntityManager,
        },
        {
          provide: RemindersService,
          useValue: mockReminderService,
        },
        {
          provide: MailDocService,
          useValue: mockMailDocService,
        },
        {
          provide: DataSource,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<PreauthorisationReferralService>(
      PreauthorisationReferralService,
    );
    entityManager = module.get<EntityManager>(
      EntityManager,
    ) as jest.Mocked<EntityManager>;
    mailDocService = module.get<MailDocService>(
      MailDocService,
    ) as jest.Mocked<MailDocService>;
    reminderService = module.get<RemindersService>(
      RemindersService,
    ) as jest.Mocked<RemindersService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('updateReferralUtilStatus', () => {
    it('should send email when referral is approved', async () => {
      const mutator = {
        id: 'mutator-id',
        fullName: 'Admin User',
      } as ProfileModel;

      const referralCode = 'REF123';
      const status = 'Approved';

      const mockUtilizations = [
        {
          ...mockUtilization,
          status: 'Pending',
          preAuthorizationReferral: mockReferral,
        },
      ];

      entityManager.find.mockResolvedValue(mockUtilizations);
      entityManager.save.mockResolvedValue(mockUtilizations);
      mailDocService.sendEmailOnReferralApprovalOrRejection.mockResolvedValue(
        true,
      );

      const result = await service.updateReferralUtilStatus(
        mutator,
        referralCode,
        status,
      );

      // Verify that utilizations are found with correct relations
      expect(entityManager.find).toHaveBeenCalledWith(
        PreAuthReferralUtilisationsModel,
        {
          where: { paCode: referralCode },
          relations: [
            'preAuthorizationReferral',
            'preAuthorizationReferral.hospital',
          ],
        },
      );

      // Verify that status is updated
      expect(result[0].status).toBe('Approved');
      expect(result[0].statusHistory).toHaveLength(1);
      expect(result[0].statusHistory[0]).toMatchObject({
        status: 'Approved',
        creatorId: 'mutator-id',
        creatorName: 'Admin User',
      });

      // Verify that utilizations are saved
      expect(entityManager.save).toHaveBeenCalledWith(
        PreAuthReferralUtilisationsModel,
        mockUtilizations,
      );

      // Verify that email is sent for each unique referral
      expect(
        mailDocService.sendEmailOnReferralApprovalOrRejection,
      ).toHaveBeenCalledWith('referral-id', 'Approved');

      expect(result).toEqual(mockUtilizations);
    });

    it('should send email when referral is rejected and handle reminder', async () => {
      const mutator = {
        id: 'mutator-id',
        fullName: 'Admin User',
      } as ProfileModel;

      const referralCode = 'REF123';
      const status = 'Rejected';

      const mockUtilizations = [
        {
          ...mockUtilization,
          status: 'Pending',
          preAuthorizationReferral: mockReferral,
        },
      ];

      entityManager.find.mockResolvedValue(mockUtilizations);
      entityManager.save.mockResolvedValue(mockUtilizations);
      mailDocService.sendEmailOnReferralApprovalOrRejection.mockResolvedValue(
        true,
      );

      const result = await service.updateReferralUtilStatus(
        mutator,
        referralCode,
        status,
      );

      // Verify that status is updated to Rejected
      expect(result[0].status).toBe('Rejected');
      expect(result[0].statusHistory).toHaveLength(1);
      expect(result[0].statusHistory[0].status).toBe('Rejected');

      // Verify that reminder is handled for rejection
      expect(reminderService.handleReminderEvent).toHaveBeenCalledWith(
        mutator,
        {
          icon: 'pre-authorisation-referral',
          action: 'rejected',
          item: mockReferral,
        },
      );

      // Verify that email is sent for rejection
      expect(
        mailDocService.sendEmailOnReferralApprovalOrRejection,
      ).toHaveBeenCalledWith('referral-id', 'Rejected');

      expect(result).toEqual(mockUtilizations);
    });

    it('should handle multiple utilizations with same referral ID', async () => {
      const mutator = {
        id: 'mutator-id',
        fullName: 'Admin User',
      } as ProfileModel;

      const referralCode = 'REF123';
      const status = 'Approved';

      const mockUtilizations = [
        {
          ...mockUtilization,
          id: 'util-id-1',
          status: 'Pending',
          preAuthorizationReferralId: 'referral-id',
          preAuthorizationReferral: mockReferral,
        },
        {
          ...mockUtilization,
          id: 'util-id-2',
          status: 'Pending',
          preAuthorizationReferralId: 'referral-id',
          preAuthorizationReferral: mockReferral,
        },
      ];

      entityManager.find.mockResolvedValue(mockUtilizations);
      entityManager.save.mockResolvedValue(mockUtilizations);
      mailDocService.sendEmailOnReferralApprovalOrRejection.mockResolvedValue(
        true,
      );

      const result = await service.updateReferralUtilStatus(
        mutator,
        referralCode,
        status,
      );

      // Verify that both utilizations are updated
      expect(result).toHaveLength(2);
      expect(result[0].status).toBe('Approved');
      expect(result[1].status).toBe('Approved');

      // Verify that email is sent only once per unique referral ID
      expect(
        mailDocService.sendEmailOnReferralApprovalOrRejection,
      ).toHaveBeenCalledTimes(1);
      expect(
        mailDocService.sendEmailOnReferralApprovalOrRejection,
      ).toHaveBeenCalledWith('referral-id', 'Approved');

      expect(result).toEqual(mockUtilizations);
    });

    it('should handle multiple utilizations with different referral IDs', async () => {
      const mutator = {
        id: 'mutator-id',
        fullName: 'Admin User',
      } as ProfileModel;

      const referralCode = 'REF123';
      const status = 'Approved';

      const anotherReferral = {
        ...mockReferral,
        id: 'referral-id-2',
        code: 'REF456',
      };

      const mockUtilizations = [
        {
          ...mockUtilization,
          id: 'util-id-1',
          status: 'Pending',
          preAuthorizationReferralId: 'referral-id',
          preAuthorizationReferral: mockReferral,
        },
        {
          ...mockUtilization,
          id: 'util-id-2',
          status: 'Pending',
          preAuthorizationReferralId: 'referral-id-2',
          preAuthorizationReferral: anotherReferral,
        },
      ];

      entityManager.find.mockResolvedValue(mockUtilizations);
      entityManager.save.mockResolvedValue(mockUtilizations);
      mailDocService.sendEmailOnReferralApprovalOrRejection.mockResolvedValue(
        true,
      );

      const result = await service.updateReferralUtilStatus(
        mutator,
        referralCode,
        status,
      );

      // Verify that both utilizations are updated
      expect(result).toHaveLength(2);
      expect(result[0].status).toBe('Approved');
      expect(result[1].status).toBe('Approved');

      // Verify that email is sent for each unique referral ID
      expect(
        mailDocService.sendEmailOnReferralApprovalOrRejection,
      ).toHaveBeenCalledTimes(2);
      expect(
        mailDocService.sendEmailOnReferralApprovalOrRejection,
      ).toHaveBeenCalledWith('referral-id', 'Approved');
      expect(
        mailDocService.sendEmailOnReferralApprovalOrRejection,
      ).toHaveBeenCalledWith('referral-id-2', 'Approved');

      expect(result).toEqual(mockUtilizations);
    });

    it('should not add status history if status is the same', async () => {
      const mutator = {
        id: 'mutator-id',
        fullName: 'Admin User',
      } as ProfileModel;

      const referralCode = 'REF123';
      const status = 'Approved';

      const mockUtilizations = [
        {
          ...mockUtilization,
          status: 'Approved', // Same status
          statusHistory: [
            {
              status: 'Approved',
              creatorId: 'previous-user',
              creatorName: 'Previous User',
              createdDate: new Date('2023-01-01'),
            },
          ],
          preAuthorizationReferral: mockReferral,
        },
      ];

      entityManager.find.mockResolvedValue(mockUtilizations);
      entityManager.save.mockResolvedValue(mockUtilizations);
      mailDocService.sendEmailOnReferralApprovalOrRejection.mockResolvedValue(
        true,
      );

      const result = await service.updateReferralUtilStatus(
        mutator,
        referralCode,
        status,
      );

      // Verify that status history is not updated when status is the same
      expect(result[0].status).toBe('Approved');
      expect(result[0].statusHistory).toHaveLength(1);
      expect(result[0].statusHistory[0].creatorId).toBe('previous-user');

      // Email should still be sent
      expect(
        mailDocService.sendEmailOnReferralApprovalOrRejection,
      ).toHaveBeenCalledWith('referral-id', 'Approved');

      expect(result).toEqual(mockUtilizations);
    });

    it('should throw NotAcceptableException when no utilizations found', async () => {
      const mutator = {
        id: 'mutator-id',
        fullName: 'Admin User',
      } as ProfileModel;

      const referralCode = 'NON_EXISTENT';
      const status = 'Approved';

      entityManager.find.mockResolvedValue([]);

      await expect(
        service.updateReferralUtilStatus(mutator, referralCode, status),
      ).rejects.toThrow(NotAcceptableException);
      await expect(
        service.updateReferralUtilStatus(mutator, referralCode, status),
      ).rejects.toThrow('Record Not Found');

      // Verify that no email is sent when no records found
      expect(
        mailDocService.sendEmailOnReferralApprovalOrRejection,
      ).not.toHaveBeenCalled();

      // Verify that no reminder is handled when no records found
      expect(reminderService.handleReminderEvent).not.toHaveBeenCalled();
    });

    it('should handle null utilizations array', async () => {
      const mutator = {
        id: 'mutator-id',
        fullName: 'Admin User',
      } as ProfileModel;

      const referralCode = 'REF123';
      const status = 'Approved';

      entityManager.find.mockResolvedValue(null);

      await expect(
        service.updateReferralUtilStatus(mutator, referralCode, status),
      ).rejects.toThrow(NotAcceptableException);
      await expect(
        service.updateReferralUtilStatus(mutator, referralCode, status),
      ).rejects.toThrow('Record Not Found');

      // Verify that no email is sent when utilizations is null
      expect(
        mailDocService.sendEmailOnReferralApprovalOrRejection,
      ).not.toHaveBeenCalled();
    });

    it('should only handle reminder for rejected status', async () => {
      const mutator = {
        id: 'mutator-id',
        fullName: 'Admin User',
      } as ProfileModel;

      const referralCode = 'REF123';
      const status = 'Approved'; // Not rejected

      const mockUtilizations = [
        {
          ...mockUtilization,
          status: 'Pending',
          preAuthorizationReferral: mockReferral,
        },
      ];

      entityManager.find.mockResolvedValue(mockUtilizations);
      entityManager.save.mockResolvedValue(mockUtilizations);
      mailDocService.sendEmailOnReferralApprovalOrRejection.mockResolvedValue(
        true,
      );

      const result = await service.updateReferralUtilStatus(
        mutator,
        referralCode,
        status,
      );

      // Verify that reminder is NOT handled for non-rejected status
      expect(reminderService.handleReminderEvent).not.toHaveBeenCalled();

      // But email should still be sent
      expect(
        mailDocService.sendEmailOnReferralApprovalOrRejection,
      ).toHaveBeenCalledWith('referral-id', 'Approved');

      expect(result).toEqual(mockUtilizations);
    });
  });
});
