/* eslint-disable prefer-arrow/prefer-arrow-functions */
/* eslint-disable max-lines */
import {
  ForbiddenException,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import moment from 'moment';
import { In, MoreThan, Repository, SelectQueryBuilder } from 'typeorm';
import {
  PreauthorizationReferralFilterInput,
  PreauthReferralDateFilterType,
} from '../inputs/pre-authorisation-referral-filter.input';
import { PreauthorisationReferralUpdateInput } from '../inputs/preauthorisation-referral.input';
import { PreauthorisationReferralModel } from '../models/preauthorisation-referral.model';
import { PreAuthReferralUtilisationsModel } from '../models/utilisations-referral.model';
import { PreauthorisationReferralResponse } from '../responses/pre-authorisation.response';
import { HmoPlanStatus } from '@clinify/hmo-providers/inputs/hmo-plan.input';
import { HmoPlanBenefitModel } from '@clinify/hmo-providers/models/hmo-plan-benefit.model';
import { HmoProfilePlanModel } from '@clinify/hmo-providers/models/hmo-profile-plan.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { PreauthorisationModel } from '@clinify/pre-authorisations/models/preauthorisation.model';
import { UserType } from '@clinify/shared/enums/users';
import { getMembershipNoFromMemberNo } from '@clinify/shared/helper';
import { RecordCreator } from '@clinify/shared/validators/filter.input';
import { validateHmoRecordArchiver } from '@clinify/shared/validators/validate-record-mutation.validator';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { takePaginatedResponses } from '@clinify/utils/pagination';

export interface IPreauthorizationReferralRepository
  extends Repository<PreauthorisationReferralModel> {
  this: Repository<PreauthorisationReferralModel>;

  baseQuery(
    query,
    options,
    keywordConditions,
    mutator?: ProfileModel,
  ): Promise<PreauthorisationReferralResponse>;

  updatePreauthorizationReferral(
    mutator: ProfileModel,
    referralId: string,
    input: PreauthorisationReferralUpdateInput,
  ): Promise<PreauthorisationReferralModel>;

  getPreauthorizationReferral(
    id: string,
  ): Promise<PreauthorisationReferralModel>;

  getPreauthorizationReferralByCode(
    referralCode: string,
  ): Promise<PreauthorisationReferralModel>;

  getActivePreauthorizationReferral(
    profileId: string,
    mutator: ProfileModel,
  ): Promise<PreauthorisationReferralModel>;

  findByProfile(
    profileId: string,
    options: Partial<PreauthorizationReferralFilterInput>,
    mutator: ProfileModel,
  ): Promise<PreauthorisationReferralResponse>;

  findByHospital(
    hospitalId: string,
    options: Partial<PreauthorizationReferralFilterInput>,
    mutator: ProfileModel,
  ): Promise<PreauthorisationReferralResponse>;

  deletePreauthorizationReferrals(
    profile: ProfileModel,
    ids: string[],
  ): Promise<PreauthorisationReferralModel[]>;

  archivePreauthorizationReferrals(
    profile: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<PreauthorisationReferralModel[]>;
}

export const CustomPreauthorizationReferralRepoMethods: Pick<
  IPreauthorizationReferralRepository,
  | 'baseQuery'
  | 'updatePreauthorizationReferral'
  | 'getPreauthorizationReferral'
  | 'getPreauthorizationReferralByCode'
  | 'findByProfile'
  | 'findByHospital'
  | 'deletePreauthorizationReferrals'
  | 'archivePreauthorizationReferrals'
  | 'getActivePreauthorizationReferral'
> = {
  async baseQuery(
    this: IPreauthorizationReferralRepository,
    query: SelectQueryBuilder<PreauthorisationReferralModel>,
    options: PreauthorizationReferralFilterInput,
    keywordConditions,
    mutator?: ProfileModel,
  ): Promise<PreauthorisationReferralResponse> {
    const {
      skip = 0,
      take = 10,
      dateRange,
      keyword,
      creator,
      status,
      hospitalId,
      providerType,
      filterDateField,
      showCompleted,
      showNotCompleted,
      timeSortOrder,
    } = options;
    const columnForDateFilter = determineDateField(filterDateField);
    if (creator)
      query = query
        .withDeleted()
        .innerJoinAndSelect('preauthorization_referrals.createdBy', 'createdBy')
        .innerJoinAndSelect(
          'preauthorization_referrals.referredProvider',
          'referredProvider',
        )
        .andWhere(
          creator === RecordCreator.SELF
            ? 'createdBy.type = :patient'
            : 'createdBy.type != :patient',
          { patient: UserType.Patient },
        );
    if (!mutator.hmoId) {
      if (hospitalId) {
        query = query.andWhere(
          `(
            preauthorization_referrals.hospital_id = :hospitalId OR
            preauthorization_referrals.referred_provider_id = :hospitalId
          )`,
          {
            hospitalId,
          },
        );
      }
    }

    if (providerType) {
      query = query.andWhere(
        `(
            hospital.plan ILIKE :providerType OR
            referredProvider.plan ILIKE :providerType
          )`,
        {
          providerType: `${providerType}%`,
        },
      );
    }

    if (dateRange?.from) {
      query = query.andWhere(
        `(preauthorization_referrals.${columnForDateFilter} >= :from)`,
        {
          from: dateRange.from,
        },
      );
    }

    if (dateRange?.to) {
      query = query.andWhere(
        `(preauthorization_referrals.${columnForDateFilter} < :to)`,
        {
          to: dateRange.to,
        },
      );
    }

    if (status) {
      query = query.andWhere('utilizations.status = :status', { status });
    }

    if (showCompleted) {
      query = query.andWhere(
        `NOT EXISTS (
          SELECT 1 FROM pre_auth_referral_utilisations u 
          WHERE u.pre_auth_referral_id = preauthorization_referrals.id 
          AND u.status = 'Pending'
        )`,
      );
    }
    if (showNotCompleted) {
      query = query.andWhere(
        `EXISTS (
          SELECT 1 FROM pre_auth_referral_utilisations u 
          WHERE u.pre_auth_referral_id = preauthorization_referrals.id 
          AND u.status = 'Pending'
        )`,
      );
    }

    if (keyword)
      query = query.andWhere(keywordConditions, { keyword: `%${keyword}%` });

    if (timeSortOrder) {
      query = query
        .addSelect(
          'preauthorization_referrals.updated_date::date',
          'updated_date_only',
        )
        .addSelect(
          'preauthorization_referrals.updated_date::time',
          'updated_time_only',
        )
        .orderBy('updated_date_only', 'DESC')
        .addOrderBy('updated_time_only', timeSortOrder);
    } else {
      query = query.orderBy('preauthorization_referrals.updatedDate', 'DESC');
    }

    query = query.skip(skip).take(take);

    const response = await query.getManyAndCount();

    return new PreauthorisationReferralResponse(
      ...takePaginatedResponses(response, take),
    );
  },
  async updatePreauthorizationReferral(
    this: IPreauthorizationReferralRepository,
    mutator: ProfileModel,
    referralId: string,
    input: PreauthorisationReferralUpdateInput,
  ): Promise<PreauthorisationReferralModel> {
    let preAuthorization = await this.findOneOrFail({
      where: { id: referralId },
      relations: ['utilizations', 'referredProvider', 'hospital', 'profile'],
    }).catch(() => {
      throw new NotFoundException('Referral Not Found');
    });

    const currentPACode = preAuthorization.utilizations[0].paCode;

    if (
      !(
        (mutator?.hmoId && mutator.hmoId === preAuthorization.providerId) ||
        (mutator?.hospitalId &&
          mutator.hospitalId === preAuthorization.hospitalId)
      )
    ) {
      throw new ForbiddenException('Not Authorized To Modify This Record');
    }

    let referredProvider = preAuthorization.referredProvider;

    if (
      input?.referredProviderId &&
      preAuthorization.referredProviderId !== input.referredProviderId
    ) {
      referredProvider = await this.manager
        .findOneOrFail(HospitalModel, {
          where: { id: input.referredProviderId },
        })
        .catch(() => {
          throw new NotFoundException('Hospital Not Found');
        });
    }

    const inputUtils = input.utilizations || [];
    delete input.utilizations;
    if (input.serviceTypeCode !== preAuthorization.serviceTypeCode) {
      const profilePlan = await this.manager.findOne(HmoProfilePlanModel, {
        where: {
          membershipNumber: getMembershipNoFromMemberNo(
            preAuthorization.enrolleeNumber,
          ),
          hmoProviderId: preAuthorization.providerId,
          status: HmoPlanStatus.Active,
        },
        relations: ['planType'],
      });

      const existingUtils = preAuthorization.utilizations || [];
      const codes = existingUtils.map((util) => util.utilizationCode);

      if (existingUtils?.length) {
        const query = this.manager
          .createQueryBuilder(HmoPlanBenefitModel, 'benefit')
          .where('benefit.hmoProviderId = :providerId', {
            providerId: preAuthorization.providerId,
          })
          .andWhere('benefit.planTypeId = :planTypeId', {
            planTypeId: profilePlan.planTypeId,
          })
          .andWhere('benefit.visitTypeId = :visitTypeId', {
            visitTypeId: input.serviceTypeCode,
          })
          .andWhere(
            `EXISTS (
            SELECT 1
            FROM jsonb_array_elements(benefit.utilisationTypes) AS elem
            WHERE elem->>'code' = ANY(:codes)
          )`,
            { codes },
          )
          .select(['benefit.id', 'benefit.utilisationTypes']);

        const result = await query.getMany();
        if (result?.length) {
          for (const util of existingUtils) {
            const benefit = result.find((b) =>
              b.utilisationTypes.some(
                (type) => type.code === util.utilizationCode,
              ),
            );
            if (!benefit)
              throw new NotAcceptableException('Invalid Utilization Code');

            const uType = benefit.utilisationTypes.find(
              (type) => type.code === util.utilizationCode,
            );
            if (!uType)
              throw new NotAcceptableException('Invalid Utilization Code');

            util.utilizationId = benefit.id;
            util.utilizationCode = uType.code;
            await this.manager.save(PreAuthReferralUtilisationsModel, util);
            const inputUtilIndex = inputUtils.findIndex(
              (u) => u.utilizationCode === util.utilizationCode,
            );
            if (inputUtilIndex > -1) {
              inputUtils[inputUtilIndex].utilizationId = benefit.id;
            }
          }
          await this.manager.update(
            PreauthorisationReferralModel,
            { id: referralId },
            {
              serviceType: input.serviceType,
              serviceTypeCode: input.serviceTypeCode,
              serviceName: input.serviceName,
            },
          );
          preAuthorization = await this.findOneOrFail({
            where: { id: referralId },
            relations: [
              'utilizations',
              'referredProvider',
              'hospital',
              'profile',
            ],
          });
        } else {
          throw new NotAcceptableException(
            'No Valid Utilization Types Found For This Visitation Type',
          );
        }
      }
    }
    const existingUtils = preAuthorization.utilizations || [];
    const deletedUtils = [];

    const newExistingUtils = existingUtils
      .map((_util) => {
        if (_util.status && _util.status !== 'Pending') return _util;
        const _exist = inputUtils.find((_item) => _item.id === _util?.id);
        if (_exist)
          return {
            ..._util,
            ..._exist,
            paCode: currentPACode,
          };

        deletedUtils.push(_util);
        return null;
      })
      .filter(Boolean);

    const newUtilizations = [];

    let pendingUtilizations = newExistingUtils.filter(
      (util) => !util?.status || util.status === 'Pending',
    );
    pendingUtilizations = pendingUtilizations.map((_pendUtil) => {
      const _utilization = inputUtils.find(
        (_util) => _util.id && _util.id === _pendUtil?.id,
      );
      if (!_utilization) return _pendUtil;

      if (
        _pendUtil?.utilizationCode !== _utilization.utilizationCode ||
        _pendUtil?.utilizationId !== _utilization.utilizationId ||
        _pendUtil?.quantity !== _utilization.quantity
      ) {
        newUtilizations.push(_utilization as PreAuthReferralUtilisationsModel);
      }

      return _utilization as PreAuthReferralUtilisationsModel;
    });
    inputUtils.forEach((util) => {
      if (util?.id) return;

      const _exist = pendingUtilizations.find(
        (_util) =>
          _util.utilizationCode === util?.utilizationCode &&
          _util.utilizationId === util?.utilizationId,
      );
      if (_exist) return;
      newUtilizations.push(util);
    });

    if (deletedUtils.length) {
      await this.manager.delete(PreAuthReferralUtilisationsModel, deletedUtils);
    }

    if (!newUtilizations.length) {
      return this.save({
        ...preAuthorization,
        ...input,
        utilizations: newExistingUtils,
        referredProvider,
        lastModifierName: mutator.fullName,
        updatedBy: mutator,
        updatedDate: new Date(),
      });
    }

    const updatedUtilization = await this.manager.save(
      PreAuthReferralUtilisationsModel,
      newUtilizations?.map((utilisation) => {
        return new PreAuthReferralUtilisationsModel({
          ...utilisation,
          preAuthorizationReferralId: referralId,
          visitDetailsId: existingUtils[0].visitDetailsId,
          paCode: currentPACode,
        });
      }),
    );

    const utilizationsToReturn = [...newExistingUtils];

    updatedUtilization.forEach((utilisation) => {
      const _index = utilizationsToReturn.findIndex(
        ({ id: _id }) => utilisation.id === _id,
      );
      if (_index === -1) {
        utilizationsToReturn.push(utilisation);
      }
      utilizationsToReturn[_index] = utilisation;
    });

    return this.save({
      ...preAuthorization,
      ...input,
      utilizations: utilizationsToReturn,
      referredProvider,
      lastModifierName: mutator.fullName,
      updatedBy: mutator,
      updatedDate: new Date(),
    });
  },
  getPreauthorizationReferral(
    id: string,
  ): Promise<PreauthorisationReferralModel> {
    const preAuthorization = this.findOneOrFail({
      where: {
        id,
      },
      relations: ['utilizations', 'referredProvider', 'hospital'],
      withDeleted: true,
    }).catch(() => {
      throw new NotFoundException('Preauthorization Not Found');
    });

    return preAuthorization;
  },
  async getPreauthorizationReferralByCode(
    referralCode: string,
  ): Promise<PreauthorisationReferralModel> {
    const preAuthorizationReferral = await this.findOneOrFail({
      where: { utilizations: { paCode: referralCode } },
      relations: ['utilizations', 'hospital'],
    }).catch(() => {
      throw new NotFoundException('Preauthorization Not Found');
    });

    return preAuthorizationReferral;
  },
  async findByProfile(
    this: IPreauthorizationReferralRepository,
    profileId: string,
    options: Partial<PreauthorizationReferralFilterInput>,
    mutator: ProfileModel,
  ): Promise<PreauthorisationReferralResponse> {
    const query = this.createQueryBuilder('preauthorization_referrals')
      .leftJoinAndSelect(
        'preauthorization_referrals.utilizations',
        'utilizations',
      )
      .leftJoinAndSelect('preauthorization_referrals.provider', 'provider')
      .leftJoinAndSelect('preauthorization_referrals.hospital', 'hospital')
      .where('preauthorization_referrals.profileId = :profileId', { profileId })
      .andWhere('preauthorization_referrals.archived = :archived', {
        archived: !!options.archive,
      });
    if (mutator.hmoId) {
      query.andWhere('(provider.id = :hmoId)', { hmoId: mutator.hmoId });
    }

    const keywordConditions = `
      (preauthorization_referrals.code ILIKE :keyword OR
      preauthorization_referrals.requested_by ILIKE :keyword OR
      preauthorization_referrals.priority ILIKE :keyword OR
      preauthorization_referrals.service_type ILIKE :keyword OR
      preauthorization_referrals.service_name ILIKE :keyword OR
      utilizations.category ILIKE :keyword OR
      utilizations.pa_code ILIKE :keyword OR
      preauthorization_referrals.diagnosis::text ILIKE :keyword OR
      provider.name ILIKE :keyword OR
      hospital.name ILIKE :keyword OR
      utilizations.type ILIKE :keyword OR
      preauthorization_referrals.visitId ILIKE :keyword)
    `;

    return this.baseQuery(query, options, keywordConditions, mutator);
  },

  async findByHospital(
    this: IPreauthorizationReferralRepository,
    hospitalId: string,
    options: Partial<PreauthorizationReferralFilterInput>,
    mutator: ProfileModel,
  ): Promise<PreauthorisationReferralResponse> {
    const query = this.createQueryBuilder('preauthorization_referrals')
      .leftJoinAndSelect('preauthorization_referrals.profile', 'profile')
      .leftJoinAndSelect(
        'preauthorization_referrals.utilizations',
        'utilizations',
      )
      .leftJoinAndSelect('preauthorization_referrals.provider', 'provider')
      .leftJoinAndSelect('preauthorization_referrals.hospital', 'hospital')
      .where('preauthorization_referrals.archived = :archived', {
        archived: !!options.archive,
      });
    if (mutator.hmoId) {
      query.andWhere('provider.id = :hmoId', { hmoId: mutator.hmoId });
      if (options.hospitalId && !options.providerInsight) {
        query.andWhere(
          `(
            preauthorization_referrals.hospital_id = :hospitalId OR
            preauthorization_referrals.referred_provider_id = :hospitalId
          )`,
          {
            hospitalId: options.hospitalId,
          },
        );
      }
      if (options.providerInsight) {
        query.andWhere(
          '(preauthorization_referrals.hospital_id = :hospitalId)',
          {
            hospitalId,
          },
        );
      }
    } else if (mutator.isPartnerProfile && mutator.partnerId) {
      query.andWhere('(hospital.partnerId = :partnerId)', {
        partnerId: mutator.partnerId,
      });
      if (options.hospitalId && !options.providerInsight) {
        query.andWhere(
          '(preauthorization_referrals.hospital_id = :hospitalId)',
          {
            hospitalId: options.hospitalId,
          },
        );
      }
      if (options.providerInsight) {
        query.andWhere(
          '(preauthorization_referrals.hospital_id = :hospitalId)',
          {
            hospitalId,
          },
        );
      }
    } else {
      query.andWhere(
        'preauthorization_referrals.hospital_id = :hospitalId OR preauthorization_referrals.referred_provider_id = :hospitalId',
        {
          hospitalId,
        },
      );
    }
    const keywordConditions = `
      (preauthorization_referrals.code ILIKE :keyword OR
      preauthorization_referrals.requested_by ILIKE :keyword OR
      preauthorization_referrals.priority ILIKE :keyword OR
      preauthorization_referrals.service_type ILIKE :keyword OR
      preauthorization_referrals.service_name ILIKE :keyword OR
      hospital.name ILIKE :keyword OR
      utilizations.category ILIKE :keyword OR
      utilizations.pa_code ILIKE :keyword OR
      preauthorization_referrals.diagnosis::text ILIKE :keyword OR
      provider.name ILIKE :keyword OR
      profile.full_name ILIKE :keyword OR
      profile.clinify_id ILIKE :keyword OR
      utilizations.type ILIKE :keyword OR
      preauthorization_referrals.enrollee_number ILIKE :keyword OR
      preauthorization_referrals.visitId ILIKE :keyword)
    `;

    return this.baseQuery(query, options, keywordConditions, mutator);
  },

  deletePreauthorizationReferrals(
    this: IPreauthorizationReferralRepository,
  ): Promise<PreauthorisationReferralModel[]> {
    throw new NotAcceptableException('Record Cannot Be Deleted');
  },

  async archivePreauthorizationReferrals(
    this: IPreauthorizationReferralRepository,
    profile: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<PreauthorisationReferralModel[]> {
    const items = await this.find({
      relations: ['utilizations', 'hospital'],
      where: { id: In(ids) },
    });
    const validResources = validateHmoRecordArchiver(
      profile,
      items,
    ) as PreauthorisationReferralModel[];
    const validIds = validResources.map((v) => v.id);
    if (!validIds.length) return [];
    await this.createQueryBuilder('pre_auths')
      .update(PreauthorisationReferralModel)
      .set({ archived: archive, updatedDate: () => 'updated_date' })
      .whereInIds(validIds)
      .execute();

    return validResources.map((v) => ({ ...v, archived: archive }));
  },

  async getActivePreauthorizationReferral(
    this: IPreauthorizationReferralRepository,
    profileId: string,
    mutator: ProfileModel,
  ): Promise<PreauthorisationReferralModel> {
    const res = await this.findOne({
      where: {
        profileId,
        createdDate: MoreThan(moment().subtract(24, 'hours').toDate()),
        referredProviderId: mutator.hospitalId,
      },
      relations: ['utilizations', 'referredProvider', 'hospital'],
    });

    return res;
  },
};

function determineDateField(t: PreauthReferralDateFilterType) {
  switch (t) {
    case PreauthReferralDateFilterType.RequestDate:
      return 'request_date_time';
    case PreauthReferralDateFilterType.CreatedDate:
      return 'created_date';
    default:
      return 'request_date_time';
  }
}
