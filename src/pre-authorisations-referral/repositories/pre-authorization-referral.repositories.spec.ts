/* eslint-disable max-lines */
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import Chance from 'chance';
import moment from 'moment/moment';
import { DataSource, EntityManager } from 'typeorm';
import {
  CustomPreauthorizationReferralRepoMethods,
  IPreauthorizationReferralRepository,
} from './pre-authorization-referral.repositories';
import { TestDataSourceOptions } from '../../data-source';
import * as db from '../../database';
import { extendDSRepo, extendModel } from '../../database/extendModel';
import { PreauthorisationReferralModel } from '../models/preauthorisation-referral.model';
import { HmoPlanTypeFactory } from '@clinify/__mocks__/factories/hmo-provider.factory';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { CustomHmoClaimRepoMethods } from '@clinify/hmo-claims/repositories/hmo-claim.repository';
import { HmoPlanStatus } from '@clinify/hmo-providers/inputs/hmo-plan.input';
import { HmoPlanTypeModel } from '@clinify/hmo-providers/models/hmo-plan-type.model';
import { HmoProfileBenefitModel } from '@clinify/hmo-providers/models/hmo-profile-benefit.model';
import { HmoProfilePlanModel } from '@clinify/hmo-providers/models/hmo-profile-plan.model';
import { HmoProviderRepository } from '@clinify/hmo-providers/repositories/hmo-provider.repository';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { TimeSortOrder } from '@clinify/pre-authorisations/inputs/pre-authorisation-filter.input';
import { UserType } from '@clinify/shared/enums/users';
import { RecordCreator } from '@clinify/shared/validators/filter.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { createHmoProfileFixtures } from '@clinify/utils/tests/hmo-profiles.fixtures';
import { createHmoProviderFixtures } from '@clinify/utils/tests/hmo-provider.fixtures';
import { createHospitals } from '@clinify/utils/tests/hospital.fixtures';
import { createPreauthorizationReferral } from '@clinify/utils/tests/preauthorization-referral.fixtures';
import { createUsers } from '@clinify/utils/tests/user.fixtures';
import { createPartner } from '@fixtures/partner.fixture';

const chance = new Chance();

describe('Pre-authorization repository', () => {
  let preAuthorizations: PreauthorisationReferralModel[];
  let profile: ProfileModel;
  let hospital: HospitalModel;
  let preAuthorization: PreauthorisationReferralModel;
  let manager: EntityManager;
  let repo: IPreauthorizationReferralRepository;

  let module: TestingModule;
  let ds: DataSource;

  const spySlaveQuery = jest.spyOn(db, 'queryWithSlave');

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        extendModel(
          PreauthorisationReferralModel,
          CustomPreauthorizationReferralRepoMethods,
        ),
        HmoProviderRepository,
        extendModel(HmoClaimModel, CustomHmoClaimRepoMethods),
      ],
    }).compile();

    ds = module.get<DataSource>(DataSource);
    manager = ds.manager;
    repo = extendDSRepo<IPreauthorizationReferralRepository>(
      ds,
      PreauthorisationReferralModel,
      CustomPreauthorizationReferralRepoMethods,
    );
    spySlaveQuery.mockImplementation((qr, pr) => manager.query(qr, pr));
  });

  beforeEach(async () => {
    [hospital] = await createHospitals(manager, 1);
    preAuthorizations = await createPreauthorizationReferral(
      manager,
      3,
      undefined,
      undefined,
      undefined,
      hospital,
    );
    preAuthorization = preAuthorizations[0];
    profile = preAuthorization.profile;
    profile.hospital = hospital;
  });

  afterAll(async () => {
    await ds.destroy();
    await module.close();
  });

  it('repo should be defined', () => {
    expect(repo).toBeDefined();
  });

  it('getPreauthorizationReferral() it should get preauthorization referral', async () => {
    const result = await repo.getPreauthorizationReferral(preAuthorization.id);
    expect(result.id).toEqual(preAuthorization.id);
  });

  it('findByProfile() should find preauth by profile', async () => {
    const res = await repo.findByProfile(
      preAuthorization.profileId,
      {},
      profile,
    );
    expect(res.list.length).toBeGreaterThan(0);
  });
  it('findByProfile() should find preauth by profile with hospitalId', async () => {
    const res = await repo.findByProfile(
      preAuthorization.profileId,
      {
        hospitalId: hospital.id,
      },
      profile,
    );
    expect(res.list.length).toBeGreaterThan(0);
  });

  it('findByProfile() should accept filter', async () => {
    const filter = {
      skip: 0,
      take: 40,
      dateRange: {
        from: new Date(),
        to: new Date(),
      },
      keyword: 'some text',
      status: 'Submitted',
    };
    const res = await repo.findByProfile(
      preAuthorization.profileId,
      filter,
      profile,
    );
    expect(res).toHaveProperty('list');
  });

  it('findByProfile() - all filters', async () => {
    const filter = {
      skip: 0,
      take: 40,
      dateRange: {
        from: new Date(),
        to: new Date(),
      },
      keyword: 'some text',
      status: 'Submitted',
      creator: RecordCreator.OTHERS,
      hospitalId: chance.guid({ version: 4 }),
      providerId: chance.guid({ version: 4 }),
    };
    const res = await repo.findByProfile(preAuthorization.profileId, filter, {
      ...profile,
      hmoId: chance.guid({ version: 4 }),
    } as ProfileModel);
    expect(res).toHaveProperty('list');
  });

  it('findByProfile() should filter by showCompleted', async () => {
    // Create preauth with completed utilizations (non-pending status)
    const completedPreauth = await manager.save(PreauthorisationReferralModel, {
      ...preAuthorization,
      id: chance.guid({ version: 4 }),
      utilizations: [
        {
          id: chance.guid({ version: 4 }),
          status: 'Approved',
          utilizationCode: 'UTIL-001',
          preAuthorizationReferralId: preAuthorization.id,
        },
      ],
    });

    const filter = {
      showCompleted: true,
    };
    const res = await repo.findByProfile(
      completedPreauth.profileId,
      filter,
      profile,
    );
    expect(res).toHaveProperty('list');
    // Should return records without pending utilizations
    expect(res.list.length).toBeGreaterThanOrEqual(0);
  });

  it('findByProfile() should filter by showNotCompleted', async () => {
    // Create preauth with pending utilizations
    const pendingPreauth = await manager.save(PreauthorisationReferralModel, {
      ...preAuthorization,
      id: chance.guid({ version: 4 }),
      utilizations: [
        {
          id: chance.guid({ version: 4 }),
          status: 'Pending',
          utilizationCode: 'UTIL-002',
          preAuthorizationReferralId: preAuthorization.id,
        },
      ],
    });

    const filter = {
      showNotCompleted: true,
    };
    const res = await repo.findByProfile(
      pendingPreauth.profileId,
      filter,
      profile,
    );
    expect(res).toHaveProperty('list');
    // Should return records with pending utilizations
    expect(res.list.length).toBeGreaterThanOrEqual(0);
  });

  it('findByHospital() should filter by showCompleted', async () => {
    // Create preauth with completed utilizations (non-pending status)
    const completedPreauth = await manager.save(PreauthorisationReferralModel, {
      ...preAuthorization,
      id: chance.guid({ version: 4 }),
      hospitalId: hospital.id,
      utilizations: [
        {
          id: chance.guid({ version: 4 }),
          status: 'Approved',
          utilizationCode: 'UTIL-003',
          preAuthorizationReferralId: preAuthorization.id,
        },
      ],
    });

    const filter = {
      showCompleted: true,
    };
    const res = await repo.findByHospital(hospital.id, filter, profile);
    expect(res).toHaveProperty('list');
    // Should return records without pending utilizations
    expect(res.list.length).toBeGreaterThanOrEqual(0);
  });

  it('findByHospital() should filter by showNotCompleted', async () => {
    // Create preauth with pending utilizations
    const pendingPreauth = await manager.save(PreauthorisationReferralModel, {
      ...preAuthorization,
      id: chance.guid({ version: 4 }),
      hospitalId: hospital.id,
      utilizations: [
        {
          id: chance.guid({ version: 4 }),
          status: 'Pending',
          utilizationCode: 'UTIL-004',
          preAuthorizationReferralId: preAuthorization.id,
        },
      ],
    });

    const filter = {
      showNotCompleted: true,
    };
    const res = await repo.findByHospital(hospital.id, filter, profile);
    expect(res).toHaveProperty('list');
    // Should return records with pending utilizations
    expect(res.list.length).toBeGreaterThanOrEqual(0);
  });

  it('findByHospital() should find by hospital', async () => {
    const res = await repo.findByHospital(hospital.id, {}, profile);
    expect(res).toHaveProperty('list');
  });

  it('findByHospital() should find by hospital against partnerId', async () => {
    const [partner] = await createPartner(manager);
    const [hospitalA, hospitalB] = await createHospitals(
      manager,
      2,
      null,
      partner,
    );
    await createPreauthorizationReferral(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalA,
    );
    await createPreauthorizationReferral(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalB,
    );
    const res = await repo.findByHospital(hospital.id, {}, {
      ...profile,
      isPartnerProfile: true,
      partnerId: partner.id,
    } as ProfileModel);
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(10);
  });

  it('findByHospital() should find by hospital against partnerId and providerInsight', async () => {
    const [partner] = await createPartner(manager);
    const [hospitalA, hospitalB] = await createHospitals(
      manager,
      2,
      null,
      partner,
    );
    await createPreauthorizationReferral(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalA,
    );
    await createPreauthorizationReferral(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalB,
    );
    const res = await repo.findByHospital(
      hospitalA.id,
      {
        providerInsight: true,
      },
      {
        ...profile,
        isPartnerProfile: true,
        partnerId: partner.id,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(5);
  });

  it('findByHospital() should find by hospital against hmoId', async () => {
    const [hospitalA] = await createHospitals(manager);
    const preauths = await createPreauthorizationReferral(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalA,
    );
    const res = await repo.findByHospital(hospital.id, {}, {
      ...profile,
      hmoId: preauths[0].providerId,
    } as ProfileModel);
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(5);
  });

  it('findByHospital() should find by hospital against hmoId and provider insight', async () => {
    const [hospitalA] = await createHospitals(manager);
    const preauths = await createPreauthorizationReferral(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalA,
    );
    const res = await repo.findByHospital(
      hospitalA.id,
      {
        providerInsight: true,
      },
      {
        ...profile,
        hmoId: preauths[0].providerId,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(5);
  });

  it('deletePreauthorizations() should delete preauthorization', async () => {
    const [pAuth] = await createPreauthorizationReferral(manager, 1);
    try {
      await repo.deletePreauthorizationReferrals(pAuth.profile, [pAuth.id]);
    } catch (error) {
      expect(error).toBeDefined();
    }
  });

  it('archivePreauthorizations() should archive preauth', async () => {
    const [res] = await repo.archivePreauthorizationReferrals(
      profile,
      [preAuthorization.id],
      true,
    );
    expect(res.archived).toEqual(true);
  });

  it('getActivePreauthorizationReferral() should get active preauth referral', async () => {
    // when createdDate is less than 24 hours
    const [preAuth] = await createPreauthorizationReferral(
      manager,
      1,
      undefined,
      undefined,
      undefined,
      hospital,
      undefined,
      new Date(),
    );
    const result = await repo.getActivePreauthorizationReferral(
      preAuth.profileId,
      profile,
    );
    expect(result.id).toEqual(preAuth.id);
  });

  it('getPreauthorizationReferralByCode(): should call getPreauthorizationReferralByCode service method', async () => {
    await createPreauthorizationReferral(
      manager,
      1,
      undefined,
      undefined,
      undefined,
      hospital,
      { referredBy: 'John Mary Doe', facilityName: 'Facility Name' },
      new Date(),
      'REF-112/3212312',
    );

    const response = await repo.getPreauthorizationReferralByCode(
      'REF-112/3212312',
    );
    expect(response.referredBy).toBe('John Mary Doe');
    expect(response.facilityName).toBe('Facility Name');
  });

  it('updatePreauthorizationReferral(): should throw error when referrals is not found', async () => {
    await expect(
      repo.updatePreauthorizationReferral(
        profile,
        chance.guid({ version: 4 }),
        { referredBy: 'John Mary Doe', additionalNote: 'Additional Note' },
      ),
    ).rejects.toThrow('Referral Not Found');
  });

  // eslint-disable-next-line max-len
  it('updatePreauthorizationReferral(): should throw error when agency or provider that own the referral is not the one updating', async () => {
    const [provider] = await createHmoProviderFixtures(manager, 1);
    const [referral] = await createPreauthorizationReferral(
      manager,
      1,
      undefined,
      undefined,
      undefined,
      hospital,
      {
        referredBy: 'John Mary Doe',
        facilityName: 'Facility Name',
        providerId: provider.id,
      },
      new Date(),
      'REF-112/32124612',
    );

    await expect(
      repo.updatePreauthorizationReferral(
        {
          ...profile,
          hmoId: chance.guid({ version: 4 }),
          hospitalId: chance.guid({ version: 4 }),
        } as any,
        referral.id,
        { referredBy: 'John Doe', additionalNote: 'Additional Note' },
      ),
    ).rejects.toThrow('Not Authorized To Modify This Record');
  });

  it('updatePreauthorizationReferral(): should update existing agency referrals', async () => {
    const [provider] = await createHmoProviderFixtures(manager, 1);
    const hmoPlansFac = HmoPlanTypeFactory.build();
    const [{ defaultProfile }] = await createUsers(manager, 1, hospital);
    const patient = defaultProfile;
    const hmoPlan = await manager.save(HmoPlanTypeModel, {
      ...hmoPlansFac,
      hmoProviderId: provider.id,
      createdBy: profile,
      creatorName: profile.fullName,
      hospitalId: profile.hospital.id,
    });

    const [hmoProfile] = await createHmoProfileFixtures(
      manager,
      1,
      patient,
      undefined,
      profile,
      provider,
      true,
    );

    // Create profile plan
    const profilePlan = new HmoProfilePlanModel({
      membershipNumber: hmoProfile.membershipNumber,
      profileId: hmoProfile.profileId,
      planTypeId: hmoPlan.id,
      hmoProviderId: provider.id,
      status: HmoPlanStatus.Active,
      createdBy: profile,
      creatorName: profile.fullName,
    });
    await manager.save(HmoProfilePlanModel, profilePlan);
    const benefit = new HmoProfileBenefitModel({
      membershipNumber: hmoProfile.membershipNumber,
      name: 'Test Benefit',
      profilePlanTypeId: profilePlan.id,
      createdBy: profile,
      creatorName: profile.fullName,
      profileId: profile.id,
    });
    await manager.save(HmoProfileBenefitModel, benefit);
    const [referral] = await createPreauthorizationReferral(
      manager,
      1,
      undefined,
      undefined,
      patient,
      hospital,
      {
        referredBy: 'John Mary Doe',
        facilityName: 'Facility Name',
        providerId: provider.id,
        enrolleeNumber: hmoProfile.memberNumber,
      },
      new Date(),
      'REF-112/32124612',
    );

    const response = await repo.updatePreauthorizationReferral(
      { ...profile, hmoId: provider.id } as any,
      referral.id,
      {
        referredBy: 'John Doe',
        additionalNote: 'Additional Note',
        serviceTypeCode: 'Gp Consultation',
      },
    );

    expect(response.referredBy).toEqual('John Doe');
    expect(response.additionalNote).toEqual('Additional Note');
  });

  it('updatePreauthorizationReferral(): should update existing provider referrals', async () => {
    const [provider] = await createHmoProviderFixtures(manager, 1);
    const [referral] = await createPreauthorizationReferral(
      manager,
      1,
      undefined,
      undefined,
      undefined,
      hospital,
      {
        referredBy: 'John Mary Doe',
        facilityName: 'Facility Name',
        providerId: provider.id,
        enrolleeNumber: 'LASHMA-0001/0',
      },
      new Date(),
      'REF-112/32124612',
    );

    const mutator = {
      ...profile,
      hmoId: null,
      hospitalId: hospital.id,
    } as any;
    const response = await repo.updatePreauthorizationReferral(
      mutator,
      referral.id,
      {
        referredBy: 'John Doe',
        additionalNote: 'Additional Note',
        enrolleeId: 'LASHMA-0001/0',
        serviceTypeCode: 'Gp Consultation',
      },
    );

    expect(response.referredBy).toEqual('John Doe');
    expect(response.additionalNote).toEqual('Additional Note');

    const utilizations = [...referral.utilizations];

    // No Changes Made
    let res = await repo.updatePreauthorizationReferral(mutator, referral.id, {
      ...referral,
      utilizations: [...utilizations],
    });
    expect(res.utilizations.length).toBe(utilizations.length);

    // New utilization added
    res = await repo.updatePreauthorizationReferral(mutator, referral.id, {
      ...referral,
      utilizations: [
        ...utilizations,
        {
          utilizationCode: 'utilization code',
          utilizationId: chance.guid({ version: 4 }),
          category: 'Category',
          type: 'Ref Service',
          quantity: '1',
          price: '1000',
        },
      ],
    });
    expect(res.utilizations.length).toBe(utilizations.length + 1);

    // Utilization Quantity Changed
    let newUtilizations = res.utilizations?.map((_itm) => {
      if (!_itm.status || _itm.status === 'Pending') {
        return { ..._itm, quantity: '5' };
      }
      return _itm;
    });
    res = await repo.updatePreauthorizationReferral(mutator, referral.id, {
      ...referral,
      utilizations: [...newUtilizations] as any,
    });
    expect(res.utilizations.length).toBe(utilizations.length + 1);

    // Delete Utilization
    newUtilizations = res.utilizations?.filter(
      (_itm) => _itm.status === 'Approved',
    );
    res = await repo.updatePreauthorizationReferral(mutator, referral.id, {
      ...referral,
      utilizations: [...newUtilizations] as any,
    });
    expect(res.utilizations.length).toBe(newUtilizations.length);
  });

  describe('timeSortOrder Filter Tests', () => {
    let timeSortProfile: ProfileModel;
    let timeSortPreauthorizations: PreauthorisationReferralModel[];

    beforeEach(async () => {
      // Create test data with different timestamps for timeSortOrder tests
      const hospitals = await createHospitals(manager, 1);
      const hmoProviders = await createHmoProviderFixtures(manager, 1);
      const [user] = await createUsers(
        manager,
        1,
        hospitals[0],
        hmoProviders[0],
        null,
        UserType.Patient,
      );
      timeSortProfile = user.profiles[0];

      // Create preauthorizations with different updated dates and times
      const baseDate = moment('2024-01-15').toDate();
      const dates = [
        moment(baseDate).hour(9).minute(30).toDate(), // 09:30
        moment(baseDate).hour(14).minute(15).toDate(), // 14:15
        moment(baseDate).hour(11).minute(45).toDate(), // 11:45
        moment(baseDate).hour(16).minute(20).toDate(), // 16:20
        moment(baseDate).hour(8).minute(10).toDate(), // 08:10
      ];

      timeSortPreauthorizations = [];
      for (const date of dates) {
        const [preauth] = await createPreauthorizationReferral(
          manager,
          1,
          null,
          null,
          timeSortProfile,
          hospitals[0],
          { providerId: hmoProviders[0].id },
          date,
        );
        timeSortPreauthorizations.push(preauth);
      }
    });

    describe('findByProfile with timeSortOrder', () => {
      it('should sort by time in ASC order when timeSortOrder is ASC', async () => {
        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by time in ascending order
        // Expected order: 08:10, 09:30, 11:45, 14:15, 16:20
        const times = result.list.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );

        expect(times[0]).toBe('08:10');
        expect(times[1]).toBe('09:30');
        expect(times[2]).toBe('11:45');
        expect(times[3]).toBe('14:15');
        expect(times[4]).toBe('16:20');
      });

      it('should sort by time in DESC order when timeSortOrder is DESC', async () => {
        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.DESC,
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by time in descending order
        // Expected order: 16:20, 14:15, 11:45, 09:30, 08:10
        const times = result.list.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );

        expect(times[0]).toBe('16:20');
        expect(times[1]).toBe('14:15');
        expect(times[2]).toBe('11:45');
        expect(times[3]).toBe('09:30');
        expect(times[4]).toBe('08:10');
      });

      it('should use default sorting when timeSortOrder is not provided', async () => {
        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by updatedDate DESC (default behavior)
        // Should be sorted by full timestamp descending
        const timestamps = result.list.map((item) =>
          moment(item.updatedDate).valueOf(),
        );

        for (let i = 0; i < timestamps.length - 1; i++) {
          expect(timestamps[i]).toBeGreaterThanOrEqual(timestamps[i + 1]);
        }
      });

      it('should handle timeSortOrder with other filters', async () => {
        // Create a preauthorization with a specific keyword
        const [specificPreauth] = await createPreauthorizationReferral(
          manager,
          1,
          null,
          null,
          timeSortProfile,
          null,
          {
            code: 'SPECIAL-CODE-123',
            providerId: timeSortPreauthorizations[0].providerId,
          },
          moment('2024-01-15').hour(12).minute(0).toDate(),
        );

        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            keyword: 'SPECIAL-CODE',
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(1);
        expect(result.list[0].code).toBe('SPECIAL-CODE-123');
      });

      it('should handle pagination with timeSortOrder', async () => {
        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 2,
            take: 2,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(2);
        expect(result).toHaveProperty('totalCount', 5);

        // Verify that pagination works correctly with time sorting
        const times = result.list.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );

        // Should get the 3rd and 4th items in ASC order: 11:45, 14:15
        expect(times[0]).toBe('11:45');
        expect(times[1]).toBe('14:15');
      });
    });

    describe('findByHospital with timeSortOrder', () => {
      it('should sort by time in ASC order when timeSortOrder is ASC', async () => {
        const hospitalId = timeSortPreauthorizations[0].hospital.id;

        const result = await repo.findByHospital(
          hospitalId,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by time in ascending order
        const times = result.list.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );

        expect(times[0]).toBe('08:10');
        expect(times[1]).toBe('09:30');
        expect(times[2]).toBe('11:45');
        expect(times[3]).toBe('14:15');
        expect(times[4]).toBe('16:20');
      });

      it('should sort by time in DESC order when timeSortOrder is DESC', async () => {
        const hospitalId = timeSortPreauthorizations[0].hospital.id;

        const result = await repo.findByHospital(
          hospitalId,
          {
            timeSortOrder: TimeSortOrder.DESC,
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by time in descending order
        const times = result.list.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );

        expect(times[0]).toBe('16:20');
        expect(times[1]).toBe('14:15');
        expect(times[2]).toBe('11:45');
        expect(times[3]).toBe('09:30');
        expect(times[4]).toBe('08:10');
      });

      it('should use default sorting when timeSortOrder is not provided', async () => {
        const hospitalId = timeSortPreauthorizations[0].hospital.id;

        const result = await repo.findByHospital(
          hospitalId,
          {
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by updatedDate DESC (default behavior)
        const timestamps = result.list.map((item) =>
          moment(item.updatedDate).valueOf(),
        );

        for (let i = 0; i < timestamps.length - 1; i++) {
          expect(timestamps[i]).toBeGreaterThanOrEqual(timestamps[i + 1]);
        }
      });
    });

    describe('Edge cases and integration tests', () => {
      it('should handle preauthorizations with same date but different times', async () => {
        // Create preauthorizations on the same date but different times
        const sameDate = moment('2024-02-01').toDate();
        const sameDatePreauths = [];

        const times = [
          moment(sameDate).hour(10).minute(0).toDate(),
          moment(sameDate).hour(10).minute(30).toDate(),
          moment(sameDate).hour(10).minute(15).toDate(),
        ];

        for (const time of times) {
          const [preauth] = await createPreauthorizationReferral(
            manager,
            1,
            null,
            null,
            timeSortProfile,
            null,
            { providerId: timeSortPreauthorizations[0].providerId },
            time,
          );
          sameDatePreauths.push(preauth);
        }

        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 0,
            take: 20,
          },
          timeSortProfile,
        );

        // Find the same-date preauths in the result
        const sameDateResults = result.list.filter(
          (item) =>
            moment(item.updatedDate).format('YYYY-MM-DD') === '2024-02-01',
        );

        expect(sameDateResults).toHaveLength(3);

        // Verify they are sorted by time
        const times_result = sameDateResults.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );

        expect(times_result[0]).toBe('10:00');
        expect(times_result[1]).toBe('10:15');
        expect(times_result[2]).toBe('10:30');
      });

      it('should handle preauthorizations across different dates with timeSortOrder', async () => {
        // Create preauthorizations on different dates
        const dates = [
          moment('2024-03-01').hour(15).minute(0).toDate(),
          moment('2024-03-02').hour(9).minute(0).toDate(),
          moment('2024-03-01').hour(10).minute(0).toDate(),
        ];

        for (const date of dates) {
          await createPreauthorizationReferral(
            manager,
            1,
            null,
            null,
            timeSortProfile,
            null,
            { providerId: timeSortPreauthorizations[0].providerId },
            date,
          );
        }

        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.DESC,
            skip: 0,
            take: 20,
          },
          timeSortProfile,
        );

        // Should be sorted by date DESC first, then by time DESC
        // Expected order: 2024-03-02 09:00, 2024-03-01 15:00, 2024-03-01 10:00, ...
        const march2Results = result.list.filter(
          (item) =>
            moment(item.updatedDate).format('YYYY-MM-DD') === '2024-03-02',
        );
        const march1Results = result.list.filter(
          (item) =>
            moment(item.updatedDate).format('YYYY-MM-DD') === '2024-03-01',
        );

        expect(march2Results).toHaveLength(1);
        expect(march1Results).toHaveLength(2);

        // March 2 should come first
        const march2Index = result.list.findIndex(
          (item) =>
            moment(item.updatedDate).format('YYYY-MM-DD') === '2024-03-02',
        );
        const march1Index = result.list.findIndex(
          (item) =>
            moment(item.updatedDate).format('YYYY-MM-DD') === '2024-03-01',
        );

        expect(march2Index).toBeLessThan(march1Index);

        // Within March 1, times should be sorted DESC (15:00 before 10:00)
        const march1Times = march1Results.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );
        expect(march1Times[0]).toBe('15:00');
        expect(march1Times[1]).toBe('10:00');
      });

      it('should handle empty results with timeSortOrder', async () => {
        // Create a new profile with no preauthorizations
        const hospitals = await createHospitals(manager, 1);
        const hmoProviders = await createHmoProviderFixtures(manager, 1);
        const [newUser] = await createUsers(
          manager,
          1,
          hospitals[0],
          hmoProviders[0],
          null,
          UserType.Patient,
        );
        const newProfile = newUser.profiles[0];

        const result = await repo.findByProfile(
          newProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 0,
            take: 10,
          },
          newProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(0);
        expect(result).toHaveProperty('totalCount', 0);
      });
    });
  });
});
