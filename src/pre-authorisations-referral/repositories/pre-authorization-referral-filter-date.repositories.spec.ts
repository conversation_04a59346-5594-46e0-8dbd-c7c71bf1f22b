import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import {
  CustomPreauthorizationReferralRepoMethods,
  IPreauthorizationReferralRepository,
} from './pre-authorization-referral.repositories';
import { TestDataSourceOptions } from '../../data-source';
import * as db from '../../database';
import { extendDSRepo, extendModel } from '../../database/extendModel';
import { PreauthReferralDateFilterType } from '../inputs/pre-authorisation-referral-filter.input';
import { PreauthorisationReferralModel } from '../models/preauthorisation-referral.model';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { CustomHmoClaimRepoMethods } from '@clinify/hmo-claims/repositories/hmo-claim.repository';
import { HmoProviderRepository } from '@clinify/hmo-providers/repositories/hmo-provider.repository';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { createHospitals } from '@clinify/utils/tests/hospital.fixtures';
import { createPreauthorizationReferral } from '@clinify/utils/tests/preauthorization-referral.fixtures';

describe('Pre-authorization referral repository - filterDateField functionality', () => {
  let profile: ProfileModel;
  let manager: EntityManager;
  let repo: IPreauthorizationReferralRepository;
  let module: TestingModule;
  let ds: DataSource;

  const spySlaveQuery = jest.spyOn(db, 'queryWithSlave');

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        extendModel(
          PreauthorisationReferralModel,
          CustomPreauthorizationReferralRepoMethods,
        ),
        HmoProviderRepository,
        extendModel(HmoClaimModel, CustomHmoClaimRepoMethods),
      ],
    }).compile();

    ds = module.get<DataSource>(DataSource);
    manager = ds.manager;
    repo = extendDSRepo<IPreauthorizationReferralRepository>(
      ds,
      PreauthorisationReferralModel,
      CustomPreauthorizationReferralRepoMethods,
    );
    spySlaveQuery.mockImplementation((qr, pr) => manager.query(qr, pr));
  });

  beforeEach(async () => {
    const [hospital] = await createHospitals(manager, 1);
    const preAuthorizationReferrals = await createPreauthorizationReferral(
      manager,
      1,
      undefined,
      undefined,
      undefined,
      hospital,
    );
    profile = preAuthorizationReferrals[0].profile;
    profile.hospital = hospital;
  });

  afterAll(async () => {
    await ds.destroy();
    await module.close();
  });

  describe('filterDateField functionality', () => {
    it('should filter by RequestDate when filterDateField is RequestDate', async () => {
      const [testHospital] = await createHospitals(manager, 1);
      const requestDate = new Date('2024-06-01T00:00:00Z');
      const createdDate = new Date('2020-01-01T00:00:00Z');

      const [createdReferral] = await createPreauthorizationReferral(
        manager,
        1,
        profile,
        undefined,
        profile,
        testHospital,
        { requestDateTime: requestDate },
        createdDate,
      );

      const result = await repo.findByProfile(
        profile.id,
        {
          dateRange: {
            from: new Date('2023-01-01T00:00:00Z'),
            to: new Date('2025-01-01T00:00:00Z'),
          },
          filterDateField: PreauthReferralDateFilterType.RequestDate,
        },
        profile,
      );

      expect(result.list.length).toBe(1);
      expect(result.list[0].id).toBe(createdReferral.id);
      expect(result.totalCount).toBe(1);
    });

    it('should filter by CreatedDate when filterDateField is CreatedDate', async () => {
      const [testHospital] = await createHospitals(manager, 1);
      const requestDate = new Date('2020-01-01T00:00:00Z');
      const createdDate = new Date('2024-06-01T00:00:00Z');

      const [createdReferral] = await createPreauthorizationReferral(
        manager,
        1,
        profile,
        undefined,
        profile,
        testHospital,
        { requestDateTime: requestDate },
        createdDate,
      );

      const result = await repo.findByProfile(
        profile.id,
        {
          dateRange: {
            from: new Date('2023-01-01T00:00:00Z'),
            to: new Date('2025-01-01T00:00:00Z'),
          },
          filterDateField: PreauthReferralDateFilterType.CreatedDate,
        },
        profile,
      );

      expect(result.list.length).toBe(1);
      expect(result.list[0].id).toBe(createdReferral.id);
      expect(result.totalCount).toBe(1);
    });

    it('should default to RequestDate when filterDateField is not specified', async () => {
      const [testHospital] = await createHospitals(manager, 1);
      const requestDate = new Date('2024-06-01T00:00:00Z');
      const createdDate = new Date('2020-01-01T00:00:00Z');

      const [createdReferral] = await createPreauthorizationReferral(
        manager,
        1,
        profile,
        undefined,
        profile,
        testHospital,
        { requestDateTime: requestDate },
        createdDate,
      );

      const result = await repo.findByProfile(
        profile.id,
        {
          dateRange: {
            from: new Date('2023-01-01T00:00:00Z'),
            to: new Date('2025-01-01T00:00:00Z'),
          },
        },
        profile,
      );

      expect(result.list.length).toBe(1);
      expect(result.list[0].id).toBe(createdReferral.id);
      expect(result.totalCount).toBe(1);
    });

    it('should work with findByHospital and CreatedDate filter', async () => {
      const [testHospital] = await createHospitals(manager, 1);
      const requestDate = new Date('2020-01-01T00:00:00Z');
      const createdDate = new Date('2024-06-01T00:00:00Z');

      const [createdReferral] = await createPreauthorizationReferral(
        manager,
        1,
        profile,
        undefined,
        profile,
        testHospital,
        { requestDateTime: requestDate },
        createdDate,
      );

      const result = await repo.findByHospital(
        testHospital.id,
        {
          dateRange: {
            from: new Date('2023-01-01T00:00:00Z'),
            to: new Date('2025-01-01T00:00:00Z'),
          },
          filterDateField: PreauthReferralDateFilterType.CreatedDate,
        },
        profile,
      );

      expect(result.list.length).toBe(1);
      expect(result.list[0].id).toBe(createdReferral.id);
      expect(result.totalCount).toBe(1);
    });

    it('should exclude records outside date range', async () => {
      const [testHospital] = await createHospitals(manager, 1);
      const oldDate = new Date('2020-01-01T00:00:00Z');

      await createPreauthorizationReferral(
        manager,
        1,
        profile,
        undefined,
        profile,
        testHospital,
        { requestDateTime: oldDate },
        oldDate,
      );

      const result = await repo.findByProfile(
        profile.id,
        {
          dateRange: {
            from: new Date('2021-01-01T00:00:00Z'),
            to: new Date('2022-01-01T00:00:00Z'),
          },
          filterDateField: PreauthReferralDateFilterType.RequestDate,
        },
        profile,
      );

      expect(result.list.length).toBe(0);
      expect(result.totalCount).toBe(0);
    });

    it('should filter correctly based on different date fields', async () => {
      const [testHospital] = await createHospitals(manager, 1);
      const requestDate = new Date('2024-06-01T00:00:00Z'); // In range
      const createdDate = new Date('2020-01-01T00:00:00Z'); // Out of range

      const [createdReferral] = await createPreauthorizationReferral(
        manager,
        1,
        profile,
        undefined,
        profile,
        testHospital,
        { requestDateTime: requestDate },
        createdDate,
      );

      // Filter by RequestDate - should find the record
      const requestDateResult = await repo.findByProfile(
        profile.id,
        {
          dateRange: {
            from: new Date('2023-01-01T00:00:00Z'),
            to: new Date('2025-01-01T00:00:00Z'),
          },
          filterDateField: PreauthReferralDateFilterType.RequestDate,
        },
        profile,
      );

      expect(requestDateResult.list.length).toBe(1);
      expect(requestDateResult.list[0].id).toBe(createdReferral.id);

      // Filter by CreatedDate - should not find the record
      const createdDateResult = await repo.findByProfile(
        profile.id,
        {
          dateRange: {
            from: new Date('2023-01-01T00:00:00Z'),
            to: new Date('2025-01-01T00:00:00Z'),
          },
          filterDateField: PreauthReferralDateFilterType.CreatedDate,
        },
        profile,
      );

      expect(createdDateResult.list.length).toBe(0);
      expect(createdDateResult.totalCount).toBe(0);
    });
  });
});
