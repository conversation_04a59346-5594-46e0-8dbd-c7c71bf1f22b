import { HttpModule } from '@nestjs/axios';
import { forward<PERSON><PERSON>, Lo<PERSON>, Module } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PreauthorisationReferralModel } from './models/preauthorisation-referral.model';
import { PreAuthReferralUtilisationsModel } from './models/utilisations-referral.model';
import { CustomPreauthorizationReferralRepoMethods } from './repositories/pre-authorization-referral.repositories';
import { PreauthorizationReferralResolver } from './resolvers/preauthorization-referral.resolvers';
import { PreauthorisationReferralService } from './services/pre-authorisation-referral.service';
import { HmoProviderModule } from '../hmo-providers/hmo-provider.module';
import { NotificationsService } from '../notifications/services/notifications.service';
import { MailerModule } from '../shared/mailer/mailer.module';
import { AuthorizationModule } from '@clinify/authorization/authorization.module';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { extendModel } from '@clinify/database/extendModel';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { HmoProviderRepository } from '@clinify/hmo-providers/repositories/hmo-provider.repository';
import { NotificationsModel } from '@clinify/notifications/models/notifications.model';
import { RemindersModule } from '@clinify/reminders/reminders.module';
import { SharedModule } from '@clinify/shared/module';
import PubSub, { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';

@Module({
  imports: [
    AuthorizationModule,
    forwardRef(() => HmoProviderModule),
    TypeOrmModule.forFeature([
      PreAuthReferralUtilisationsModel,
      HmoProviderModel,
      NotificationsModel,
      PreauthorisationReferralModel,
    ]),
    HttpModule.register({}),
    MailerModule,
    TypeormExtendedModule.forCustomRepository([HmoProviderRepository]),
    RemindersModule,
    forwardRef(() => SharedModule),
  ],
  providers: [
    extendModel(
      PreauthorisationReferralModel,
      CustomPreauthorizationReferralRepoMethods,
    ),
    PreauthorisationReferralService,
    PreauthorizationReferralResolver,
    NotificationsService,
    Logger,
    { provide: PUB_SUB, useFactory: () => PubSub },
    EventEmitter2,
  ],
  exports: [PreauthorisationReferralService],
})
export class PreauthorizationReferralModule {}
