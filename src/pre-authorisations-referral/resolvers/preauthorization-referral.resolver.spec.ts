import { EventEmitter2 } from '@nestjs/event-emitter';
import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { PreauthorizationReferralResolver } from './preauthorization-referral.resolvers';
import { AuthorizationModule } from '../../authorization/authorization.module';
import { TestDataSourceOptions } from '../../data-source';
import { extendModel } from '../../database/extendModel';
import { HmoProviderModule } from '../../hmo-providers/hmo-provider.module';
import { HmoProviderModel } from '../../hmo-providers/models/hmo-provider.model';
import { NotificationsService } from '../../notifications/services/notifications.service';
import { PreauthorisationReferralModel } from '../models/preauthorisation-referral.model';
import { PreAuthReferralUtilisationsModel } from '../models/utilisations-referral.model';
import { CustomPreauthorizationReferralRepoMethods } from '../repositories/pre-authorization-referral.repositories';
import { PreauthorisationReferralService } from '../services/pre-authorisation-referral.service';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { BankModule } from '@clinify/banks/bank.module';
import { BankService } from '@clinify/banks/services/bank.service';
import { HmoProviderRepository } from '@clinify/hmo-providers/repositories/hmo-provider.repository';
import {
  DefaultInvImage,
  InventoryUpload,
} from '@clinify/integrations/inventory-blk/entities/FileUpload.entity';
import { PriceUpload } from '@clinify/integrations/price-blk/entities/PriceFileUpload.entity';
import { UtilizationResolver } from '@clinify/pre-authorisations/resolvers/utilization.resolver';
import {
  preauthorizationFactory,
  utilizationFactory,
} from '@mocks/factories/preauthorization.factory';
import { mockUser } from '@mocks/factories/user.factory';

jest.mock('@aws-sdk/client-bedrock-runtime', () => {
  const mockInvokeModelCommand = jest.fn();
  return {
    BedrockRuntimeClient: jest.fn(() => ({
      send: jest.fn(),
    })),
    InvokeModelCommand: mockInvokeModelCommand,
  };
});

const profile = profileFactory.build();
const user = mockUser;

const preauthorizationData = preauthorizationFactory.build();
const list = preauthorizationFactory.buildList(4);

const PreauthorizationServiceMock = {
  addPreauthorizationReferral: jest.fn(() => preauthorizationData),
  deletePreauthorizationReferrals: jest.fn(() => [preauthorizationData]),
  getPreauthorizationReferral: jest.fn(() => preauthorizationData),
  getPreAuthDetail: jest.fn(() => preauthorizationData),
  getPreAuthDetailBulk: jest.fn(() => [preauthorizationData]),
  findByHospital: jest.fn(() => ({ itemCount: 3, list })),
  findByProfile: jest.fn(() => ({ itemCount: 3, list })),
  archivePreauthorizationReferrals: jest.fn(() => preauthorizationData),
  getActivePreauthorizationReferral: jest.fn(() => preauthorizationData),
  deletePreauthorizationReferralss: jest.fn(() => [preauthorizationData]),
  getHmoProvider: jest.fn(() => ({ id: 'id' })),
  getPreauthorizationReferralByCode: jest.fn(() => preauthorizationData),
  updateReferralUtilStatus: jest.fn(() => utilizationFactory.buildList(2)),
  updateReferralUtilisationStatus: jest.fn(() => utilizationFactory.build()),
  updatePreauthorizationReferral: jest.fn(() => preauthorizationData),
};

const ManagerMock = {
  findOneOrFail: jest.fn(() => user.defaultProfile),
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

const mockEventEmitter = {
  emit: jest.fn(),
};

describe('PreathorizationReferralResolver', () => {
  let resolver: PreauthorizationReferralResolver;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        AuthorizationModule,
        HmoProviderModule,
        BankModule,
        TypeOrmModule.forFeature([
          PreAuthReferralUtilisationsModel,
          PreauthorisationReferralModel,
          HmoProviderModel,
        ]),
      ],
      providers: [
        UtilizationResolver,
        PreauthorizationReferralResolver,
        extendModel(
          PreauthorisationReferralModel,
          CustomPreauthorizationReferralRepoMethods,
        ),
        HmoProviderRepository,
        PreauthorisationReferralService,
        NotificationsService,
        {
          provide: EntityManager,
          useValue: ManagerMock,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
      ],
    })
      .overrideProvider('PUB_SUB')
      .useValue(pubSubMock)
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .overrideProvider(PreauthorisationReferralService)
      .useValue(PreauthorizationServiceMock)
      .overrideProvider(BankService)
      .useValue({})
      .overrideProvider(NotificationsService)
      .useValue({
        handleNoticationEvent: jest.fn(),
      })
      .compile();

    resolver = module.get<PreauthorizationReferralResolver>(
      PreauthorizationReferralResolver,
    );
    jest.clearAllMocks();
  });

  it('addPreauthoriztionReferral(): should save preauthorization', async () => {
    const preAuthInput = preauthorizationData;
    delete preAuthInput.profile;
    const response = await resolver.addPreauthorizationReferral(
      profile,
      preAuthInput,
    );
    expect(
      PreauthorizationServiceMock.addPreauthorizationReferral,
    ).toHaveBeenCalledWith(profile, preAuthInput);
    expect(response).toEqual(preauthorizationData);
  });

  it('preauthorizationReferral(): should get preauthorization', async () => {
    const response = await resolver.preauthorizationReferral(
      'id',
      'clinify-id',
    );
    expect(
      PreauthorizationServiceMock.getPreauthorizationReferral,
    ).toHaveBeenCalledWith('id');
    expect(response).toEqual(preauthorizationData);
  });

  it('fetchPreauthorizationReferralsByHospitalId(): should get preauthorizations by hospitalId', async () => {
    const response = await resolver.fetchPreauthorizationReferralsByHospitalId(
      'hospitalId',
      profile,
      {
        take: 1,
        skip: 0,
      },
    );
    expect(PreauthorizationServiceMock.findByHospital).toHaveBeenCalledWith(
      'hospitalId',
      {
        take: 1,
        skip: 0,
      },
      profile,
    );
    expect(response.list).toEqual(list);
  });

  it('archivePreauthorizations(): should archivePreauthorizations', async () => {
    await resolver.archivePreauthorizationReferrals(profile, ['id'], true);
    expect(
      PreauthorizationServiceMock.archivePreauthorizationReferrals,
    ).toHaveBeenCalledWith(profile, ['id'], true);
  });

  it('getActivePreauthorizationReferral(): should get active preauthorization', async () => {
    const response = await resolver.activePreauthorizationReferral(
      'id',
      profile,
    );
    expect(
      PreauthorizationServiceMock.getActivePreauthorizationReferral,
    ).toHaveBeenCalledWith('id', profile);
    expect(response).toEqual(preauthorizationData);
  });

  it('addPreauthorisationReferralSubsHandler(): should return asyncIterator', () => {
    resolver.addPreauthorisationReferralSubsHandler(profile.id);
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'PreauthorisationReferralAdded',
    );
  });

  it('deletePreauthorizationReferrals(): should delete preauthorization', async () => {
    const response = await resolver.deletePreauthorizationReferralss(profile, [
      'id',
    ]);
    expect(
      PreauthorizationServiceMock.deletePreauthorizationReferrals,
    ).toHaveBeenCalledWith(profile, ['id']);
    expect(response).toEqual([preauthorizationData]);
  });

  it('getGrandTotal(): should get grand total', () => {
    const preauthorizationData = preauthorizationFactory.build();
    const response = resolver.getGrandTotal(preauthorizationData);
    const total = preauthorizationData.utilizations
      .reduce(
        (acc, curr) => Number(acc) + Number(curr.price) * Number(curr.quantity),
        0,
      )
      .toString();
    expect(response).toEqual(total);
  });

  it('getTotalQuantity(): should get total quantity', () => {
    const preauthorizationData = preauthorizationFactory.build();
    const response = resolver.getTotalQuantity(preauthorizationData);
    const total = preauthorizationData.utilizations
      .reduce((acc, curr) => Number(acc) + Number(curr.quantity), 0)
      .toString();
    expect(response).toEqual(total);
  });

  it('getProvider(): should get provider', async () => {
    const preauthorizationData = preauthorizationFactory.build();
    preauthorizationData.provider = { id: 'id' };
    const response = await resolver.getProvider(preauthorizationData);
    expect(response).toEqual(preauthorizationData.provider);

    preauthorizationData.provider = null;
    await resolver.getProvider(preauthorizationData);
    expect(PreauthorizationServiceMock.getHmoProvider).toHaveBeenCalledWith(
      preauthorizationData.providerId,
    );
  });

  it('getPreauthorizationReferralByCode(): should call getPreauthorizationReferralByCode service method', async () => {
    await resolver.getPreauthorizationReferralByCode('Referral-code');
    expect(
      PreauthorizationServiceMock.getPreauthorizationReferralByCode,
    ).toHaveBeenCalledWith('Referral-code');
  });

  it('updateReferralUtilStatus(): should call updateReferralUtilStatus service method', async () => {
    await resolver.updateReferralUtilStatus(
      profile,
      'Referral-code',
      'Approved',
    );
    expect(
      PreauthorizationServiceMock.updateReferralUtilStatus,
    ).toHaveBeenCalledWith(profile, 'Referral-code', 'Approved');
  });

  describe('updateReferralUtilisationStatus', () => {
    const input = {
      id: 'utilization-id',
      status: 'Approved',
      rejectionReason: ['Invalid documentation'],
      specifyReasonForRejection: 'Missing required documents',
    };

    it('should call updateReferralUtilisationStatus service method and publish event', async () => {
      const mockUtilization = utilizationFactory.build();
      PreauthorizationServiceMock.updateReferralUtilisationStatus = jest
        .fn()
        .mockResolvedValue(mockUtilization);

      const result = await resolver.updateReferralUtilisationStatus(
        profile,
        input,
      );

      expect(
        PreauthorizationServiceMock.updateReferralUtilisationStatus,
      ).toHaveBeenCalledWith(profile, input);

      expect(pubSubMock.publish).toHaveBeenCalledWith(
        'UtilizationReferralUpdated',
        { UtilizationReferralUpdated: mockUtilization },
      );

      expect(result).toEqual(mockUtilization);
    });

    it('should handle rejected status correctly', async () => {
      const rejectedInput = {
        ...input,
        status: 'Rejected',
        rejectionReason: ['Insufficient documentation', 'Invalid diagnosis'],
        specifyReasonForRejection: 'Patient records incomplete',
      };

      const mockUtilization = utilizationFactory.build({
        status: 'Rejected',
        rejectionReason: rejectedInput.rejectionReason,
        specifyReasonForRejection: rejectedInput.specifyReasonForRejection,
      });

      PreauthorizationServiceMock.updateReferralUtilisationStatus = jest
        .fn()
        .mockResolvedValue(mockUtilization);

      const result = await resolver.updateReferralUtilisationStatus(
        profile,
        rejectedInput,
      );

      expect(
        PreauthorizationServiceMock.updateReferralUtilisationStatus,
      ).toHaveBeenCalledWith(profile, rejectedInput);

      expect(pubSubMock.publish).toHaveBeenCalledWith(
        'UtilizationReferralUpdated',
        { UtilizationReferralUpdated: mockUtilization },
      );

      expect(result.status).toBe('Rejected');
      expect(result.rejectionReason).toEqual(rejectedInput.rejectionReason);
      expect(result.specifyReasonForRejection).toBe(
        rejectedInput.specifyReasonForRejection,
      );
    });

    it('should handle input without optional rejection fields', async () => {
      const simpleInput = {
        id: 'utilization-id',
        status: 'Approved',
      };

      const mockUtilization = utilizationFactory.build({
        status: 'Approved',
        rejectionReason: undefined,
        specifyReasonForRejection: undefined,
      });

      PreauthorizationServiceMock.updateReferralUtilisationStatus = jest
        .fn()
        .mockResolvedValue(mockUtilization);

      const result = await resolver.updateReferralUtilisationStatus(
        profile,
        simpleInput,
      );

      expect(
        PreauthorizationServiceMock.updateReferralUtilisationStatus,
      ).toHaveBeenCalledWith(profile, simpleInput);

      expect(pubSubMock.publish).toHaveBeenCalledWith(
        'UtilizationReferralUpdated',
        { UtilizationReferralUpdated: mockUtilization },
      );

      expect(result.status).toBe('Approved');
      expect(result.rejectionReason).toBeUndefined();
      expect(result.specifyReasonForRejection).toBeUndefined();
    });

    it('should handle service errors gracefully', async () => {
      const errorMessage = 'Utilization Record Not Found';
      PreauthorizationServiceMock.updateReferralUtilisationStatus = jest
        .fn()
        .mockRejectedValue(new Error(errorMessage));

      // Reset the mock to clear previous calls
      pubSubMock.publish.mockClear();

      await expect(
        resolver.updateReferralUtilisationStatus(profile, input),
      ).rejects.toThrow(errorMessage);

      expect(
        PreauthorizationServiceMock.updateReferralUtilisationStatus,
      ).toHaveBeenCalledWith(profile, input);

      // Should not publish event when service fails
      expect(pubSubMock.publish).not.toHaveBeenCalled();
    });

    it('should handle different status values', async () => {
      const statuses = ['Pending', 'Approved', 'Rejected', 'Under Review'];

      for (const status of statuses) {
        const statusInput = { ...input, status };
        const mockUtilization = utilizationFactory.build({ status });

        PreauthorizationServiceMock.updateReferralUtilisationStatus = jest
          .fn()
          .mockResolvedValue(mockUtilization);

        const result = await resolver.updateReferralUtilisationStatus(
          profile,
          statusInput,
        );

        expect(
          PreauthorizationServiceMock.updateReferralUtilisationStatus,
        ).toHaveBeenCalledWith(profile, statusInput);

        expect(pubSubMock.publish).toHaveBeenCalledWith(
          'UtilizationReferralUpdated',
          { UtilizationReferralUpdated: mockUtilization },
        );

        expect(result.status).toBe(status);
      }
    });
  });

  it('updateReferralUtilizationSubsHandler(): should trigger UtilizationReferralUpdated subscription', () => {
    resolver.updateReferralUtilizationSubsHandler(
      'profile-id',
      'hospital-id',
      'hmoProvider-id',
    );
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'UtilizationReferralUpdated',
    );
  });

  it('updatePreauthorizationReferral(): should call the updatePreauthorizationReferral service method', async () => {
    await resolver.updatePreauthorizationReferral(
      profile,
      'referral-id',
      preauthorizationData,
    );

    expect(
      PreauthorizationServiceMock.updatePreauthorizationReferral,
    ).toHaveBeenLastCalledWith(profile, 'referral-id', preauthorizationData);
    expect(pubSubMock.publish).toHaveBeenLastCalledWith(
      'PreauthorisationReferralUpdated',
      { PreauthorisationReferralUpdated: preauthorizationData },
    );
  });

  it('updatePreauthorisationReferralSubsHandler(): should trigger PreauthorisationReferralUpdated subscription', () => {
    resolver.updatePreauthorisationReferralSubsHandler(
      'profile-id',
      'hospital-id',
      'hmoProvider-id',
    );
    expect(pubSubMock.asyncIterator).toHaveBeenLastCalledWith(
      'PreauthorisationReferralUpdated',
    );
  });
});
