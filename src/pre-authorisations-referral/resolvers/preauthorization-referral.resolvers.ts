import { Inject, UseGuards } from '@nestjs/common';
import {
  Args,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
  Subscription,
} from '@nestjs/graphql';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { RolesAuthGuard } from '../../authentication/guards/roles.guard';
import { HmoProviderModel } from '../../hmo-providers/models/hmo-provider.model';
import { UserType } from '../../shared/enums/users';
import { PreauthorizationReferralFilterInput } from '../inputs/pre-authorisation-referral-filter.input';
import {
  PreauthorisationReferralInput,
  PreauthorisationReferralUpdateInput,
  UpdateReferralUtilisationStatusInput,
} from '../inputs/preauthorisation-referral.input';
import { PreauthorisationReferralModel } from '../models/preauthorisation-referral.model';
import { PreAuthReferralUtilisationsModel } from '../models/utilisations-referral.model';
import { PreauthorisationReferralResponse } from '../responses/pre-authorisation.response';
import { PreauthorisationReferralService } from '../services/pre-authorisation-referral.service';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { PinAuthGuard } from '@clinify/authentication/guards/pin.guard';
import { ProfileDataAccessGuard } from '@clinify/authentication/guards/profile-data-acess.guard';
import { AuthorizationGuard } from '@clinify/authorization/guards/authorization.guard';
import { NotificationsService } from '@clinify/notifications/services/notifications.service';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import {
  DashboardIcon,
  NotificationTag,
} from '@clinify/shared/enums/notifications';
import { AppServices } from '@clinify/shared/enums/services';
import { ProfileModel } from '@clinify/users/models/profile.model';
import {
  filterPreauthorisationReferralAdded,
  filterPreauthorisationReferralUpdated,
  filterUtilizationReferralUpdated,
} from '@clinify/utils/subscriptions/filters';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const {
  PreauthorisationReferralAdded,
  PreauthorisationReferralUpdated,
  UtilizationReferralUpdated,
} = SubscriptionTypes;

@LogService(AppServices.PreAuthorization)
@UseGuards(GqlAuthGuard, AuthorizationGuard)
@Resolver(() => PreauthorisationReferralModel)
export class PreauthorizationReferralResolver {
  constructor(
    private preAuthReferralService: PreauthorisationReferralService,
    private notificationsService: NotificationsService,
    @Inject(PUB_SUB) private pubSub: RedisPubSub,
  ) {}

  @UseGuards(ProfileDataAccessGuard('clinifyId'))
  @Query(() => PreauthorisationReferralModel)
  async preauthorizationReferral(
    @Args('id') id: string,
    @Args('clinifyId') _clinifyId: string,
  ): Promise<PreauthorisationReferralModel> {
    return this.preAuthReferralService.getPreauthorizationReferral(id);
  }

  @UseGuards(GqlAuthGuard, RolesAuthGuard(UserType.SuperAdmin))
  @Query(() => PreauthorisationReferralResponse)
  async fetchPreauthorizationReferralsByHospitalId(
    @Args('hospitalId') hospitalId: string,
    @CurrentProfile() mutator: ProfileModel,
    @Args({
      name: 'filterOptions',
      nullable: true,
      type: () => PreauthorizationReferralFilterInput,
    })
    filterOptions?: PreauthorizationReferralFilterInput,
  ): Promise<PreauthorisationReferralResponse> {
    return this.preAuthReferralService.findByHospital(
      hospitalId,
      filterOptions,
      mutator,
    );
  }

  @UseGuards(PinAuthGuard, ProfileDataAccessGuard('input.clinifyId'))
  @Mutation(() => PreauthorisationReferralModel)
  async addPreauthorizationReferral(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: PreauthorisationReferralInput,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<PreauthorisationReferralModel> {
    const item = await this.preAuthReferralService.addPreauthorizationReferral(
      profile,
      input,
    );

    this.pubSub.publish(PreauthorisationReferralAdded, {
      [PreauthorisationReferralAdded]: item,
    });
    await this.notificationsService.handleNoticationEvent({
      profile,
      details: {
        modelName: DashboardIcon.PreAuthorizationReferral,
        action: NotificationTag.PreAuthorizationReferralCreated,
        item,
      },
    });
    await this.notificationsService.handleNoticationEvent({
      profile,
      details: {
        modelName: DashboardIcon.PreAuthorizationReferral,
        action: NotificationTag.PreAuthorizationReferral,
        item,
      },
    });

    return item;
  }

  @Subscription(() => PreauthorisationReferralModel, {
    name: PreauthorisationReferralAdded,
    filter: filterPreauthorisationReferralAdded,
  })
  addPreauthorisationReferralSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(PreauthorisationReferralAdded);
  }

  @UseGuards(PinAuthGuard, ProfileDataAccessGuard('input.clinifyId'))
  @Mutation(() => PreauthorisationReferralModel)
  async updatePreauthorizationReferral(
    @CurrentProfile() mutator: ProfileModel,
    @Args('id') referralId: string,
    @Args('input') input: PreauthorisationReferralUpdateInput,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<PreauthorisationReferralModel> {
    const item =
      await this.preAuthReferralService.updatePreauthorizationReferral(
        mutator,
        referralId,
        input,
      );

    this.pubSub.publish(PreauthorisationReferralUpdated, {
      [PreauthorisationReferralUpdated]: item,
    });
    return item;
  }

  @Subscription(() => PreauthorisationReferralModel, {
    name: PreauthorisationReferralUpdated,
    filter: filterPreauthorisationReferralUpdated,
  })
  updatePreauthorisationReferralSubsHandler(
    @Args('profileId') _profileId: string,
    @Args('hospitalId') _hospitalId: string,
    @Args('hmoProviderId', { nullable: true }) _hmoProviderId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(PreauthorisationReferralUpdated);
  }

  @Mutation(() => [PreauthorisationReferralModel])
  async deletePreauthorizationReferralss(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) ids: string[],
  ): Promise<PreauthorisationReferralModel[]> {
    const items =
      await this.preAuthReferralService.deletePreauthorizationReferrals(
        profile,
        ids,
      );
    return items;
  }

  @Mutation(() => [PreauthorisationReferralModel])
  async archivePreauthorizationReferrals(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) ids: string[],
    @Args('archive') archive: boolean,
  ): Promise<PreauthorisationReferralModel[]> {
    return this.preAuthReferralService.archivePreauthorizationReferrals(
      profile,
      ids,
      archive,
    );
  }

  @ResolveField(() => String, { name: 'grandTotal', defaultValue: '0' })
  getGrandTotal(@Parent() root: PreauthorisationReferralModel): string {
    return root.utilizations
      .reduce(
        (acc, curr) => acc + Number(curr.price) * Number(curr.quantity),
        0,
      )
      .toString();
  }

  @ResolveField(() => String, { name: 'totalQuantity', defaultValue: '0' })
  getTotalQuantity(@Parent() root: PreauthorisationReferralModel): string {
    return root.utilizations
      .reduce((acc, curr) => acc + Number(curr.quantity), 0)
      .toString();
  }

  @ResolveField(() => HmoProviderModel, { name: 'provider' })
  async getProvider(
    @Parent() root: PreauthorisationReferralModel,
  ): Promise<HmoProviderModel> {
    if (root.provider) {
      return root.provider;
    }
    const provider = await this.preAuthReferralService.getHmoProvider(
      root.providerId,
    );
    return provider;
  }

  @UseGuards(ProfileDataAccessGuard('profileId'))
  @Query(() => PreauthorisationReferralModel)
  async activePreauthorizationReferral(
    @Args('profileId') profileId: string,
    @CurrentProfile() mutator: ProfileModel,
  ): Promise<PreauthorisationReferralModel> {
    return this.preAuthReferralService.getActivePreauthorizationReferral(
      profileId,
      mutator,
    );
  }

  @Query(() => PreauthorisationReferralModel)
  async getPreauthorizationReferralByCode(
    @Args('referralCode') referralCode: string,
  ): Promise<PreauthorisationReferralModel> {
    return this.preAuthReferralService.getPreauthorizationReferralByCode(
      referralCode,
    );
  }

  @Mutation(() => [PreAuthReferralUtilisationsModel])
  async updateReferralUtilStatus(
    @CurrentProfile() mutator: ProfileModel,
    @Args({ name: 'referralCode', type: () => String })
    referralCode: string,
    @Args({ name: 'status', type: () => String })
    status: string,
  ): Promise<PreAuthReferralUtilisationsModel[]> {
    const items = await this.preAuthReferralService.updateReferralUtilStatus(
      mutator,
      referralCode,
      status,
    );

    items.forEach((item) => {
      this.pubSub.publish(UtilizationReferralUpdated, {
        [UtilizationReferralUpdated]: item,
      });
    });

    return items;
  }

  @Mutation(() => PreAuthReferralUtilisationsModel)
  async updateReferralUtilisationStatus(
    @CurrentProfile() mutator: ProfileModel,
    @Args('input') input: UpdateReferralUtilisationStatusInput,
  ): Promise<PreAuthReferralUtilisationsModel> {
    const item =
      await this.preAuthReferralService.updateReferralUtilisationStatus(
        mutator,
        input,
      );

    this.pubSub.publish(UtilizationReferralUpdated, {
      [UtilizationReferralUpdated]: item,
    });

    return item;
  }

  @Subscription(() => PreAuthReferralUtilisationsModel, {
    name: UtilizationReferralUpdated,
    filter: filterUtilizationReferralUpdated,
  })
  updateReferralUtilizationSubsHandler(
    @Args('profileId') _profileId: string,
    @Args('hospitalId') _hospitalId: string,
    @Args('hmoProviderId', { nullable: true }) _hmoProviderId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(UtilizationReferralUpdated);
  }
}
