import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PreAuthReferralUtilisationsModel } from './utilisations-referral.model';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { PreauthorisationStatus } from '@clinify/pre-authorisations/interface/preauthorizations.interface';
import { DiagnosisInput } from '@clinify/shared/validators/service-detail.input';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
export abstract class BaseAudits {
  @IsDate()
  @Field({ nullable: false })
  @Index()
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'updated_by', nullable: true })
  lastModifierId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'created_by' })
  createdBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'created_by', nullable: true })
  creatorId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'creator_name', nullable: true })
  creatorName?: string;
}

@ObjectType()
export abstract class AuditEntitiesWithProfile extends BaseAudits {
  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn({ name: 'profile_id' })
  profile?: ProfileModel;

  @Index()
  @Column({ name: 'profile_id' })
  @Field({ nullable: true })
  profileId?: string;
}

@ObjectType()
@Entity('pre_authorizations_referral')
export class PreauthorisationReferralModel extends AuditEntitiesWithProfile {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsUUID('4')
  id: string;

  @Field(() => Date)
  @Column({ name: 'request_date_time' })
  requestDateTime?: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'requested_by', type: 'text' })
  requestedBy?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'referred_by', type: 'text', nullable: true })
  referredBy?: string;

  @Field(() => String)
  @Column({ name: 'service_type', type: 'text' })
  serviceType: string;

  @Field(() => String)
  @Column({ name: 'service_type_code', type: 'text' })
  serviceTypeCode: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'service_name', type: 'text', nullable: true })
  serviceName: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'priority', type: 'text', nullable: true })
  priority?: string;

  @Field(() => [DiagnosisInput], { nullable: true })
  @Column({
    nullable: true,
    name: 'diagnosis',
    type: 'jsonb',
  })
  diagnosis?: DiagnosisInput[];

  @Field(() => [PreAuthReferralUtilisationsModel], { nullable: true })
  @OneToMany(
    () => PreAuthReferralUtilisationsModel,
    (utilisation) => utilisation.preAuthorizationReferral,
    { cascade: true },
  )
  utilizations?: PreAuthReferralUtilisationsModel[];

  @Field(() => String, { nullable: true })
  @Column({ name: 'additional_note', type: 'text', nullable: true })
  additionalNote?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'provider_refferal_remarks', type: 'text', nullable: true })
  referralProviderRemark?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'presenting_complain', type: 'text', nullable: true })
  presentingComplain: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'specialty' })
  specialty: string;

  @Field({ nullable: true })
  @Column({ name: 'rank', nullable: true })
  rank: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'department' })
  department: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'facility_name' })
  facilityName: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'facility_address' })
  facilityAddress: string;

  @Field({ nullable: true })
  @Column({ name: 'visit_id', nullable: true })
  visitId?: string;

  @Field(() => [String], { nullable: true })
  @Column({ name: 'visit_detail_ids', type: 'jsonb', nullable: true })
  visitDetailIds?: string[];

  @Field(() => [String], { nullable: true })
  @Column({ name: 'documents', type: 'text', array: true, nullable: true })
  documentUrl?: string[];

  @Field(() => PreauthorisationStatus)
  @Column({
    name: 'status',
    type: 'text',
    default: PreauthorisationStatus.Submitted,
  })
  status?: PreauthorisationStatus;

  @Field(() => String, { nullable: true })
  @Column({ name: 'code', type: 'text', nullable: true })
  code?: string;

  @Field(() => HospitalModel, { nullable: true })
  @ManyToOne(() => HospitalModel, (hospital) => hospital.preauthorisations)
  @JoinColumn({ name: 'hospital_id' })
  hospital?: HospitalModel;

  @Index()
  @Column({ name: 'hospital_id', nullable: false })
  hospitalId: string;

  @Field(() => HospitalModel, { nullable: true })
  @ManyToOne(() => HospitalModel, (hospital) => hospital.preauthorisations)
  @JoinColumn({ name: 'referred_provider_id' })
  referredProvider?: HospitalModel;

  @Index()
  @Column({ name: 'referred_provider_id', nullable: false })
  referredProviderId: string;

  @Field(() => String, { nullable: false })
  @Column({ name: 'referred_provider_name', type: 'text', nullable: true })
  referredProviderName: string;

  @Field(() => HmoProviderModel, { nullable: true })
  @ManyToOne(() => HmoProviderModel, (provider) => provider.preauthorisations)
  @JoinColumn({ name: 'provider_id' })
  provider?: HmoProviderModel;

  @Column({ name: 'provider_id', nullable: false })
  providerId: string;

  @Field(() => Boolean, { defaultValue: false })
  @Column({ name: 'archived', type: 'boolean', default: false })
  archived?: boolean;

  @Field({ nullable: true })
  @Column({ name: 'enrollee_number', nullable: true })
  enrolleeNumber?: string;

  @Field(() => Boolean, { nullable: true })
  @Column({ name: 'is_external_plan_type', type: 'boolean', default: false })
  isExternalPlanType?: boolean;

  @Field(() => String, { nullable: true })
  @Column({ name: 'external_plan_type_id', nullable: true })
  externalPlanTypeId?: string;

  constructor(preAuth: Partial<PreauthorisationReferralModel>) {
    super();
    Object.assign(this, preAuth);
  }
}
