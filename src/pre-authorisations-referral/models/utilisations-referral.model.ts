import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsUUID } from 'class-validator';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { PreauthorisationReferralModel } from './preauthorisation-referral.model';
import { ReferalStatusHistory } from '../inputs/status-history.input';

@ObjectType()
@Entity('pre_auth_referral_utilisations')
export class PreAuthReferralUtilisationsModel {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsUUID('4')
  id: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'visit_details_id', nullable: true })
  visitDetailsId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'category', type: 'text', nullable: true })
  category: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'utilizationId', nullable: true })
  utilizationId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'utilizationCode', type: 'text' })
  utilizationCode: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'type', type: 'text', nullable: true })
  type: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'status', type: 'text', nullable: true })
  status?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'quantity', type: 'text', nullable: true })
  quantity: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'pa_code', type: 'text', nullable: true })
  paCode: string;

  @Field(() => String, { nullable: true, description: 'Value is in Naira' })
  @Column({ name: 'price', type: 'text', nullable: true })
  price: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'rhesus_factor', type: 'text', nullable: true })
  rhesusFactor: string;

  @ManyToOne(() => PreauthorisationReferralModel, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'pre_auth_referral_id' })
  preAuthorizationReferral: PreauthorisationReferralModel;

  @Field(() => String)
  @Column({ name: 'pre_auth_referral_id', type: 'text' })
  preAuthorizationReferralId: string;

  @Field(() => [ReferalStatusHistory], { nullable: true })
  @Column({
    nullable: true,
    name: 'status_history',
    type: 'jsonb',
  })
  statusHistory?: ReferalStatusHistory[];

  @Field(() => [String], { nullable: true })
  @Column({
    name: 'rejection_reason',
    nullable: true,
    type: 'text',
    array: true,
  })
  rejectionReason?: string[];

  @Field(() => String, { nullable: true })
  @Column({
    name: 'specify_reason_for_rejection',
    type: 'text',
    nullable: true,
  })
  specifyReasonForRejection?: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'specialty' })
  specialty?: string;

  constructor(preAuth: Partial<PreAuthReferralUtilisationsModel>) {
    Object.assign(this, preAuth);
  }
}
