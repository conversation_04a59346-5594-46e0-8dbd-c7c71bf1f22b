import { Field, Int, ObjectType } from '@nestjs/graphql';
import { PreauthorisationReferralModel } from '../models/preauthorisation-referral.model';

@ObjectType()
export class PreauthorisationReferralResponse {
  constructor(preAuths: PreauthorisationReferralModel[], totalCount: number) {
    this.list = preAuths;
    this.totalCount = totalCount;
  }

  @Field(() => Int)
  totalCount: number;

  @Field(() => [PreauthorisationReferralModel])
  list: PreauthorisationReferralModel[];
}
