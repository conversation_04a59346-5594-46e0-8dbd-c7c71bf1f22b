import { Field, InputType, registerEnumType } from '@nestjs/graphql';
import { IsOptional } from 'class-validator';
import { TimeSortOrder } from '@clinify/pre-authorisations/inputs/pre-authorisation-filter.input';
import { FilterInput } from '@clinify/shared/validators/filter.input';

export enum PreauthReferralDateFilterType {
  CreatedDate = 'CreatedDate',
  RequestDate = 'RequestDate',
}

registerEnumType(PreauthReferralDateFilterType, {
  name: 'PreauthReferralDateFilterType',
});

@InputType()
export class PreauthorizationReferralFilterInput extends FilterInput {
  @Field(() => String, { nullable: true })
  @IsOptional()
  status?: string;

  @Field(() => String, { nullable: true })
  hmo?: string;

  @Field(() => String, { nullable: true })
  hospitalId?: string;

  @Field({ defaultValue: false, nullable: true })
  providerInsight?: boolean;

  @Field(() => String, { nullable: true })
  providerType?: string;

  @Field(() => PreauthReferralDateFilterType, {
    defaultValue: PreauthReferralDateFilterType.RequestDate,
  })
  filterDateField?: PreauthReferralDateFilterType;

  @Field({ nullable: true })
  showCompleted?: boolean;

  @Field({ nullable: true })
  showNotCompleted?: boolean;

  @Field(() => TimeSortOrder, { nullable: true })
  timeSortOrder?: TimeSortOrder;
}
