import { Field, InputType, PartialType } from '@nestjs/graphql';
import { IsOptional, IsUUID } from 'class-validator';
import { PreauthUtilisationInput } from '@clinify/pre-authorisations/inputs/preauth-utilisation.input';
import { DiagnosisInput } from '@clinify/shared/validators/service-detail.input';

@InputType()
export class PreauthorisationReferralInput {
  @Field(() => String)
  clinifyId?: string;

  @Field(() => String)
  enrolleeId: string;

  @Field(() => Date)
  requestDateTime?: Date;

  @Field(() => String)
  requestedBy?: string;

  @Field(() => String, { nullable: true })
  referredBy?: string;

  @Field(() => String, { nullable: true })
  facilityName?: string;

  @Field(() => String, { nullable: true })
  facilityAddress?: string;

  @Field(() => String, { nullable: true })
  rank?: string;

  @Field(() => String, { nullable: true })
  department?: string;

  @Field(() => String, { nullable: true })
  specialty?: string;

  @Field(() => String, { nullable: true })
  presentingComplain?: string;

  @Field(() => String)
  providerId: string;

  @Field(() => String)
  serviceType: string;

  @Field(() => String)
  serviceTypeCode: string;

  @Field(() => String, { nullable: true })
  serviceName: string;

  @Field(() => String, { nullable: false })
  referredProviderId: string;

  @Field(() => String, { nullable: false })
  referredProviderName: string;

  @Field(() => String, { nullable: false })
  referringProviderId: string;

  @Field(() => String, { nullable: false })
  referringProviderName: string;

  @Field(() => String, { nullable: true })
  referralProviderRemark: string;

  @Field(() => String, { nullable: true })
  priority?: string;

  @Field(() => [DiagnosisInput])
  diagnosis?: DiagnosisInput[];

  @Field(() => [PreauthUtilisationInput])
  utilizations?: PreauthUtilisationInput[];

  @Field(() => String, { nullable: true })
  patientType?: string;

  @Field(() => String, { nullable: true })
  paymentType?: string;

  @Field(() => String, { nullable: true })
  additionalNote?: string;

  @Field(() => String, { nullable: true })
  referralRemarks: string;

  @Field(() => [String], { nullable: true })
  documentUrl?: string[];

  @Field(() => String, { nullable: true })
  visitId?: string;

  @Field(() => String, { nullable: true })
  memberUniqueId?: string;

  @Field(() => String, { nullable: true })
  staffEmail?: string;

  @Field({ nullable: true })
  isExternalPlanType?: boolean;

  @Field(() => String, { nullable: true })
  externalPlanTypeId?: string;
}

@InputType()
export class PreauthorisationReferralUpdateInput extends PartialType(
  PreauthorisationReferralInput,
) {
  @Field({ nullable: true })
  @IsUUID('4')
  @IsOptional()
  id?: string;
}

@InputType()
export class UpdateReferralUtilisationStatusInput {
  @Field(() => String)
  id: string;

  @Field(() => String)
  status: string;

  @Field(() => [String], { nullable: true })
  rejectionReason?: string[];

  @Field(() => String, { nullable: true })
  specifyReasonForRejection?: string;
}
