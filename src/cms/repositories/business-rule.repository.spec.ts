/* eslint-disable max-lines */
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import moment from 'moment-timezone';
import { DataSource, EntityManager } from 'typeorm';
import { BusinessRuleRepository } from './business-rule.repository';
import {
  NewBusinessRuleInput,
  NewBusinessRuleItemInput,
  BusinessRuleItemType,
  BusinessRuleConditionInput,
  BusinessRuleActionInput,
} from '@clinify/cms/inputs/business-rule.input';
import {
  BusinessRuleModel,
  BusinessRuleItemModel,
} from '@clinify/cms/models/business-rule.model';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { TestDataSourceOptions } from '@clinify/data-source';
import { HmoProfileModel } from '@clinify/hmo-profiles/models/hmo-profile.model';
import {
  BenefitCategory,
  BenefitCoverage,
} from '@clinify/hmo-providers/inputs/hmo-plan.input';
import { HmoPlanBenefitModel } from '@clinify/hmo-providers/models/hmo-plan-benefit.model';
import { HmoPlanTypeModel } from '@clinify/hmo-providers/models/hmo-plan-type.model';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { HmoVisitTypeModel } from '@clinify/hmo-providers/models/hmo-visit-type.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { UserType } from '@clinify/shared/enums/users';
import { Gender } from '@clinify/shared/validators/personal-information.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { createHmoClaim } from '@clinify/utils/tests/hmo-claim.fixture';
import { createHmoHospital } from '@clinify/utils/tests/hmo-hospital.fixture';
import { createHmoProfileFixtures } from '@clinify/utils/tests/hmo-profiles.fixtures';
import { createHmoProviderFixtures } from '@clinify/utils/tests/hmo-provider.fixtures';
import { createHospitals } from '@clinify/utils/tests/hospital.fixtures';
import { createUsers } from '@clinify/utils/tests/user.fixtures';

const chance = new Chance();

describe('BusinessRuleRepository', () => {
  let repository: BusinessRuleRepository;
  let manager: EntityManager;
  let module: TestingModule;
  let ds: DataSource;
  let profile: ProfileModel;
  let hospital: HospitalModel;
  let hmoProvider: HmoProviderModel;
  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeormExtendedModule.forCustomRepository([BusinessRuleRepository]),
      ],
      providers: [],
    }).compile();

    ds = module.get<DataSource>(DataSource);

    manager = ds.manager;
    repository = module.get<BusinessRuleRepository>(BusinessRuleRepository);
  });
  const createHospitalAndProfile = async (gender = Gender.Male) => {
    const [hmoProvider] = await createHmoProviderFixtures(manager, 1);
    const [hospital] = await createHospitals(
      manager,
      1,
      undefined,
      undefined,
      undefined,
      hmoProvider.id,
    );
    await createHmoHospital(manager, hospital, hmoProvider);
    const [user] = await createUsers(
      manager,
      1,
      hospital,
      null,
      null,
      UserType.Patient,
      undefined,
      undefined,
      undefined,
      gender,
    );
    profile = user.profiles[0];
    const [hmoProfile] = await createHmoProfileFixtures(
      manager,
      1,
      profile,
      undefined,
      undefined,
      hmoProvider,
      true,
      Gender.Male,
    );
    return { hospital, profile, hmoProvider, hmoProfile };
  };
  beforeEach(async () => {
    ({ hospital, profile, hmoProvider } = await createHospitalAndProfile());

    const mockBusinessRule = new BusinessRuleModel({
      flag: 'RED',
      hospitalId: hospital.id,
      items: [
        new BusinessRuleItemModel({
          category: 'gender',
          operator: 'Is',
          value: 'Male',
          type: BusinessRuleItemType.CONDITION,
        }),
      ],
      creatorId: profile.id,
      creatorName: profile.fullName,
    });
    await manager.save(mockBusinessRule);
  });

  afterAll(async () => {
    await ds.destroy();
    await module.close();
  });

  describe('createBusinessRule()', () => {
    it('should create new business rules', async () => {
      const input: NewBusinessRuleInput[] = [
        {
          items: [
            {
              category: 'gender',
              operator: 'Is',
              value: 'Male',
              type: BusinessRuleItemType.CONDITION,
            } as NewBusinessRuleItemInput,
          ],
          flag: 'RED',
        },
      ];
      const result = await repository.createBusinessRule(profile, input);

      expect(result).toHaveLength(1);
      expect(result[0].flag).toBe('RED');
      expect(result[0].creatorId).toBe(profile.id);
      expect(result[0].hospitalId).toBe(hospital.id);
    });
  });

  describe('checkBusinessRule', () => {
    let complexRule: BusinessRuleModel;
    let hospital: HospitalModel;
    let profile: ProfileModel;
    let hmoProvider: HmoProviderModel;
    let hmoProfile: HmoProfileModel;

    beforeEach(async () => {
      ({ hospital, profile, hmoProvider, hmoProfile } =
        await createHospitalAndProfile());
      // Create a complex rule with multiple conditions
      complexRule = new BusinessRuleModel({
        flag: 'ORANGE',
        hospitalId: hospital.id,
        items: [
          new BusinessRuleItemModel({
            category: 'gender',
            operator: 'Is',
            value: 'Male',
            type: BusinessRuleItemType.CONDITION,
          }),
          new BusinessRuleItemModel({
            category: 'ageRange',
            operator: 'Is',
            value: '20-40 Years',
            type: BusinessRuleItemType.CONDITION,
          }),
          new BusinessRuleItemModel({
            category: 'diagnosis',
            operator: 'Is',
            value: 'DIABETES',
            type: BusinessRuleItemType.CONDITION,
          }),
        ],
        creatorId: profile.id,
        creatorName: profile.fullName,
      });
      complexRule = await manager.save(complexRule);
    });

    it('should match complex combination with correct age range', async () => {
      const result = await repository.evaluateBusinessRuleConditions(
        {
          hmoProviderId: hmoProvider.id,
          enrolleeId: hmoProfile.memberNumber,
          diagnosis: ['DIABETES'],
          visitationType: 'OPD',
          planId: 'plan-1',
        },
        [],
      );

      expect(result).toContain(complexRule.id);
    });

    it('should return empty array when no rules match', async () => {
      const result = await repository.evaluateBusinessRuleConditions(
        {
          hmoProviderId: hmoProvider.id,
          enrolleeId: hmoProfile.memberNumber,
          diagnosis: ['TEST'],
          visitationType: 'OPD',
          planId: 'plan-1',
        },
        [],
      );

      expect(result).toHaveLength(0);
    });

    describe('multi-condition OR matching', () => {
      let orRule1: BusinessRuleModel;
      let orRule2: BusinessRuleModel;
      let orRule3: BusinessRuleModel;
      let hospital: HospitalModel;
      let profile: ProfileModel;
      let hmoProvider: HmoProviderModel;
      let hmoProfile: HmoProfileModel;

      beforeEach(async () => {
        ({ hospital, profile, hmoProvider, hmoProfile } =
          await createHospitalAndProfile(Gender.Female));

        // Rule 1: Multiple gender options (OR within same category)
        orRule1 = new BusinessRuleModel({
          flag: 'GENDER_OR',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'gender',
              operator: 'Is',
              value: 'Male',
              type: BusinessRuleItemType.CONDITION,
            }),
            new BusinessRuleItemModel({
              category: 'gender',
              operator: 'Is',
              value: 'Female',
              type: BusinessRuleItemType.CONDITION,
            }),
            new BusinessRuleItemModel({
              category: 'diagnosis',
              operator: 'Is',
              value: 'HYPERTENSION',
              type: BusinessRuleItemType.CONDITION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });

        // Rule 2: Multiple age ranges (OR within same category)
        orRule2 = new BusinessRuleModel({
          flag: 'AGE_OR',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'ageRange',
              operator: 'Is',
              value: '0-18 Years',
              type: BusinessRuleItemType.CONDITION,
            }),
            new BusinessRuleItemModel({
              category: 'ageRange',
              operator: 'Is',
              value: '> 65 Years',
              type: BusinessRuleItemType.CONDITION,
            }),
            new BusinessRuleItemModel({
              category: 'diagnosis',
              operator: 'Is',
              value: 'DIABETES',
              type: BusinessRuleItemType.CONDITION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });

        // Rule 3: Multiple diagnoses (OR within same category)
        orRule3 = new BusinessRuleModel({
          flag: 'DIAGNOSIS_OR',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'diagnosis',
              operator: 'Is',
              value: 'DIABETES',
              type: BusinessRuleItemType.CONDITION,
            }),
            new BusinessRuleItemModel({
              category: 'diagnosis',
              operator: 'Is',
              value: 'HYPERTENSION',
              type: BusinessRuleItemType.CONDITION,
            }),
            new BusinessRuleItemModel({
              category: 'diagnosis',
              operator: 'Is',
              value: 'ASTHMA',
              type: BusinessRuleItemType.CONDITION,
            }),
            new BusinessRuleItemModel({
              category: 'gender',
              operator: 'Is',
              value: 'Male',
              type: BusinessRuleItemType.CONDITION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });

        await manager.save([orRule1, orRule2, orRule3]);
      });

      it('should match rule when one of multiple gender conditions is met (OR logic)', async () => {
        // Test with Male gender - should match orRule1
        const result = await repository.evaluateBusinessRuleConditions(
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['HYPERTENSION'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
          [],
        );

        expect(result).toContain(orRule1.id);
        expect(result).not.toContain(orRule2.id);
        expect(result).not.toContain(orRule3.id);
      });

      it('should match rule when one of multiple diagnosis conditions is met (OR logic)', async () => {
        // Test with DIABETES diagnosis - should match orRule3
        const result = await repository.evaluateBusinessRuleConditions(
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
          [],
        );

        expect(result).toContain(orRule3.id);
        expect(result).not.toContain(orRule1.id);
        expect(result).not.toContain(orRule2.id);
      });

      it('should match multiple rules when different OR conditions are satisfied', async () => {
        // Test with HYPERTENSION diagnosis - should match both orRule1 and orRule3
        const result = await repository.evaluateBusinessRuleConditions(
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['HYPERTENSION'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
          [],
        );

        expect(result).toContain(orRule1.id);
        expect(result).not.toContain(orRule3.id);
        expect(result).not.toContain(orRule2.id);
      });

      it('should not match when none of the OR conditions within each category are satisfied', async () => {
        // Test with Female gender and age not in the OR ranges
        const [femaleUser] = await createUsers(
          manager,
          1,
          hospital,
          null,
          null,
          UserType.Patient,
          undefined,
          undefined,
          undefined,
          Gender.Female,
        );
        const femaleProfile = femaleUser.profiles[0];

        await manager.save(femaleProfile);

        const [femaleHmoProfile] = await createHmoProfileFixtures(
          manager,
          1,
          femaleProfile,
          undefined,
          undefined,
          hmoProvider,
          true,
          Gender.Female,
        );

        const result = await repository.evaluateBusinessRuleConditions(
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: femaleHmoProfile.memberNumber,
            diagnosis: ['COMMON_COLD'], // Diagnosis not in any OR rule
            visitationType: 'OPD',
            planId: 'plan-1',
          },
          [],
        );

        expect(result).toHaveLength(0);
      });

      it('should handle mixed AND and OR logic correctly', async () => {
        // Create a rule with AND between categories but OR within categories
        const mixedRule = new BusinessRuleModel({
          flag: 'MIXED_LOGIC',
          hospitalId: hospital.id,
          items: [
            // Gender category with OR logic
            new BusinessRuleItemModel({
              category: 'gender',
              operator: 'Is',
              value: 'Male',
              type: BusinessRuleItemType.CONDITION,
            }),
            new BusinessRuleItemModel({
              category: 'gender',
              operator: 'Is',
              value: 'Female',
              type: BusinessRuleItemType.CONDITION,
            }),
            // Visit type category (AND with gender)
            new BusinessRuleItemModel({
              category: 'visitationType',
              operator: 'Is',
              value: 'OPD',
              type: BusinessRuleItemType.CONDITION,
            }),
            // Diagnosis category with OR logic (AND with other categories)
            new BusinessRuleItemModel({
              category: 'diagnosis',
              operator: 'Is',
              value: 'DIABETES',
              type: BusinessRuleItemType.CONDITION,
            }),
            new BusinessRuleItemModel({
              category: 'diagnosis',
              operator: 'Is',
              value: 'HYPERTENSION',
              type: BusinessRuleItemType.CONDITION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });

        await manager.save(mixedRule);

        // Should match: Male (gender OR satisfied) AND OPD (visit type) AND DIABETES (diagnosis OR satisfied)
        const result = await repository.evaluateBusinessRuleConditions(
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
          [],
        );

        expect(result).toContain(mixedRule.id);
      });

      it('should not match when AND condition fails even if OR conditions within categories are satisfied', async () => {
        const mixedRule = new BusinessRuleModel({
          flag: 'MIXED_LOGIC_FAIL',
          hospitalId: hospital.id,
          items: [
            // Gender category with OR logic
            new BusinessRuleItemModel({
              category: 'gender',
              operator: 'Is',
              value: 'Male',
              type: BusinessRuleItemType.CONDITION,
            }),
            new BusinessRuleItemModel({
              category: 'gender',
              operator: 'Is',
              value: 'Female',
              type: BusinessRuleItemType.CONDITION,
            }),
            // Visit type category (AND with gender) - this will fail
            new BusinessRuleItemModel({
              category: 'visitationType',
              operator: 'Is',
              value: 'IPD',
              type: BusinessRuleItemType.CONDITION,
            }),
            // Diagnosis category with OR logic
            new BusinessRuleItemModel({
              category: 'diagnosis',
              operator: 'Is',
              value: 'DIABETES',
              type: BusinessRuleItemType.CONDITION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });

        await manager.save(mixedRule);

        // Should NOT match: Male (gender OR satisfied) AND IPD (visit type fails) AND DIABETES (diagnosis OR satisfied)
        const result = await repository.evaluateBusinessRuleConditions(
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD', // This doesn't match IPD requirement
            planId: 'plan-1',
          },
          [],
        );

        expect(result).not.toContain(mixedRule.id);
      });
    });
  });

  describe('evaluateBusinessRules', () => {
    describe('sumAll functionality', () => {
      let sumAllRule1: BusinessRuleModel;
      let sumAllRule2: BusinessRuleModel;
      let noSumAllRule: BusinessRuleModel;
      let hospital: HospitalModel;
      let profile: ProfileModel;
      let hmoProfile: HmoProfileModel;
      let hmoProvider: HmoProviderModel;

      beforeEach(async () => {
        ({ hospital, profile, hmoProvider, hmoProfile } =
          await createHospitalAndProfile());

        // Create two rules with sumAll = true
        sumAllRule1 = new BusinessRuleModel({
          flag: 'YELLOW',
          hospitalId: hospital.id,
          sumAll: true,
          items: [
            new BusinessRuleItemModel({
              category: 'amount',
              operator: 'Between',
              value: '5000-10000',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });

        sumAllRule2 = new BusinessRuleModel({
          flag: 'YELLOW',
          hospitalId: hospital.id,
          sumAll: true,
          items: [
            new BusinessRuleItemModel({
              category: 'amount',
              operator: 'Less Than',
              value: '200',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });

        // Create a rule with sumAll = false
        noSumAllRule = new BusinessRuleModel({
          flag: 'RED',
          hospitalId: hospital.id,
          sumAll: false,
          items: [
            new BusinessRuleItemModel({
              category: 'quantity',
              operator: 'Less Than',
              value: '2',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });

        await manager.save([sumAllRule1, sumAllRule2, noSumAllRule]);
      });

      it('should sum amounts from multiple matching rules when sumAll is true', async () => {
        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 5000,
            },
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-2',
              amount: 1000,
            },
          ],
          [sumAllRule1.id, sumAllRule2.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(2);
      });

      it('should only consider first matching rule when sumAll is false', async () => {
        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 150,
            },
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-2',
              amount: 200,
            },
          ],
          [noSumAllRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        const uniqueRuleIds = [...new Set(result.map((r) => r.ruleId))];
        expect(uniqueRuleIds).toHaveLength(1);
        expect(uniqueRuleIds[0]).toBe(noSumAllRule.id);
        expect(result[0].flag).toBe('RED');
      });

      it('should handle mixed sumAll and non-sumAll rules correctly', async () => {
        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 2000,
            },
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-2',
              amount: 2500,
            },
          ],
          [sumAllRule1.id, noSumAllRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        const uniqueRuleIds = [...new Set(result.map((r) => r.ruleId))];
        expect(uniqueRuleIds).toHaveLength(1);
      });
    });

    let actionRule: BusinessRuleModel;
    let hospital: HospitalModel;
    let profile: ProfileModel;
    let hmoProfile: HmoProfileModel;
    beforeEach(async () => {
      ({ hospital, profile, hmoProfile } = await createHospitalAndProfile());
      actionRule = new BusinessRuleModel({
        flag: 'YELLOW',
        hospitalId: hospital.id,
        items: [
          new BusinessRuleItemModel({
            category: 'utilizationType',
            operator: 'Is',
            value: 'DRUG',
            type: BusinessRuleItemType.ACTION,
          }),
        ],
        creatorId: profile.id,
        creatorName: profile.fullName,
      });
      actionRule = await manager.save(actionRule);
    });

    it('should evaluate action rules and return matching flags', async () => {
      const result = await repository.evaluateBusinessRuleActions(
        [
          {
            utilizationType: 'DRUG',
            utilizationCategory: 'PHARMACY',
            quantity: 1,
            utilizationId: 'util-1',
            amount: 100,
          },
        ],
        [actionRule.id],
        {
          hmoProviderId: hmoProvider.id,
          enrolleeId: hmoProfile.memberNumber,
          diagnosis: ['DIABETES'],
          visitationType: 'OPD',
          planId: 'plan-1',
        },
      );

      expect(result).toHaveLength(1);
      expect(result[0].flag).toBe('YELLOW');
      expect(result[0].ruleId).toBe(actionRule.id);
    });
  });

  describe('evaluateCondition', () => {
    it('should evaluate "Is" condition correctly', () => {
      const result = (repository as any).evaluateCondition(
        'Is',
        'test',
        'test',
      );
      expect(result).toBe(true);
    });

    it('should evaluate "Is Not" condition correctly', () => {
      const result = (repository as any).evaluateCondition(
        'Is Not',
        'test',
        'different',
      );
      expect(result).toBe(true);
    });

    it('should evaluate numeric comparisons correctly', () => {
      expect(
        (repository as any).evaluateCondition('Less Than', '10', '5'),
      ).toBe(true);
      expect(
        (repository as any).evaluateCondition('Greater Than', '5', '10'),
      ).toBe(true);
    });

    describe('Between operator', () => {
      const operator = 'Between';

      it('should return true when inputValue is between ruleValue range', () => {
        const ruleValue = '10 - 20';
        const inputValue = '15';
        expect(
          repository.evaluateCondition(operator, ruleValue, inputValue),
        ).toBe(true);
      });

      it('should return true when inputValue is equal to the minimum of ruleValue range', () => {
        const ruleValue = '10-20'; // Test without spaces
        const inputValue = '10';
        expect(
          repository.evaluateCondition(operator, ruleValue, inputValue),
        ).toBe(true);
      });

      it('should return true when inputValue is equal to the maximum of ruleValue range', () => {
        const ruleValue = ' 10 - 20 '; // Test with leading/trailing spaces
        const inputValue = '20';
        expect(
          repository.evaluateCondition(operator, ruleValue, inputValue),
        ).toBe(true);
      });

      it('should return false when inputValue is less than the minimum of ruleValue range', () => {
        const ruleValue = '10-20';
        const inputValue = '9.99';
        expect(
          repository.evaluateCondition(operator, ruleValue, inputValue),
        ).toBe(false);
      });

      it('should return false when inputValue is greater than the maximum of ruleValue range', () => {
        const ruleValue = '10-20';
        const inputValue = '20.01';
        expect(
          repository.evaluateCondition(operator, ruleValue, inputValue),
        ).toBe(false);
      });

      it('should handle non-numeric characters in ruleValue', () => {
        const ruleValue = '$10.50 - £20.75';
        const inputValue = '15';
        expect(
          repository.evaluateCondition(operator, ruleValue, inputValue),
        ).toBe(true);
      });
    });
  });

  describe('combined conditions and actions', () => {
    let mixedRule1: BusinessRuleModel;
    let mixedRule2: BusinessRuleModel;
    let mixedRule3: BusinessRuleModel;
    let mixedRule4: BusinessRuleModel;
    let hospital: HospitalModel;
    let profile: ProfileModel;
    let hmoProvider: HmoProviderModel;
    let hmoProfile: HmoProfileModel;

    beforeEach(async () => {
      ({ hospital, profile, hmoProvider, hmoProfile } =
        await createHospitalAndProfile());
      // Rule 1: Male patients over 40 with diabetes requesting high quantity drugs
      mixedRule1 = new BusinessRuleModel({
        flag: 'RED',
        hospitalId: hospital.id,
        items: [
          // Conditions
          new BusinessRuleItemModel({
            category: 'gender',
            operator: 'Is',
            value: 'Male',
            type: BusinessRuleItemType.CONDITION,
          }),
          new BusinessRuleItemModel({
            category: 'ageRange',
            operator: 'Is',
            value: '> 40 Years',
            type: BusinessRuleItemType.CONDITION,
          }),
          new BusinessRuleItemModel({
            category: 'diagnosis',
            operator: 'Is',
            value: 'DIABETES',
            type: BusinessRuleItemType.CONDITION,
          }),
          // Actions
          new BusinessRuleItemModel({
            category: 'utilizationType',
            operator: 'Is',
            value: 'DRUG',
            type: BusinessRuleItemType.ACTION,
          }),
          new BusinessRuleItemModel({
            category: 'utilizationCategory',
            operator: 'Is',
            value: 'PHARMACY',
            type: BusinessRuleItemType.ACTION,
          }),
          new BusinessRuleItemModel({
            category: 'quantity',
            operator: 'Greater Than',
            value: '10',
            type: BusinessRuleItemType.ACTION,
          }),
        ],
        creatorId: profile.id,
        creatorName: profile.fullName,
      });

      // Rule 2: Male patients over 40 with diabetes with different utilization categories
      mixedRule2 = new BusinessRuleModel({
        flag: 'YELLOW',
        hospitalId: hospital.id,
        items: [
          // Similar conditions but different actions
          new BusinessRuleItemModel({
            category: 'gender',
            operator: 'Is',
            value: 'Male',
            type: BusinessRuleItemType.CONDITION,
          }),
          new BusinessRuleItemModel({
            category: 'ageRange',
            operator: 'Is',
            value: '> 40 Years',
            type: BusinessRuleItemType.CONDITION,
          }),
          new BusinessRuleItemModel({
            category: 'ageRange',
            operator: 'Is',
            value: '20-40 Years',
            type: BusinessRuleItemType.CONDITION,
          }),
          new BusinessRuleItemModel({
            category: 'diagnosis',
            operator: 'Is',
            value: 'DIABETES',
            type: BusinessRuleItemType.CONDITION,
          }),
          // Multiple actions
          new BusinessRuleItemModel({
            category: 'utilizationType',
            operator: 'Is',
            value: 'DRUG',
            type: BusinessRuleItemType.ACTION,
          }),
          new BusinessRuleItemModel({
            category: 'utilizationCategory',
            operator: 'Is',
            value: 'PHARMACY',
            type: BusinessRuleItemType.ACTION,
          }),
          new BusinessRuleItemModel({
            category: 'quantity',
            operator: 'Less Than',
            value: '5',
            type: BusinessRuleItemType.ACTION,
          }),
          new BusinessRuleItemModel({
            category: 'amount',
            operator: 'Is',
            value: '200',
            type: BusinessRuleItemType.ACTION,
          }),
        ],
        creatorId: profile.id,
        creatorName: profile.fullName,
      });
      mixedRule3 = new BusinessRuleModel({
        flag: 'RED',
        hospitalId: hospital.id,
        items: [
          new BusinessRuleItemModel({
            category: 'diagnosis',
            operator: 'Is',
            value: 'A01',
            type: BusinessRuleItemType.CONDITION,
          }),
          // Actions
          new BusinessRuleItemModel({
            category: 'usage',
            operator: 'Is',
            value: '3',
            type: BusinessRuleItemType.ACTION,
          }),
        ],
        creatorId: profile.id,
        creatorName: profile.fullName,
      });
      mixedRule4 = new BusinessRuleModel({
        flag: 'BLUE',
        hospitalId: hospital.id,
        items: [
          new BusinessRuleItemModel({
            category: 'diagnosis',
            operator: 'Is',
            value: 'AMOUNT 023',
            type: BusinessRuleItemType.CONDITION,
          }),
          // Actions
          new BusinessRuleItemModel({
            category: 'amount',
            operator: 'Less Than',
            value: '300',
            type: BusinessRuleItemType.ACTION,
          }),
          new BusinessRuleItemModel({
            category: 'utilizationType',
            operator: 'Contains',
            value: 'drug',
            type: BusinessRuleItemType.ACTION,
          }),
        ],
        creatorId: profile.id,
        creatorName: profile.fullName,
      });

      await manager.save([mixedRule1, mixedRule2, mixedRule3, mixedRule4]);
    });

    it('should match rules with both conditions and actions', async () => {
      // First check if conditions match
      const matchingRules = await repository.evaluateBusinessRuleConditions(
        {
          hmoProviderId: hmoProvider.id,
          enrolleeId: hmoProfile.memberNumber,
          diagnosis: ['DIABETES'],
          visitationType: 'OPD',
          planId: 'plan-1',
        },
        [],
      );
      expect(matchingRules).not.toContain(mixedRule1.id);

      // Then evaluate actions for the matching rules
      const result1 = await repository.evaluateBusinessRuleActions(
        [
          {
            utilizationType: 'DRUG',
            utilizationCategory: 'PHARMACY',
            quantity: 4,
            utilizationId: 'util-1',
            amount: 200,
          },
        ],
        matchingRules,
        {
          hmoProviderId: hmoProvider.id,
          enrolleeId: hmoProfile.memberNumber,
          diagnosis: ['DIABETES'],
          visitationType: 'OPD',
          planId: 'plan-1',
        },
      );
      expect(result1).toHaveLength(0);

      const result2 = await repository.evaluateBusinessRuleActions(
        [
          {
            utilizationType: 'DRUG',
            utilizationCategory: 'PHARMACY',
            quantity: 15,
            utilizationId: 'util-2',
            amount: 150,
          },
        ],
        matchingRules,
        {
          hmoProviderId: hmoProvider.id,
          enrolleeId: hmoProfile.memberNumber,
          diagnosis: ['DIABETES'],
          visitationType: 'OPD',
          planId: 'plan-1',
        },
      );

      expect(result2).toHaveLength(0);
    });

    it('should handle multiple action combinations', async () => {
      const matchingRules = await repository.evaluateBusinessRuleConditions(
        {
          hmoProviderId: hmoProvider.id,
          enrolleeId: hmoProfile.memberNumber,
          diagnosis: ['DIABETES'],
          visitationType: 'OPD',
          planId: 'plan-1',
        },
        [],
      );

      const result = await repository.evaluateBusinessRuleActions(
        [
          {
            utilizationType: 'DRUG',
            utilizationCategory: 'PHARMACY',
            quantity: 7,
            utilizationId: 'util-1',
            amount: 70,
          },
        ],
        matchingRules,
        {
          hmoProviderId: hmoProvider.id,
          enrolleeId: hmoProfile.memberNumber,
          diagnosis: ['DIABETES'],
          visitationType: 'OPD',
          planId: 'plan-1',
        },
      );

      expect(result).toHaveLength(0);
    });

    it('should evaluate complete business rule in one call', async () => {
      const conditionInput: BusinessRuleConditionInput = {
        hmoProviderId: hmoProvider.id,
        enrolleeId: hmoProfile.memberNumber,
        diagnosis: ['DIABETES'],
        visitationType: 'OPD',
        planId: 'plan-1',
      };

      const actionInput: BusinessRuleActionInput[] = [
        {
          utilizationType: 'DRUG',
          utilizationCategory: 'PHARMACY',
          quantity: 4,
          utilizationId: 'util-1',
          amount: 200,
        },
      ];

      const result = await repository.evaluateBusinessRule(
        conditionInput,
        actionInput,
      );

      expect(result).toHaveLength(0);
    });

    it('should return empty array when conditions match but actions do not', async () => {
      const result = await repository.evaluateBusinessRule(
        {
          hmoProviderId: hmoProvider.id,
          enrolleeId: hmoProfile.memberNumber,
          diagnosis: ['DIABETES'],
          visitationType: 'OPD',
          planId: 'plan-1',
        },
        [
          {
            utilizationType: 'DRUG',
            utilizationCategory: 'PHARMACY',
            quantity: 7,
            utilizationId: 'util-2',
            amount: 70,
          },
        ],
      );

      expect(result).toHaveLength(0);
    });

    it('should return true when condition category is diagnosis and action category is usage', async () => {
      await createHmoClaim(
        manager,
        2,
        profile,
        undefined,
        undefined,
        hospital,
        {
          providerId: hmoProvider.id,
          enrolleeNumber: hmoProfile.memberNumber,
          diagnosis: [
            {
              diagnosisICD10: 'A01',
            },
          ],
        },
        undefined,
        'submitted',
        undefined,
        hmoProvider,
      );
      const result = await repository.evaluateBusinessRule(
        {
          hmoProviderId: hmoProvider.id,
          enrolleeId: hmoProfile.memberNumber,
          diagnosis: ['A01'],
          visitationType: 'OPD',
          planId: 'plan-1',
        },
        [
          {
            utilizationType: 'DRUG',
            utilizationCategory: 'PHARMACY',
            quantity: 7,
            utilizationId: 'util-2',
            amount: 70,
          },
        ],
      );
      expect(result).toHaveLength(1);
      expect(result[0].flag).toBe('RED');
      expect(result[0].ruleId).toBe(mixedRule3.id);
    });

    it('should return true when amount is less than 300 and utilization type is DRUG', async () => {
      const conditionInput: BusinessRuleConditionInput = {
        hmoProviderId: hmoProvider.id,
        enrolleeId: hmoProfile.memberNumber,
        diagnosis: ['AMOUNT 023'],
        visitationType: 'DRUG',
        planId: 'plan-1',
      };

      const actionInput: BusinessRuleActionInput[] = [
        {
          utilizationType: 'Testing drug',
          utilizationCategory: 'PHARMACY',
          quantity: 4,
          utilizationId: 'util-1',
          amount: 20,
        },
      ];

      const result = await repository.evaluateBusinessRule(
        conditionInput,
        actionInput,
      );

      expect(result).toHaveLength(1);
      expect(result[0].flag).toBe('BLUE');
      expect(result[0].ruleId).toBe(mixedRule4.id);
    });
  });

  describe('Business Rule Dynamic values', () => {
    beforeEach(async () => {
      const planItem = new HmoPlanTypeModel({
        name: 'BHCP',
        hospitalId: hospital.id,
        creatorId: profile.id,
        creatorName: profile.fullName,
        hmoProviderId: hmoProvider.id,
        planDateTime: new Date(),
        premiumDetails: [{ category: 'Drugs' }],
      });
      const planType = await manager.save(planItem);
      const visitationTypeItem = new HmoVisitTypeModel({
        name: 'OPD',
        code: 'OPD',
        planTypeId: planType.id,
        hmoProviderId: hmoProvider.id,
        creatorId: profile.id,
        creatorName: profile.fullName,
      });
      const utilizationTypeItem = new HmoPlanBenefitModel({
        name: 'Consultation',
        code: 'CONS001',
        planTypeId: planItem.id,
        hmoProviderId: hmoProvider.id,
        utilisationCategory: 'Drugs',
        utilisationTypes: [
          {
            id: chance.guid({ version: 4 }),
            name: 'Consultation',
            code: 'CONS001',
            price: '5000',
            benefitCategory: BenefitCategory.FeeForService,
            benefitCoverage: BenefitCoverage.Covered,
          },
        ],
        creatorId: profile.id,
        creatorName: profile.fullName,
      });
      await manager.save([visitationTypeItem, utilizationTypeItem]);
    });
    it('getUtilizationTypes()should get utilization types', async () => {
      const result = await repository.getUtilizationTypes(
        ds,
        hmoProvider.id,
        {},
      );
      expect(result).toHaveLength(1);
      expect(result[0]).toBe('Consultation');
    });
    it('getUtilizationCategories() should get utilization categories', async () => {
      const result = await repository.getUtilizationCategories(
        ds,
        hmoProvider.id,
        {},
      );
      expect(result).toHaveLength(1);
      expect(result[0]).toBe('Drugs');
    });
    it('getVisitationTypes() should get visit types', async () => {
      const result = await repository.getVisitationTypes(
        ds,
        hmoProvider.id,
        {},
      );
      expect(result).toHaveLength(1);
      expect(result[0]).toBe('OPD');
    });
  });

  describe('tokenizeDateRange()', () => {
    describe('with "Is" operator (default)', () => {
      it('should parse range patterns correctly', () => {
        const result1 = repository.tokenizeDateRange('0 - 30 Days');
        expect(result1).toEqual({
          a: 0,
          b: 30,
          operator: 'between',
          calendrical: 'days',
          ruleOperator: 'Is',
          shouldMatch: true,
        });

        const result2 = repository.tokenizeDateRange('1 - 11 Months');
        expect(result2).toEqual({
          a: 1,
          b: 11,
          operator: 'between',
          calendrical: 'months',
          ruleOperator: 'Is',
          shouldMatch: true,
        });

        const result3 = repository.tokenizeDateRange('20 - 29 Years');
        expect(result3).toEqual({
          a: 20,
          b: 29,
          operator: 'between',
          calendrical: 'years',
          ruleOperator: 'Is',
          shouldMatch: true,
        });
      });

      it('should parse greater than patterns correctly', () => {
        const result1 = repository.tokenizeDateRange('>55 Years');
        expect(result1).toEqual({
          a: 55,
          operator: 'greater',
          calendrical: 'years',
          ruleOperator: 'Is',
          shouldMatch: true,
        });

        const result2 = repository.tokenizeDateRange('> 40 Days');
        expect(result2).toEqual({
          a: 40,
          operator: 'greater',
          calendrical: 'days',
          ruleOperator: 'Is',
          shouldMatch: true,
        });
      });

      it('should parse less than patterns correctly', () => {
        const result1 = repository.tokenizeDateRange('<18 Years');
        expect(result1).toEqual({
          a: 18,
          operator: 'less',
          calendrical: 'years',
          ruleOperator: 'Is',
          shouldMatch: true,
        });

        const result2 = repository.tokenizeDateRange('< 5 Months');
        expect(result2).toEqual({
          a: 5,
          operator: 'less',
          calendrical: 'months',
          ruleOperator: 'Is',
          shouldMatch: true,
        });
      });

      it('should parse single number patterns correctly', () => {
        const result1 = repository.tokenizeDateRange('17 Years');
        expect(result1).toEqual({
          a: 17,
          operator: 'equal',
          calendrical: 'years',
          ruleOperator: 'Is',
          shouldMatch: true,
        });

        const result2 = repository.tokenizeDateRange('30 Days');
        expect(result2).toEqual({
          a: 30,
          operator: 'equal',
          calendrical: 'days',
          ruleOperator: 'Is',
          shouldMatch: true,
        });
      });

      it('should handle different dash types', () => {
        const result1 = repository.tokenizeDateRange('1-5 Years'); // hyphen
        const result2 = repository.tokenizeDateRange('1–5 Years'); // en dash
        const result3 = repository.tokenizeDateRange('1—5 Years'); // em dash

        const expected = {
          a: 1,
          b: 5,
          operator: 'between',
          calendrical: 'years',
          ruleOperator: 'Is',
          shouldMatch: true,
        };

        expect(result1).toEqual(expected);
        expect(result2).toEqual(expected);
        expect(result3).toEqual(expected);
      });
    });

    describe('with "Is Not" operator', () => {
      it('should parse range patterns with shouldMatch false', () => {
        const result = repository.tokenizeDateRange('20 - 29 Years', 'Is Not');
        expect(result).toEqual({
          a: 20,
          b: 29,
          operator: 'between',
          calendrical: 'years',
          ruleOperator: 'Is Not',
          shouldMatch: false,
        });
      });

      it('should parse greater than patterns with shouldMatch false', () => {
        const result = repository.tokenizeDateRange('>55 Years', 'Is Not');
        expect(result).toEqual({
          a: 55,
          operator: 'greater',
          calendrical: 'years',
          ruleOperator: 'Is Not',
          shouldMatch: false,
        });
      });

      it('should parse single number patterns with shouldMatch false', () => {
        const result = repository.tokenizeDateRange('17 Years', 'Is Not');
        expect(result).toEqual({
          a: 17,
          operator: 'equal',
          calendrical: 'years',
          ruleOperator: 'Is Not',
          shouldMatch: false,
        });
      });
    });

    describe('edge cases', () => {
      it('should return null for invalid inputs', () => {
        expect(repository.tokenizeDateRange('')).toBeNull();
        expect(repository.tokenizeDateRange(null as any)).toBeNull();
        expect(repository.tokenizeDateRange(undefined as any)).toBeNull();
        expect(repository.tokenizeDateRange('invalid text')).toBeNull();
      });

      it('should handle whitespace correctly', () => {
        const result = repository.tokenizeDateRange('  20 - 29 Years  ');
        expect(result).toEqual({
          a: 20,
          b: 29,
          operator: 'between',
          calendrical: 'years',
          ruleOperator: 'Is',
          shouldMatch: true,
        });
      });

      it('should be case insensitive', () => {
        const result1 = repository.tokenizeDateRange('20 - 29 YEARS');
        const result2 = repository.tokenizeDateRange('20 - 29 years');
        const result3 = repository.tokenizeDateRange('CUSTOM');

        expect(result1.calendrical).toBe('years');
        expect(result2.calendrical).toBe('years');
        expect(result3).toEqual(null);
      });

      it('should default to year when no unit is specified', () => {
        const result = repository.tokenizeDateRange('25');
        expect(result).toEqual({
          a: 25,
          operator: 'equal',
          calendrical: 'years',
          ruleOperator: 'Is',
          shouldMatch: true,
        });
      });
    });

    describe('calendrical unit detection', () => {
      it('should detect day unit', () => {
        const result = repository.tokenizeDateRange('30 days');
        expect(result.calendrical).toBe('days');
      });

      it('should detect week unit', () => {
        const result = repository.tokenizeDateRange('2 weeks');
        expect(result.calendrical).toBe('weeks');
      });

      it('should detect month unit', () => {
        const result = repository.tokenizeDateRange('6 months');
        expect(result.calendrical).toBe('months');
      });

      it('should detect year unit', () => {
        const result = repository.tokenizeDateRange('25 years');
        expect(result.calendrical).toBe('years');
      });
    });
  });

  describe('evaluateDateRange()', () => {
    const now = moment().tz('Africa/Lagos');

    describe('with "Is" operator', () => {
      it('should return true when date is in range for "between" operator', () => {
        const dateOfBirth = now.clone().subtract(25, 'years').toDate();
        const result = repository.evaluateDateRange(
          '20 - 30 Years',
          dateOfBirth,
          'Is',
        );
        expect(result).toBe(true);
      });

      it('should return false when date is not in range for "between" operator', () => {
        const dateOfBirth = now.clone().subtract(35, 'years').toDate();
        const result = repository.evaluateDateRange(
          '20 - 30 Years',
          dateOfBirth,
          'Is',
        );
        expect(result).toBe(false);
      });

      it('should return true when date matches for "equal" operator', () => {
        const dateOfBirth = now.clone().subtract(17, 'years').toDate();
        const result = repository.evaluateDateRange(
          '17 Years',
          dateOfBirth,
          'Is',
        );
        expect(result).toBe(true);
      });

      it('should return false when date does not match for "equal" operator', () => {
        const dateOfBirth = now.clone().subtract(18, 'years').toDate();
        const result = repository.evaluateDateRange(
          '17 Years',
          dateOfBirth,
          'Is',
        );
        expect(result).toBe(false);
      });

      it('should return true when date is greater for "greater" operator', () => {
        const dateOfBirth = now.clone().subtract(60, 'years').toDate();
        const result = repository.evaluateDateRange(
          '>55 Years',
          dateOfBirth,
          'Is',
        );
        expect(result).toBe(true);
      });

      it('should return false when date is not greater for "greater" operator', () => {
        const dateOfBirth = now.clone().subtract(50, 'years').toDate();
        const result = repository.evaluateDateRange(
          '>55 Years',
          dateOfBirth,
          'Is',
        );
        expect(result).toBe(false);
      });

      it('should return true when date is less for "less" operator', () => {
        const dateOfBirth = now.clone().subtract(10, 'years').toDate();
        const result = repository.evaluateDateRange(
          '<18 Years',
          dateOfBirth,
          'Is',
        );
        expect(result).toBe(true);
      });
      it('should return false when date is not less for "less" operator', () => {
        const dateOfBirth = now.clone().subtract(20, 'years').toDate();
        const result = repository.evaluateDateRange(
          '<18 Years',
          dateOfBirth,
          'Is',
        );
        expect(result).toBe(false);
      });
    });

    describe('with "Is Not" operator', () => {
      it('should return false when date is in range for "between" operator', () => {
        const dateOfBirth = now.clone().subtract(25, 'years').toDate();
        const result = repository.evaluateDateRange(
          '20 - 30 Years',
          dateOfBirth,
          'Is Not',
        );
        expect(result).toBe(false);
      });

      it('should return true when date is not in range for "between" operator', () => {
        const dateOfBirth = now.clone().subtract(35, 'years').toDate();
        const result = repository.evaluateDateRange(
          '20 - 30 Years',
          dateOfBirth,
          'Is Not',
        );
        expect(result).toBe(true);
      });

      it('should return false when date matches for "equal" operator', () => {
        const dateOfBirth = now.clone().subtract(17, 'years').toDate();
        const result = repository.evaluateDateRange(
          '17 Years',
          dateOfBirth,
          'Is Not',
        );
        expect(result).toBe(false);
      });

      it('should return true when date does not match for "equal" operator', () => {
        const dateOfBirth = now.clone().subtract(18, 'years').toDate();
        const result = repository.evaluateDateRange(
          '17 Years',
          dateOfBirth,
          'Is Not',
        );
        expect(result).toBe(true);
      });
    });

    describe('edge cases', () => {
      it('should return false for invalid date range text', () => {
        const dateOfBirth = now.clone().subtract(25, 'years').toDate();
        const result = repository.evaluateDateRange(
          'invalid',
          dateOfBirth,
          'Is',
        );
        expect(result).toBe(false);
      });

      it('should handle different date formats', () => {
        const dateString = now
          .clone()
          .subtract(25, 'years')
          .format('YYYY-MM-DD');
        const result = repository.evaluateDateRange(
          '20 - 30 Years',
          dateString,
          'Is',
        );
        expect(result).toBe(true);
      });

      it('should handle boundary conditions for between operator', () => {
        // Test exact boundary dates
        const exactLowerBound = now.clone().subtract(20, 'years').toDate();
        const exactUpperBound = now.clone().subtract(30, 'years').toDate();

        const result1 = repository.evaluateDateRange(
          '20 - 30 Years',
          exactLowerBound,
          'Is',
        );
        const result2 = repository.evaluateDateRange(
          '20 - 30 Years',
          exactUpperBound,
          'Is',
        );

        expect(result1).toBe(true);
        expect(result2).toBe(true);
      });
    });

    describe('with different calendrical units', () => {
      it('should work with days', () => {
        const dateOfBirth = now.clone().subtract(15, 'days').toDate();
        const result = repository.evaluateDateRange(
          '0 - 30 Days',
          dateOfBirth,
          'Is',
        );
        expect(result).toBe(true);
      });

      it('should work with months', () => {
        const dateOfBirth = now.clone().subtract(6, 'months').toDate();
        const result = repository.evaluateDateRange(
          '1 - 11 Months',
          dateOfBirth,
          'Is',
        );
        expect(result).toBe(true);
      });

      it('should work with weeks', () => {
        const dateOfBirth = now.clone().subtract(1, 'week').toDate();
        const result = repository.evaluateDateRange(
          '0 - 2 Weeks',
          dateOfBirth,
          'Is',
        );
        expect(result).toBe(true);
      });
    });
  });

  describe('enrolleeStatus evaluation', () => {
    let enrolleeStatusRule: BusinessRuleModel;
    let hospital: HospitalModel;
    let profile: ProfileModel;
    let hmoProfile: HmoProfileModel;
    let hmoProvider: HmoProviderModel;

    beforeEach(async () => {
      ({ hospital, profile, hmoProfile, hmoProvider } =
        await createHospitalAndProfile());
    });

    describe('with "Is" operator', () => {
      beforeEach(async () => {
        enrolleeStatusRule = new BusinessRuleModel({
          flag: 'BLUE',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'enrolleeStatus',
              operator: 'Is',
              value: 'ACTIVE',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });
        enrolleeStatusRule = await manager.save(enrolleeStatusRule);
      });

      it('should match when enrollee status matches rule value', async () => {
        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              enrolleeStatus: 'ACTIVE',
            },
          ],
          [enrolleeStatusRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(1);
        expect(result[0].flag).toBe('BLUE');
        expect(result[0].ruleId).toBe(enrolleeStatusRule.id);
      });

      it('should not match when enrollee status does not match rule value', async () => {
        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              enrolleeStatus: 'INACTIVE',
            },
          ],
          [enrolleeStatusRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(0);
      });

      it('should not match when enrollee status is undefined', async () => {
        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              // enrolleeStatus is undefined
            },
          ],
          [enrolleeStatusRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(0);
      });
    });

    describe('with "Is Not" operator', () => {
      beforeEach(async () => {
        enrolleeStatusRule = new BusinessRuleModel({
          flag: 'GREEN',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'enrolleeStatus',
              operator: 'Is Not',
              value: 'SUSPENDED',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });
        enrolleeStatusRule = await manager.save(enrolleeStatusRule);
      });

      it('should match when enrollee status does not match rule value', async () => {
        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              enrolleeStatus: 'ACTIVE',
            },
          ],
          [enrolleeStatusRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(1);
        expect(result[0].flag).toBe('GREEN');
        expect(result[0].ruleId).toBe(enrolleeStatusRule.id);
      });

      it('should not match when enrollee status matches rule value', async () => {
        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              enrolleeStatus: 'SUSPENDED',
            },
          ],
          [enrolleeStatusRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(0);
      });

      it('should match when enrollee status is undefined (treated as not equal)', async () => {
        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              // enrolleeStatus is undefined
            },
          ],
          [enrolleeStatusRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(1);
        expect(result[0].flag).toBe('GREEN');
      });
    });

    describe('with different enrollee status values', () => {
      const statusTestCases = [
        { status: 'ACTIVE', flag: 'STATUS_ACTIVE' },
        { status: 'INACTIVE', flag: 'STATUS_INACTIVE' },
        { status: 'SUSPENDED', flag: 'STATUS_SUSPENDED' },
        { status: 'PENDING', flag: 'STATUS_PENDING' },
        { status: 'EXPIRED', flag: 'STATUS_EXPIRED' },
      ];

      it('should handle multiple enrollee status rules correctly', async () => {
        // Create rules for each status
        const rules = await Promise.all(
          statusTestCases.map(async (testCase) => {
            const rule = new BusinessRuleModel({
              flag: testCase.flag,
              hospitalId: hospital.id,
              items: [
                new BusinessRuleItemModel({
                  category: 'enrolleeStatus',
                  operator: 'Is',
                  value: testCase.status,
                  type: BusinessRuleItemType.ACTION,
                }),
              ],
              creatorId: profile.id,
              creatorName: profile.fullName,
            });
            return manager.save(rule);
          }),
        );

        // Test each status
        for (const testCase of statusTestCases) {
          const result = await repository.evaluateBusinessRuleActions(
            [
              {
                utilizationType: 'DRUG',
                utilizationCategory: 'PHARMACY',
                quantity: 1,
                utilizationId: 'util-1',
                amount: 100,
                enrolleeStatus: testCase.status,
              },
            ],
            rules.map((r) => r.id),
            {
              hmoProviderId: hmoProvider.id,
              enrolleeId: hmoProfile.memberNumber,
              diagnosis: ['DIABETES'],
              visitationType: 'OPD',
              planId: 'plan-1',
            },
          );

          expect(result).toHaveLength(1);
          expect(result[0].flag).toBe(testCase.flag);
        }
      });
    });

    describe('combined with other action categories', () => {
      it('should work with multiple action categories including enrolleeStatus', async () => {
        const combinedRule = new BusinessRuleModel({
          flag: 'COMBINED',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'enrolleeStatus',
              operator: 'Is',
              value: 'ACTIVE',
              type: BusinessRuleItemType.ACTION,
            }),
            new BusinessRuleItemModel({
              category: 'utilizationType',
              operator: 'Is',
              value: 'DRUG',
              type: BusinessRuleItemType.ACTION,
            }),
            new BusinessRuleItemModel({
              category: 'amount',
              operator: 'Greater Than',
              value: '50',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });
        const savedRule = await manager.save(combinedRule);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              enrolleeStatus: 'ACTIVE',
            },
          ],
          [savedRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(1);
        expect(result[0].flag).toBe('COMBINED');
        expect(result[0].ruleId).toBe(savedRule.id);
      });

      it('should not match when one of the combined conditions fails', async () => {
        const combinedRule = new BusinessRuleModel({
          flag: 'COMBINED',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'enrolleeStatus',
              operator: 'Is',
              value: 'ACTIVE',
              type: BusinessRuleItemType.ACTION,
            }),
            new BusinessRuleItemModel({
              category: 'utilizationType',
              operator: 'Is',
              value: 'DRUG',
              type: BusinessRuleItemType.ACTION,
            }),
            new BusinessRuleItemModel({
              category: 'amount',
              operator: 'Greater Than',
              value: '50',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });
        const savedRule = await manager.save(combinedRule);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              enrolleeStatus: 'INACTIVE', // This doesn't match
            },
          ],
          [savedRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(0);
      });
    });
  });

  describe('planCategory evaluation', () => {
    let planCategoryRule: BusinessRuleModel;
    let hospital: HospitalModel;
    let profile: ProfileModel;
    let hmoProfile: HmoProfileModel;
    let hmoProvider: HmoProviderModel;

    beforeEach(async () => {
      ({ hospital, profile, hmoProfile, hmoProvider } =
        await createHospitalAndProfile());
    });

    describe('with "Is" operator', () => {
      beforeEach(async () => {
        planCategoryRule = new BusinessRuleModel({
          flag: 'PLAN_BLUE',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'planCategory',
              operator: 'Is',
              value: 'PREMIUM',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });
        planCategoryRule = await manager.save(planCategoryRule);
      });

      it('should match when plan category array contains rule value', async () => {
        // Set planCategory on hmoProfile
        hmoProfile.planCategory = 'PREMIUM';
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              planCategory: hmoProfile.planCategory,
            },
          ],
          [planCategoryRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(1);
        expect(result[0].flag).toBe('PLAN_BLUE');
        expect(result[0].ruleId).toBe(planCategoryRule.id);
      });

      it('should match when plan category array has only the matching value', async () => {
        // Set planCategory on hmoProfile
        hmoProfile.planCategory = 'PREMIUM';
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              planCategory: hmoProfile.planCategory,
            },
          ],
          [planCategoryRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(1);
        expect(result[0].flag).toBe('PLAN_BLUE');
      });

      it('should not match when plan category array does not contain rule value', async () => {
        // Set planCategory on hmoProfile to a non-matching value
        hmoProfile.planCategory = 'BASIC';
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
            },
          ],
          [planCategoryRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(0);
      });

      it('should not match when plan category is empty', async () => {
        // Set planCategory on hmoProfile to empty string
        hmoProfile.planCategory = '';
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
            },
          ],
          [planCategoryRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(0);
      });

      it('should not match when plan category is undefined', async () => {
        // Leave planCategory on hmoProfile as undefined (default state)
        hmoProfile.planCategory = undefined;
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
            },
          ],
          [planCategoryRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(0);
      });
    });

    describe('with "Is Not" operator', () => {
      beforeEach(async () => {
        planCategoryRule = new BusinessRuleModel({
          flag: 'PLAN_RED',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'planCategory',
              operator: 'Is Not',
              value: 'RESTRICTED',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });
        planCategoryRule = await manager.save(planCategoryRule);
      });

      it('should match when plan category array does not contain rule value', async () => {
        // Set planCategory on hmoProfile to a value different from RESTRICTED
        hmoProfile.planCategory = 'BASIC';
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              planCategory: hmoProfile.planCategory,
            },
          ],
          [planCategoryRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(1);
        expect(result[0].flag).toBe('PLAN_RED');
        expect(result[0].ruleId).toBe(planCategoryRule.id);
      });

      it('should not match when plan category array contains rule value', async () => {
        // Set planCategory on hmoProfile to RESTRICTED (which matches the rule)
        hmoProfile.planCategory = 'RESTRICTED';
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              planCategory: hmoProfile.planCategory,
            },
          ],
          [planCategoryRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(0);
      });

      it('should match when plan category is empty', async () => {
        // Set planCategory on hmoProfile to empty string
        hmoProfile.planCategory = '';
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
            },
          ],
          [planCategoryRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(1);
        expect(result[0].flag).toBe('PLAN_RED');
      });

      it('should match when plan category is undefined', async () => {
        // Leave planCategory on hmoProfile as undefined (default state)
        hmoProfile.planCategory = undefined;
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
            },
          ],
          [planCategoryRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(1);
        expect(result[0].flag).toBe('PLAN_RED');
      });
    });

    describe('with different plan category values', () => {
      const planCategoryTestCases = [
        { category: 'BASIC', flag: 'PLAN_BASIC' },
        { category: 'STANDARD', flag: 'PLAN_STANDARD' },
        { category: 'PREMIUM', flag: 'PLAN_PREMIUM' },
        { category: 'GOLD', flag: 'PLAN_GOLD' },
        { category: 'PLATINUM', flag: 'PLAN_PLATINUM' },
      ];

      it('should handle multiple plan category rules correctly', async () => {
        // Create rules for each plan category
        const rules = await Promise.all(
          planCategoryTestCases.map(async (testCase) => {
            const rule = new BusinessRuleModel({
              flag: testCase.flag,
              hospitalId: hospital.id,
              items: [
                new BusinessRuleItemModel({
                  category: 'planCategory',
                  operator: 'Is',
                  value: testCase.category,
                  type: BusinessRuleItemType.ACTION,
                }),
              ],
              creatorId: profile.id,
              creatorName: profile.fullName,
            });
            return manager.save(rule);
          }),
        );

        // Test each plan category
        for (const testCase of planCategoryTestCases) {
          // Set planCategory on hmoProfile
          hmoProfile.planCategory = testCase.category;
          await manager.save(hmoProfile);

          const result = await repository.evaluateBusinessRuleActions(
            [
              {
                utilizationType: 'DRUG',
                utilizationCategory: 'PHARMACY',
                quantity: 1,
                utilizationId: 'util-1',
                amount: 100,
                planCategory: hmoProfile.planCategory,
              },
            ],
            rules.map((r) => r.id),
            {
              hmoProviderId: hmoProvider.id,
              enrolleeId: hmoProfile.memberNumber,
              diagnosis: ['DIABETES'],
              visitationType: 'OPD',
              planId: 'plan-1',
            },
          );

          expect(result).toHaveLength(1);
          expect(result[0].flag).toBe(testCase.flag);
        }
      });

      it('should match multiple rules when plan category array contains multiple values', async () => {
        // Create rules for BASIC and PREMIUM
        const basicRule = new BusinessRuleModel({
          flag: 'BASIC_PLAN',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'planCategory',
              operator: 'Is',
              value: 'BASIC',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });

        const premiumRule = new BusinessRuleModel({
          flag: 'PREMIUM_PLAN',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'planCategory',
              operator: 'Is',
              value: 'PREMIUM',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });

        const savedRules = await manager.save([basicRule, premiumRule]);

        // Set planCategory on hmoProfile to BASIC (which should match the BASIC rule)
        hmoProfile.planCategory = 'BASIC';
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              planCategory: hmoProfile.planCategory,
            },
          ],
          savedRules.map((r) => r.id),
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(1);
        const flags = result.map((r) => r.flag);
        expect(flags).toContain('BASIC_PLAN');
        expect(flags).not.toContain('PREMIUM_PLAN');
      });
    });

    describe('combined with other action categories', () => {
      it('should work with multiple action categories including planCategory', async () => {
        const combinedRule = new BusinessRuleModel({
          flag: 'COMBINED_PLAN',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'planCategory',
              operator: 'Is',
              value: 'PREMIUM',
              type: BusinessRuleItemType.ACTION,
            }),
            new BusinessRuleItemModel({
              category: 'utilizationType',
              operator: 'Is',
              value: 'DRUG',
              type: BusinessRuleItemType.ACTION,
            }),
            new BusinessRuleItemModel({
              category: 'amount',
              operator: 'Greater Than',
              value: '50',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });
        const savedRule = await manager.save(combinedRule);

        // Set planCategory on hmoProfile to PREMIUM (which matches the rule)
        hmoProfile.planCategory = 'PREMIUM';
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              planCategory: hmoProfile.planCategory,
            },
          ],
          [savedRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(1);
        expect(result[0].flag).toBe('COMBINED_PLAN');
        expect(result[0].ruleId).toBe(savedRule.id);
      });

      it('should not match when plan category condition fails in combined rule', async () => {
        const combinedRule = new BusinessRuleModel({
          flag: 'COMBINED_PLAN',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'planCategory',
              operator: 'Is',
              value: 'PREMIUM',
              type: BusinessRuleItemType.ACTION,
            }),
            new BusinessRuleItemModel({
              category: 'utilizationType',
              operator: 'Is',
              value: 'DRUG',
              type: BusinessRuleItemType.ACTION,
            }),
            new BusinessRuleItemModel({
              category: 'amount',
              operator: 'Greater Than',
              value: '50',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });
        const savedRule = await manager.save(combinedRule);

        // Set planCategory on hmoProfile to STANDARD (which doesn't match PREMIUM requirement)
        hmoProfile.planCategory = 'STANDARD';
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              planCategory: hmoProfile.planCategory,
            },
          ],
          [savedRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(0);
      });
    });

    describe('case sensitivity and exact matching', () => {
      beforeEach(async () => {
        planCategoryRule = new BusinessRuleModel({
          flag: 'CASE_SENSITIVE',
          hospitalId: hospital.id,
          items: [
            new BusinessRuleItemModel({
              category: 'planCategory',
              operator: 'Is',
              value: 'Premium',
              type: BusinessRuleItemType.ACTION,
            }),
          ],
          creatorId: profile.id,
          creatorName: profile.fullName,
        });
        planCategoryRule = await manager.save(planCategoryRule);
      });

      it('should be case sensitive for exact matching', async () => {
        // Set planCategory on hmoProfile to different case
        hmoProfile.planCategory = 'PREMIUM'; // Different case from rule value 'Premium'
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              planCategory: hmoProfile.planCategory,
            },
          ],
          [planCategoryRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(0);
      });

      it('should match with exact case', async () => {
        // Set planCategory on hmoProfile to exact case match
        hmoProfile.planCategory = 'Premium'; // Exact case match with rule value
        await manager.save(hmoProfile);

        const result = await repository.evaluateBusinessRuleActions(
          [
            {
              utilizationType: 'DRUG',
              utilizationCategory: 'PHARMACY',
              quantity: 1,
              utilizationId: 'util-1',
              amount: 100,
              planCategory: hmoProfile.planCategory,
            },
          ],
          [planCategoryRule.id],
          {
            hmoProviderId: hmoProvider.id,
            enrolleeId: hmoProfile.memberNumber,
            diagnosis: ['DIABETES'],
            visitationType: 'OPD',
            planId: 'plan-1',
          },
        );

        expect(result).toHaveLength(1);
        expect(result[0].flag).toBe('CASE_SENSITIVE');
      });
    });
  });

  describe('evaluateFrequency', () => {
    let hmoProvider: HmoProviderModel;
    let hmoProfile: HmoProfileModel;
    let hospital: HospitalModel;
    let profile: ProfileModel;

    beforeEach(async () => {
      ({ hospital, profile, hmoProvider, hmoProfile } =
        await createHospitalAndProfile());
    });

    const createUtilisations = async (options: {
      count: number;
      date: Date;
      category: string;
      type: string;
      diagnosis?: string;
    }) => {
      const { count, date, category, type, diagnosis } = options;
      const hmoClaimData: any = {
        providerId: hmoProvider.id,
        enrolleeNumber: hmoProfile.memberNumber,
      };

      if (diagnosis) {
        hmoClaimData.diagnosis = [{ diagnosisICD10: diagnosis }];
      }
      const [hmoClaim] = await createHmoClaim(
        manager,
        1,
        profile,
        undefined,
        undefined,
        hospital,
        hmoClaimData,
        date,
        'submitted',
        undefined,
        hmoProvider,
      );
      for (let i = 0; i < count; i++) {
        await manager.query(
          `INSERT INTO pre_auth_utilisations (id, hmo_claim, category, type, quantity, price, "utilizationCode", created_date, status)
             VALUES (gen_random_uuid(), $1, $2, $3, 1, 100.00, 'TEST${i}', $4, 'approved')`,
          [hmoClaim.id, category, type, date],
        );
      }
    };

    describe('with utilizationCategory target', () => {
      beforeEach(async () => {
        // Create 6 utilisations with PHARMACY category within the last 10 days
        await createUtilisations({
          count: 6,
          date: moment().subtract(5, 'days').toDate(),
          category: 'PHARMACY',
          type: 'DRUG',
        });
        // Create 2 utilisations older than 30 days
        await createUtilisations({
          count: 2,
          date: moment().subtract(35, 'days').toDate(),
          category: 'PHARMACY',
          type: 'DRUG',
        });
      });

      it('should return true when count is greater than rule value within frequency period', async () => {
        const rule = new BusinessRuleItemModel({
          category: 'frequency',
          operator: 'Greater Than',
          value: '5',
          type: BusinessRuleItemType.CONDITION,
          extra: {
            frequencyUnit: 'days',
            frequencyTarget: 'utilizationCategory',
            frequencyTargetValue: 'PHARMACY',
          },
        });

        const result = await repository.evaluateFrequency(
          hmoProfile.memberNumber,
          hmoProvider.id,
          rule,
          {
            utilizationType: 'DRUG',
            utilizationCategory: 'PHARMACY',
            quantity: 1,
            utilizationId: 'util-1',
            amount: 100,
            diagnosis: ['DIABETES'],
          },
        );

        expect(result).toBe(true);
      });

      it('should return false for utilisations outside frequency period', async () => {
        const rule = new BusinessRuleItemModel({
          category: 'frequency',
          operator: 'Greater Than',
          value: '3',
          type: BusinessRuleItemType.CONDITION,
          extra: {
            frequencyUnit: 'days',
            frequencyTarget: 'utilizationCategory',
            frequencyTargetValue: 'PHARMACY',
            frequencyTargetOperator: 'Is',
            frequencyTargetQuantity: '1',
          },
        });

        const result = await repository.evaluateFrequency(
          hmoProfile.memberNumber,
          hmoProvider.id,
          rule,
          {
            utilizationType: 'DRUG',
            utilizationCategory: 'PHARMACY',
            quantity: 1,
            utilizationId: 'util-1',
            amount: 100,
            diagnosis: ['DIABETES'],
          },
        );

        expect(result).toBe(false);
      });
    });

    describe('with utilizationType target', () => {
      beforeEach(async () => {
        await createUtilisations({
          count: 4,
          date: moment().subtract(2, 'weeks').toDate(),
          category: 'PHARMACY',
          type: 'SPECIAL_DRUG',
        });
      });

      it('should return true when count matches rule with "Less Than" operator', async () => {
        const rule = new BusinessRuleItemModel({
          category: 'frequency',
          operator: 'Less Than',
          value: '5',
          type: BusinessRuleItemType.CONDITION,
          extra: {
            frequencyUnit: 'months',
            frequencyTarget: 'utilizationType',
            frequencyTargetValue: 'SPECIAL_DRUG',
          },
        });

        const result = await repository.evaluateFrequency(
          hmoProfile.memberNumber,
          hmoProvider.id,
          rule,
          {
            utilizationType: 'SPECIAL_DRUG',
            utilizationCategory: 'PHARMACY',
            quantity: 1,
            utilizationId: 'util-1',
            amount: 100,
            diagnosis: ['DIABETES'],
          },
        );

        expect(result).toBe(true);
      });
    });

    describe('with diagnosis target', () => {
      beforeEach(async () => {
        await createUtilisations({
          count: 3,
          date: moment().subtract(6, 'months').toDate(),
          category: 'INVESTIGATION',
          type: 'SCAN',
          diagnosis: 'A01',
        });
      });

      it('should return false when diagnosis does not match', async () => {
        const rule = new BusinessRuleItemModel({
          category: 'frequency',
          operator: 'Is',
          value: '3',
          type: BusinessRuleItemType.CONDITION,
          extra: {
            frequencyUnit: 'years',
            frequencyTarget: 'diagnosis',
            frequencyTargetValue: 'B02', // Different diagnosis
            frequencyTargetOperator: 'Is',
            frequencyTargetQuantity: '1',
          },
        });

        const result = await repository.evaluateFrequency(
          hmoProfile.memberNumber,
          hmoProvider.id,
          rule,
          {
            utilizationType: 'SCAN',
            utilizationCategory: 'INVESTIGATION',
            quantity: 1,
            utilizationId: 'util-1',
            amount: 100,
            diagnosis: ['B02'],
          },
        );

        expect(result).toBe(false);
      });
    });

    describe('edge cases', () => {
      it('should return false when extra is not provided', async () => {
        const invalidRule = new BusinessRuleItemModel({
          category: 'frequency',
          operator: 'Greater Than',
          value: '5',
          type: BusinessRuleItemType.CONDITION,
          // No extra field
        });

        const result = await repository.evaluateFrequency(
          hmoProfile.memberNumber,
          hmoProvider.id,
          invalidRule,
          {
            utilizationType: 'DRUG',
            utilizationCategory: 'PHARMACY',
            quantity: 1,
            utilizationId: 'util-1',
            amount: 100,
            diagnosis: ['DIABETES'],
          },
        );

        expect(result).toBe(false);
      });

      it('should return false for invalid frequency target', async () => {
        const invalidRule = new BusinessRuleItemModel({
          category: 'frequency',
          operator: 'Greater Than',
          value: '5',
          type: BusinessRuleItemType.CONDITION,
          extra: {
            frequencyUnit: 'days',
            frequencyTarget: 'invalidTarget',
            frequencyTargetValue: 'PHARMACY',
          },
        });

        const result = await repository.evaluateFrequency(
          hmoProfile.memberNumber,
          hmoProvider.id,
          invalidRule,
          {
            utilizationType: 'DRUG',
            utilizationCategory: 'PHARMACY',
            quantity: 1,
            utilizationId: 'util-1',
            amount: 100,
            diagnosis: ['DIABETES'],
          },
        );

        expect(result).toBe(false);
      });
    });
  });

  describe('composeRuleItemMessage', () => {
    it('should compose correct message for frequency category with utilizationCategory target', () => {
      const item = new BusinessRuleItemModel({
        category: 'frequency',
        operator: 'Greater Than',
        value: '5',
        type: BusinessRuleItemType.CONDITION,
        extra: {
          frequencyTarget: 'utilizationCategory',
        },
      });

      const message = repository.composeRuleItemMessage(item);
      expect(message).toBe('Incorrect Utilization Category');
    });

    it('should compose correct message for frequency category with utilizationType target', () => {
      const item = new BusinessRuleItemModel({
        category: 'frequency',
        operator: 'Greater Than',
        value: '5',
        type: BusinessRuleItemType.CONDITION,
        extra: {
          frequencyTarget: 'utilizationType',
        },
      });

      const message = repository.composeRuleItemMessage(item);
      expect(message).toBe('Incorrect Utilization Type');
    });

    it('should compose correct message for frequency category with diagnosis target', () => {
      const item = new BusinessRuleItemModel({
        category: 'frequency',
        operator: 'Greater Than',
        value: '5',
        type: BusinessRuleItemType.CONDITION,
        extra: {
          frequencyTarget: 'diagnosis',
        },
      });

      const message = repository.composeRuleItemMessage(item);
      expect(message).toBe('Incorrect Diagnosis');
    });

    it('should compose default message for frequency category with unknown target', () => {
      const item = new BusinessRuleItemModel({
        category: 'frequency',
        operator: 'Greater Than',
        value: '5',
        type: BusinessRuleItemType.CONDITION,
        extra: {
          frequencyTarget: 'unknown',
        },
      });

      const message = repository.composeRuleItemMessage(item);
      expect(message).toBe('-');
    });

    it('should compose default message for frequency category without extra', () => {
      const item = new BusinessRuleItemModel({
        category: 'frequency',
        operator: 'Greater Than',
        value: '5',
        type: BusinessRuleItemType.CONDITION,
      });

      const message = repository.composeRuleItemMessage(item);
      expect(message).toBe('-');
    });
  });

  describe('evaluateBusinessRuleConditions with frequency', () => {
    let frequencyRule: BusinessRuleModel;
    let hospital: HospitalModel;
    let profile: ProfileModel;
    let hmoProvider: HmoProviderModel;
    let hmoProfile: HmoProfileModel;

    beforeEach(async () => {
      ({ hospital, profile, hmoProvider, hmoProfile } =
        await createHospitalAndProfile());
    });

    it('should not match when frequency condition is not met', async () => {
      // Create a rule with frequency condition
      frequencyRule = new BusinessRuleModel({
        flag: 'FREQUENCY_EXCEEDED',
        hospitalId: hospital.id,
        items: [
          new BusinessRuleItemModel({
            category: 'frequency',
            operator: 'Greater Than',
            value: '5',
            type: BusinessRuleItemType.CONDITION,
            extra: {
              frequencyUnit: 'days',
              frequencyTarget: 'utilizationCategory',
              frequencyTargetValue: 'PHARMACY',
            },
          }),
        ],
        creatorId: profile.id,
        creatorName: profile.fullName,
      });
      frequencyRule = await manager.save(frequencyRule);

      // Create test data that doesn't meet frequency condition
      const [hmoClaim] = await createHmoClaim(
        manager,
        1,
        profile,
        undefined,
        undefined,
        undefined,
        hmoProvider,
      );

      // Update the HMO claim to have the correct enrolleeNumber
      await manager.query(
        'UPDATE hmo_claims SET "enrolleeNumber" = $1, provider_id = $2 WHERE id = $3',
        [hmoProfile.memberNumber, hmoProvider.id, hmoClaim.id],
      );

      const recentDate = moment()
        .tz('Africa/Lagos')
        .format('YYYY-MM-DD HH:mm:ss');
      await manager.query(
        `INSERT INTO pre_auth_utilisations (id, hmo_claim, category, type, quantity, price, "utilizationCode", created_date, status)
           VALUES 
           (gen_random_uuid(), $1, 'PHARMACY', 'DRUG', 1, 100.00, 'TEST004', $2, 'approved'),
           (gen_random_uuid(), $1, 'PHARMACY', 'DRUG', 1, 100.00, 'TEST005', $2, 'approved')`,
        [hmoClaim.id, recentDate],
      );

      const result = await repository.evaluateBusinessRuleConditions(
        {
          hmoProviderId: hmoProvider.id,
          enrolleeId: hmoProfile.memberNumber,
          diagnosis: ['DIABETES'],
          visitationType: 'OPD',
          planId: 'plan-1',
        },
        [],
      );

      expect(result).not.toContain(frequencyRule.id);
    });
  });
});
