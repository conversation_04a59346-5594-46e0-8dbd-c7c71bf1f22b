/* eslint-disable max-lines */
/* eslint-disable @typescript-eslint/quotes */
import groupBy from 'lodash.groupby';
import moment from 'moment-timezone';
import { DataSource, In, Repository } from 'typeorm';
import {
  BusinessRuleActionInput,
  BusinessRuleConditionInput,
  BusinessRuleFilterInput,
  NewBusinessRuleInput,
  UpdateBusinessRuleInput,
} from '@clinify/cms/inputs/business-rule.input';
import {
  BusinessRuleItemModel,
  BusinessRuleModel,
} from '@clinify/cms/models/business-rule.model';
import { CustomRepository } from '@clinify/custom-repository/decorators/custom-repo.decorator';
import { queryDSWithSlave } from '@clinify/database';
import { HmoProfileModel } from '@clinify/hmo-profiles/models/hmo-profile.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import {
  FlagDto,
  FlaggedRuleItemDto,
} from '@clinify/pre-authorisations/inputs/flag.dto';
import { ProfileModel } from '@clinify/users/models/profile.model';

@CustomRepository(BusinessRuleModel)
export class BusinessRuleRepository extends Repository<BusinessRuleModel> {
  composeRuleItemMessage(item: BusinessRuleItemModel): string {
    const { category, extra } = item;
    const messages: string[] = [];

    switch (category) {
      case 'utilizationCategory':
        messages.push('Incorrect Utilization Category');
        break;
      case 'utilizationType':
        messages.push('Incorrect Utilization Type');
        break;
      case 'planCategory':
        messages.push('Incorrect Plan Category');
        break;
      case 'enrolleeStatus':
        messages.push('Incorrect Plan Status');
        break;
      case 'specialty':
        messages.push('Incorrect Specialty');
        break;
      case 'quantity':
        messages.push('Incorrect Utilization Quantity');
        break;
      case 'usage':
        messages.push('Incorrect Usage');
        break;
      case 'amount':
        messages.push('Incorrect Amount');
        break;
      case 'plan':
        messages.push('Incorrect Plan Type');
        break;
      case 'gender':
        messages.push('Incorrect Gender');
        break;
      case 'ageRange':
        messages.push('Incorrect Age');
        break;
      case 'diagnosis':
        messages.push('Incorrect Diagnosis');
        break;
      case 'visitationType':
        messages.push('Incorrect Visitation Type');
        break;
      case 'frequency': {
        switch (extra?.frequencyTarget) {
          case 'utilizationCategory':
            messages.push('Incorrect Utilization Category');
            break;
          case 'utilizationType':
            messages.push('Incorrect Utilization Type');
            break;
          case 'diagnosis':
            messages.push('Incorrect Diagnosis');
            break;
          default:
            messages.push('-');
        }
        break;
      }
      default:
        messages.push('Business Rule Violation');
        break;
    }
    return messages.join('\n');
  }
  async createBusinessRule(
    mutator: ProfileModel,
    rules: NewBusinessRuleInput[],
  ): Promise<BusinessRuleModel[]> {
    const newRules = rules.map((rule) => {
      const newRule = new BusinessRuleModel({
        items: rule.items.map((item) => new BusinessRuleItemModel(item)),
        flag: rule.flag,
        matchAll: rule.matchAll,
        sumAll: rule.sumAll,
        creatorId: mutator.id,
        creatorName: mutator.fullName,
        hospitalId: mutator.hospitalId,
      });
      return this.save(newRule);
    });
    return Promise.all(newRules);
  }

  async updateBusinessRule(
    mutator: ProfileModel,
    rule: UpdateBusinessRuleInput,
  ): Promise<BusinessRuleModel> {
    const existingRule = await this.findOne({
      where: { id: rule.id },
      relations: ['items'],
    });
    if (!existingRule) {
      throw new Error('Rule Not Found');
    }
    existingRule.items = rule.items.map((newItem) => {
      const existingItem = existingRule.items.find(
        (existingItem) => existingItem.id === newItem.id,
      );
      if (existingItem) {
        existingItem.category = newItem.category;
        existingItem.operator = newItem.operator;
        existingItem.value = newItem.value;
        existingItem.type = newItem.type;
        return existingItem;
      }
      return new BusinessRuleItemModel(newItem);
    });
    existingRule.flag = rule.flag;
    existingRule.matchAll = rule.matchAll;
    existingRule.sumAll = rule.sumAll;
    existingRule.lastModifierId = mutator.id;
    existingRule.lastModifierName = mutator.fullName;
    return this.save(existingRule);
  }

  async deleteBusinessRule(
    id: string,
    hospitalId: string,
  ): Promise<BusinessRuleModel> {
    const existingRule = await this.findOne({
      where: { id, hospitalId },
      relations: ['items'],
    });
    if (!existingRule) {
      throw new Error('Rule Not Found');
    }
    await this.delete(id);
    return existingRule;
  }

  async fetchBusinessRules(hospitalId: string): Promise<BusinessRuleModel[]> {
    const rules = await this.find({
      where: { hospitalId },
      relations: ['items'],
    });
    return rules;
  }
  evaluateCondition(
    operator: string,
    ruleValue: string,
    inputValue: string | string[],
  ): boolean {
    switch (operator) {
      case 'Is': {
        if (Array.isArray(inputValue)) {
          return inputValue.includes(ruleValue);
        }
        return ruleValue === inputValue;
      }
      case 'Is Not':
        if (Array.isArray(inputValue)) {
          return !inputValue.includes(ruleValue);
        }
        return ruleValue !== inputValue;
      case 'Less Than':
        return Number(inputValue) < Number(ruleValue);
      case 'Greater Than':
        return Number(inputValue) > Number(ruleValue);
      case 'Contains':
        if (Array.isArray(inputValue)) {
          return inputValue.some((v) =>
            v.toLowerCase().includes(ruleValue.toLowerCase()),
          );
        }
        return inputValue.toLowerCase().includes(ruleValue.toLowerCase());
      case 'Between':
        const [min, max] = ruleValue
          .split('-')
          .map((v) => Number(v.trim().replace(/[^0-9.]/g, '')));
        return Number(inputValue) >= min && Number(inputValue) <= max;
      default:
        return false;
    }
  }
  private async evaluateUsage(
    enrolleeId: string,
    hmoProviderId: string,
    diagnosisRuleItem: BusinessRuleItemModel,
    args: BusinessRuleItemModel,
  ): Promise<boolean> {
    const { operator, value } = diagnosisRuleItem;
    const currentYear = moment().tz('Africa/Lagos').year();
    const claimsCount = await this.manager.query(
      `SELECT SUM(count) as count FROM (
        SELECT COUNT(DISTINCT hmo_claims.id) as count
        FROM hmo_claims
        WHERE "enrolleeNumber" = $1
          AND provider_id = $2
          AND DATE_PART('year', submit_date_time) = $3
          AND diagnosis IS NOT NULL
          AND LOWER(status) NOT IN ('rejected', 'draft')
          AND EXISTS (
            SELECT 1
            FROM jsonb_array_elements(hmo_claims.diagnosis) AS d
            WHERE (d ->> 'diagnosisICD10')::text ILIKE $4
              OR (d ->> 'diagnosisICD11')::text ILIKE $4
              OR (d ->> 'diagnosisSNOMED')::text ILIKE $4
          )
        UNION ALL
        SELECT COUNT(DISTINCT pa.id) as count
        FROM pre_authorizations pa
        WHERE pa.enrollee_number = $1
          AND pa.provider_id = $2
          AND DATE_PART('year', pa.created_date) = $3
          AND pa.diagnosis IS NOT NULL
          AND pa.claim_status = 'Not Submitted'
          AND EXISTS (
            SELECT 1
            FROM jsonb_array_elements(pa.diagnosis) AS d
            WHERE (d ->> 'diagnosisICD10')::text ILIKE $4
              OR (d ->> 'diagnosisICD11')::text ILIKE $4
              OR (d ->> 'diagnosisSNOMED')::text ILIKE $4
          )
      ) as combined_counts`,
      [
        enrolleeId,
        hmoProviderId,
        currentYear,
        operator === 'Contains' ? `%${value}%` : value,
      ],
    );
    const isMatch = this.evaluateCondition(
      args.operator,
      args.value,
      `${Number(claimsCount?.[0]?.count ?? '0') + 1}`,
    );
    return isMatch;
  }
  async evaluateFrequency(
    enrolleeId: string,
    hmoProviderId: string,
    ruleItem: BusinessRuleItemModel,
    args: BusinessRuleActionInput & { diagnosis: string[] },
  ): Promise<boolean> {
    const { value = '0', extra } = ruleItem;
    if (!extra) return false;
    const {
      frequencyUnit,
      frequencyTarget,
      frequencyTargetValue,
      frequencyTargetQuantity = '0',
      frequencyTargetOperator = 'Is',
    } = extra;
    if (frequencyTarget === 'diagnosis') {
      const isMatch = args.diagnosis.some((d) =>
        this.evaluateCondition(
          frequencyTargetOperator,
          frequencyTargetValue,
          d,
        ),
      );
      if (!isMatch) return false;
    } else if (frequencyTarget === 'utilizationCategory') {
      const isMatch = this.evaluateCondition(
        frequencyTargetOperator,
        frequencyTargetValue,
        args.utilizationCategory,
      );
      if (!isMatch) return false;
    } else if (frequencyTarget === 'utilizationType') {
      const isMatch = this.evaluateCondition(
        frequencyTargetOperator,
        frequencyTargetValue,
        args.utilizationType,
      );
      if (!isMatch) return false;
    } else {
      return false;
    }

    const frequencyValue = frequencyUnit as
      | 'days'
      | 'weeks'
      | 'months'
      | 'years';
    const target = frequencyTarget;

    // Calculate the date range based on frequency value and unit
    const timeAgo = moment()
      .tz('Africa/Lagos')
      .subtract(Number(value), frequencyValue)
      .format('YYYY-MM-DD HH:mm:ss');
    let count = 0;

    // Helper function to build condition based on operator
    const buildCondition = (operator: string, column: string): string => {
      switch (operator) {
        case 'Is':
          return `${column} = $4`;
        case 'Is Not':
          return `${column} != $4`;
        case 'Contains':
          return `${column} ILIKE $4`;
        default:
          return `${column} = $4`;
      }
    };

    // Helper function to format value based on operator
    const formatValue = (operator: string, value: string): string => {
      switch (operator) {
        case 'Contains':
          return `%${value}%`;
        default:
          return value;
      }
    };

    const formattedValue = formatValue(
      frequencyTargetOperator,
      frequencyTargetValue,
    );

    switch (target) {
      case 'utilizationCategory': {
        const condition = buildCondition(
          frequencyTargetOperator,
          'util.category',
        );
        const result = await this.manager.query(
          `SELECT 
              COUNT(DISTINCT util.id) as count
            FROM
              pre_auth_utilisations util
              LEFT JOIN hmo_claims ON hmo_claims.id = util.hmo_claim
              LEFT JOIN pre_authorizations pa ON pa.id = util.pre_auth
            WHERE
              (
                (
                  hmo_claims."enrolleeNumber" = $1
                  AND hmo_claims.provider_id = $2
                  AND LOWER(hmo_claims.status) != 'draft'
                )
                OR (
                  pa.enrollee_number = $1
                  AND pa.provider_id = $2
                  AND pa.claim_status = 'Not Submitted'
                )
              )
              AND ${condition}
              AND util.created_date >= $3
              AND LOWER(util.status) != 'rejected'`,
          [enrolleeId, hmoProviderId, timeAgo, formattedValue],
        );

        count = Number(result[0]?.count || 0);
        break;
      }
      case 'utilizationType': {
        const condition = buildCondition(frequencyTargetOperator, 'util.type');
        const result = await this.manager.query(
          `SELECT 
              COUNT(DISTINCT util.id) as count
            FROM
              pre_auth_utilisations util
              LEFT JOIN hmo_claims ON hmo_claims.id = util.hmo_claim
              LEFT JOIN pre_authorizations pa ON pa.id = util.pre_auth
            WHERE
              (
                (
                  hmo_claims."enrolleeNumber" = $1
                  AND hmo_claims.provider_id = $2
                  AND LOWER(hmo_claims.status) != 'draft'
                )
                OR (
                  pa.enrollee_number = $1
                  AND pa.provider_id = $2
                  AND pa.claim_status = 'Not Submitted'
                )
              )
              AND ${condition}
              AND util.created_date >= $3
              AND LOWER(util.status) != 'rejected'`,
          [enrolleeId, hmoProviderId, timeAgo, formattedValue],
        );
        count = Number(result[0]?.count || 0);
        break;
      }
      case 'diagnosis': {
        const buildDiagnosisCondition = (operator: string): string => {
          switch (operator) {
            case 'Is':
              return `(d ->> 'diagnosisICD10')::text = $4
                     OR (d ->> 'diagnosisICD11')::text = $4
                     OR (d ->> 'diagnosisSNOMED')::text = $4`;
            case 'Is Not':
              return `(d ->> 'diagnosisICD10')::text != $4
                     AND (d ->> 'diagnosisICD11')::text != $4
                     AND (d ->> 'diagnosisSNOMED')::text != $4`;
            case 'Contains':
              return `(d ->> 'diagnosisICD10')::text ILIKE $4
                     OR (d ->> 'diagnosisICD11')::text ILIKE $4
                     OR (d ->> 'diagnosisSNOMED')::text ILIKE $4`;
            default:
              return `(d ->> 'diagnosisICD10')::text = $4
                     OR (d ->> 'diagnosisICD11')::text = $4
                     OR (d ->> 'diagnosisSNOMED')::text = $4`;
          }
        };

        const diagnosisCondition = buildDiagnosisCondition(
          frequencyTargetOperator,
        );
        const result = await this.manager.query(
          `SELECT SUM(count) as count FROM (
            (SELECT COUNT(DISTINCT hmo_claims.id) as count FROM hmo_claims
            WHERE hmo_claims."enrolleeNumber" = $1 
              AND hmo_claims.provider_id = $2 
              AND hmo_claims.created_date >= $3 
              AND LOWER(hmo_claims.status) NOT IN ('rejected', 'draft')
              AND EXISTS (
                SELECT 1
                FROM jsonb_array_elements(hmo_claims.diagnosis) AS d
                WHERE ${diagnosisCondition}
              )
              )
            UNION ALL
            (SELECT COUNT(DISTINCT pa.id) as count
            FROM pre_authorizations pa
            WHERE pa.enrollee_number = $1
              AND pa.provider_id = $2
              AND pa.created_date >= $3
              AND pa.claim_status = 'Not Submitted'
              AND EXISTS (
                SELECT 1
                FROM jsonb_array_elements(pa.diagnosis) AS d
                WHERE ${diagnosisCondition}
              )
              )
          ) as combined_counts`,
          [enrolleeId, hmoProviderId, timeAgo, formattedValue],
        );

        count = Number(result[0]?.count || 0);
        break;
      }
      default:
        return false;
    }

    // Evaluate the count against the rule's condition
    return count >= Number(frequencyTargetQuantity || 0);
  }
  async evaluateBusinessRuleConditions(
    args: BusinessRuleConditionInput,
    actionArgs: BusinessRuleActionInput[],
  ): Promise<string[]> {
    const { hmoProviderId, diagnosis, visitationType, planId, enrolleeId } =
      args;
    const hospital = await this.manager.findOne(HospitalModel, {
      where: { hmoId: hmoProviderId },
      select: ['id'],
    });
    if (!hospital) return [];
    const hmoProfile = await this.manager.findOne(HmoProfileModel, {
      where: { memberNumber: enrolleeId, providerId: hmoProviderId },
      relations: ['profile', 'profile.details'],
    });
    if (!hmoProfile) return [];
    const profile = hmoProfile.profile;
    const rules = await this.fetchBusinessRules(hospital.id);
    if (rules.length === 0) return [];
    const matchedRules: string[] = [];

    for (const rule of rules) {
      // groups rules by category as they will serve as OR condition
      const groupedRules = groupBy(
        rule.items.filter((item) => item.type === 'condition'),
        'category',
      );
      let allCategoriesMatch = true;
      for (const items of Object.values(groupedRules)) {
        const categoryMatch = [];
        // Check all items in the category (OR condition)
        for (const item of items) {
          let isMatch = false;
          switch (item.category) {
            case 'plan':
              isMatch = this.evaluateCondition(
                item.operator,
                item.value,
                planId,
              );
              break;
            case 'gender':
              isMatch = this.evaluateCondition(
                item.operator,
                item.value,
                profile.gender,
              );
              break;
            case 'ageRange':
              const babyAgeRange = this.tokenizeDateRange(item.value);
              if (
                babyAgeRange.a === 0 &&
                babyAgeRange.b === 30 &&
                babyAgeRange.calendrical === 'days'
              ) {
                const hasFamily = actionArgs.some((action) =>
                  action.planCategory?.includes('Family'),
                );
                if (hasFamily) {
                  break;
                }
              }
              isMatch = this.evaluateDateRange(
                item.value,
                profile.details.dateOfBirth,
                item.operator === 'Is' ? 'Is' : 'Is Not',
              );
              break;
            case 'diagnosis':
              isMatch = diagnosis.some((d) =>
                this.evaluateCondition(item.operator, item.value, d),
              );
              break;
            case 'visitationType':
              isMatch = this.evaluateCondition(
                item.operator,
                item.value,
                visitationType,
              );
              break;
          }
          categoryMatch.push(isMatch);
          if (isMatch) {
            break; // One match in category is enough (OR condition)
          }
        }

        if (!categoryMatch.every((match) => match)) {
          // If any category doesn't match, the entire rule doesn't apply
          allCategoriesMatch = false;
          break; // Exit the categories loop since we know the rule won't match
        }
      }

      // Only add the rule ID if all categories matched
      if (allCategoriesMatch) {
        matchedRules.push(rule.id);
      }
    }

    return matchedRules;
  }

  async evaluateBusinessRuleActions(
    args: BusinessRuleActionInput[],
    ids: string[],
    conditionArgs: BusinessRuleConditionInput,
  ): Promise<FlagDto[]> {
    const { enrolleeId, hmoProviderId, diagnosis } = conditionArgs;
    const rules = await this.find({
      where: { id: In(ids) },
      relations: ['items'],
    });

    let matchedRules: FlagDto[] = [];
    const matchedItems: {
      item: BusinessRuleItemModel;
      ruleId: string;
      utilizationId: string;
    }[] = [];
    const totalAmount = args.reduce(
      (acc, action) => acc + (action.amount || 0),
      0,
    );
    const totalQuantity = args.reduce(
      (acc, action) => acc + (action.quantity || 0),
      0,
    );
    for (const action of args) {
      for (const rule of rules) {
        const groupedRules = groupBy(
          rule.items.filter((item) => item.type === 'action'),
          'category',
        );
        const categoryMatch = [];
        // And condition, all must match
        for (const items of Object.values(groupedRules)) {
          // Or condition, one match is enough
          const orMatch: {
            item: typeof matchedItems[0];
            match: boolean;
          }[] = [];
          for (const item of items) {
            let isMatch = false;
            switch (item.category) {
              case 'utilizationCategory':
                isMatch = this.evaluateCondition(
                  item.operator,
                  item.value,
                  action.utilizationCategory,
                );
                break;
              case 'utilizationType':
                isMatch = this.evaluateCondition(
                  item.operator,
                  item.value,
                  action.utilizationType,
                );
                break;
              case 'planCategory':
                isMatch = this.evaluateCondition(
                  item.operator,
                  item.value,
                  action.planCategory,
                );
                break;
              case 'enrolleeStatus':
                isMatch = this.evaluateCondition(
                  item.operator,
                  item.value,
                  action.enrolleeStatus,
                );
                break;
              case 'specialty':
                isMatch = this.evaluateCondition(
                  item.operator,
                  item.value,
                  action.specialty,
                );
                break;
              case 'quantity':
                isMatch = this.evaluateCondition(
                  item.operator,
                  item.value,
                  rule.sumAll
                    ? totalQuantity.toString()
                    : action.quantity.toString(),
                );
                break;
              case 'usage':
                const diagnosisValue = rule.items.find(
                  (item) => item.category === 'diagnosis',
                );
                if (!diagnosisValue?.value) {
                  isMatch = false;
                  break;
                }
                if (
                  diagnosis?.length &&
                  !diagnosis.some((d) =>
                    this.evaluateCondition(
                      diagnosisValue.operator,
                      diagnosisValue.value,
                      d,
                    ),
                  )
                ) {
                  isMatch = false;
                  break;
                }

                isMatch = await this.evaluateUsage(
                  enrolleeId,
                  hmoProviderId,
                  diagnosisValue,
                  item,
                );
                break;
              case 'amount':
                isMatch = this.evaluateCondition(
                  item.operator,
                  item.value,
                  rule.sumAll
                    ? totalAmount.toString()
                    : action.amount.toString(),
                );
                break;
              case 'frequency':
                isMatch = await this.evaluateFrequency(
                  enrolleeId,
                  hmoProviderId,
                  item,
                  {
                    ...action,
                    diagnosis,
                  },
                );
              default:
                break;
            }
            orMatch.push({
              item: {
                item,
                ruleId: rule.id,
                utilizationId: action.utilizationId,
              },
              match: isMatch,
            });
          }
          const matchedOr = orMatch.find((m) => m.match);
          if (matchedOr) {
            categoryMatch.push(true);
            matchedItems.push({
              item: matchedOr.item.item,
              ruleId: rule.id,
              utilizationId: action.utilizationId,
            });
          } else {
            categoryMatch.push(false);
          }
        }
        if (categoryMatch.every((match) => match)) {
          const flag: FlagDto = {
            flag: rule.flag,
            ruleId: rule.id,
          };
          const items = matchedItems
            .filter(
              (m) =>
                m.ruleId === rule.id &&
                m.utilizationId === action.utilizationId,
            )
            .map((m) => {
              const item = new FlaggedRuleItemDto();
              item.category = m.item.category;
              item.id = m.item.id;
              item.utilizationId = m.utilizationId;
              item.ruleId = m.ruleId;
              item.frequencyCategory = m.item.extra?.frequencyTarget;
              item.message = this.composeRuleItemMessage(m.item);
              return item;
            });
          matchedRules.push({
            ...flag,
            utilizationId: action.utilizationId,
            flaggedItems: items,
          });
        }
      }
    }
    // When matchAll is true, all rules must match
    const matchAllRules = rules.filter((rule) => rule.matchAll);
    // If matchAll rules are present, check if the rule has more than one match
    if (matchAllRules.length && matchedRules.length) {
      for (const rule of matchAllRules) {
        const matches = matchedRules.filter((m) => m.ruleId === rule.id);

        const matchItems = [
          ...new Set(
            matchedItems
              .filter((m) => m.ruleId === rule.id)
              .map((m) => m.item.id),
          ),
        ];

        if (
          matches.length <= 1 ||
          matchItems.length <
            rule.items.filter((i) => i.type === 'action').length
        ) {
          matchedRules = matchedRules.filter((m) => m.ruleId !== rule.id);
        }
      }
    }
    return matchedRules;
  }

  async evaluateBusinessRule(
    conditionArgs: BusinessRuleConditionInput,
    actionArgs: BusinessRuleActionInput[],
  ): Promise<FlagDto[]> {
    const conditions = await this.evaluateBusinessRuleConditions(
      conditionArgs,
      actionArgs,
    );

    if (conditions.length === 0) return [];
    const actions = await this.evaluateBusinessRuleActions(
      actionArgs,
      conditions,
      conditionArgs,
    );
    return actions;
  }

  async getVisitationTypes(
    dataSource: DataSource,
    hmoProviderId: string,
    filter: BusinessRuleFilterInput,
  ): Promise<string[]> {
    if (!hmoProviderId) return [];
    const { skip = 0, take = 50, keyword } = filter;
    const hmoProviderRoles = await queryDSWithSlave(
      dataSource,
      `SELECT DISTINCT name
        FROM hmo_visit_types
        WHERE hmo_provider_id = $1
        ${keyword ? 'AND name ILIKE $4' : ''}
          ORDER BY name ASC
          OFFSET $2
          LIMIT $3
      `,
      [hmoProviderId, skip, take, ...(keyword ? [`%${keyword}%`] : [])],
    );
    return hmoProviderRoles.map((v) => v.name);
  }

  async getUtilizationCategories(
    dataSource: DataSource,
    hmoProviderId: string,
    filter: BusinessRuleFilterInput,
  ): Promise<string[]> {
    if (!hmoProviderId) return [];
    const { skip = 0, take = 50, keyword } = filter;
    const hmoUtilizationCategories = await queryDSWithSlave(
      dataSource,
      `SELECT DISTINCT utilisation_category
        FROM hmo_plan_benefits
        WHERE hmo_provider_id = $1
        ${keyword ? 'AND utilisation_category ILIKE $4' : ''}
          ORDER BY utilisation_category ASC
          OFFSET $2
          LIMIT $3
      `,
      [hmoProviderId, skip, take, ...(keyword ? [`%${keyword}%`] : [])],
    );
    return hmoUtilizationCategories.map((v) => v.utilisation_category);
  }

  async getUtilizationTypes(
    dataSource: DataSource,
    hmoProviderId: string,
    filter: BusinessRuleFilterInput,
  ): Promise<string[]> {
    if (!hmoProviderId) return [];
    const { skip = 0, take = 50, keyword } = filter;
    const hmoUtilizationTypes = await queryDSWithSlave(
      dataSource,
      `SELECT DISTINCT ut->>'name' as name
        FROM hmo_plan_benefits,
        LATERAL jsonb_array_elements(utilisation_types) AS ut
        WHERE hmo_provider_id = $1
        ${keyword ? "AND ut->>'name' ILIKE $4" : ''}
          ORDER BY ut->>'name' ASC
          OFFSET $2
          LIMIT $3
      `,
      [hmoProviderId, skip, take, ...(keyword ? [`%${keyword}%`] : [])],
    );

    return hmoUtilizationTypes.map((v) => v.name);
  }

  /**
   * @description
   * Tokenizes date range text into structured objects
   * @param dateRangeText - Text representation of date range (e.g., "0 - 30 Days", ">55 Years")
   * @param ruleOperator - Rule operator ("Is" or "Is Not") to determine matching logic
   * @returns Parsed range object or null if invalid
   */

  tokenizeDateRange(
    dateRangeText: string,
    ruleOperator: 'Is' | 'Is Not' = 'Is',
  ): {
    a: number;
    b?: number;
    operator: 'greater' | 'less' | 'between' | 'equal';
    calendrical: 'days' | 'weeks' | 'months' | 'years';
    ruleOperator: 'Is' | 'Is Not';
    shouldMatch: boolean;
  } | null {
    if (!dateRangeText || typeof dateRangeText !== 'string') {
      return null;
    }

    const text = dateRangeText.trim().toLowerCase();
    const shouldMatch = ruleOperator === 'Is';

    // Determine calendrical unit
    let calendrical: 'days' | 'weeks' | 'months' | 'years' = 'years';
    if (text.includes('day')) calendrical = 'days';
    else if (text.includes('week')) calendrical = 'weeks';
    else if (text.includes('month')) calendrical = 'months';
    else if (text.includes('year')) calendrical = 'years';

    // Handle greater than pattern (">55 Years", ">= 18")
    const greaterThanMatch = text.match(/[>≥]\s*=?\s*(\d+)/);
    if (greaterThanMatch) {
      return {
        a: parseInt(greaterThanMatch[1], 10),
        operator: 'greater',
        calendrical,
        ruleOperator,
        shouldMatch,
      };
    }

    // Handle less than pattern ("<18 Years", "<= 5")
    const lessThanMatch = text.match(/[<≤]\s*=?\s*(\d+)/);
    if (lessThanMatch) {
      return {
        a: parseInt(lessThanMatch[1], 10),
        operator: 'less',
        calendrical,
        ruleOperator,
        shouldMatch,
      };
    }

    // Handle range pattern ("0 - 30 Days", "1-5 Years", "20 - 29 Years")
    const rangeMatch = text.match(/(\d+)\s*[-–—]\s*(\d+)/);
    if (rangeMatch) {
      return {
        a: parseInt(rangeMatch[1], 10),
        b: parseInt(rangeMatch[2], 10),
        operator: 'between',
        calendrical,
        ruleOperator,
        shouldMatch,
      };
    }

    // Handle single number pattern ("17 Years", "18")
    const singleMatch = text.match(/(\d+)/);
    if (singleMatch) {
      return {
        a: parseInt(singleMatch[1], 10),
        operator: 'equal',
        calendrical,
        ruleOperator,
        shouldMatch,
      };
    }

    return null;
  }
  evaluateDateRange(
    dateRangeText: string,
    value: Date | string,
    ruleOperator: 'Is' | 'Is Not',
  ): boolean {
    const range = this.tokenizeDateRange(dateRangeText, ruleOperator);
    if (!range) return false;
    const dateValue = moment(value).tz('Africa/Lagos');
    const now = moment().tz('Africa/Lagos');
    let isInRange = false;
    const getAge = (date: moment.Moment, unit: string): number => {
      const today = moment().tz('Africa/Lagos');
      return today.diff(date, unit as any);
    };
    const rangeAgeA = getAge(
      now.clone().subtract(range.a, range.calendrical),
      range.calendrical,
    );
    const patientAge = getAge(dateValue, range.calendrical);
    switch (range.operator) {
      case 'greater':
        isInRange = patientAge > rangeAgeA;
        break;
      case 'less':
        isInRange = patientAge < rangeAgeA;
        break;
      case 'between':
        if (range.b === undefined) return false;
        const rangeAgeB = getAge(
          now.clone().subtract(range.b, range.calendrical),
          range.calendrical,
        );
        isInRange = patientAge <= rangeAgeB && patientAge >= rangeAgeA;
        break;
      case 'equal':
        isInRange = patientAge === rangeAgeA;
        break;
      default:
        return false;
    }
    // Apply the rule operator logic
    return range.shouldMatch ? isInRange : !isInRange;
  }
}
