import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { IsDate, IsEmpty, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ReProcessingStatus } from '@clinify/shared/enums/dead-letter-queue';

@ObjectType()
@Entity('dead_letter_queue')
export class DeadLetterQueueModel {
  @IsEmpty()
  @IsUUID('4')
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate: Date;

  @Field(() => String, { nullable: false })
  @Column({
    name: 'paystack_event',
    type: 'text',
    nullable: false,
  })
  paystackEvent: string;

  @Field(() => String, { nullable: false })
  @Column({
    type: 'text',
    nullable: false,
  })
  message: string;

  @Field(() => String, { nullable: true })
  @Column({
    type: 'text',
    nullable: true,
  })
  error: string;

  @Field(() => Int, { nullable: false })
  @Column({
    name: 'retries_count',
    type: 'integer',
    nullable: false,
    default: 0,
  })
  retriesCount: number;

  @Field(() => ReProcessingStatus, { nullable: false })
  @Column({
    name: 'status',
    type: 'enum',
    enum: ReProcessingStatus,
    nullable: false,
    default: ReProcessingStatus.NOT_YET_PROCESSED,
  })
  status: ReProcessingStatus;

  constructor(deadLetterQueue: Partial<DeadLetterQueueModel>) {
    Object.assign(this, deadLetterQueue);
  }
}
