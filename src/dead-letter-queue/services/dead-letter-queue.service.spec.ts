import { Test } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DeadLetterQueueService } from './dead-letter-queue.service';
import { DeadLetterQueueModel } from '../models/dead-letter-queue.model';
import { deadLetterQueueFactory } from '@clinify/__mocks__/factories/dead-letter-queue.factory';
import { PaymentWebhookService } from '@clinify/integrations/payment-webhook/services/payment-webhook.service';
import { ReProcessingStatus } from '@clinify/shared/enums/dead-letter-queue';

const deadLetterMessages = deadLetterQueueFactory.buildList(3);
const deadLetterMessage = deadLetterMessages[0];

const mockDeadLetterQueueRepository = {
  getMessage: jest.fn(() => deadLetterMessage),
  getAllMessages: jest.fn(() => deadLetterMessages),
  getUnsuccessfulMessages: jest.fn(() => deadLetterMessages),
  getSuccessfulMessages: jest.fn(() => deadLetterMessages),
  updateMessage: jest.fn(() => deadLetterMessage),
  saveFailedMessages: jest.fn(() => deadLetterMessages),
};

const mockPaymentWebhookService = {
  handlePaystackResponse: jest.fn(() => ({ success: true })),
};

describe('DeadLetterQueueService', () => {
  let service: DeadLetterQueueService;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [],
      providers: [
        DeadLetterQueueService,
        {
          provide: PaymentWebhookService,
          useValue: mockPaymentWebhookService,
        },
        {
          provide: getRepositoryToken(DeadLetterQueueModel),
          useValue: mockDeadLetterQueueRepository,
        },
      ],
    }).compile();
    service = module.get(DeadLetterQueueService);
  });

  it('reprocessMessage() should reporcess a failed message record', async () => {
    const actualValue = await service.reprocessMessage(deadLetterMessage.id);
    expect(actualValue).toEqual(deadLetterMessage);
    expect(mockDeadLetterQueueRepository.getMessage).toHaveBeenCalledTimes(1);
    expect(mockDeadLetterQueueRepository.updateMessage).toHaveBeenCalledTimes(
      1,
    );
    expect(
      mockPaymentWebhookService.handlePaystackResponse,
    ).toHaveBeenCalledTimes(1);
  });

  it('reprocessMessage() should save error_log and increament_count when reprocessing fails', async () => {
    mockPaymentWebhookService.handlePaystackResponse = jest
      .fn()
      .mockImplementationOnce(() => ({ success: false }));
    const actualValue = await service.reprocessMessage(deadLetterMessage.id);
    expect(actualValue).toEqual(deadLetterMessage);
    expect(mockDeadLetterQueueRepository.getMessage).toHaveBeenCalledTimes(1);
    expect(mockDeadLetterQueueRepository.updateMessage).toHaveBeenCalledTimes(
      1,
    );
    expect(
      mockPaymentWebhookService.handlePaystackResponse,
    ).toHaveBeenCalledTimes(1);
  });

  it('getMessage() should return specific message', async () => {
    const actualValue = await service.getMessage(deadLetterMessage.id);
    expect(actualValue.id).toEqual(deadLetterMessage.id);
    expect(mockDeadLetterQueueRepository.getMessage).toHaveBeenCalledTimes(1);
  });

  it('getAllMessages() should return all messages', async () => {
    const actualValue = await service.getAllMessages({});
    expect(actualValue).toStrictEqual(deadLetterMessages);
    expect(mockDeadLetterQueueRepository.getAllMessages).toHaveBeenCalledTimes(
      1,
    );
  });

  it('getAllMessages() should throw error if operation fails', async () => {
    mockDeadLetterQueueRepository.getAllMessages = jest
      .fn()
      .mockImplementationOnce(() => {
        throw new Error();
      });
    try {
      await service.getAllMessages({});
    } catch ({ message }) {
      expect(
        mockDeadLetterQueueRepository.getAllMessages,
      ).toHaveBeenCalledTimes(1);
      expect(message).toEqual('Error Fetching Messages');
    }
  });

  it('getUnsuccessfulMessages() should return all unsuccessful messages', async () => {
    const actualValue = await service.getUnsuccessfulMessages({});
    expect(actualValue).toStrictEqual(deadLetterMessages);
    expect(
      mockDeadLetterQueueRepository.getUnsuccessfulMessages,
    ).toHaveBeenCalledTimes(1);
  });

  it('getUnsuccessfulMessages() should throw error if operation fails', async () => {
    mockDeadLetterQueueRepository.getUnsuccessfulMessages = jest
      .fn()
      .mockImplementationOnce(() => {
        throw new Error();
      });
    try {
      await service.getUnsuccessfulMessages({});
    } catch ({ message }) {
      expect(
        mockDeadLetterQueueRepository.getUnsuccessfulMessages,
      ).toHaveBeenCalledTimes(1);
      expect(message).toEqual('Error Fetching Messages');
    }
  });

  it('getSuccessfulMessages() should return all successful messages', async () => {
    const actualValue = await service.getSuccessfulMessages({});
    expect(actualValue).toStrictEqual(deadLetterMessages);
    expect(
      mockDeadLetterQueueRepository.getSuccessfulMessages,
    ).toHaveBeenCalledTimes(1);
  });

  it('getSuccessfulMessages() should throw error if operation fails', async () => {
    mockDeadLetterQueueRepository.getSuccessfulMessages = jest
      .fn()
      .mockImplementationOnce(() => {
        throw new Error();
      });
    try {
      await service.getSuccessfulMessages({});
    } catch ({ message }) {
      expect(
        mockDeadLetterQueueRepository.getSuccessfulMessages,
      ).toHaveBeenCalledTimes(1);
      expect(message).toEqual('Error Fetching Messages');
    }
  });

  it('updateMessage() should update a message record', async () => {
    const actualValue = await service.updateMessage(deadLetterMessage.id, {
      retriesCount: 2,
    });
    expect(actualValue).toStrictEqual(deadLetterMessage);
    expect(mockDeadLetterQueueRepository.updateMessage).toHaveBeenCalledTimes(
      1,
    );
  });

  it('updateMessage() should throw error if update fails', async () => {
    mockDeadLetterQueueRepository.updateMessage = jest
      .fn()
      .mockImplementationOnce(() => {
        throw new Error();
      });
    try {
      await service.updateMessage(deadLetterMessage.id, {
        retriesCount: 2,
      });
    } catch ({ message }) {
      expect(mockDeadLetterQueueRepository.updateMessage).toHaveBeenCalledTimes(
        1,
      );
      expect(message).toEqual('Error Updating Message');
    }
  });

  it('reprocessMessage() should throw an error when operation fails', async () => {
    mockPaymentWebhookService.handlePaystackResponse = jest
      .fn()
      .mockImplementationOnce(() => {
        throw new Error();
      });
    try {
      await service.reprocessMessage(deadLetterMessage.id);
    } catch ({ message }) {
      expect(mockDeadLetterQueueRepository.getMessage).toHaveBeenCalledTimes(1);
      expect(message).toEqual('Error Processing Message');
    }
  });

  it('reprocessMessage() should erroor if message has already been processed', async () => {
    mockDeadLetterQueueRepository.getMessage = jest
      .fn()
      .mockImplementationOnce(() => ({
        ...deadLetterMessage,
        status: ReProcessingStatus.SUCCESS,
      }));
    try {
      await service.reprocessMessage(deadLetterMessage.id);
    } catch ({ message }) {
      expect(message).toEqual('Message Already Processed');
      expect(mockDeadLetterQueueRepository.getMessage).toHaveBeenCalledTimes(1);
    }
  });

  it('saveFailedMessages() should save messages', async () => {
    const message = deadLetterQueueFactory.build();
    const data = await service.saveFailedMessages([message]);
    expect(
      mockDeadLetterQueueRepository.saveFailedMessages,
    ).toHaveBeenCalledTimes(1);
    expect(data).toEqual(deadLetterMessages);
  });

  it('saveFailedMessages() should error if operation fails', async () => {
    mockDeadLetterQueueRepository.saveFailedMessages = jest
      .fn()
      .mockImplementationOnce(() => {
        throw new Error('');
      });
    try {
      await service.saveFailedMessages(deadLetterMessage.id);
    } catch ({ message }) {
      expect(message).toEqual('Error Saving Messages');
    }
  });
});
