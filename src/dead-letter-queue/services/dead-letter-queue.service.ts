import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeadLetterQueueFilter } from '../inputs/dead-letter-queue-filter.input';
import {
  ICreateMessageRecord,
  IUpdateMessageRecord,
} from '../interfaces/dead-letter-queue.interface';
import { DeadLetterQueueModel } from '../models/dead-letter-queue.model';
import { IDeadLetterQueueRepository } from '../repositories/dead-letter-queue.repository';
import { DeadLetterQueueResponse } from '../responses/dead-letter-queue.response';
import { PaymentWebhookService } from '@clinify/integrations/payment-webhook/services/payment-webhook.service';
import { ReProcessingStatus } from '@clinify/shared/enums/dead-letter-queue';

@Injectable()
export class DeadLetterQueueService {
  public constructor(
    @Inject(forwardRef(() => PaymentWebhookService))
    private readonly paymentWebhookService: PaymentWebhookService,
    @InjectRepository(DeadLetterQueueModel)
    private readonly deadLetterQueueRepository: IDeadLetterQueueRepository,
  ) {}

  async getMessage(messageId: string): Promise<DeadLetterQueueModel> {
    return await this.deadLetterQueueRepository.getMessage(messageId);
  }

  async getAllMessages(
    options?: DeadLetterQueueFilter,
  ): Promise<DeadLetterQueueResponse> {
    try {
      return await this.deadLetterQueueRepository.getAllMessages(options);
    } catch (error) {
      throw new InternalServerErrorException('Error Fetching Messages', error);
    }
  }

  async getUnsuccessfulMessages(
    options?: DeadLetterQueueFilter,
  ): Promise<DeadLetterQueueResponse> {
    try {
      return await this.deadLetterQueueRepository.getUnsuccessfulMessages(
        options,
      );
    } catch (error) {
      throw new InternalServerErrorException('Error Fetching Messages', error);
    }
  }

  async getSuccessfulMessages(
    options?: DeadLetterQueueFilter,
  ): Promise<DeadLetterQueueResponse> {
    try {
      return await this.deadLetterQueueRepository.getSuccessfulMessages(
        options,
      );
    } catch (error) {
      throw new InternalServerErrorException('Error Fetching Messages', error);
    }
  }

  async saveFailedMessages(
    message: ICreateMessageRecord[],
  ): Promise<DeadLetterQueueModel[]> {
    try {
      return await this.deadLetterQueueRepository.saveFailedMessages(message);
    } catch (error) {
      throw new InternalServerErrorException('Error Saving Messages', error);
    }
  }

  async updateMessage(
    messageId: string,
    update: IUpdateMessageRecord,
  ): Promise<DeadLetterQueueModel> {
    try {
      return await this.deadLetterQueueRepository.updateMessage(
        messageId,
        update,
      );
    } catch (error) {
      throw new InternalServerErrorException('Error Updating Message', error);
    }
  }

  async reprocessMessage(messageId: string): Promise<DeadLetterQueueModel> {
    const { message, retriesCount, status } =
      await this.deadLetterQueueRepository.getMessage(messageId);
    if (status === ReProcessingStatus.SUCCESS)
      throw new BadRequestException('Message Already Processed');
    try {
      const response = await this.paymentWebhookService.handlePaystackResponse({
        body: message,
      });
      if (response.success)
        return await this.updateMessage(messageId, {
          retriesCount: retriesCount + 1,
          status: ReProcessingStatus.SUCCESS,
        });
      return await this.updateMessage(messageId, {
        retriesCount: retriesCount + 1,
        status: ReProcessingStatus.FAILED,
        error: response.error,
      });
    } catch (error) {
      throw new InternalServerErrorException('Error Processing Message', error);
    }
  }
}
