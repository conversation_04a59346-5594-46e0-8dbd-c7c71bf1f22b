import { Field, ObjectType, Int } from '@nestjs/graphql';
import { DeadLetterQueueModel } from '../models/dead-letter-queue.model';

@ObjectType()
export class DeadLetterQueueResponse {
  constructor(deadLetterQueue: DeadLetterQueueModel[], totalCount: number) {
    this.list = deadLetterQueue;
    this.totalCount = totalCount;
  }

  @Field(() => Int)
  totalCount: number;

  @Field(() => [DeadLetterQueueModel])
  list: DeadLetterQueueModel[];
}
