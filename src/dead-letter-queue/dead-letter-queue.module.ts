import { HttpModule } from '@nestjs/axios';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DeadLetterQueueModel } from './models/dead-letter-queue.model';
import { CustomDeadLetterQueueRepoMethods } from './repositories/dead-letter-queue.repository';
import { DeadLetterQueueResolver } from './resolvers/dead-letter-queue.resolver';
import { DeadLetterQueueService } from './services/dead-letter-queue.service';
import { extendModel } from '@clinify/database/extendModel';
import { IntegrationsModule } from '@clinify/integrations/module';
import { SharedModule } from '@clinify/shared/module';

@Module({
  providers: [
    extendModel(DeadLetterQueueModel, CustomDeadLetterQueueRepoMethods),
    DeadLetterQueueService,
    DeadLetterQueueResolver,
  ],
  imports: [
    HttpModule,
    forwardRef(() => SharedModule),
    TypeOrmModule.forFeature([DeadLetterQueueModel]),
    forwardRef(() => IntegrationsModule),
  ],
  exports: [DeadLetterQueueService],
})
export class DeadLetterQueueModule {}
