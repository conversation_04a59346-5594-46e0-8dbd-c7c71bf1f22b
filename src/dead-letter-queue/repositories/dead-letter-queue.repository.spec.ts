import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import {
  CustomDeadLetterQueueRepoMethods,
  IDeadLetterQueueRepository,
} from './dead-letter-queue.repository';
import { DeadLetterQueueModel } from '../models/dead-letter-queue.model';
import { deadLetterQueueFactory } from '@clinify/__mocks__/factories/dead-letter-queue.factory';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendDSRepo } from '@clinify/database/extendModel';
import { createDeadLetterMessages } from '@clinify/utils/tests/dead-letter-queue.fixtures';

describe('DeadLetterQueueRepository', () => {
  let DeadLetterMessages: DeadLetterQueueModel[];
  let DeadLetterMessage: DeadLetterQueueModel;
  let ds: DataSource;
  let manager: EntityManager;
  let repository: IDeadLetterQueueRepository;

  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [],
    }).compile();

    ds = module.get<DataSource>(DataSource);
    manager = ds.manager;
    repository = extendDSRepo<IDeadLetterQueueRepository>(
      ds,
      DeadLetterQueueModel,
      CustomDeadLetterQueueRepoMethods,
    );
    await manager.withRepository(repository).clear();
    DeadLetterMessages = await createDeadLetterMessages(manager, 3);
    DeadLetterMessage = DeadLetterMessages[0];
  });
  afterAll(async () => {
    await repository.clear();
    await ds.destroy();
    await module.close();
  });
  it('getMessage() should return a message record', async () => {
    const returnedMessage = await repository.getMessage(DeadLetterMessage.id);
    expect(returnedMessage).toEqual(DeadLetterMessage);
  });

  it('getMessage() should throw error if message Not Found', async () => {
    const ficticiousId = '7baf9d8e-4e36-11eb-a7db-275d90ef3106';
    try {
      await repository.getMessage(ficticiousId);
    } catch ({ message }) {
      expect(message).toEqual('Message Not Found');
    }
  });

  it('getAllMessages() should return all messages', async () => {
    const paginatedMessages = await repository.getAllMessages({
      skip: 0,
      take: 1,
    });
    const unpaginatedMessages = await repository.getAllMessages({});
    expect(unpaginatedMessages.list.length).toEqual(DeadLetterMessages.length);
    expect(paginatedMessages.list.length).toEqual(1);
  });

  it('getUnsuccessfulMessages() should return all unsuccessful messages', async () => {
    const paginatedMessages = await repository.getUnsuccessfulMessages({
      skip: 0,
      take: 1,
    });
    const unpaginatedMessages = await repository.getUnsuccessfulMessages({});
    expect(unpaginatedMessages.list.length).toEqual(2);
    expect(paginatedMessages.list.length).toEqual(1);
    expect(paginatedMessages.totalCount).toEqual(2);
  });

  it('getSuccessfulMessages() should return filtered successful messages', async () => {
    const { createdDate } = DeadLetterMessage;
    const returnedMessages = await repository.getSuccessfulMessages({
      skip: 0,
      take: 50,
      dateRange: {
        from: createdDate,
        to: createdDate,
      },
    });
    const unpaginatedMessages = await repository.getSuccessfulMessages({});
    expect(unpaginatedMessages.list.length).toEqual(1);
    expect(returnedMessages.list.length).toEqual(1);
  });

  it('getSuccessfulMessages() should return unfiltered successful messages', async () => {
    const returnedMessages = await repository.getSuccessfulMessages();
    const unpaginatedMessages = await repository.getSuccessfulMessages({});
    expect(unpaginatedMessages.list.length).toEqual(1);
    expect(returnedMessages.list.length).toEqual(1);
  });

  it('updateMessage() should update a message', async () => {
    await repository.updateMessage(DeadLetterMessage.id, {
      retriesCount: 2,
    });
    const returnedMessage = await repository.getMessage(DeadLetterMessage.id);
    expect(returnedMessage.retriesCount).toEqual(2);
  });

  it('updateMessage() should throw error if operation fails', async () => {
    try {
      await repository.updateMessage(
        DeadLetterMessages[1].id,
        DeadLetterMessage,
      );
    } catch ({ message }) {
      expect(message).toEqual('Message Already Processed');
    }
  });

  it('saveFailedMessages() should return all successful messages', async () => {
    const message = deadLetterQueueFactory.build();
    await repository.saveFailedMessages([message]);
    const returnedMessage = await repository.getMessage(message.id);
    expect(returnedMessage.id).toEqual(message.id);
  });
});
