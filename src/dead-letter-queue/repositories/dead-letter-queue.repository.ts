import { NotFoundException, BadRequestException } from '@nestjs/common';
import moment from 'moment';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { DeadLetterQueueFilter } from '../inputs/dead-letter-queue-filter.input';
import {
  ICreateMessageRecord,
  IUpdateMessageRecord,
} from '../interfaces/dead-letter-queue.interface';
import { DeadLetterQueueModel } from '../models/dead-letter-queue.model';
import { DeadLetterQueueResponse } from '../responses/dead-letter-queue.response';
import { ReProcessingStatus } from '@clinify/shared/enums/dead-letter-queue';
import { takePaginatedResponses } from '@clinify/utils/pagination';

const withinRange = (
  query: SelectQueryBuilder<DeadLetterQueueModel>,
  { dateRange, skip = 0, take = 50 }: Partial<DeadLetterQueueFilter> = {},
) => {
  if (dateRange?.from) {
    const from = moment(dateRange.from).startOf('day').toDate();
    query = query.andWhere('(dead_letter_queue.createdDate >= :from)', {
      from,
    });
  }

  if (dateRange?.to) {
    const to = moment(dateRange.to).endOf('day').toDate();
    query = query.andWhere('(dead_letter_queue.createdDate < :to)', { to });
  }
  query.orderBy('dead_letter_queue.createdDate', 'DESC').skip(skip).take(take);
};

export interface IDeadLetterQueueRepository
  extends Repository<DeadLetterQueueModel> {
  this: Repository<DeadLetterQueueModel>;
  getMessage(messageId: string): Promise<DeadLetterQueueModel>;
  getAllMessages(
    options?: DeadLetterQueueFilter,
  ): Promise<DeadLetterQueueResponse>;
  getUnsuccessfulMessages(
    options?: DeadLetterQueueFilter,
  ): Promise<DeadLetterQueueResponse>;
  getSuccessfulMessages(
    options?: DeadLetterQueueFilter,
  ): Promise<DeadLetterQueueResponse>;
  saveFailedMessages(
    messages: ICreateMessageRecord[],
  ): Promise<DeadLetterQueueModel[]>;
  updateMessage(
    messageId: string,
    update: IUpdateMessageRecord,
  ): Promise<DeadLetterQueueModel>;
}

export const CustomDeadLetterQueueRepoMethods: Pick<
  IDeadLetterQueueRepository,
  | 'getMessage'
  | 'getAllMessages'
  | 'getUnsuccessfulMessages'
  | 'getSuccessfulMessages'
  | 'saveFailedMessages'
  | 'updateMessage'
> = {
  async getMessage(
    this: IDeadLetterQueueRepository,
    messageId: string,
  ): Promise<DeadLetterQueueModel> {
    return this.findOneOrFail({
      where: { id: messageId },
    }).catch(() => {
      throw new NotFoundException('Message Not Found');
    });
  },

  async getAllMessages(
    this: IDeadLetterQueueRepository,
    options?: DeadLetterQueueFilter,
  ): Promise<DeadLetterQueueResponse> {
    const query = this.createQueryBuilder('dead_letter_queue');

    withinRange(query, options);
    const [response, count] = await query.getManyAndCount();
    return new DeadLetterQueueResponse(response, count);
  },

  async getUnsuccessfulMessages(
    this: IDeadLetterQueueRepository,
    options?: DeadLetterQueueFilter,
  ): Promise<DeadLetterQueueResponse> {
    const success = ReProcessingStatus.SUCCESS;
    const query = this.createQueryBuilder('dead_letter_queue').where(
      'dead_letter_queue.status != :success',
      { success },
    );

    withinRange(query, options);
    const response = await query.getManyAndCount();
    return new DeadLetterQueueResponse(...takePaginatedResponses(response, 50));
  },

  async getSuccessfulMessages(
    this: IDeadLetterQueueRepository,
    options?: DeadLetterQueueFilter,
  ): Promise<DeadLetterQueueResponse> {
    const success = ReProcessingStatus.SUCCESS;
    const query = this.createQueryBuilder('dead_letter_queue').where(
      'dead_letter_queue.status = :success',
      { success },
    );

    withinRange(query, options);
    const response = await query.getManyAndCount();
    return new DeadLetterQueueResponse(...takePaginatedResponses(response, 50));
  },

  async saveFailedMessages(
    this: IDeadLetterQueueRepository,
    messages: ICreateMessageRecord[],
  ): Promise<DeadLetterQueueModel[]> {
    const messageModels = messages.map(
      (item) => new DeadLetterQueueModel(item),
    );
    return this.save(messageModels);
  },

  async updateMessage(
    this: IDeadLetterQueueRepository,
    messageId: string,
    update: IUpdateMessageRecord,
  ): Promise<DeadLetterQueueModel> {
    const message = await this.getMessage(messageId);
    if (message.status === ReProcessingStatus.SUCCESS)
      throw new BadRequestException('Message Already Processed');
    return this.save({
      ...message,
      ...update,
    });
  },
};
