import { TestingModule, Test } from '@nestjs/testing';
import { DeadLetterQueueResolver } from './dead-letter-queue.resolver';
import { DeadLetterQueueService } from '../services/dead-letter-queue.service';
import { deadLetterQueueFactory } from '@clinify/__mocks__/factories/dead-letter-queue.factory';

const deadLetterMessages = deadLetterQueueFactory.buildList(2);
const deadLetterMessage = deadLetterMessages[0];

const mockDeadLetterQueueService = {
  getMessage: jest.fn(() => deadLetterMessage),
  getAllMessages: jest.fn(() => deadLetterMessages),
  getUnsuccessfulMessages: jest.fn(() => deadLetterMessages),
  reprocessMessage: jest.fn(() => deadLetterMessage),
};

describe('DeadLetterQueueResolver', () => {
  let resolver: DeadLetterQueueResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeadLetterQueueResolver,
        DeadLetterQueueService,
        {
          provide: DeadLetterQueueService,
          useValue: mockDeadLetterQueueService,
        },
      ],
    }).compile();

    resolver = module.get<DeadLetterQueueResolver>(DeadLetterQueueResolver);
    jest.clearAllMocks();
  });

  it('getMessage() should retrieve a DLQ message', async () => {
    const response = await resolver.getMessage(deadLetterMessage.id);
    expect(response).toEqual(deadLetterMessage);
    expect(mockDeadLetterQueueService.getMessage).toHaveBeenCalledTimes(1);
  });

  it('getAllMessages() should retrieve a DLQ message', async () => {
    const response = await resolver.getAllMessages();
    expect(response).toEqual(deadLetterMessages);
    expect(mockDeadLetterQueueService.getAllMessages).toHaveBeenCalledTimes(1);
  });

  it('getUnsuccessfulMessages() should retrieve a DLQ message', async () => {
    const response = await resolver.getUnsuccessfulMessages();
    expect(response).toEqual(deadLetterMessages);
    expect(
      mockDeadLetterQueueService.getUnsuccessfulMessages,
    ).toHaveBeenCalledTimes(1);
  });

  it('reprocessMessage() should retrieve a DLQ message', async () => {
    const response = await resolver.reprocessMessage(deadLetterMessage.id);
    expect(response).toEqual(deadLetterMessage);
    expect(mockDeadLetterQueueService.reprocessMessage).toHaveBeenCalledTimes(
      1,
    );
  });
});
