import { UseGuards } from '@nestjs/common';
import { Query, Resolver, Args, Mutation } from '@nestjs/graphql';

import { DeadLetterQueueFilter } from '../inputs/dead-letter-queue-filter.input';
import { DeadLetterQueueModel } from '../models/dead-letter-queue.model';
import { DeadLetterQueueResponse } from '../responses/dead-letter-queue.response';
import { DeadLetterQueueService } from '../services/dead-letter-queue.service';

import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { RolesAuthGuard } from '@clinify/authentication/guards/roles.guard';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { AppServices } from '@clinify/shared/enums/services';
import { UserType } from '@clinify/shared/enums/users';

@LogService(AppServices.Queue)
@UseGuards(GqlAuthGuard)
@Resolver(() => DeadLetterQueueModel)
export class DeadLetterQueueResolver {
  constructor(
    private readonly deadLetterQueueService: DeadLetterQueueService,
  ) {}

  @Query(() => DeadLetterQueueModel)
  @UseGuards(RolesAuthGuard(UserType.Admin))
  async getMessage(
    @Args('id') messageId: string,
  ): Promise<DeadLetterQueueModel> {
    return this.deadLetterQueueService.getMessage(messageId);
  }

  @Query(() => DeadLetterQueueResponse)
  @UseGuards(RolesAuthGuard(UserType.Admin))
  async getAllMessages(
    @Args({
      name: 'filter',
      type: () => DeadLetterQueueFilter,
      nullable: true,
    })
    options?: DeadLetterQueueFilter,
  ): Promise<DeadLetterQueueResponse> {
    return this.deadLetterQueueService.getAllMessages(options);
  }

  @Query(() => DeadLetterQueueResponse)
  @UseGuards(RolesAuthGuard(UserType.Admin))
  async getUnsuccessfulMessages(
    @Args({
      name: 'filter',
      type: () => DeadLetterQueueFilter,
      nullable: true,
    })
    options?: DeadLetterQueueFilter,
  ): Promise<DeadLetterQueueResponse> {
    return this.deadLetterQueueService.getUnsuccessfulMessages(options);
  }

  @Mutation(() => DeadLetterQueueModel)
  @UseGuards(RolesAuthGuard(UserType.Admin))
  async reprocessMessage(
    @Args('id') messageId: string,
  ): Promise<DeadLetterQueueModel> {
    return this.deadLetterQueueService.reprocessMessage(messageId);
  }
}
