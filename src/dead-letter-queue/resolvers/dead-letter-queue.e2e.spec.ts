import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { GraphQLModule } from '@nestjs/graphql';
import { getModelToken } from '@nestjs/mongoose';
import { Test } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import request from 'supertest';
import { DeadLetterQueueModule } from '../dead-letter-queue.module';
import { DeadLetterQueueService } from '../services/dead-letter-queue.service';
import { deadLetterQueueFactory } from '@clinify/__mocks__/factories/dead-letter-queue.factory';
import gqlAuthGuardMock from '@clinify/__mocks__/gqlAuthGuard.mock';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { TestDataSourceOptions } from '@clinify/data-source';
import {
  DefaultInvImage,
  InventoryUpload,
} from '@clinify/integrations/inventory-blk/entities/FileUpload.entity';
import { PriceUpload } from '@clinify/integrations/price-blk/entities/PriceFileUpload.entity';
import { UserType } from '@clinify/shared/enums/users';

jest.mock('@aws-sdk/client-bedrock-runtime', () => {
  const mockInvokeModelCommand = jest.fn();
  return {
    BedrockRuntimeClient: jest.fn(() => ({
      send: jest.fn(),
    })),
    InvokeModelCommand: mockInvokeModelCommand,
  };
});

const deadLetterMessages = deadLetterQueueFactory.buildList(3);
const deadLetterMessage = deadLetterMessages[0];

const mockDeadLetterQueueService = {
  getMessage: jest.fn(() => deadLetterMessage),
  getAllMessages: jest.fn(() => ({
    list: deadLetterMessages,
    totalCount: 1,
  })),
  getUnsuccessfulMessages: jest.fn(() => ({
    list: deadLetterMessages,
    totalCount: 1,
  })),
  reprocessMessage: jest.fn(() => deadLetterMessage),
};

describe('DeadLetterQueueResolver', () => {
  let testHttpServer: request.SuperTest<request.Test>;
  let app: INestApplication;

  beforeAll(async () => {
    const builder = await Test.createTestingModule({
      imports: [
        DeadLetterQueueModule,
        TypeOrmModule.forRoot(TestDataSourceOptions),
        EventEmitterModule.forRoot(),
        GraphQLModule.forRoot<ApolloDriverConfig>({
          debug: false,
          playground: false,
          driver: ApolloDriver,
          autoSchemaFile: true,
          include: [DeadLetterQueueModule],
          context: ({ req }) => ({ req }),
        }),
      ],
    })
      .overrideProvider(DeadLetterQueueService)
      .useValue(mockDeadLetterQueueService)
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .overrideGuard(GqlAuthGuard)
      .useValue(gqlAuthGuardMock(UserType.Admin))
      .compile();

    app = builder.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    testHttpServer = request(app.getHttpServer());
  });

  afterAll(async () => await app.close());

  it('getMessage should return a single message', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query{
            getMessage(
              id: "${deadLetterMessage.id}"
              ){
                 id
               }
             }`,
      })
      .expect(({ body }) => {
        const { getMessage } = body.data;
        expect(mockDeadLetterQueueService.getMessage).toBeCalled();
        expect(getMessage.id).toEqual(deadLetterMessage.id);
      })
      .expect(200)
      .end(done);
  });

  it('getAllMessages should return all messages', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query{
            getAllMessages(
                filter: {
                    skip: ${0},
                    take: ${50}
                }
              ){
                list {
              id
            }
             totalCount
           }
             }`,
      })
      .expect(({ body }) => {
        const { getAllMessages } = body.data;
        expect(getAllMessages.list[0].id).toEqual(deadLetterMessages[0].id);
        expect(getAllMessages.list.length).toEqual(deadLetterMessages.length);
      })
      .expect(200)
      .end(done);
  });

  it('getUnsuccessfulMessages should return unsuccessful messages', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query{
            getUnsuccessfulMessages(
                filter: {
                    skip: ${0},
                    take: ${50}
                }
              ){
                list {
              id
            }
             totalCount
           }
             }`,
      })
      .expect(({ body }) => {
        const { getUnsuccessfulMessages } = body.data;
        expect(mockDeadLetterQueueService.getUnsuccessfulMessages).toBeCalled();
        expect(getUnsuccessfulMessages.list[0].id).toEqual(
          deadLetterMessages[0].id,
        );
        expect(getUnsuccessfulMessages.list.length).toEqual(
          deadLetterMessages.length,
        );
      })
      .expect(200)
      .end(done);
  });

  it('reprocessMessage should reprcess a failed messages', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation{
            reprocessMessage(
                id: "${deadLetterMessage.id}"
              ){
                id
                }
             }`,
      })
      .expect(({ body }) => {
        const { reprocessMessage } = body.data;
        expect(mockDeadLetterQueueService.reprocessMessage).toBeCalled();
        expect(reprocessMessage.id).toEqual(deadLetterMessage.id);
      })
      .expect(200)
      .end(done);
  });
});
