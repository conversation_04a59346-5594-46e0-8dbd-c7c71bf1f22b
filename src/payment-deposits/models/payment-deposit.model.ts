import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { Currency } from '@clinify/shared/enums/currency';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
@Entity('payment_deposits')
export class PaymentDepositModel {
  @IsUUID('4')
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field()
  @Column({
    name: 'deposit_date',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  depositDate: Date;

  @Field({ nullable: true })
  @Column({ name: 'refund_date', type: 'timestamp', nullable: true })
  refundDate?: Date;

  @Field({ nullable: true })
  @Column({ name: 'initial_deposit_balance', nullable: true, type: 'float' })
  initialDepositBalance?: number;

  @Field({ nullable: true })
  @Column({ name: 'final_deposit_balance', nullable: true, type: 'float' })
  finalDepositBalance?: number;

  @Field()
  @CreateDateColumn({ name: 'created_date' })
  createdDate: Date;

  @Field({ nullable: true })
  @UpdateDateColumn({ name: 'updated_date', nullable: true })
  updatedDate?: Date;

  @Column({
    name: 'is_manual_refund',
    type: 'boolean',
    nullable: true,
    default: false,
  })
  @Field({ nullable: true })
  isManualRefund?: boolean;

  @Field(() => Currency)
  @Column({
    name: 'currency',
    type: 'enum',
    enum: Currency,
    default: Currency.KOBO,
  })
  currency: Currency;

  @Field()
  @Column({ name: 'amount_deposited', default: 0, type: 'float' })
  amountDeposited: number;

  @Field()
  @Column({ name: 'amount_used', default: 0, type: 'float' })
  amountUsed: number;

  @Field({ nullable: true })
  @Column({
    name: 'amount_refunded',
    nullable: true,
    type: 'float',
    default: 0,
  })
  amountRefunded?: number;

  @Field(() => HospitalModel)
  @ManyToOne(() => HospitalModel)
  @JoinColumn({ name: 'hospital_id' })
  hospital: HospitalModel;

  @Field()
  @Index()
  @Column({ name: 'hospital_id' })
  hospitalId: string;

  @ManyToOne(() => ProfileModel)
  @JoinColumn({ name: 'profile_id' })
  profile: ProfileModel;

  @Field()
  @Index()
  @Column({ name: 'profile_id' })
  profileId: string;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel)
  @JoinColumn({ name: 'collected_by_id' })
  collectedBy: ProfileModel;

  @Field({ nullable: true })
  @Column({ name: 'collected_by_id', nullable: true })
  collectedById: string;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel)
  @JoinColumn({ name: 'withdrawn_by_id' })
  withdrawnBy: ProfileModel;

  @Field({ nullable: true })
  @Column({ name: 'withdrawn_by_id', nullable: true })
  withdrawnById: string;

  @Field({ nullable: true })
  @Column({ name: 'refunded_by', nullable: true })
  refundedBy?: string;

  @Field({ nullable: true })
  @Column({ name: 'deposit_method', nullable: true })
  depositMethod: string;

  @Field({ nullable: true })
  @Column({ name: 'description', nullable: true })
  description?: string;

  @Field({ nullable: true })
  @Column({ name: 'additional_note', nullable: true })
  additionalNote?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel)
  @JoinColumn({ name: 'creator_id' })
  createdBy: ProfileModel;

  @Field(() => String)
  @Column({ name: 'creator_id' })
  creatorId: string;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel)
  @JoinColumn({ name: 'last_modifier_id' })
  updatedBy?: ProfileModel;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'last_modifier_id' })
  lastModifierId?: string;

  @Column({ type: 'boolean', default: false })
  archived?: boolean;

  @Field({ nullable: true })
  @Column({ name: 'auto_generated', default: false, type: 'boolean' })
  autoGenerated: boolean;

  constructor(paymentDeposit: Partial<PaymentDepositModel>) {
    Object.assign(this, paymentDeposit);
  }
}
