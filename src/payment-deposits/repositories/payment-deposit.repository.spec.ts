import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { DataSource, EntityManager } from 'typeorm';
import { paymentDepositFactory } from '@clinify/__mocks__/factories/payment-deposit.factory';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { TestDataSourceOptions } from '@clinify/data-source';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { PaymentDepositModel } from '@clinify/payment-deposits/models/payment-deposit.model';
import { PaymentDepositRepository } from '@clinify/payment-deposits/repositories/payment-deposit.repository';
import { Currency } from '@clinify/shared/enums/currency';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { createHospitals } from '@fixtures/hospital.fixtures';
import { createPaymentDeposit } from '@fixtures/payment-deposit.fixtures';
import { createUsers } from '@fixtures/user.fixtures';

const chance = new Chance();

describe('PaymentDepositRepository', () => {
  let ds: DataSource;
  let repo: PaymentDepositRepository;
  let paymentDeposits: PaymentDepositModel[];
  let paymentDeposit: PaymentDepositModel;
  let manager: EntityManager;
  let profile: ProfileModel;
  let patientProfile: ProfileModel;
  let hospital: HospitalModel;

  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeormExtendedModule.forCustomRepository([PaymentDepositRepository]),
      ],
      providers: [],
    }).compile();

    ds = module.get<DataSource>(DataSource);
    manager = ds.manager;
    repo = module.get<PaymentDepositRepository>(PaymentDepositRepository);

    [hospital] = await createHospitals(manager, 1);
    const [user, patient] = await createUsers(manager, 2, hospital);
    profile = user.defaultProfile;
    patientProfile = patient.defaultProfile;

    paymentDeposits = await createPaymentDeposit(
      manager,
      2,
      profile,
      patientProfile,
      hospital,
    );
    paymentDeposit = paymentDeposits[0];
  });

  afterAll(async () => {
    await ds.destroy();
    await module.close();
  });

  it('should be defined', () => {
    expect(repo).toBeDefined();
  });

  it('getProfileDeposits', async () => {
    const res = await repo.getProfileDeposits(
      patientProfile.id,
      hospital.id,
      {},
    );
    expect(res.totalCount).toEqual(2);
    expect(res.list).toContainEqual(
      expect.objectContaining({ id: paymentDeposit.id }),
    );
  });

  it('getProfileDeposits with filter', async () => {
    const [response] = await createPaymentDeposit(
      manager,
      1,
      profile,
      patientProfile,
      hospital,
      [
        {
          ...paymentDepositFactory.build(),
          description: 'Unique Description',
        },
      ],
    );
    const res = await repo.getProfileDeposits(patientProfile.id, hospital.id, {
      keyword: 'Unique Description',
    });
    expect(res.totalCount).toEqual(1);
    expect(res.list).toContainEqual(
      expect.objectContaining({ id: response.id }),
    );
  });

  it('getProfileDeposits with no result', async () => {
    const res = await repo.getProfileDeposits(patientProfile.id, hospital.id, {
      keyword: chance.word({ length: 20 }),
      dateRange: {
        from: new Date(),
        to: new Date(),
      },
    });
    expect(res.totalCount).toEqual(0);
    expect(res.list).toEqual([]);
  });

  it('getPaymentDepositBalance', async () => {
    const res = await repo.getPaymentDepositSummary(
      ds,
      patientProfile.id,
      hospital.id,
      Currency.KOBO,
    );
    expect(res.balance).toEqual(3000);
    expect(res.currency).toEqual(Currency.KOBO);
  });
});
