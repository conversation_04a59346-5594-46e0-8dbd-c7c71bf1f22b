/* eslint-disable max-len */
import moment from 'moment/moment';
import { DataSource, Repository } from 'typeorm';
import { CustomRepository } from '@clinify/custom-repository/decorators/custom-repo.decorator';
import { queryDSWithSlave } from '@clinify/database';
import { PaymentDepositFilterInput } from '@clinify/payment-deposits/inputs/payment-deposit.input';
import { PaymentDepositModel } from '@clinify/payment-deposits/models/payment-deposit.model';
import {
  PaymentDepositBalance,
  PaymentDepositListResponse,
} from '@clinify/payment-deposits/responses/payment-deposit.response';
import { Currency } from '@clinify/shared/enums/currency';
import { takePaginatedResponses } from '@clinify/utils/pagination';

@CustomRepository(PaymentDepositModel)
export class PaymentDepositRepository extends Repository<PaymentDepositModel> {
  async getProfileDeposits(
    profileId: string,
    hospitalId: string,
    filterInput: PaymentDepositFilterInput,
  ): Promise<PaymentDepositListResponse> {
    const { skip, take, archive, keyword, dateRange } = filterInput;
    let query = this.createQueryBuilder('deposits')
      .withDeleted()
      .leftJoinAndSelect('deposits.createdBy', 'createdBy')
      .leftJoinAndSelect('deposits.collectedBy', 'collectedBy')
      .leftJoinAndSelect('deposits.withdrawnBy', 'withdrawnBy')
      .where('deposits.archived = :archived', { archived: !!archive })
      .andWhere('deposits.isManualRefund = :isManualRefund', {
        isManualRefund: !!filterInput.showRefunds,
      })
      .andWhere('(deposits.profileId = :profileId)', { profileId })
      .andWhere('(deposits.hospitalId = :hospitalId)', { hospitalId });
    if (dateRange?.from) {
      query = query.andWhere(
        '(deposits.depositDate >= :from OR deposits.refundDate >= :from)',
        {
          from: moment(dateRange.from).startOf('day').toDate(),
        },
      );
    }
    if (dateRange?.to) {
      query = query.andWhere(
        '(deposits.depositDate < :to OR deposits.refundDate < :to)',
        {
          to: moment(dateRange.to).endOf('day').toDate(),
        },
      );
    }
    if (keyword) {
      query = query.andWhere(
        `
      (deposits.description ILIKE :keyword OR
      createdBy.fullName ILIKE :keyword OR
      collectedBy.fullName ILIKE :keyword
      )
      `,
        { keyword: `%${keyword}%` },
      );
    }

    query = query.orderBy('deposits.createdDate', 'DESC').skip(skip).take(take);
    const response = await query.getManyAndCount();

    return new PaymentDepositListResponse(
      ...takePaginatedResponses(response, take),
    );
  }

  async getPaymentDepositSummary(
    dataSource: DataSource,
    profileId: string,
    hospitalId: string,
    currency: Currency,
  ): Promise<PaymentDepositBalance> {
    const result = await queryDSWithSlave(
      dataSource,
      `
    SELECT
        COALESCE(SUM(payment_deposits.amount_deposited - payment_deposits.amount_used - payment_deposits.amount_refunded), 0) AS "balance",
        COALESCE(SUM(payment_deposits.amount_deposited - payment_deposits.amount_refunded) FILTER (WHERE payment_deposits.auto_generated = false), 0) AS "totalDeposited",
        COALESCE(SUM(payment_deposits.amount_used) FILTER (WHERE payment_deposits.auto_generated = true) - SUM(payment_deposits.amount_deposited) FILTER (WHERE payment_deposits.auto_generated = true), 0) AS "totalUsed",
        COALESCE(SUM(payment_deposits.amount_refunded), 0) AS "totalRefunded"
        FROM payment_deposits
        WHERE payment_deposits.profile_id = '${profileId}' 
        AND payment_deposits.hospital_id = '${hospitalId}' 
        AND payment_deposits.currency = '${currency}'
    `,
    );

    return new PaymentDepositBalance({ ...result[0], currency });
  }
}
