import { Inject, UseGuards } from '@nestjs/common';
import {
  Args,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
  Subscription,
} from '@nestjs/graphql';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { ProfileDataAccessGuard } from '@clinify/authentication/guards/profile-data-acess.guard';
import { AuthorizationGuard } from '@clinify/authorization/guards/authorization.guard';
import {
  EditPaymentDepositInput,
  EditPaymentDepositRefundInput,
  PaymentDepositInput,
  PaymentDepositRefundInput,
} from '@clinify/payment-deposits/inputs/payment-deposit.input';
import { PaymentDepositModel } from '@clinify/payment-deposits/models/payment-deposit.model';
import { PaymentDepositBalance } from '@clinify/payment-deposits/responses/payment-deposit.response';
import { PaymentDepositService } from '@clinify/payment-deposits/services/payment-deposit.service';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { Currency } from '@clinify/shared/enums/currency';
import { AppServices } from '@clinify/shared/enums/services';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { ProfileService } from '@clinify/users/services/profile.service';
import {
  filterPaymentDepositAdded,
  filterPaymentDepositRemoved,
  filterRefundDepositAdded,
  filterRefundDepositUpdated,
} from '@clinify/utils/subscriptions/filters';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const {
  PaymentDepositAdded,
  PaymentDepositRemoved,
  RefundDepositAdded,
  RefundDepositUpdated,
} = SubscriptionTypes;

@UseGuards(GqlAuthGuard, AuthorizationGuard)
@Resolver(() => PaymentDepositModel)
@LogService(AppServices.PaymentDeposit)
export class PaymentDepositResolver {
  constructor(
    private readonly service: PaymentDepositService,
    private readonly profileService: ProfileService,
    @Inject(PUB_SUB) private readonly pubSub: RedisPubSub,
  ) {}

  @UseGuards(ProfileDataAccessGuard('clinifyId'))
  @Query(() => PaymentDepositModel)
  paymentDeposit(
    @Args('id') id: string,
    @Args('clinifyId') _clinifyId: string,
  ): Promise<PaymentDepositModel> {
    return this.service.getPaymentDeposit(id);
  }

  @UseGuards(ProfileDataAccessGuard('input.profileId'))
  @Mutation(() => PaymentDepositModel)
  async addPaymentDeposit(
    @CurrentProfile() mutator: ProfileModel,
    @Args('input', { type: () => PaymentDepositInput })
    input: PaymentDepositInput,
  ): Promise<PaymentDepositModel> {
    const paymentDeposit = await this.service.addPaymentDeposit(mutator, input);
    this.pubSub.publish(PaymentDepositAdded, {
      [PaymentDepositAdded]: paymentDeposit,
    });

    return paymentDeposit;
  }
  @UseGuards(ProfileDataAccessGuard('input.profileId'))
  @Mutation(() => PaymentDepositModel)
  async addPaymentDepositRefund(
    @CurrentProfile() mutator: ProfileModel,
    @Args('input', { type: () => PaymentDepositRefundInput })
    input: PaymentDepositRefundInput,
  ): Promise<PaymentDepositModel> {
    const record = await this.service.refundPaymentDeposit(mutator, input);
    this.pubSub.publish(RefundDepositAdded, {
      [RefundDepositAdded]: record,
    });

    return record;
  }

  @UseGuards(ProfileDataAccessGuard('input.profileId'))
  @Mutation(() => PaymentDepositModel)
  async updatePaymentDepositRefund(
    @CurrentProfile() mutator: ProfileModel,
    @Args('id') id: string,
    @Args('input', { type: () => EditPaymentDepositRefundInput })
    input: EditPaymentDepositRefundInput,
  ): Promise<PaymentDepositModel> {
    const record = await this.service.updateRefundPaymentDeposit(
      mutator,
      id,
      input,
    );
    this.pubSub.publish(RefundDepositUpdated, {
      [RefundDepositUpdated]: record,
    });

    return record;
  }

  @Mutation(() => PaymentDepositModel)
  updatePaymentDeposit(
    @CurrentProfile() mutator: ProfileModel,
    @Args('id') id: string,
    @Args('input', { type: () => EditPaymentDepositInput })
    input: EditPaymentDepositInput,
  ): Promise<PaymentDepositModel> {
    return this.service.updatePaymentDeposit(mutator, id, input);
  }

  @Mutation(() => [PaymentDepositModel])
  archivePaymentDeposits(
    @CurrentProfile() mutator: ProfileModel,
    @Args('ids', { type: () => [String] }) ids: string[],
    @Args('archive') archive: boolean,
  ): Promise<PaymentDepositModel[]> {
    return this.service.archivePaymentDeposits(mutator, ids, archive);
  }

  @Mutation(() => [PaymentDepositModel])
  async deletePaymentDeposits(
    @CurrentProfile() mutator: ProfileModel,
    @Args('ids', { type: () => [String] }) ids: string[],
  ): Promise<PaymentDepositModel[]> {
    const paymentDeposits = await this.service.deletePaymentDeposits(
      mutator,
      ids,
    );
    paymentDeposits.forEach((item) => {
      this.pubSub.publish(PaymentDepositRemoved, {
        [PaymentDepositRemoved]: item,
      });
    });

    return paymentDeposits;
  }

  @Query(() => PaymentDepositBalance)
  paymentDepositSummary(
    @CurrentProfile() mutator: ProfileModel,
    @Args('profileId') profileId: string,
    @Args('currency', { nullable: true, defaultValue: Currency.KOBO })
    currency?: Currency,
  ): Promise<PaymentDepositBalance> {
    return this.service.getPaymentDepositSummary(mutator, profileId, currency);
  }

  @ResolveField(() => String, { name: 'creatorName' })
  getCreatorName(@Parent() root: PaymentDepositModel): string {
    return root.createdBy?.fullName;
  }

  @ResolveField(() => String, { name: 'lastModifierName', nullable: true })
  getLastModifierName(@Parent() root: PaymentDepositModel): string {
    return root.updatedBy?.fullName;
  }

  @ResolveField(() => ProfileModel, { nullable: true, name: 'collectedBy' })
  getCollectedBy(
    @Parent() root: PaymentDepositModel,
  ): Promise<ProfileModel> | ProfileModel {
    if (root.collectedBy) return root.collectedBy;
    if (root.collectedById)
      return this.profileService.byProfileIdLight(root.collectedById, {
        id: true,
        fullName: true,
      });
  }

  @ResolveField(() => ProfileModel, { nullable: true, name: 'withdrawnBy' })
  getWithdrawnBy(
    @Parent() root: PaymentDepositModel,
  ): Promise<ProfileModel> | ProfileModel {
    if (root.withdrawnBy) return root.withdrawnBy;
    if (root.withdrawnById)
      return this.profileService.byProfileIdLight(root.withdrawnById, {
        id: true,
        fullName: true,
      });
  }

  @Subscription(() => PaymentDepositModel, {
    name: PaymentDepositAdded,
    filter: filterPaymentDepositAdded,
  })
  addPaymentDepositSubsHandler(
    @Args('hospitalId') _hospitalId: string,
    @Args('profileId') _profileId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(PaymentDepositAdded);
  }

  @Subscription(() => PaymentDepositModel, {
    name: PaymentDepositRemoved,
    filter: filterPaymentDepositRemoved,
  })
  removePaymentDepositSubsHandler(
    @Args('hospitalId') _hospitalId: string,
    @Args('profileId') _profileId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(PaymentDepositRemoved);
  }

  @Subscription(() => PaymentDepositModel, {
    name: RefundDepositAdded,
    filter: filterRefundDepositAdded,
  })
  addRefundDepositSubsHandler(
    @Args('hospitalId') _hospitalId: string,
    @Args('profileId') _profileId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(RefundDepositAdded);
  }

  @Subscription(() => PaymentDepositModel, {
    name: RefundDepositUpdated,
    filter: filterRefundDepositUpdated,
  })
  updateRefundDepositSubsHandler(
    @Args('hospitalId') _hospitalId: string,
    @Args('profileId') _profileId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(RefundDepositUpdated);
  }
}
