import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { CustomPermissionRepoMethods } from '@clinify/authorization/repositories/permission.repository';
import { PermissionService } from '@clinify/authorization/services/permission.service';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendModel } from '@clinify/database/extendModel';
import { PaymentDepositResolver } from '@clinify/payment-deposits/resolvers/payment-deposit.resolver';
import { PaymentDepositService } from '@clinify/payment-deposits/services/payment-deposit.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { CustomProfileRepoMethods } from '@clinify/users/repositories/profile.repository';
import { ProfileService } from '@clinify/users/services/profile.service';
import { mockPaymentDeposit } from '@mocks/factories/payment-deposit.factory';
import { mockProfile } from '@mocks/factories/profile.factory';
import { PubSubMock } from '@mocks/pub-sub.mock';

const MockPaymentDepositService = {
  getPaymentDeposit: jest.fn(() => Promise.resolve(mockPaymentDeposit)),
  addPaymentDeposit: jest.fn(() => Promise.resolve(mockPaymentDeposit)),
  updatePaymentDeposit: jest.fn(() => Promise.resolve(mockPaymentDeposit)),
  archivePaymentDeposits: jest.fn(() => Promise.resolve([mockPaymentDeposit])),
  deletePaymentDeposits: jest.fn(() => Promise.resolve([mockPaymentDeposit])),
  getPaymentDepositSummary: jest.fn(() =>
    Promise.resolve({
      balance: 1000,
      currency: 'KOBO',
    }),
  ),
  refundPaymentDeposit: jest.fn(() => Promise.resolve(mockPaymentDeposit)),
  updateRefundPaymentDeposit: jest.fn(() =>
    Promise.resolve(mockPaymentDeposit),
  ),
};
const MockProfileService = {};

describe('PaymentDepositResolver', () => {
  let module: TestingModule;
  let resolver: PaymentDepositResolver;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        PaymentDepositResolver,
        PermissionService,
        extendModel(PermissionModel, CustomPermissionRepoMethods),
        extendModel(ProfileModel, CustomProfileRepoMethods),
        { provide: PaymentDepositService, useValue: MockPaymentDepositService },
        { provide: ProfileService, useValue: MockProfileService },
        { provide: 'PUB_SUB', useValue: PubSubMock },
      ],
    })
      .overrideProvider('PUB_SUB')
      .useValue(PubSubMock)
      .compile();
    resolver = module.get<PaymentDepositResolver>(PaymentDepositResolver);
  });

  afterAll(async () => {
    jest.clearAllMocks();
    await module.close();
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  it('paymentDeposit', async () => {
    const res = await resolver.paymentDeposit('id', 'clinify-id');
    expect(MockPaymentDepositService.getPaymentDeposit).toHaveBeenCalledWith(
      'id',
    );
    expect(res).toEqual(mockPaymentDeposit);
  });

  it('addPaymentDeposit', async () => {
    const res = await resolver.addPaymentDeposit(
      mockProfile,
      mockPaymentDeposit,
    );
    expect(MockPaymentDepositService.addPaymentDeposit).toHaveBeenCalledWith(
      mockProfile,
      mockPaymentDeposit,
    );
    expect(res).toEqual(mockPaymentDeposit);
  });

  it('updatePaymentDeposit', async () => {
    const res = await resolver.updatePaymentDeposit(
      mockProfile,
      'id',
      mockPaymentDeposit,
    );
    expect(MockPaymentDepositService.updatePaymentDeposit).toHaveBeenCalledWith(
      mockProfile,
      'id',
      mockPaymentDeposit,
    );
    expect(res).toEqual(mockPaymentDeposit);
  });

  it('archivePaymentDeposits', async () => {
    const res = await resolver.archivePaymentDeposits(
      mockProfile,
      ['id'],
      true,
    );
    expect(
      MockPaymentDepositService.archivePaymentDeposits,
    ).toHaveBeenCalledWith(mockProfile, ['id'], true);
    expect(res).toEqual([mockPaymentDeposit]);
  });

  it('deletePaymentDeposits', async () => {
    const res = await resolver.deletePaymentDeposits(mockProfile, ['id']);
    expect(
      MockPaymentDepositService.deletePaymentDeposits,
    ).toHaveBeenCalledWith(mockProfile, ['id']);
    expect(res).toEqual([mockPaymentDeposit]);
  });

  it('paymentDepositSummary', async () => {
    const res = await resolver.paymentDepositSummary(
      mockProfile,
      mockProfile.id,
    );
    expect(
      MockPaymentDepositService.getPaymentDepositSummary,
    ).toHaveBeenCalledWith(mockProfile, mockProfile.id, undefined);
    expect(res.balance).toEqual(1000);
  });

  it('getCreatorName', () => {
    const fullName = resolver.getCreatorName(mockPaymentDeposit);
    expect(fullName).toEqual(mockProfile.fullName);
  });

  it('getLastModifierName', () => {
    const fullName = resolver.getLastModifierName(mockPaymentDeposit);
    expect(fullName).not.toBeDefined();
  });

  it('addPaymentDepositSubsHandler', () => {
    resolver.addPaymentDepositSubsHandler('id', 'id');
    expect(PubSubMock.asyncIterator).toHaveBeenCalledWith(
      'PaymentDepositAdded',
    );
  });

  it('removePaymentDepositSubsHandler', () => {
    resolver.removePaymentDepositSubsHandler('id', 'id');
    expect(PubSubMock.asyncIterator).toHaveBeenCalledWith(
      'PaymentDepositRemoved',
    );
  });

  it('getWithdrawnBy', () => {
    const withdrawnBy = resolver.getWithdrawnBy(mockPaymentDeposit);
    expect(withdrawnBy).toBeFalsy();
  });

  it('getWithdrawnBy', () => {
    const collectedBy = resolver.getCollectedBy(mockPaymentDeposit);
    expect(collectedBy).toEqual(mockProfile);
  });

  it('addPaymentDepositRefund', async () => {
    const refund = await resolver.addPaymentDepositRefund(
      mockProfile,
      mockPaymentDeposit,
    );
    expect(MockPaymentDepositService.refundPaymentDeposit).toHaveBeenCalledWith(
      mockProfile,
      mockPaymentDeposit,
    );
    expect(refund).toEqual(mockPaymentDeposit);
  });

  it('updatePaymentDepositRefund', async () => {
    const refund = await resolver.updatePaymentDepositRefund(
      mockProfile,
      'id',
      mockPaymentDeposit,
    );
    expect(
      MockPaymentDepositService.updateRefundPaymentDeposit,
    ).toHaveBeenCalledWith(mockProfile, 'id', mockPaymentDeposit);
    expect(refund).toEqual(mockPaymentDeposit);
  });

  it('addRefundDepositSubsHandler', () => {
    resolver.addRefundDepositSubsHandler('id', 'id');
    expect(PubSubMock.asyncIterator).toHaveBeenCalledWith('RefundDepositAdded');
  });

  it('updateRefundDepositSubsHandler', () => {
    resolver.updateRefundDepositSubsHandler('id', 'id');
    expect(PubSubMock.asyncIterator).toHaveBeenCalledWith(
      'RefundDepositUpdated',
    );
  });
});
