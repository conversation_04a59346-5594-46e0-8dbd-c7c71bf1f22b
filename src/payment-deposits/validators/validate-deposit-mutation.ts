import { ForbiddenException } from '@nestjs/common';
import { PaymentDepositModel } from '@clinify/payment-deposits/models/payment-deposit.model';
import { ProfileModel } from '@clinify/users/models/profile.model';

export const validateDepositUpdate = (
  mutator: ProfileModel,
  deposit: PaymentDepositModel,
) => {
  if (deposit.hospitalId !== mutator.hospitalId) {
    throw new ForbiddenException('Not Authorized To Modify This Record');
  }
};

export const validateDepositArchival = (
  mutator: ProfileModel,
  deposits: PaymentDepositModel[],
): PaymentDepositModel[] => {
  return deposits.filter(
    (deposit) => deposit.hospitalId === mutator.hospitalId,
  );
};

export const validateDepositRemoval = (
  mutator: ProfileModel,
  deposits: PaymentDepositModel[],
) => {
  return deposits.filter(
    (deposit) =>
      deposit.hospitalId === mutator.hospitalId && !deposit.autoGenerated,
  );
};
