import { Field, InputType, OmitType } from '@nestjs/graphql';
import { Currency } from '@clinify/shared/enums/currency';
import { FilterInput } from '@clinify/shared/validators/filter.input';

@InputType()
export class PaymentDepositInput {
  @Field()
  depositDate: Date;

  @Field({ name: 'currency', defaultValue: Currency.KOBO, nullable: true })
  currency?: Currency;

  @Field({ defaultValue: 0 })
  amountDeposited: number;

  @Field({ defaultValue: 0 })
  amountUsed: number;

  @Field()
  profileId: string;

  @Field()
  collectedById?: string;

  @Field({ nullable: true })
  withdrawnById?: string;

  @Field()
  depositMethod?: string;

  @Field({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  additionalNote?: string;
}

@InputType()
export class PaymentDepositRefundInput {
  @Field()
  refundDate: Date;

  @Field({ name: 'currency', defaultValue: Currency.KOBO, nullable: true })
  currency?: Currency;

  @Field({ nullable: true })
  refundedBy?: string;

  @Field({ nullable: true })
  amountRefunded?: number;

  @Field({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  additionalNote?: string;

  @Field()
  profileId: string;
}

@InputType()
export class EditPaymentDepositRefundInput extends OmitType(
  PaymentDepositRefundInput,
  ['profileId'],
) {}

@InputType()
export class EditPaymentDepositInput extends OmitType(PaymentDepositInput, [
  'profileId',
]) {}

@InputType()
export class PaymentDepositFilterInput extends OmitType(FilterInput, [
  'referral',
]) {
  @Field({ nullable: true, defaultValue: false })
  showRefunds?: boolean;
}
