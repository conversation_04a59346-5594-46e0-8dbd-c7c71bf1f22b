import { forwardRef, Module } from '@nestjs/common';
import { AuthorizationModule } from '@clinify/authorization/authorization.module';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { PaymentDepositRepository } from '@clinify/payment-deposits/repositories/payment-deposit.repository';
import { PaymentDepositResolver } from '@clinify/payment-deposits/resolvers/payment-deposit.resolver';
import { PaymentDepositService } from '@clinify/payment-deposits/services/payment-deposit.service';
import { SharedModule } from '@clinify/shared/module';
import { UserModule } from '@clinify/users/user.module';
import PubSub, { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';

@Module({
  imports: [
    TypeormExtendedModule.forCustomRepository([PaymentDepositRepository]),
    forwardRef(() => SharedModule),
    AuthorizationModule,
    forwardRef(() => UserModule),
  ],
  providers: [
    PaymentDepositResolver,
    PaymentDepositService,
    { provide: PUB_SUB, useFactory: () => PubSub },
  ],
  exports: [PaymentDepositService],
})
export class PaymentDepositModule {}
