import { Field, ObjectType } from '@nestjs/graphql';
import { PaymentDepositModel } from '@clinify/payment-deposits/models/payment-deposit.model';
import { Currency } from '@clinify/shared/enums/currency';

@ObjectType()
export class PaymentDepositBalance {
  @Field()
  balance: number;

  @Field(() => Currency)
  currency: Currency;

  @Field()
  totalDeposited: number;

  @Field()
  totalUsed: number;

  @Field()
  totalRefunded: number;

  constructor(balance: PaymentDepositBalance) {
    Object.assign(this, balance);
  }
}

@ObjectType()
export class PaymentDepositListResponse {
  @Field(() => [PaymentDepositModel])
  list: PaymentDepositModel[];

  @Field()
  totalCount: number;

  constructor(deposits: PaymentDepositModel[], totalCount: number) {
    this.list = deposits;
    this.totalCount = totalCount;
  }
}
