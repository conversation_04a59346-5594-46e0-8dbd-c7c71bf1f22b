import {
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { DataSource, EntityManager, In } from 'typeorm';
import {
  EditPaymentDepositInput,
  EditPaymentDepositRefundInput,
  PaymentDepositFilterInput,
  PaymentDepositInput,
  PaymentDepositRefundInput,
} from '@clinify/payment-deposits/inputs/payment-deposit.input';
import { PaymentDepositModel } from '@clinify/payment-deposits/models/payment-deposit.model';
import { PaymentDepositRepository } from '@clinify/payment-deposits/repositories/payment-deposit.repository';
import {
  PaymentDepositBalance,
  PaymentDepositListResponse,
} from '@clinify/payment-deposits/responses/payment-deposit.response';
import {
  validateDepositArchival,
  validateDepositRemoval,
  validateDepositUpdate,
} from '@clinify/payment-deposits/validators/validate-deposit-mutation';
import { Currency } from '@clinify/shared/enums/currency';
import { ProfileModel } from '@clinify/users/models/profile.model';

@Injectable()
export class PaymentDepositService {
  constructor(
    private readonly repository: PaymentDepositRepository,
    private readonly dataSource: DataSource,
  ) {}

  getPaymentDeposit(id: string): Promise<PaymentDepositModel> {
    return this.repository
      .findOneOrFail({
        where: { id },
        relations: {
          createdBy: true,
          updatedBy: true,
          profile: true,
          hospital: true,
          collectedBy: true,
          withdrawnBy: true,
        },
        withDeleted: true,
      })
      .catch(() => {
        throw new NotFoundException('Payment Deposit Not Found');
      });
  }

  getProfileDeposits(
    profileId: string,
    hospitalId: string,
    filterInput: PaymentDepositFilterInput,
  ): Promise<PaymentDepositListResponse> {
    return this.repository.getProfileDeposits(
      profileId,
      hospitalId,
      filterInput,
    );
  }

  addPaymentDeposit(
    mutator: ProfileModel,
    input: PaymentDepositInput,
    manager?: EntityManager,
    autoGenerated?: boolean,
  ): Promise<PaymentDepositModel> {
    const repo = manager
      ? manager.withRepository(this.repository)
      : this.repository;
    return repo.save({
      ...input,
      creatorId: mutator.id,
      createdBy: mutator,
      hospitalId: mutator.hospitalId,
      autoGenerated,
    });
  }

  async refundPaymentDeposit(
    mutator: ProfileModel,
    input: PaymentDepositRefundInput,
  ): Promise<PaymentDepositModel> {
    const { balance: initialBalance } = await this.getPaymentDepositSummary(
      mutator,
      input.profileId,
      input.currency,
    );
    if (input.amountRefunded > initialBalance) {
      throw new ConflictException(
        'Amount To Be Refunded Is More Than Deposit Balance',
      );
    }
    return this.repository.save({
      ...input,
      initialDepositBalance: initialBalance,
      finalDepositBalance: initialBalance - input.amountRefunded,
      creatorId: mutator.id,
      createdBy: mutator,
      hospitalId: mutator.hospitalId,
      autoGenerated: false,
      isManualRefund: true,
    });
  }

  async updateRefundPaymentDeposit(
    mutator: ProfileModel,
    id: string,
    input: EditPaymentDepositRefundInput,
  ): Promise<PaymentDepositModel> {
    const record = await this.repository
      .findOneOrFail({
        where: { id },
      })
      .catch(() => {
        throw new NotFoundException('Payment Deposit Not Found');
      });
    validateDepositUpdate(mutator, record);
    const updatedEntity = {
      id,
      ...input,
      updatedBy: mutator,
      lastModifierId: mutator.id,
      ...(record.amountRefunded !== input.amountRefunded
        ? {
            finalDepositBalance:
              record.initialDepositBalance - input.amountRefunded,
          }
        : {}),
    };
    await this.repository.update(id, updatedEntity);

    return { ...record, ...updatedEntity };
  }

  async updatePaymentDeposit(
    mutator: ProfileModel,
    id: string,
    input: EditPaymentDepositInput,
  ): Promise<PaymentDepositModel> {
    const paymentDeposit = await this.repository
      .findOneOrFail({
        where: { id },
      })
      .catch(() => {
        throw new NotFoundException('Payment Deposit Not Found');
      });
    validateDepositUpdate(mutator, paymentDeposit);
    const updatedEntity = {
      id,
      ...input,
      updatedBy: mutator,
      lastModifierId: mutator.id,
    };

    await this.repository.update(id, updatedEntity);

    return { ...paymentDeposit, ...updatedEntity };
  }

  async archivePaymentDeposits(
    mutator: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<PaymentDepositModel[]> {
    const paymentDeposits = await this.repository.find({
      where: { id: In(ids) },
    });
    const validResources = validateDepositArchival(mutator, paymentDeposits);
    if (!validResources.length) {
      throw new ForbiddenException('Not Authorized To Archive This Record');
    }
    await this.repository.update(
      validResources.map(({ id }) => id),
      { archived: archive },
    );
    return validResources;
  }

  async deletePaymentDeposits(
    mutator: ProfileModel,
    ids: string[],
  ): Promise<PaymentDepositModel[]> {
    const paymentDeposits = await this.repository.find({
      where: {
        id: In(ids),
      },
    });
    const validResources = validateDepositRemoval(mutator, paymentDeposits);
    if (!validResources.length) {
      throw new ForbiddenException('Not Authorized To Delete This Record');
    }
    await this.repository.delete(validResources.map(({ id }) => id));
    return validResources;
  }

  getPaymentDepositSummary(
    mutator: ProfileModel,
    profileId: string,
    currency = Currency.KOBO,
  ): Promise<PaymentDepositBalance> {
    return this.repository.getPaymentDepositSummary(
      this.dataSource,
      profileId,
      mutator.hospitalId,
      currency,
    );
  }

  async makeServicePaymentFromDeposit(
    mutator: ProfileModel,
    profileId: string,
    amountPaid: number,
    description?: string,
    currency = Currency.KOBO,
    manager?: EntityManager,
    autoGenerated?: boolean,
    skipBalanceCheck?: boolean,
  ): Promise<PaymentDepositModel> {
    const repo = manager
      ? manager.withRepository(this.repository)
      : this.repository;
    const { balance } = await this.getPaymentDepositSummary(
      mutator,
      profileId,
      currency,
    );
    if (balance < amountPaid && !skipBalanceCheck) {
      throw new ConflictException('Insufficient Deposit Balance');
    }
    return repo.save(
      new PaymentDepositModel({
        amountUsed: amountPaid,
        currency,
        createdBy: mutator,
        creatorId: mutator.id,
        description: description ?? 'Service Payment',
        hospitalId: mutator.hospitalId,
        profileId,
        withdrawnById: mutator.id,
        withdrawnBy: mutator,
        autoGenerated,
      }),
    );
  }
}
