import { ConflictException, ForbiddenException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { PaymentDepositRepository } from '@clinify/payment-deposits/repositories/payment-deposit.repository';
import { PaymentDepositService } from '@clinify/payment-deposits/services/payment-deposit.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { managerMock } from '@mocks/database.mock';
import { mockPaymentDeposit } from '@mocks/factories/payment-deposit.factory';
import { mockProfile } from '@mocks/factories/profile.factory';

const MockPaymentDepositRepository = {
  findOneOrFail: jest.fn(() => Promise.resolve(mockPaymentDeposit)),
  find: jest.fn(() => Promise.resolve([mockPaymentDeposit])),
  getProfileDeposits: jest.fn(() =>
    Promise.resolve({
      list: [],
      totalCount: 0,
    }),
  ),
  save: jest.fn(() => Promise.resolve(mockPaymentDeposit)),
  update: jest.fn(() => Promise.resolve(mockPaymentDeposit)),
  delete: jest.fn(() => Promise.resolve()),
  getPaymentDepositSummary: jest.fn(() =>
    Promise.resolve({ balance: 1000, currency: 'KOBO' }),
  ),
};

const dsMock = {
  getRepository: jest.fn(() => ({
    extend: jest.fn(),
  })),
  transaction: jest.fn((cb) => cb(managerMock)),
  manager: managerMock,
};

describe('PaymentDepositService', () => {
  let service: PaymentDepositService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentDepositService,
        {
          provide: PaymentDepositRepository,
          useValue: MockPaymentDepositRepository,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: {},
        },
        {
          provide: DataSource,
          useValue: dsMock,
        },
      ],
    }).compile();

    service = module.get<PaymentDepositService>(PaymentDepositService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('getPaymentDeposit', async () => {
    const res = await service.getPaymentDeposit('id');
    expect(MockPaymentDepositRepository.findOneOrFail).toHaveBeenCalledWith(
      expect.objectContaining({ where: { id: 'id' } }),
    );
    expect(res).toEqual(mockPaymentDeposit);
  });

  it('getProfileDeposits', async () => {
    const res = await service.getProfileDeposits('id', 'hospital-id', {});
    expect(
      MockPaymentDepositRepository.getProfileDeposits,
    ).toHaveBeenCalledWith('id', 'hospital-id', {});
    expect(res).toEqual(expect.objectContaining({ list: [], totalCount: 0 }));
  });

  it('addPaymentDeposit', async () => {
    const res = await service.addPaymentDeposit(
      mockProfile,
      mockPaymentDeposit,
    );
    expect(MockPaymentDepositRepository.save).toHaveBeenCalledWith(
      expect.objectContaining({
        ...mockPaymentDeposit,
        creatorId: mockProfile.id,
        createdBy: mockProfile,
      }),
    );
    expect(res).toEqual(mockPaymentDeposit);
  });

  it('addRefundDeposit', async () => {
    const res = await service.refundPaymentDeposit(
      mockProfile,
      mockPaymentDeposit,
    );
    expect(MockPaymentDepositRepository.save).toHaveBeenCalledWith(
      expect.objectContaining({
        ...mockPaymentDeposit,
        creatorId: mockProfile.id,
        createdBy: mockProfile,
        isManualRefund: true,
        autoGenerated: false,
      }),
    );
    expect(res).toEqual(mockPaymentDeposit);
  });

  it('addRefundDeposit should fail if Amount To Be Refunded Is More Than Deposit Balance', async () => {
    MockPaymentDepositRepository.getPaymentDepositSummary.mockImplementationOnce(
      () => Promise.resolve({ balance: 0, currency: 'KOBO' }),
    );
    const input = { ...mockPaymentDeposit, amountRefunded: 1000 };
    await expect(
      service.refundPaymentDeposit(mockProfile, input),
    ).rejects.toThrow(
      new ConflictException(
        'Amount To Be Refunded Is More Than Deposit Balance',
      ),
    );
  });

  it('updateRefundPaymentDeposit', async () => {
    MockPaymentDepositRepository.findOneOrFail
      .mockImplementationOnce(() =>
        Promise.resolve({
          ...mockPaymentDeposit,
          hospitalId: mockProfile.hospitalId,
        }),
      )
      .mockImplementationOnce(() =>
        Promise.resolve({
          ...mockPaymentDeposit,
          hospitalId: mockProfile.hospitalId,
        }),
      );
    await service.updateRefundPaymentDeposit(
      mockProfile,
      'id',
      mockPaymentDeposit,
    );
    expect(MockPaymentDepositRepository.update).toHaveBeenCalledWith(
      'id',
      expect.objectContaining({
        ...mockPaymentDeposit,
        updatedBy: mockProfile,
      }),
    );

    await service.updateRefundPaymentDeposit(mockProfile, 'id', {
      ...mockPaymentDeposit,
      amountRefunded: 2,
    });
    expect(MockPaymentDepositRepository.update).toHaveBeenNthCalledWith(
      1,
      'id',
      expect.objectContaining({
        ...mockPaymentDeposit,
        updatedBy: mockProfile,
      }),
    );
  });

  it('updatePaymentDeposit', async () => {
    MockPaymentDepositRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...mockPaymentDeposit,
        hospitalId: mockProfile.hospitalId,
      }),
    );
    const res = await service.updatePaymentDeposit(
      mockProfile,
      'id',
      mockPaymentDeposit,
    );
    expect(MockPaymentDepositRepository.update).toHaveBeenCalledWith(
      'id',
      expect.anything(),
    );
    expect(res).toEqual(expect.objectContaining({ id: mockPaymentDeposit.id }));
  });

  it('updatePaymentDeposit should throw if user is not permitted', async () => {
    const res = service.updatePaymentDeposit(
      mockProfile,
      'id',
      mockPaymentDeposit,
    );
    await expect(res).rejects.toThrow(
      new ForbiddenException('Not Authorized To Modify This Record'),
    );
  });

  it('archivePaymentDeposits', async () => {
    MockPaymentDepositRepository.find.mockImplementationOnce(() =>
      Promise.resolve([
        { ...mockPaymentDeposit, hospitalId: mockProfile.hospitalId },
      ]),
    );
    const res = await service.archivePaymentDeposits(mockProfile, ['id'], true);
    expect(MockPaymentDepositRepository.update).toHaveBeenCalledWith(
      [mockPaymentDeposit.id],
      { archived: true },
    );
    expect(res).toEqual([
      { ...mockPaymentDeposit, hospitalId: mockProfile.hospitalId },
    ]);
  });

  it('archivePaymentDeposits should throw if mutator is not permitted to archive', async () => {
    const res = service.archivePaymentDeposits(mockProfile, ['id'], true);
    await expect(res).rejects.toThrow(
      new ForbiddenException('Not Authorized To Archive This Record'),
    );
  });

  it('deletePaymentDeposits', async () => {
    MockPaymentDepositRepository.find.mockImplementationOnce(() =>
      Promise.resolve([
        {
          ...mockPaymentDeposit,
          hospitalId: mockProfile.hospitalId,
        },
      ]),
    );
    const res = await service.deletePaymentDeposits(mockProfile, ['id']);
    expect(MockPaymentDepositRepository.delete).toHaveBeenCalledWith([
      mockPaymentDeposit.id,
    ]);
    expect(res).toEqual([
      { ...mockPaymentDeposit, hospitalId: mockProfile.hospitalId },
    ]);
  });

  it('deletePaymentDeposits should throw if mutator is not permitted to delete', async () => {
    const res = service.deletePaymentDeposits(mockProfile, ['id']);
    await expect(res).rejects.toThrow(
      new ForbiddenException('Not Authorized To Delete This Record'),
    );
  });

  it('getPaymentDepositSummary', async () => {
    const res = await service.getPaymentDepositSummary(mockProfile, 'id');
    expect(
      MockPaymentDepositRepository.getPaymentDepositSummary,
    ).toHaveBeenCalledWith(dsMock, 'id', mockProfile.hospitalId, 'KOBO');
    expect(res).toEqual({ balance: 1000, currency: 'KOBO' });
  });

  it('makeServicePaymentFromDeposit', async () => {
    const res = await service.makeServicePaymentFromDeposit(
      mockProfile,
      mockPaymentDeposit,
      1000,
      'KOBO',
    );
    expect(MockPaymentDepositRepository.save).toHaveBeenCalled();
    expect(res).toEqual(
      expect.objectContaining({
        id: mockPaymentDeposit.id,
      }),
    );
  });
});
