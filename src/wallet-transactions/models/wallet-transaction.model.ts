import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsEmpty, IsInt, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BankAccountInformation } from '@clinify/payouts/dtos/bank-account-information';
import { Currency } from '@clinify/shared/enums/currency';
import {
  TransactionStatus,
  TransactionType,
} from '@clinify/shared/enums/transaction';
import { WalletModel } from '@clinify/wallets/models/wallet.model';

@ObjectType()
@Entity('wallet_transactions')
export class WalletTransactionModel {
  @IsEmpty()
  @IsUUID('4')
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate: Date;

  @Field(() => WalletModel, { nullable: true })
  @ManyToOne(() => WalletModel, (fromWallet) => fromWallet.id, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn({ name: 'sender_wallet' })
  senderWallet: WalletModel;

  @Field({ nullable: true })
  @Column({ name: 'sender_wallet', nullable: true })
  senderWalletId?: string;

  @Field(() => BankAccountInformation, { nullable: true })
  @Column({ name: 'sender_bank_details', type: 'jsonb', nullable: true })
  senderBankDetails?: BankAccountInformation;

  @Field(() => WalletModel, { nullable: true })
  @ManyToOne(() => WalletModel, (toWallet) => toWallet.id, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn({ name: 'receiver_wallet' })
  receiverWallet: WalletModel;

  @Field()
  @Column({ name: 'receiver_wallet', nullable: true }) // TODO: update migration to make this column nullable
  receiverWalletId: string;

  @IsInt()
  @Field(() => Number, { nullable: true })
  @Column({
    name: 'sender_initial_balance',
    type: 'numeric',
    nullable: true,
  })
  senderInitialBalance: number;

  @IsInt()
  @Field(() => Number, { nullable: true })
  @Column({
    name: 'reciever_initial_balance',
    type: 'numeric',
    nullable: true,
  })
  recieverInitialBalance: number;

  @IsInt()
  @Field(() => Number, { nullable: false })
  @Column({
    type: 'numeric',
    nullable: false,
  })
  amount: number;

  @IsInt()
  @Field(() => Number, { nullable: false })
  @Column({
    type: 'numeric',
    nullable: false,
    default: 0,
  })
  commisionPercent: number;

  @IsInt()
  @Field(() => Number, { nullable: false })
  @Column({
    type: 'numeric',
    nullable: false,
    default: 0,
  })
  amountDeducted: number;

  @IsInt()
  @Field(() => Number, { nullable: false })
  @Column({
    type: 'numeric',
    nullable: false,
    default: 0,
  })
  amountSent: number;

  @Field(() => String, { nullable: false })
  @Column({
    name: 'transaction_reference',
    type: 'text',
    nullable: false,
    unique: true,
  })
  transactionReference: string; // fk can be in subscriptions, drug payment, funding ref etc.

  @Field(() => Currency, { nullable: false })
  @Column({
    type: 'enum',
    enum: Currency,
    nullable: false,
  })
  currency: Currency;

  @Field(() => String, { nullable: false })
  @Column({
    type: 'text',
    nullable: false,
    default: 'new transaction',
  })
  description: string;

  @Field(() => String, { nullable: false })
  @Column({
    type: 'text',
    nullable: false,
    default: 'new transaction',
    name: 'transaction_details',
  })
  transactionDetails: string;

  @Field(() => TransactionType, { nullable: false })
  @Column({
    name: 'transaction_type',
    type: 'enum',
    enum: TransactionType,
    nullable: false,
  })
  transactionType: TransactionType;

  @Field(() => TransactionStatus, { nullable: false })
  @Column({
    name: 'transaction_status',
    type: 'enum',
    enum: TransactionStatus,
    nullable: false,
    default: TransactionStatus.INIT,
  })
  transactionStatus: TransactionStatus;

  @Column({ name: 'archived', default: false })
  archived: boolean;

  constructor(walletTransaction: Partial<WalletTransactionModel>) {
    Object.assign(this, walletTransaction);
  }
}
