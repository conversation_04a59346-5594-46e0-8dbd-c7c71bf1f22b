import { Inject, UseGuards } from '@nestjs/common';
import { Args, Mutation, Query, Resolver, Subscription } from '@nestjs/graphql';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { GenerateTransactionReferenceInput } from '../inputs/init-transaction.input';
import { WalletTransactionFilter } from '../inputs/wallet-transaction-filter.input';
import { WalletTransactionModel } from '../models/wallet-transaction.model';
import { InitTransactionResponse } from '../responses/init-transaction.response';
import { WalletTransactionResponse } from '../responses/wallet-transaction.response';
import { WalletTransactionService } from '../services/wallet-transaction.service';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { RolesAuthGuard } from '@clinify/authentication/guards/roles.guard';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { AppServices } from '@clinify/shared/enums/services';
import { UserType } from '@clinify/shared/enums/users';
import { TransactionReferenceService } from '@clinify/shared/services/transaction-reference.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import {
  filterArchiveWallet,
  filterWalletTransactionEvent,
} from '@clinify/utils/subscriptions/filters';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const { WalletTranscationArchived, WalletTransactionEvent } = SubscriptionTypes;

@UseGuards(GqlAuthGuard)
@LogService(AppServices.Bill)
@Resolver(() => WalletTransactionModel)
export class WalletTransactionResolver {
  constructor(
    private readonly transactionReferenceService: TransactionReferenceService,
    private readonly walletTransactionService: WalletTransactionService,
    @Inject(PUB_SUB) private pubSub: RedisPubSub,
  ) {}

  @Mutation(() => InitTransactionResponse)
  @UseGuards(RolesAuthGuard(UserType.Patient, UserType.Patient))
  async generateTransactionReference(
    @Args({
      name: 'referenceDetail',
      type: () => GenerateTransactionReferenceInput,
      nullable: false,
    })
    { walletId, transactionType }: GenerateTransactionReferenceInput,
  ): Promise<InitTransactionResponse> {
    return this.transactionReferenceService.createTransactionReference(
      walletId,
      transactionType,
    );
  }

  @Query(() => WalletTransactionModel)
  async getWalletTransaction(
    @Args('transactionId') transactionId: string,
    @CurrentProfile() profile: ProfileModel,
  ): Promise<WalletTransactionModel> {
    return this.walletTransactionService.getWalletTransaction(
      profile,
      transactionId,
    );
  }

  @Query(() => WalletTransactionResponse)
  async getUserWalletTransactions(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'filter',
      type: () => WalletTransactionFilter,
      nullable: true,
    })
    options?: Partial<WalletTransactionFilter>,
  ): Promise<WalletTransactionResponse> {
    return this.walletTransactionService.getWalletTransactions(
      profile,
      options,
    );
  }

  @Mutation(() => [WalletTransactionModel])
  async archiveWalletTransaction(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) transactionIds: string[],
    @Args({
      name: 'archive',
      type: () => Boolean,
      defaultValue: true,
    })
    archive: boolean,
  ): Promise<WalletTransactionModel[]> {
    const item = await this.walletTransactionService.archiveWalletTransaction(
      profile,
      transactionIds,
      archive,
    );
    await this.pubSub.publish(WalletTranscationArchived, {
      [WalletTranscationArchived]: item,
      profile,
    });
    return item;
  }

  @Subscription(() => [WalletTransactionModel], {
    name: WalletTranscationArchived,
    filter: filterArchiveWallet,
  })
  walletTranscationArchivedHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(WalletTranscationArchived);
  }

  @Subscription(() => WalletTransactionModel, {
    name: WalletTransactionEvent,
    filter: filterWalletTransactionEvent,
  })
  walletTransactionAddedSubsHandler(
    @Args('walletId', { nullable: true }) _walletId?: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(WalletTransactionEvent);
  }
}
