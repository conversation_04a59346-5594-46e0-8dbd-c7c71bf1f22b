import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { INestApplication, ValidationPipe } from '@nestjs/common';

import { EventEmitterModule } from '@nestjs/event-emitter';
import { GraphQLModule } from '@nestjs/graphql';

import { getModelToken } from '@nestjs/mongoose';
import { Test } from '@nestjs/testing';
import { getRepositoryToken, TypeOrmModule } from '@nestjs/typeorm';
import request from 'supertest';
import { AppointmentModule } from '../../appointments/module';
import { WalletTransactionModel } from '../models/wallet-transaction.model';
import { WalletTransactionService } from '../services/wallet-transaction.service';
import { WalletTransactionModule } from '../wallet-transaction.module';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { TestDataSourceOptions } from '@clinify/data-source';
import {
  DefaultInvImage,
  InventoryUpload,
} from '@clinify/integrations/inventory-blk/entities/FileUpload.entity';
import { PriceUpload } from '@clinify/integrations/price-blk/entities/PriceFileUpload.entity';
import { TransactionType } from '@clinify/shared/enums/transaction';
import { UserType } from '@clinify/shared/enums/users';
import { TransactionReferenceService } from '@clinify/shared/services/transaction-reference.service';
import { walletTransactionFactory } from '@mocks/factories/wallet-transaction.factory';
import { walletFactory } from '@mocks/factories/wallet.factory';
import gqlAuthGuardMock from '@mocks/gqlAuthGuard.mock';

jest.mock('@aws-sdk/client-bedrock-runtime', () => {
  const mockInvokeModelCommand = jest.fn();
  return {
    BedrockRuntimeClient: jest.fn(() => ({
      send: jest.fn(),
    })),
    InvokeModelCommand: mockInvokeModelCommand,
  };
});

const walletTransaction = walletTransactionFactory.build();
const wallet = walletFactory.build();

const mockWalletTransactionRepository = {
  saveWalletTransaction: jest.fn(() => walletTransaction),
  getWalletTransactions: jest.fn(() => [walletTransaction]),
  create: jest.fn(() => walletTransaction),
  getWalletTransaction: jest.fn(() => walletTransaction),
};

const mockWalletTransactionService = {
  createWalletTransaction: jest.fn(() => walletTransaction),
  getWalletTransaction: jest.fn(() => walletTransaction),
  getWalletTransactions: jest.fn(() => ({
    list: [walletTransaction],
    totalCount: 1,
  })),
};

const mockTransactionReferenceService = {
  createTransactionReference: jest.fn(() => ({
    id: 'walletTransactionReference',
  })),
};

describe('walletTransactionResolver', () => {
  let testHttpServer: request.SuperTest<request.Test>;
  let app: INestApplication;

  beforeAll(async () => {
    const builder = await Test.createTestingModule({
      imports: [
        WalletTransactionModule,
        TypeOrmModule.forRoot(TestDataSourceOptions),
        EventEmitterModule.forRoot(),
        GraphQLModule.forRoot<ApolloDriverConfig>({
          debug: false,
          playground: false,
          driver: ApolloDriver,
          autoSchemaFile: true,
          include: [AppointmentModule, WalletTransactionModule],
          context: ({ req }) => ({ req }),
        }),
      ],
    })
      .overrideProvider(getRepositoryToken(WalletTransactionModel))
      .useValue(mockWalletTransactionRepository)
      .overrideProvider(WalletTransactionService)
      .useValue(mockWalletTransactionService)
      .overrideProvider(TransactionReferenceService)
      .useValue(mockTransactionReferenceService)
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .overrideGuard(GqlAuthGuard)
      .useValue(gqlAuthGuardMock(UserType.Patient))
      .compile();

    app = builder.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    testHttpServer = request(app.getHttpServer());
  });

  afterAll(async () => await app.close());

  it('initTransaction should create a transaction reference code', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation{
          generateTransactionReference(
            referenceDetail: {
            walletId: "${wallet.id}"
            transactionType: ${TransactionType.TOPUP}
          }){
               id
             }
           }`,
      })
      .expect(({ body }) => {
        const { generateTransactionReference } = body.data;
        expect(
          mockTransactionReferenceService.createTransactionReference,
        ).toBeCalled();
        expect(generateTransactionReference.id).toEqual(
          'walletTransactionReference',
        );
      })
      .expect(200)
      .end(done);
  });

  it('getWalletTransaction should return a specified transaction detail', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query{
          getWalletTransaction(
            transactionId: "${walletTransaction.id}"
          ){
               id
             }
           }`,
      })
      .expect(({ body }) => {
        const { getWalletTransaction } = body.data;
        expect(mockWalletTransactionService.getWalletTransaction).toBeCalled();
        expect(getWalletTransaction.id).toEqual(walletTransaction.id);
      })
      .expect(200)
      .end(done);
  });

  it('getUserWalletTransactions should return a specified transaction detail', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query{
          getUserWalletTransactions(
            filter: {
              skip: ${0},
              take: ${50}
            }
          ){
            list {
              amount
              id
            }
            totalCount
          }
        }`,
      })
      .expect(({ body }) => {
        const { getUserWalletTransactions } = body.data;
        expect(mockWalletTransactionService.getWalletTransactions).toBeCalled();
        expect(getUserWalletTransactions.list[0].id).toEqual(
          walletTransaction.id,
        );
      })
      .expect(200)
      .end(done);
  });
});
