import { Test, TestingModule } from '@nestjs/testing';
import { WalletTransactionResolver } from './wallet-transaction.resolver';
import { WalletTransactionService } from '../services/wallet-transaction.service';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { userFactory } from '@clinify/__mocks__/factories/user.factory';
import { TransactionType } from '@clinify/shared/enums/transaction';
import { TransactionReferenceService } from '@clinify/shared/services/transaction-reference.service';
import { walletTransactionFactory } from '@mocks/factories/wallet-transaction.factory';
import { walletFactory } from '@mocks/factories/wallet.factory';

const profile = profileFactory.build();
const walletTransaction = walletTransactionFactory.build();
const wallet = walletFactory.build();
const user = userFactory.build();
const clinifyId = 'clinify-id';

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

const mockWalletTransactionService = {
  createWalletTransaction: jest.fn(() => walletTransaction),
  getWalletTransaction: jest.fn(() => walletTransaction),
  getWalletTransactions: jest.fn(() => [walletTransaction]),
  archiveWalletTransaction: jest.fn(() => [walletTransaction]),
};

const mockTransactionReferenceService = {
  createTransactionReference: jest.fn(() => 'walletTransactionReference'),
};

describe('WalletTransactionResolver', () => {
  let resolver: WalletTransactionResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WalletTransactionResolver,
        TransactionReferenceService,
        WalletTransactionService,
        {
          provide: WalletTransactionService,
          useValue: mockWalletTransactionService,
        },
        {
          provide: TransactionReferenceService,
          useValue: mockTransactionReferenceService,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
      ],
    }).compile();

    resolver = module.get<WalletTransactionResolver>(WalletTransactionResolver);
    jest.clearAllMocks();
  });

  it('initTransaction() should generate a transaction reference code', async () => {
    const response = await resolver.generateTransactionReference({
      walletId: wallet.id,
      transactionType: TransactionType.TOPUP,
    });
    expect(response).toEqual('walletTransactionReference');
  });

  it('getWalletTransaction() should return a wallet transaction instance', async () => {
    const response = await resolver.getWalletTransaction(
      wallet.profile,
      walletTransaction.id,
    );
    expect(response).toEqual(walletTransaction);
  });

  it('getUserWalletTransactions() should return all profile wallet transactions', async () => {
    const response = await resolver.getUserWalletTransactions(user, {
      skip: 0,
      take: 10,
    });
    expect(response[0]).toEqual(walletTransaction);
  });

  it('getUserWalletTransactions() should call the archiveWalletTransaction service', async () => {
    const response = await resolver.archiveWalletTransaction(
      profile,
      ['first-transaction', 'second-transaction'],
      true,
    );
    expect(
      mockWalletTransactionService.archiveWalletTransaction,
    ).toHaveBeenCalledWith(
      profile,
      ['first-transaction', 'second-transaction'],
      true,
    );
    expect(response[0]).toEqual(walletTransaction);
  });

  it('getUserWalletTransactions() should call the archiveWalletTransaction service', async () => {
    const response = await resolver.archiveWalletTransaction(
      profile,
      ['first-transaction', 'second-transaction'],
      false,
    );
    expect(
      mockWalletTransactionService.archiveWalletTransaction,
    ).toHaveBeenCalledWith(
      profile,
      ['first-transaction', 'second-transaction'],
      false,
    );
    expect(response[0]).toEqual(walletTransaction);
  });

  it('billingEventSubsHandler() should trigger WalletTranscationArchived subscription', () => {
    resolver.walletTranscationArchivedHandler(clinifyId);
    expect(pubSubMock.asyncIterator).toHaveBeenLastCalledWith(
      'WalletTranscationArchived',
    );
  });

  it('walletTransactionAddedSubsHandler', () => {
    resolver.walletTransactionAddedSubsHandler('id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalletTransactionEvent',
    );
  });
});
