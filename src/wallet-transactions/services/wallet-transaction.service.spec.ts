import { Test } from '@nestjs/testing';
import Chance from 'chance';
import { EntityManager } from 'typeorm';
import { WalletTransactionService } from './wallet-transaction.service';
import { loggerMock } from '@clinify/__mocks__/logger';
import { AccountHolder } from '@clinify/shared/enums/account-holder';
import { Currency } from '@clinify/shared/enums/currency';
import {
  TransactionStatus,
  TransactionType,
} from '@clinify/shared/enums/transaction';
import { TransactionProfileService } from '@clinify/shared/services/transacting-profile.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { WalletTransactionRepository } from '@clinify/wallet-transactions/repositories/wallet-transaction.repository';
import { WalletModel } from '@clinify/wallets/models/wallet.model';
import { WalletRepository } from '@clinify/wallets/repositories/wallet.repository';
import { managerMock } from '@mocks/database.mock';
import { userFactory } from '@mocks/factories/user.factory';
import { walletTransactionFactory } from '@mocks/factories/wallet-transaction.factory';
import { walletFactory } from '@mocks/factories/wallet.factory';
import { PubSubMock } from '@mocks/pub-sub.mock';

const chance = new Chance();

const mockUser = userFactory.build();
const mockSenderWallet = walletFactory.build();
const mockReceiverWallet = walletFactory.build();

const profile = new ProfileModel();
profile.type = 'Patient';
profile.isDefault = true;
profile.id = chance.guid({ version: 4 });

const ManagerMock: any = {
  queryRunner: { isTransactionActive: true },
  withRepository: jest.fn((value) => value),
};

const depositWalletTransaction = {
  ...walletTransactionFactory.build(),
  senderWallet: null,
};

const mockWalletTransactionRepository = {
  saveWalletTransaction: jest.fn(() => depositWalletTransaction),
  getWalletTransactions: jest.fn(() => [depositWalletTransaction]),
  create: jest.fn(() => transferWalletTransaction),
  getWalletTransaction: jest.fn(() => depositWalletTransaction),
  updateWalletTransaction: jest.fn(() => depositWalletTransaction),
  save: jest.fn(() => depositWalletTransaction),
  findOne: jest.fn(() => depositWalletTransaction),
  archiveWalletTransaction: jest.fn(() => [
    depositWalletTransaction,
    transferWalletTransaction,
  ]),
  byClinifyId: jest.fn(),
};

const mockWalletRepository = {
  save: jest.fn(() => wallets[0]),
  update: jest.fn(),
};

const mockTransactionProfileService = {
  getTransactingProfile: jest.fn(() => ({
    profileType: 'profile',
    profile: { id: 'testId', clinifyId: '1234qwertt' },
  })),
};

const transferWalletTransaction = walletTransactionFactory.build();
const wallets = walletFactory.buildList(2);
describe('WalletTransactionService', () => {
  let service: WalletTransactionService;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [],
      providers: [
        WalletTransactionService,
        {
          provide: WalletTransactionRepository,
          useValue: mockWalletTransactionRepository,
        },
        {
          provide: TransactionProfileService,
          useValue: mockTransactionProfileService,
        },
        {
          provide: WalletRepository,
          useValue: mockWalletRepository,
        },
        {
          provide: EntityManager,
          useValue: ManagerMock,
        },
        { ...loggerMock },
        { provide: PUB_SUB, useValue: PubSubMock },
      ],
    }).compile();
    service = module.get(WalletTransactionService);
  });

  it('deductTransactionFee() should remove the approprate percentage transaction fee for payment', async () => {
    const amount = await service.deductTransactionFee(
      TransactionType.PAYMENT,
      1000,
      {
        ...wallets[0],
        holderType: AccountHolder.User,
        profile: { ...profile, transactionCharge: 10 },
      },
    );
    expect(amount).toEqual(900);
  });

  it('deductTransactionFee() should not remove transaction fee for payment if it is not specified', async () => {
    const amount = await service.deductTransactionFee(
      TransactionType.PAYMENT,
      1000,
      { ...wallets[0], profile: {} },
    );
    expect(amount).toEqual(1000);
  });

  it('deductTransactionFee() transaction fee should not be removed if transaction is transfer', async () => {
    const amount = await service.deductTransactionFee(
      TransactionType.TRANSFER,
      1000,
      { ...wallets[0], profile: { ...profile, transactionCharge: 10 } },
    );
    expect(amount).toEqual(1000);
  });

  it('deductTransactionFee() transaction fee should be removed if transaction is CANCELLATION_LEVY', async () => {
    service.clinifyCancellationCommission = 10;
    const amount = await service.deductTransactionFee(
      TransactionType.CANCELLATION_LEVY,
      1000,
      { ...wallets[0], profile: { ...profile, transactionCharge: 10 } },
    );
    expect(amount).toEqual(900);
  });

  it('deductTransactionFee() transaction fee should not be removed if charge is not specified', async () => {
    service.clinifyCancellationCommission = 0;
    const amount = await service.deductTransactionFee(
      TransactionType.CANCELLATION_LEVY,
      1000,
      { ...wallets[0], profile: { ...profile, transactionCharge: 10 } },
    );
    expect(amount).toEqual(1000);
  });

  it('createWalletTransaction() should return a wallet walletTransaction for a transfer', async () => {
    const user = { ...mockUser, defaultProfile: undefined };
    user.profiles = [];

    await service.createWalletTransaction(
      transferWalletTransaction,
      ManagerMock,
    );
    expect(mockWalletTransactionRepository.save).toBeCalled();
  });

  it('createWalletTransaction() should return a wallet walletTransaction for a Lcancellation Levy', async () => {
    const user = { ...mockUser, defaultProfile: undefined };
    user.profiles = [];
    mockWalletTransactionRepository.findOne = jest
      .fn()
      .mockResolvedValueOnce(user);
    const cancellationTransaction = {
      ...transferWalletTransaction,
      transactionType: TransactionType.CANCELLATION_LEVY,
    };
    mockWalletTransactionRepository.create = jest
      .fn()
      .mockImplementationOnce(() => cancellationTransaction);
    await service.createWalletTransaction(cancellationTransaction, ManagerMock);
    expect(mockWalletTransactionRepository.save).toBeCalled();
  });

  it('createWalletTransaction() should return a wallet walletTransaction for a payment request', async () => {
    const user = { ...mockUser, defaultProfile: undefined };
    user.profiles = [];
    mockWalletTransactionRepository.findOne = jest
      .fn()
      .mockResolvedValueOnce(user);
    mockWalletTransactionRepository.create = jest.fn(
      () => transferWalletTransaction,
    );
    await service.createWalletTransaction(
      {
        ...transferWalletTransaction,
        description: null,
        transactionType: TransactionType.PAYMENT,
      },
      ManagerMock,
    );
    expect(mockWalletTransactionRepository.save).toBeCalled();
  });

  it('createWalletTransaction() should return a wallet walletTransaction for a transfer', async () => {
    const receiverWallet = new WalletModel(wallets[0]); // patient wallet
    receiverWallet.id = chance.guid({ version: 4 });
    const senderWallet = new WalletModel(wallets[1]); // patient wallet
    senderWallet.id = chance.guid({ version: 4 });
    const amount = 1000;
    const walletTransaction = transferWalletTransaction;
    walletTransaction.receiverWallet = receiverWallet;
    walletTransaction.amount = amount;
    walletTransaction.toInitialBalance = 0;
    walletTransaction.fromInitialBalance = amount;
    mockWalletTransactionRepository.create = jest.fn(() => walletTransaction);
    await service.createWalletTransaction(
      {
        senderWallet,
        receiverWallet,
        amount,
        transactionType: TransactionType.TRANSFER,
        transactionReference: chance.apple_token(),
        transactionStatus: TransactionStatus.SUCCESS,
        description: 'Transfer',
      },
      ManagerMock,
    );
    expect(mockWalletTransactionRepository.save).toBeCalled();
  });

  it('should throw an error for invalid transaction when amount less than or equal to 0', async () => {
    const receiverWallet = new WalletModel(wallets[0]); // patient wallet
    receiverWallet.id = chance.guid({ version: 4 });
    const senderWallet = new WalletModel(wallets[1]); // patient wallet
    senderWallet.id = chance.guid({ version: 4 });
    const amount = 0;
    const walletTransaction = transferWalletTransaction;
    walletTransaction.receiverWallet = senderWallet;
    walletTransaction.amount = amount;
    walletTransaction.toInitialBalance = senderWallet.getTotalBalance();
    walletTransaction.fromInitialBalance = 0;
    mockWalletTransactionRepository.create = jest.fn(() => walletTransaction);
    try {
      await service.createWalletTransaction(
        {
          receiverWallet,
          senderWallet,
          amount,
          transactionType: TransactionType.TRANSFER,
          transactionReference: chance.apple_token(),
          transactionStatus: TransactionStatus.SUCCESS,
          description: 'Transfer',
        },
        ManagerMock,
      );
    } catch (err) {
      expect(err.response.message).toStrictEqual('Invalid Wallet Transaction');
    }
  });

  it('createWalletTransaction() should return a wallet walletTransaction for a deposit', async () => {
    const receiverWallet = new WalletModel(wallets[1]); // patient wallet
    receiverWallet.id = chance.guid({ version: 4 });
    const amount = 1000;
    const walletTransaction = depositWalletTransaction;
    walletTransaction.receiverWallet = receiverWallet;
    walletTransaction.amount = amount;
    walletTransaction.fromInitialBalance = 0;
    walletTransaction.toInitialBalance = amount;
    mockWalletTransactionRepository.create = jest.fn(
      () => depositWalletTransaction,
    );

    await service.createWalletTransaction(
      {
        receiverWallet,
        amount,
        transactionType: TransactionType.TOPUP,
        transactionReference: chance.apple_token(),
        transactionStatus: TransactionStatus.SUCCESS,
        description: 'Deposit',
      },
      ManagerMock,
    );
    expect(mockWalletTransactionRepository.save).toBeCalled();
  });

  it('createWalletTransaction() should throw an error when wallet type is not supplied', async () => {
    const receiverWallet = new WalletModel(wallets[0]); // patient wallet
    const amount = 1000;
    const walletTransaction = transferWalletTransaction;
    walletTransaction.receiverWallet = receiverWallet;
    walletTransaction.amount = amount;
    walletTransaction.toInitialBalance = receiverWallet.getTotalBalance();
    walletTransaction.fromInitialBalance = 0;
    mockWalletTransactionRepository.create = jest.fn(() => walletTransaction);
    const expectedValue = walletTransaction;
    expectedValue.receiverWallet.totalReceived = 1000;
    expectedValue.currency = Currency.KOBO;
    expectedValue.description = 'Card Deposit Top Up';
    try {
      await service.createWalletTransaction({
        receiverWallet,
        amount,
        transactionReference: chance.apple_token(),
        transactionStatus: TransactionStatus.SUCCESS,
        description: 'Deposit',
      });
    } catch (error) {
      expect(error.message).toBe('Invalid Wallet Transaction');
    }
  });

  it('Withdrawal should create withdrawal a transaction record', async () => {
    const senderWallet = new WalletModel(wallets[0]); // patient wallet
    const amount = 1000;
    const walletTransaction = transferWalletTransaction;
    walletTransaction.senderWallet = senderWallet;
    walletTransaction.amount = -amount;
    walletTransaction.fromInitialBalance = senderWallet.getTotalBalance();
    walletTransaction.toInitialBalance =
      senderWallet.getTotalBalance() - amount;
    mockWalletTransactionRepository.create = jest.fn(() => walletTransaction);
    const expectedValue = walletTransaction;
    expectedValue.senderWallet.totalSent = amount;
    expectedValue.currency = Currency.KOBO;
    expectedValue.description = 'Card Deposit Top Up';
    try {
      await service.createWalletTransaction({
        senderWallet,
        amount,
        transactionType: TransactionType.WITHDRAWAL,
        transactionReference: chance.apple_token(),
        transactionStatus: TransactionStatus.SUCCESS,
        description: 'Deposit',
      });
    } catch (error) {
      expect(error.message).toBe('Invalid Wallet Transaction');
    }
  });

  it('saveWalletTransactionInTransaction() should throw Error Making Transaction, when transaction reverses', async () => {
    const receiverWallet = new WalletModel(wallets[1]); // patient wallet
    receiverWallet.id = chance.guid({ version: 4 });
    const amount = 1000;
    const walletTransaction = depositWalletTransaction;
    walletTransaction.receiverWallet = receiverWallet;
    walletTransaction.amount = amount;
    walletTransaction.fromInitialBalance = 0;
    walletTransaction.toInitialBalance = amount;
    mockWalletTransactionRepository.create = jest.fn(
      () => depositWalletTransaction,
    );
    mockWalletTransactionRepository.save.mockImplementationOnce(() =>
      Promise.reject(Error('Error Saving Wallet Transaction')),
    );

    try {
      await service.createWalletTransaction({
        receiverWallet,
        amount,
        transactionType: TransactionType.TOPUP,
        transactionReference: chance.apple_token(),
        transactionStatus: TransactionStatus.SUCCESS,
        description: 'Deposit',
      });
    } catch (err) {
      expect(mockWalletTransactionRepository.save).toBeCalled();
      expect(err.response.message).toStrictEqual(
        'Error Saving Wallet Transaction',
      );
    }
  });

  it('getWalletTransactions() should return wallet transactions by a user', async () => {
    const receiverWallet = new WalletModel(wallets[0]);
    const transaction = await service.getWalletTransactions(
      receiverWallet.profile,
      {},
    );
    expect(transaction[0]).toBe(depositWalletTransaction);
  });

  it('getWalletTransaction() should a single wallet transaction record', async () => {
    const transaction = await service.getWalletTransaction(
      profile,
      depositWalletTransaction.id,
    );
    expect(transaction).toBe(depositWalletTransaction);
  });

  it('getWalletTransaction() should return an error when wallet cant be found', async () => {
    const serviceMock = jest.spyOn(
      mockWalletTransactionRepository,
      'getWalletTransaction',
    );
    serviceMock.mockImplementationOnce(() => undefined);
    try {
      await service.getWalletTransaction(profile, depositWalletTransaction.id);
    } catch ({ message }) {
      expect(message).toBe('Wallet Transaction Not Found');
    }
  });

  it('getWalletTransactionByReference() should throw error if Transaction Not Found', async () => {
    mockWalletTransactionRepository.findOne = jest
      .fn()
      .mockResolvedValueOnce(depositWalletTransaction);
    const transaction = await service.getWalletTransactionByReference(
      depositWalletTransaction.transactionReference,
    );
    expect(transaction).toBe(depositWalletTransaction);
  });

  it('updateWalletTransaction() should update a wallet transaction record', async () => {
    const transaction = await service.updateWalletTransaction(
      depositWalletTransaction.id,
      { transactionStatus: TransactionStatus.FAILED },
    );
    expect(transaction).toBe(depositWalletTransaction);
  });

  it('updateWalletTransaction() should throw and error if update fails', async () => {
    mockWalletTransactionRepository.updateWalletTransaction = jest.fn(() => {
      throw Error('failed');
    });
    try {
      await service.updateWalletTransaction(depositWalletTransaction.id, {
        transactionStatus: TransactionStatus.FAILED,
      });
    } catch ({ message }) {
      expect(message).toBe('Error Updating Wallet Transaction');
    }
  });

  it('getUserWalletTransactions() should call the archiveWalletTransaction repository', async () => {
    const response = await service.archiveWalletTransaction(
      profile,
      ['first-transaction', 'second-transaction'],
      true,
    );
    expect(
      mockWalletTransactionRepository.archiveWalletTransaction,
    ).toHaveBeenCalledWith(
      profile,
      ['first-transaction', 'second-transaction'],
      true,
    );
    expect(response).toEqual([
      depositWalletTransaction,
      transferWalletTransaction,
    ]);
  });

  it('getUserWalletTransactions() should call the archiveWalletTransaction repository', async () => {
    const response = await service.archiveWalletTransaction(
      profile,
      ['first-transaction', 'second-transaction'],
      false,
    );
    expect(
      mockWalletTransactionRepository.archiveWalletTransaction,
    ).toHaveBeenCalledWith(
      profile,
      ['first-transaction', 'second-transaction'],
      false,
    );
    expect(response).toEqual([
      depositWalletTransaction,
      transferWalletTransaction,
    ]);
  });

  it('performWalletTransaction', async () => {
    const res = await service.performWalletTransaction(
      managerMock as any,
      'ref',
      mockReceiverWallet,
      100,
      TransactionType.PAYMENT,
      mockSenderWallet,
    );
    expect(res).toEqual(depositWalletTransaction);
  });
});
