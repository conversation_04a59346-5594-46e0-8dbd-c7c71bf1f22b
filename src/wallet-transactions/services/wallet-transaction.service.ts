import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { EntityManager } from 'typeorm';
import { WalletTransactionFilter } from '../inputs/wallet-transaction-filter.input';
import { IWalletTransaction } from '../interfaces/wallet-transaction.interface';
import { WalletTransactionModel } from '../models/wallet-transaction.model';
import { WalletTransactionRepository } from '../repositories/wallet-transaction.repository';
import { WalletTransactionResponse } from '../responses/wallet-transaction.response';
import { config } from '@clinify/config';
import { BankAccountInformation } from '@clinify/payouts/dtos/bank-account-information';
import { Currency } from '@clinify/shared/enums/currency';
import {
  TransactionStatus,
  TransactionType,
} from '@clinify/shared/enums/transaction';
import { generateWalletTransactionDescription } from '@clinify/shared/helper';
import { TransactionProfileService } from '@clinify/shared/services/transacting-profile.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';
import { WalletModel } from '@clinify/wallets/models/wallet.model';
import { WalletRepository } from '@clinify/wallets/repositories/wallet.repository';

const { WalletBalance } = SubscriptionTypes;

@Injectable()
export class WalletTransactionService {
  public constructor(
    private readonly walletTransactionRepository: WalletTransactionRepository,
    private readonly walletRepository: WalletRepository,
    private readonly transactionProfileService: TransactionProfileService,
    @Inject(EntityManager) readonly entityManager: EntityManager,
    @Inject(PUB_SUB) private readonly pubSub: RedisPubSub,
  ) {}

  clinifyCancellationCommission = config.cancellationLevyClinifyCommission;

  async getWalletTransactions(
    profile: ProfileModel,
    options?: WalletTransactionFilter,
  ): Promise<WalletTransactionResponse> {
    const { profileType, ...profileDataByType } =
      this.transactionProfileService.getTransactingProfile(profile);
    const transactions =
      await this.walletTransactionRepository.getWalletTransactions(
        profileDataByType[profileType].id,
        profileType,
        options,
      );
    return transactions;
  }

  async getWalletTransaction(
    profile: ProfileModel,
    transactionId: string,
  ): Promise<WalletTransactionModel> {
    const { profileType, ...profileDataByType } =
      this.transactionProfileService.getTransactingProfile(profile);
    const transaction =
      await this.walletTransactionRepository.getWalletTransaction(
        profileDataByType[profileType].id,
        profileType,
        transactionId,
      );
    if (!transaction) {
      throw new NotFoundException('Wallet Transaction Not Found');
    }
    return transaction;
  }

  async getWalletTransactionByReference(
    transactionReference: string,
  ): Promise<WalletTransactionModel> {
    const transaction = await this.walletTransactionRepository.findOne({
      where: {
        transactionReference,
      },
      relations: [
        'receiverWallet',
        'receiverWallet.profile',
        'receiverWallet.hospital',
        'senderWallet',
        'senderWallet.profile',
        'senderWallet.hospital',
      ],
    });
    return transaction;
  }

  async createWalletTransaction(
    {
      senderWallet,
      receiverWallet,
      amount,
      transactionType,
      transactionDetails,
      transactionReference,
      transactionStatus,
      description,
      currency,
    }: Partial<IWalletTransaction>,
    entityManager?: Partial<EntityManager>,
  ): Promise<WalletTransactionModel> {
    const manager = entityManager || this.entityManager;
    const repository = manager?.withRepository(
      this.walletTransactionRepository,
    );
    const walletRepo = manager?.withRepository(this.walletRepository);

    const walletTransaction: WalletTransactionModel = repository.create({
      senderInitialBalance: senderWallet?.getTotalBalance(),
      recieverInitialBalance: receiverWallet?.getTotalBalance(),
      transactionDetails,
      amount,
      transactionType,
      transactionReference,
      transactionStatus,
    });

    const transactiondescription = description ? `| ${description}` : '';

    const amountToSend = this.deductTransactionFee(
      transactionType,
      amount,
      receiverWallet,
    );

    switch (transactionType) {
      case TransactionType.TOPUP:
        receiverWallet.totalReceived += amount;
        walletTransaction.amountSent = amountToSend;
        walletTransaction.receiverWallet = receiverWallet;
        walletTransaction.currency = receiverWallet.currency;
        walletTransaction.description =
          `By Card Deposit ${transactiondescription}`.trim();
        break;
      case TransactionType.WITHDRAWAL:
        senderWallet.totalSent += amount;
        walletTransaction.senderWallet = senderWallet;
        walletTransaction.amountSent = amountToSend;
        walletTransaction.currency = senderWallet.currency;
        walletTransaction.description =
          `Withdraw to bank ${transactiondescription}`.trim();
        break;
      case TransactionType.CANCELLATION_LEVY:
        walletTransaction.commisionPercent = this.clinifyCancellationCommission;
        receiverWallet.totalReceived += amountToSend;
        senderWallet.totalSent += amount;
        walletTransaction.amountDeducted = amount - amountToSend;
        walletTransaction.amountSent = amountToSend;
        walletTransaction.currency = senderWallet.currency;
        walletTransaction.description = description;
        walletTransaction.senderWallet = senderWallet;
        walletTransaction.receiverWallet = receiverWallet;
        break;
      case TransactionType.PAYMENT: // fallthrough
        walletTransaction.commisionPercent =
          receiverWallet.profile?.transactionCharge || 0;
        walletTransaction.receiverWallet = receiverWallet;
        walletTransaction.currency = currency;
        break;
      case TransactionType.REVERSAL:
      case TransactionType.TRANSFER:
        receiverWallet.totalReceived += amountToSend;
        senderWallet.totalSent += amount;
        walletTransaction.amountDeducted = amount - amountToSend;
        walletTransaction.amountSent = amountToSend;
        walletTransaction.currency = senderWallet.currency;
        walletTransaction.description = description;
        walletTransaction.senderWallet = senderWallet;
        walletTransaction.receiverWallet = receiverWallet;
        break;
      default:
        break;
    }
    if (isValidWalletTransaction(walletTransaction)) {
      const transaction = await this.saveWalletTransactionInTransaction(
        senderWallet,
        receiverWallet,
        walletTransaction,
        repository,
        walletRepo,
      );
      return transaction;
    }
    throw new BadRequestException('Invalid Wallet Transaction');
  }

  async saveWalletTransactionInTransaction(
    senderWallet: WalletModel | null,
    receiverWallet: WalletModel,
    walletTransaction: WalletTransactionModel,
    repository?: WalletTransactionRepository,
    walletRepo?: WalletRepository,
  ): Promise<WalletTransactionModel> {
    try {
      senderWallet && (await walletRepo.save(senderWallet));
      receiverWallet && (await walletRepo.save(receiverWallet));
      const transaction = await repository.save(walletTransaction);
      return transaction;
    } catch (error) {
      throw new BadRequestException('Error Saving Wallet Transaction', error);
    }
  }

  async updateWalletTransaction(
    transactionId: string,
    dataUpdate: Partial<IWalletTransaction>,
  ): Promise<WalletTransactionModel> {
    try {
      const transaction =
        await this.walletTransactionRepository.updateWalletTransaction(
          transactionId,
          dataUpdate,
        );
      return transaction;
    } catch (error) {
      throw new BadRequestException('Error Updating Wallet Transaction', error);
    }
  }

  deductTransactionFee(
    transactionType: TransactionType,
    amount: number,
    receiverWallet: WalletModel,
  ): number {
    switch (transactionType) {
      case TransactionType.PAYMENT:
        return receiverWallet[receiverWallet.holderType]?.transactionCharge > 0
          ? amount -
              amount *
                (receiverWallet[receiverWallet.holderType].transactionCharge /
                  100)
          : amount;
      case TransactionType.CANCELLATION_LEVY:
        return this.clinifyCancellationCommission > 0
          ? amount - amount * (this.clinifyCancellationCommission / 100)
          : amount;
      default:
        return amount;
    }
  }

  async archiveWalletTransaction(
    profile: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<WalletTransactionModel[]> {
    const transaction =
      await this.walletTransactionRepository.archiveWalletTransaction(
        profile,
        ids,
        archive,
      );
    return transaction;
  }

  async performWalletTransaction(
    manager: EntityManager,
    transactionReference: string,
    receiverWallet: WalletModel,
    amount: number,
    transactionType: TransactionType,
    senderWallet?: WalletModel,
    senderBankDetails?: BankAccountInformation,
    currency = Currency.KOBO,
    description?: string,
  ): Promise<WalletTransactionModel> {
    const walletRepo = manager.withRepository(this.walletRepository);
    const walletTrxnRepo = manager.withRepository(
      this.walletTransactionRepository,
    );
    if (senderWallet) {
      if (senderWallet.getTotalBalance() < amount) {
        throw new ConflictException('Insufficient Funds');
      }
      if (transactionType === TransactionType.WITHDRAWAL) {
        await walletRepo.update(
          { id: senderWallet.id },
          { totalWithdrawn: () => `total_withdrawn + ${amount}` },
        );
        this.pubSub.publish(WalletBalance, {
          [WalletBalance]: new WalletModel({
            ...senderWallet,
            totalWithdrawn: Number(senderWallet.totalWithdrawn) + amount,
          }),
        });
      } else {
        await walletRepo.update(
          { id: senderWallet.id },
          { totalSent: Number(senderWallet.totalSent) + amount },
        );
        this.pubSub.publish(WalletBalance, {
          [WalletBalance]: new WalletModel({
            ...senderWallet,
            totalSent: Number(senderWallet.totalSent) + amount,
          }),
        });
      }
    }
    if (receiverWallet) {
      await walletRepo.update(
        { id: receiverWallet.id },
        { totalReceived: () => `total_received + ${amount}` },
      );
      this.pubSub.publish(WalletBalance, {
        [WalletBalance]: new WalletModel({
          ...receiverWallet,
          totalReceived: Number(receiverWallet.totalReceived) + amount,
        }),
      });
    }
    return walletTrxnRepo.save({
      senderWallet,
      receiverWallet,
      description:
        description ?? generateWalletTransactionDescription(transactionType),
      currency,
      amount,
      amountSent: amount,
      senderInitialBalance: senderWallet?.getTotalBalance(),
      recieverInitialBalance: receiverWallet?.getTotalBalance(),
      transactionType,
      senderBankDetails,
      transactionReference,
      transactionStatus: TransactionStatus.SUCCESS,
    });
  }
}

const isValidWalletTransaction = (walletTransaction: WalletTransactionModel) =>
  walletTransaction.amount > 0 &&
  (walletTransaction.senderWallet || walletTransaction.receiverWallet) &&
  walletTransaction.senderWallet?.id !== walletTransaction.receiverWallet?.id;
