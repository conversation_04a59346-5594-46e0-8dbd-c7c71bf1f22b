import { forwardRef, Logger, Module } from '@nestjs/common';
import { WalletTransactionRepository } from './repositories/wallet-transaction.repository';
import { WalletTransactionResolver } from './resolvers/wallet-transaction.resolver';
import { WalletTransactionService } from './services/wallet-transaction.service';
import { SharedModule } from '../shared/module';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import PubSub, { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { WalletRepository } from '@clinify/wallets/repositories/wallet.repository';
import { WalletModule } from '@clinify/wallets/wallet.module';

@Module({
  providers: [
    WalletTransactionService,
    WalletTransactionResolver,
    { provide: PUB_SUB, useFactory: () => PubSub },
    Logger,
  ],
  imports: [
    forwardRef(() => SharedModule),
    TypeormExtendedModule.forCustomRepository([
      WalletTransactionRepository,
      WalletRepository,
    ]),
    forwardRef(() => WalletModule),
  ],
  exports: [WalletTransactionService],
})
export class WalletTransactionModule {}
