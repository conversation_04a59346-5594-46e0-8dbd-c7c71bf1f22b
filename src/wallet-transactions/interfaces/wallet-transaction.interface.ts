import { Currency } from '@clinify/shared/enums/currency';
import {
  TransactionStatus,
  TransactionType,
} from '@clinify/shared/enums/transaction';
import { WalletModel } from '@clinify/wallets/models/wallet.model';

export interface IWalletTransaction {
  id: string;
  createdDate?: Date;
  updatedDate?: Date;
  senderWallet?: WalletModel;
  receiverWallet: WalletModel;
  transactionDetails: string;
  oldBalance: number;
  newBalance: number;
  amount: number;
  transactionReference: string;
  currency: Currency;
  description: string;
  transactionType: TransactionType;
  transactionStatus: TransactionStatus;
}
