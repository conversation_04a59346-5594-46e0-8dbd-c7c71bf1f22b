import { Field, InputType, OmitType } from '@nestjs/graphql';
import { IsOptional } from 'class-validator';
import {
  TransactionStatus,
  TransactionType,
} from '@clinify/shared/enums/transaction';
import { FilterInput } from '@clinify/shared/validators/filter.input';

@InputType()
export class WalletTransactionFilter extends OmitType(FilterInput, [
  'creator',
] as const) {
  @Field(() => TransactionType, { nullable: true })
  @IsOptional()
  transactionType?: TransactionType;

  @Field(() => TransactionStatus, { nullable: true })
  status?: TransactionStatus;
}
