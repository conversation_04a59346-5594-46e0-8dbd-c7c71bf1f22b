import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import moment from 'moment';
import { DataSource, EntityManager } from 'typeorm';
import { WalletTransactionModel } from '../models/wallet-transaction.model';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { TestDataSourceOptions } from '@clinify/data-source';
import { AccountHolder } from '@clinify/shared/enums/account-holder';
import {
  TransactionStatus,
  TransactionType,
} from '@clinify/shared/enums/transaction';
import { createWalletTransactions } from '@clinify/utils/tests/wallet-transaction.fixtures';
import { WalletTransactionRepository } from '@clinify/wallet-transactions/repositories/wallet-transaction.repository';

const chance = new Chance();
describe('walletTransactionRepository', () => {
  let walletTransaction: WalletTransactionModel;
  let dataSource: DataSource;
  let manager: EntityManager;
  let repository: WalletTransactionRepository;

  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeormExtendedModule.forCustomRepository([
          WalletTransactionRepository,
        ]),
      ],
      providers: [],
    }).compile();

    dataSource = module.get<DataSource>(DataSource);
    manager = dataSource.manager;
    repository = module.get<WalletTransactionRepository>(
      WalletTransactionRepository,
    );
  });

  beforeEach(async () => {
    walletTransaction = (await createWalletTransactions(manager))[0];
  });

  afterAll(async () => {
    await dataSource.destroy();
    await module.close();
  });

  it('getWalletTransactions() should return record with searched transaction type', async () => {
    const userTransaction = (
      await createWalletTransactions(manager, 1, {
        transactionType: TransactionType.WITHDRAWAL,
      })
    )[0];

    const returnedTransaction = await repository.getWalletTransactions(
      userTransaction.senderWallet.profile.id,
      AccountHolder.User,
      {
        keyword: 'withdra',
      },
    );

    expect(returnedTransaction.list[0].transactionType).toEqual(
      TransactionType.WITHDRAWAL,
    );
    expect(returnedTransaction.totalCount).toEqual(1);
  });

  it('getWalletTransactions() should return record with searched description', async () => {
    const description = 'This is a sentence';

    const userTransaction = (
      await createWalletTransactions(manager, 1, {
        description,
      })
    )[0];

    const returnedTransaction = await repository.getWalletTransactions(
      userTransaction.senderWallet.profile.id,
      AccountHolder.User,
      {
        keyword: 'sente',
      },
    );

    expect(returnedTransaction.list[0].description).toEqual(description);
  });

  it('getWalletTransactions() should return filtered transactions by walletId', async () => {
    const { createdDate } = walletTransaction;
    const returnedTransaction = await repository.getWalletTransactions(
      walletTransaction.senderWallet.profile.id,
      AccountHolder.User,
      {
        transactionType: TransactionType.TOPUP,
        status: TransactionStatus.SUCCESS,
        dateRange: {
          from: moment(createdDate).startOf('day').toDate(),
          to: moment(createdDate).endOf('day').toDate(),
        },
      },
    );
    expect(returnedTransaction.list[0].transactionType).toEqual(
      TransactionType.TOPUP,
    );
  });

  it('getWalletTransactions() should return transactions by walletId', async () => {
    const returnedTransaction = await repository.getWalletTransactions(
      walletTransaction.senderWallet.profile.id,
      AccountHolder.User,
    );
    expect(returnedTransaction.list[0].transactionType).toEqual(
      TransactionType.TOPUP,
    );
  });

  it('getWalletTransaction() should not return data if transaction doesnt exist', async () => {
    const fictitiousId = '611b1cc6-7741-11eb-96f8-3b0e6dce99d7';
    const savedTransaction = await repository.getWalletTransaction(
      walletTransaction.senderWallet.profile.id,
      AccountHolder.Hospital,
      fictitiousId,
    );
    expect(savedTransaction).toEqual(null);
  });

  it('getWalletTransaction() should get wallet transaction instance by id', async () => {
    const savedTransaction = await repository.getWalletTransaction(
      walletTransaction.receiverWallet.profile.id,
      AccountHolder.User,
      walletTransaction.id,
    );
    expect(savedTransaction.id).toEqual(walletTransaction.id);
  });

  it('updateWalletTransaction() should update a wallet transaction record', async () => {
    const savedTransaction = await repository.updateWalletTransaction(
      walletTransaction.id,
      {
        amount: 40,
      },
    );
    expect(savedTransaction.amount).toEqual(40);
  });

  it('updateWalletTransaction() should return error if record Not Found', async () => {
    const invalidId = '95cafc16-1887-11eb-b608-fb8bfa386f67';
    try {
      await repository.updateWalletTransaction(invalidId, {
        amount: 40,
      });
    } catch ({ message }) {
      expect(message).toEqual('Wallet Transaction Not Found');
    }
  });

  it('archiveWalletTransaction() should archive transactions', async () => {
    const userTransactions = await createWalletTransactions(manager, 3, {
      transactionType: TransactionType.WITHDRAWAL,
    });
    const userTransaction = userTransactions[0];
    const transactions = await repository.getWalletTransactions(
      userTransaction.senderWallet.profile.id,
      AccountHolder.User,
      { archive: false },
    );
    expect(transactions.totalCount).toBe(3);

    const transactions1 = await repository.getWalletTransactions(
      userTransaction.senderWallet.profile.id,
      AccountHolder.User,
      { archive: true },
    );
    expect(transactions1.totalCount).toBe(0);

    await repository.archiveWalletTransaction(
      userTransaction.senderWallet.profile,
      [
        userTransactions[0].id,
        userTransactions[1].id,
        chance.guid({ version: 4 }),
      ],
      true,
    );

    const archivedTransactions = await repository.getWalletTransactions(
      userTransaction.senderWallet.profile.id,
      AccountHolder.User,
      { archive: true },
    );
    expect(archivedTransactions.totalCount).toBe(2);

    const unArchivedTransactions = await repository.getWalletTransactions(
      userTransaction.senderWallet.profile.id,
      AccountHolder.User,
      { archive: false },
    );
    expect(unArchivedTransactions.totalCount).toBe(1);

    await repository.archiveWalletTransaction(
      userTransaction.senderWallet.profile,
      [userTransactions[0].id, chance.guid({ version: 4 })],
      false,
    );

    const archivedTransactions1 = await repository.getWalletTransactions(
      userTransaction.senderWallet.profile.id,
      AccountHolder.User,
      { archive: true },
    );
    expect(archivedTransactions1.totalCount).toBe(1);

    const unArchivedTransactions1 = await repository.getWalletTransactions(
      userTransaction.senderWallet.profile.id,
      AccountHolder.User,
      { archive: false },
    );
    expect(unArchivedTransactions1.totalCount).toBe(2);
  });
});
