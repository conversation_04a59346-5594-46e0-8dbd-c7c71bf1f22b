import { NotFoundException } from '@nestjs/common';
import { In, Repository, SelectQueryBuilder } from 'typeorm';
import { WalletTransactionFilter } from '../inputs/wallet-transaction-filter.input';
import { IWalletTransaction } from '../interfaces/wallet-transaction.interface';
import { WalletTransactionModel } from '../models/wallet-transaction.model';
import { WalletTransactionResponse } from '../responses/wallet-transaction.response';
import { CustomRepository } from '@clinify/custom-repository/decorators/custom-repo.decorator';
import { AccountHolder } from '@clinify/shared/enums/account-holder';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { takePaginatedResponses } from '@clinify/utils/pagination';

const withinRange = (
  query: SelectQueryBuilder<WalletTransactionModel>,
  {
    dateRange,
    keyword,
    transactionType,
    status,
    skip = 0,
    take = 50,
  }: Partial<WalletTransactionFilter> = {},
) => {
  if (dateRange?.from) {
    query = query.andWhere('(wallet_transactions.created_date > :from)', {
      from: dateRange.from,
    });
  }

  if (dateRange?.to) {
    query = query.andWhere('(wallet_transactions.created_date < :to)', {
      to: dateRange.to,
    });
  }

  if (keyword) {
    query = query.andWhere(
      '(wallet_transactions.description ILIKE :keyword OR wallet_transactions.transaction_type::text ILIKE :keyword)',
      {
        keyword: `%${keyword}%`,
      },
    );
  }

  if (transactionType) {
    query = query.andWhere(
      '(wallet_transactions.transactionType = :transactionType)',
      { transactionType },
    );
  }

  if (status) {
    query = query.andWhere(
      '(wallet_transactions.transactionStatus = :status)',
      { status },
    );
  }
  return query
    .orderBy('wallet_transactions.createdDate', 'DESC')
    .skip(skip)
    .take(take);
};

const filterByProfile = (
  query: SelectQueryBuilder<WalletTransactionModel>,
  holderType: AccountHolder,
  profileId: string,
) => {
  return query
    .leftJoinAndSelect('senderWallet.profile', 'senderProfile')
    .leftJoinAndSelect('senderWallet.hospital', 'senderHospital')
    .leftJoinAndSelect('receiverWallet.profile', 'receiverProfile')
    .leftJoinAndSelect('receiverWallet.hospital', 'receiverHospital')
    .andWhere(
      '(senderWallet.profile = :id OR senderWallet.hospital = :id OR receiverWallet.profile = :id OR receiverWallet.hospital = :id)',
      { id: profileId },
    );
};

@CustomRepository(WalletTransactionModel)
export class WalletTransactionRepository extends Repository<WalletTransactionModel> {
  async getWalletTransactions(
    profileId: string,
    holderType: AccountHolder,
    options?: Partial<WalletTransactionFilter>,
  ): Promise<WalletTransactionResponse> {
    const { archive } = { ...options };
    const query = this.createQueryBuilder('wallet_transactions')
      .leftJoinAndSelect('wallet_transactions.senderWallet', 'senderWallet')
      .leftJoinAndSelect('wallet_transactions.receiverWallet', 'receiverWallet')
      .where('wallet_transactions.archived = :archived', {
        archived: !!archive,
      });

    filterByProfile(query, holderType, profileId);
    withinRange(query, options);
    const response = await query.getManyAndCount();
    return new WalletTransactionResponse(
      ...takePaginatedResponses(response, 50),
    );
  }

  async getWalletTransaction(
    profileId: string,
    holderType: AccountHolder,
    id: string,
  ): Promise<WalletTransactionModel> {
    const query = this.createQueryBuilder('wallet_transactions')
      .leftJoinAndSelect('wallet_transactions.senderWallet', 'senderWallet')
      .leftJoinAndSelect('wallet_transactions.receiverWallet', 'receiverWallet')
      .leftJoinAndSelect('senderWallet.profile', 'senderProfile')
      .leftJoinAndSelect('senderWallet.hospital', 'senderHospital')
      .leftJoinAndSelect('receiverWallet.profile', 'receiverProfile')
      .leftJoinAndSelect('receiverWallet.hospital', 'receiverHospital')
      .where('wallet_transactions.id = :id', { id });

    return await query.getOne();
  }

  async updateWalletTransaction(
    transactionId: string,
    dataUpdate: Partial<IWalletTransaction>,
  ): Promise<WalletTransactionModel> {
    const transaction = await this.findOneOrFail({
      where: { id: transactionId },
      relations: ['senderWallet', 'receiverWallet'],
    }).catch(() => {
      throw new NotFoundException('Wallet Transaction Not Found');
    });
    return await this.save({
      ...transaction,
      ...dataUpdate,
    });
  }

  async archiveWalletTransaction(
    profile: ProfileModel,
    transactionIds: string[],
    archive: boolean,
  ): Promise<WalletTransactionModel[]> {
    const walletTransactions = await this.find({
      where: { id: In(transactionIds) },
    });
    await this.createQueryBuilder('allergies')
      .update(WalletTransactionModel)
      .set({ archived: archive, updatedDate: () => 'updated_date' })
      .whereInIds(transactionIds)
      .execute();

    return walletTransactions.map((v) => ({ ...v, archived: archive }));
  }
}
