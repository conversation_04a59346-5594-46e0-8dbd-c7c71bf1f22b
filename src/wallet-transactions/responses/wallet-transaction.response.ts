import { Field, ObjectType, Int } from '@nestjs/graphql';
import { WalletTransactionModel } from '../models/wallet-transaction.model';

@ObjectType()
export class WalletTransactionResponse {
  constructor(
    walletTransactions: WalletTransactionModel[],
    totalCount: number,
  ) {
    this.list = walletTransactions;
    this.totalCount = totalCount;
  }

  @Field(() => Int)
  totalCount: number;

  @Field(() => [WalletTransactionModel])
  list: WalletTransactionModel[];
}
