import { Test, TestingModule } from '@nestjs/testing';
import { WalkInReferralResolver } from './walk-in-referral.resolver';
import { WalkInReferralModel } from '../models/walk-in-referral.model';
import { WalkInReferralService } from '../services/walk-in-referral.service';
import { hospitalFactory } from '@clinify/__mocks__/factories/hospital.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { walkInReferralFactory } from '@clinify/__mocks__/factories/walkInReferral.factory';
import { UserType } from '@clinify/shared/enums/users';

const mockWalkInReferralService = {
  getOneWalkInReferral: jest.fn(),
  saveWalkInReferral: jest.fn(),
  updateWalkInReferral: jest.fn(),
  deleteWalkInReferrals: jest.fn(),
  archiveWalkInReferrals: jest.fn(),
  concealReferralReason: jest.fn(),
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

describe('WalkInReferralResolver', () => {
  let resolver: WalkInReferralResolver;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WalkInReferralResolver,
        WalkInReferralService,
        {
          provide: WalkInReferralService,
          useValue: mockWalkInReferralService,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
      ],
    }).compile();

    resolver = module.get<WalkInReferralResolver>(WalkInReferralResolver);
  });

  it('walkInReferral(): should call getOneWalkInReferral service method', async () => {
    const mutator = profileFactory.build();

    await resolver.walkInReferral(mutator, 'record-id');
    expect(mockWalkInReferralService.getOneWalkInReferral).toHaveBeenCalledWith(
      mutator,
      'record-id',
    );
  });

  it('walkInReferralEventHandler(): should call WalkInReferralEvent pub sub event', async () => {
    await resolver.walkInReferralEventHandler('hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalkInReferralEvent',
    );
  });

  it('addWalkInReferral(): should call saveWalkInReferral service method', async () => {
    const mutator = profileFactory.build();
    const hospital = hospitalFactory.build();
    const input = walkInReferralFactory.build();

    const response = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    };

    mockWalkInReferralService.saveWalkInReferral = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.addWalkInReferral(mutator, input);

    expect(mockWalkInReferralService.saveWalkInReferral).toHaveBeenCalledWith(
      mutator,
      input,
    );
  });

  it('addWalkInReferralSubsHandler(): should call WalkInReferralAdded pub sub event', async () => {
    await resolver.addWalkInReferralSubsHandler('hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalkInReferralAdded',
    );
  });

  it('updateWalkInReferral(): should call updateWalkInReferral service method', async () => {
    const mutator = profileFactory.build();
    const hospital = hospitalFactory.build();
    const input = walkInReferralFactory.build();

    const response = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    };

    mockWalkInReferralService.updateWalkInReferral = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.updateWalkInReferral(mutator, input, 'record-id');

    expect(mockWalkInReferralService.updateWalkInReferral).toHaveBeenCalledWith(
      mutator,
      input,
      'record-id',
    );
  });

  it('updateWalkInReferralSubsHandler(): should call WalkInReferralUpdated pub sub event', async () => {
    await resolver.updateWalkInReferralSubsHandler('hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalkInReferralUpdated',
    );
  });

  it('deleteWalkInReferrals(): should call deleteWalkInReferrals service method', async () => {
    const mutator = profileFactory.build();
    const hospital = hospitalFactory.build();
    const input = walkInReferralFactory.build();

    const response = [
      {
        ...input,
        createdBy: mutator,
        creatorId: mutator.id,
        hospital,
        hospitalId: hospital.id,
      },
    ];

    mockWalkInReferralService.deleteWalkInReferrals = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.deleteWalkInReferrals(mutator, ['record-id']);

    expect(
      mockWalkInReferralService.deleteWalkInReferrals,
    ).toHaveBeenCalledWith(mutator, ['record-id']);
  });

  it('removeWalkInReferralsHandler(): should call WalkInReferralRemoved pub sub event', async () => {
    await resolver.removeWalkInReferralsHandler('hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalkInReferralRemoved',
    );
  });

  it('archiveWalkInReferrals(): should call archiveWalkInReferrals service method', async () => {
    const mutator = profileFactory.build();
    const hospital = hospitalFactory.build();
    const input = walkInReferralFactory.build();

    const response = [
      {
        ...input,
        createdBy: mutator,
        creatorId: mutator.id,
        hospital,
        hospitalId: hospital.id,
      },
    ];

    mockWalkInReferralService.archiveWalkInReferrals = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.archiveWalkInReferrals(mutator, ['record-id'], true);

    expect(
      mockWalkInReferralService.archiveWalkInReferrals,
    ).toHaveBeenCalledWith(mutator, ['record-id'], true);
  });

  it('archiveWalkInReferrals(): should call archiveWalkInReferrals service method', async () => {
    const mutator = profileFactory.build();
    const hospital = hospitalFactory.build();
    const input = walkInReferralFactory.build();

    const response = [
      {
        ...input,
        createdBy: mutator,
        creatorId: mutator.id,
        hospital,
        hospitalId: hospital.id,
      },
    ];

    mockWalkInReferralService.archiveWalkInReferrals = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.archiveWalkInReferrals(mutator, ['record-id'], false);

    expect(
      mockWalkInReferralService.archiveWalkInReferrals,
    ).toHaveBeenCalledWith(mutator, ['record-id'], false);
  });

  it('archiveWalkInReferralsHandler(): should call WalkInReferralArchived pub sub event', async () => {
    await resolver.archiveWalkInReferralsHandler('hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalkInReferralArchived',
    );
  });

  it('unArchiveWalkInReferralsHandler(): should call WalkInReferralUnarchived pub sub event', async () => {
    await resolver.unArchiveWalkInReferralsHandler('hospital-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'WalkInReferralUnarchived',
    );
  });

  it('concealReferralReason(): should call concealReferralReason service method', async () => {
    const mutator = profileFactory.build();
    const hospital = hospitalFactory.build();
    const input = walkInReferralFactory.build();

    const response = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    };
    mockWalkInReferralService.concealReferralReason = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.concealReferralReason(mutator, 'record-id', true);

    expect(
      mockWalkInReferralService.concealReferralReason,
    ).toHaveBeenCalledWith(mutator, 'record-id', true);
  });

  it('getReferralReason(): should return referral reason', () => {
    const mutator = profileFactory.build({ type: UserType.OrganizationDoctor });
    const hospital = hospitalFactory.build();
    const input = walkInReferralFactory.build({
      referralReason: 'Specialized Care',
      concealReferralReason: true,
    });

    const root = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    } as WalkInReferralModel;

    const result = resolver.getReferralReason(mutator, root);

    expect(result).toBe('Specialized Care');
  });

  it('getReferralReason(): should return referral reason', () => {
    const mutator = profileFactory.build({ type: UserType.OrganizationDoctor });
    const hospital = hospitalFactory.build();
    const input = walkInReferralFactory.build({
      referralReason: 'Specialized Care',
      concealReferralReason: false,
    });

    const root = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    } as WalkInReferralModel;

    const result = resolver.getReferralReason(mutator, root);

    expect(result).toBe('Specialized Care');
  });

  it('getReferralReason(): should return referral reason', () => {
    const mutator = profileFactory.build({ type: UserType.Patient });
    const hospital = hospitalFactory.build();
    const input = walkInReferralFactory.build({
      referralReason: 'Specialized Care',
      concealReferralReason: false,
    });

    const root = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    } as WalkInReferralModel;

    const result = resolver.getReferralReason(mutator, root);

    expect(result).toBe('Specialized Care');
  });

  it('getReferralReason(): should return referral reason', () => {
    const mutator = profileFactory.build({ type: UserType.Patient });
    const hospital = hospitalFactory.build();
    const input = walkInReferralFactory.build({
      referralReason: 'Specialized Care',
      concealReferralReason: true,
    });

    const root = {
      ...input,
      profile: mutator,
      profileId: mutator.id,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    } as WalkInReferralModel;

    const result = resolver.getReferralReason(mutator, root);

    expect(result).toBe('Specialized Care');
  });

  it('getReferralReason(): should not return referral reason', () => {
    const patient = profileFactory.build({ type: UserType.Patient });
    const mutator = profileFactory.build({ type: UserType.OrganizationDoctor });
    const hospital = hospitalFactory.build();
    const input = walkInReferralFactory.build({
      referralReason: 'Specialized Care',
      concealReferralReason: true,
    });

    const root = {
      ...input,
      profile: patient,
      profileId: patient.id,
      createdBy: mutator,
      creatorId: mutator.id,
      hospital,
      hospitalId: hospital.id,
    } as WalkInReferralModel;

    const result = resolver.getReferralReason(patient, root);

    expect(result).toBe(null);
  });
});
