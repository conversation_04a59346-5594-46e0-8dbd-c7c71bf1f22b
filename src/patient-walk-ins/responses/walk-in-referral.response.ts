import { Field, Int, ObjectType } from '@nestjs/graphql';
import { WalkInReferralModel } from '../models/walk-in-referral.model';

@ObjectType()
export class WalkInReferralResponse {
  constructor(walkInReferral: WalkInReferralModel[], totalCount: number) {
    this.list = walkInReferral;
    this.totalCount = totalCount;
  }

  @Field(() => Int)
  totalCount: number;

  @Field(() => [WalkInReferralModel])
  list: WalkInReferralModel[];
}
