import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { WalkInReferralService } from './walk-in-referral.service';
import { WalkInFilterInput } from '../inputs/walk-in-filter.input';
import { WalkInReferralRepository } from '../repositories/walk-in-referral.repository';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { walkInReferralFactory } from '@clinify/__mocks__/factories/walkInReferral.factory';
import { UserType } from '@clinify/shared/enums/users';
import { ProfileModel } from '@clinify/users/models/profile.model';

const chance = new Chance();

const mockWalkInReferralRepository = {
  save: jest.fn(),
  findByHospital: jest.fn(),
  getOneWalkInReferral: jest.fn(),
  updateWalkInReferral: jest.fn(),
  deleteWalkInReferrals: jest.fn(),
  archiveWalkInReferrals: jest.fn(),
  concealReferralReason: jest.fn(),
};

const mockProfileRepository = {
  findOneOrFail: jest.fn(),
};

describe('WalkInReferralService', () => {
  let service: WalkInReferralService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [],
      providers: [
        WalkInReferralService,
        {
          provide: WalkInReferralRepository,
          useValue: mockWalkInReferralRepository,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: mockProfileRepository,
        },
      ],
    }).compile();

    service = module.get<WalkInReferralService>(WalkInReferralService);
  });

  it('saveWalkInReferral(): should save new walk in transfer entry without profile', async () => {
    const mutator = profileFactory.build({
      type: UserType.OrganizationRecordOfficer,
    });
    const input = walkInReferralFactory.build({ profileId: null });

    await service.saveWalkInReferral(mutator, input);

    expect(mockWalkInReferralRepository.save).toHaveBeenCalledWith({
      ...input,
      profile: undefined,
      hospital: mutator.hospital,
      creatorName: mutator.fullName,
      createdBy: mutator,
    });
  });

  it('saveWalkInReferral(): should throw error when hospital id is missing', async () => {
    const mutator = profileFactory.build({
      type: UserType.OrganizationRecordOfficer,
      hospital: null,
      hospitalId: null,
    });
    const input = walkInReferralFactory.build({ profileId: null });

    await expect(service.saveWalkInReferral(mutator, input)).rejects.toThrow(
      'Not Authorized To Create Record',
    );
  });

  it('saveWalkInReferral(): should throw error when mutator is patient', async () => {
    const mutator = profileFactory.build({
      type: UserType.Patient,
    });
    const input = walkInReferralFactory.build({ profileId: null });

    await expect(service.saveWalkInReferral(mutator, input)).rejects.toThrow(
      'Not Authorized To Create Record',
    );
  });

  it('saveWalkInReferral(): should save new walk in transfer entry with profile', async () => {
    const mutator = profileFactory.build({
      type: UserType.OrganizationRecordOfficer,
    });
    const input = walkInReferralFactory.build({
      profileId: chance.guid({ version: 4 }),
    });

    mockProfileRepository.findOneOrFail = jest
      .fn()
      .mockRejectedValue(new Error());

    await expect(
      service.saveWalkInReferral(mutator, input),
    ).rejects.toThrowError('Patient Not Found');
  });

  it('saveWalkInReferral(): should save new walk in transfer entry with profile', async () => {
    const mutator = profileFactory.build({
      type: UserType.OrganizationRecordOfficer,
    });
    const patient = profileFactory.build({
      type: UserType.Patient,
    });
    const input = walkInReferralFactory.build({
      profileId: chance.guid({ version: 4 }),
    });

    mockProfileRepository.findOneOrFail = jest.fn().mockResolvedValue(patient);

    await service.saveWalkInReferral(mutator, input);

    expect(mockWalkInReferralRepository.save).toHaveBeenCalledWith({
      ...input,
      profile: patient,
      hospital: mutator.hospital,
      creatorName: mutator.fullName,
      createdBy: mutator,
    });
  });

  it('getHospitalWalkInReferrals(): should call getHospitalWalkInReferrals repository method', async () => {
    const filter: WalkInFilterInput = {
      skip: 20,
      take: 50,
    };
    await service.getHospitalWalkInReferrals('profile-id', filter);

    expect(mockWalkInReferralRepository.findByHospital).toHaveBeenCalledWith(
      'profile-id',
      filter,
    );
  });

  it('getOneWalkInReferral(): should call getOneWalkInReferral repository method', async () => {
    const mutator = profileFactory.build();

    mockWalkInReferralRepository.getOneWalkInReferral = jest
      .fn()
      .mockResolvedValue(null);

    await expect(
      service.getOneWalkInReferral(mutator, 'record-id'),
    ).rejects.toThrowError('Record Not Found');
  });

  it('getOneWalkInReferral(): should call getOneWalkInReferral repository method', async () => {
    const mutator = profileFactory.build();
    const response = {
      ...walkInReferralFactory.build(),
      createdBy: mutator,
      creatorName: mutator.fullName,
      createdDate: new Date(),
      updatedDate: new Date(),
    };

    mockWalkInReferralRepository.getOneWalkInReferral = jest
      .fn()
      .mockResolvedValue(response);

    const result = await service.getOneWalkInReferral(mutator, 'record-id');
    expect(result).toBe(response);
  });

  it('updateWalkInReferral(): should call updateWalkInReferral repository method', async () => {
    const mutator = profileFactory.build();
    const input = walkInReferralFactory.build();

    await service.updateWalkInReferral(mutator, input, 'record-id');

    expect(
      mockWalkInReferralRepository.updateWalkInReferral,
    ).toHaveBeenCalledWith(mutator, {
      ...input,
      id: 'record-id',
    });
  });

  it('deleteWalkInReferrals(): should call deleteWalkInReferrals repository method', async () => {
    const mutator = profileFactory.build();

    await service.deleteWalkInReferrals(mutator, ['record-id']);

    expect(
      mockWalkInReferralRepository.deleteWalkInReferrals,
    ).toHaveBeenCalledWith(mutator, ['record-id']);
  });

  it('archiveWalkInReferrals(): should call archiveWalkInReferrals repository method', async () => {
    const mutator = profileFactory.build();

    await service.archiveWalkInReferrals(mutator, ['record-id'], true);

    expect(
      mockWalkInReferralRepository.archiveWalkInReferrals,
    ).toHaveBeenCalledWith(mutator, ['record-id'], true);
  });

  it('concealReferralReason(): should call concealReferralReason repository method', async () => {
    const mutator = profileFactory.build();

    await service.concealReferralReason(mutator, 'record-id', false);

    expect(
      mockWalkInReferralRepository.concealReferralReason,
    ).toHaveBeenCalledWith(mutator, 'record-id', false);
  });
});
