import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { DataSource, EntityManager } from 'typeorm';
import { WalkInReferralRepository } from './walk-in-referral.repository';
import { WalkInReferralModel } from '../models/walk-in-referral.model';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { TestDataSourceOptions } from '@clinify/data-source';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { createWalkInReferralFixtures } from '@clinify/utils/tests/walk-in-referral.fixtures';

const chance = new Chance();

describe('WalkInReferralRepository', () => {
  let ds: DataSource;
  let repository: WalkInReferralRepository;
  let manager: EntityManager;
  let referrals: WalkInReferralModel[];
  let referral: WalkInReferralModel;
  let mutator: ProfileModel;
  let hospital: HospitalModel;
  let filterKeyword: string;

  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeormExtendedModule.forCustomRepository([WalkInReferralRepository]),
      ],
      providers: [],
    }).compile();

    ds = module.get(DataSource);
    manager = ds.manager;
    repository = module.get(WalkInReferralRepository);

    referrals = await createWalkInReferralFixtures(manager, 2);
    referral = referrals[1];
    mutator = referrals[0].createdBy;
    hospital = referrals[0].hospital;
    filterKeyword = referrals[1].referredBy;
  });

  afterAll(async () => {
    await ds.destroy();
    await module.close();
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  it('findByHospital(): should return hospital walk in referrals', async () => {
    const response = await repository.findByHospital(hospital.id, {
      skip: 0,
      take: 1,
    });

    expect(response.totalCount).toBe(2);

    const response2 = await repository.findByHospital(
      chance.guid({ version: 4 }),
      {},
    );

    expect(response2.totalCount).toBe(0);

    const response3 = await repository.findByHospital(hospital.id, {
      dateRange: { from: new Date('2021-04-14'), to: new Date('2023-04-14') },
    });

    expect(response3.totalCount).toBe(0);

    const response4 = await repository.findByHospital(hospital.id, {
      dateRange: { from: new Date('2021-04-14'), to: new Date() },
    });

    expect(response4.totalCount).toBe(2);

    const response5 = await repository.findByHospital(hospital.id, {
      keyword: filterKeyword,
    });

    expect(response5.totalCount).toBeTruthy();
  });

  it('getOneWalkInReferral(): should fetch single walk in referral', async () => {
    const response = await repository.getOneWalkInReferral(
      mutator,
      referral.id,
    );

    expect(response.referredBy).toBe(referral.referredBy);
  });

  it('updateWalkInReferral(): should update single walk in referral', async () => {
    await repository.updateWalkInReferral(mutator, {
      ...referral,
      referredBy: 'John Wick',
    });

    const response = await repository.getOneWalkInReferral(
      mutator,
      referral.id,
    );

    expect(response.referredBy).toBe('John Wick');
    expect(response.lastModifierId).toBe(mutator.id);
    expect(response.lastModifierName).toBe(mutator.fullName);
  });

  it('updateWalkInReferral(): should throw error when record is not found', async () => {
    await expect(
      repository.updateWalkInReferral(mutator, {
        ...referral,
        id: chance.guid({ version: 4 }),
        referredBy: 'John Wick',
      }),
    ).rejects.toThrowError('Record Not Found');
  });

  it('archiveWalkInReferrals(): should update archive state of multiple walk in referral', async () => {
    const response = await repository.archiveWalkInReferrals(
      mutator,
      [chance.guid({ version: 4 })],
      false,
    );
    expect(response.length).toBe(0);

    const response1 = await repository.findByHospital(hospital.id, {
      archive: false,
    });
    expect(response1.totalCount).toBe(2);

    const response2 = await repository.findByHospital(hospital.id, {
      archive: true,
    });
    expect(response2.totalCount).toBe(0);

    const ids = referrals.map(({ id }) => id);

    await repository.archiveWalkInReferrals(mutator, ids, true);

    const response3 = await repository.findByHospital(hospital.id, {
      archive: false,
    });
    expect(response3.totalCount).toBe(0);

    const response4 = await repository.findByHospital(hospital.id, {
      archive: true,
    });
    expect(response4.totalCount).toBe(2);

    await repository.archiveWalkInReferrals(mutator, ids, false);

    const response5 = await repository.findByHospital(hospital.id, {
      archive: false,
    });
    expect(response5.totalCount).toBe(2);

    const response6 = await repository.findByHospital(hospital.id, {
      archive: true,
    });
    expect(response6.totalCount).toBe(0);
  });

  it('concealReferralReason(): should throw error when record is not found', async () => {
    await expect(
      repository.concealReferralReason(
        mutator,
        chance.guid({ version: 4 }),
        false,
      ),
    ).rejects.toThrowError('Record Not Found');
  });

  it('concealReferralReason(): should toggle hide of referral reason', async () => {
    const response1 = await repository.getOneWalkInReferral(
      mutator,
      referral.id,
    );
    expect(response1.concealReferralReason).toBe(true);

    await repository.concealReferralReason(mutator, referral.id, false);

    const response2 = await repository.getOneWalkInReferral(
      mutator,
      referral.id,
    );
    expect(response2.concealReferralReason).toBe(false);

    await repository.concealReferralReason(mutator, referral.id, true);

    const response3 = await repository.getOneWalkInReferral(
      mutator,
      referral.id,
    );
    expect(response3.concealReferralReason).toBe(true);
  });

  it('deleteWalkInReferrals(): should delete multiple walk in referrals', async () => {
    const response1 = await repository.findByHospital(hospital.id, {});
    expect(response1.totalCount).toBe(2);

    const ids = referrals.map(({ id }) => id);

    await repository.deleteWalkInReferrals(mutator, ids);

    const response2 = await repository.findByHospital(hospital.id, {});
    expect(response2.totalCount).toBe(0);
  });
});
