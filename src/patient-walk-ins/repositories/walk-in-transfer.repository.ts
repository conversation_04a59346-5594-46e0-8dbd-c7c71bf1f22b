import { NotFoundException } from '@nestjs/common';
import cloneDeep from 'lodash.clonedeep';
import { In, Repository } from 'typeorm';
import { WalkInFilterInput } from '../inputs/walk-in-filter.input';
import { WalkInTransferInput } from '../inputs/walk-in-transfer.input';
import { WalkInTransferModel } from '../models/walk-in-transfer.model';
import { WalkInTransferResponse } from '../responses/walk-in-transfer.response';
import { CustomRepository } from '@clinify/custom-repository/decorators/custom-repo.decorator';
import {
  validateMutator,
  validateRecordRemover,
} from '@clinify/shared/validators/validate-record-mutation.validator';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { takePaginatedResponses } from '@clinify/utils/pagination';

@CustomRepository(WalkInTransferModel)
export class WalkInTransferRepository extends Repository<WalkInTransferModel> {
  async findByHospital(
    hospitalId: string,
    filter: WalkInFilterInput,
  ): Promise<WalkInTransferResponse> {
    const { skip = 0, take = 50, dateRange, keyword, archive } = { ...filter };

    let query = this.createQueryBuilder('walk_in_transfers')
      .where('walk_in_transfers.hospital = :hospitalId', { hospitalId })
      .andWhere('walk_in_transfers.archived = :archived', {
        archived: !!archive,
      });

    if (keyword) {
      query = query.andWhere(
        `(
          walk_in_transfers.transferred_by ILIKE :keyword OR
          walk_in_transfers.transfer_facility_name ILIKE :keyword OR
          walk_in_transfers.patient_information::text ILIKE :keyword
        )`,
        {
          keyword: `%${keyword}%`,
        },
      );
    }

    if (dateRange?.from) {
      query = query.andWhere(
        '(walk_in_transfers.transfer_date_time >= :from)',
        {
          from: dateRange.from,
        },
      );
    }

    if (dateRange?.to) {
      query = query.andWhere('(walk_in_transfers.transfer_date_time < :to)', {
        to: dateRange.to,
      });
    }

    query = query
      .orderBy('walk_in_transfers.createdDate', 'DESC')
      .skip(skip)
      .take(take);

    const response = await query.getManyAndCount();

    return new WalkInTransferResponse(
      ...takePaginatedResponses(response, take),
    );
  }

  getOneWalkInTransfer(
    mutator: ProfileModel,
    recordId: string,
  ): Promise<WalkInTransferModel> {
    const record = this.createQueryBuilder('walk_in_transfers').where(
      'walk_in_transfers.id = :id',
      { id: recordId },
    );

    return record.getOne();
  }

  async updateWalkInTransfer(
    mutator: ProfileModel,
    input: WalkInTransferInput,
  ): Promise<WalkInTransferModel> {
    const record = await this.createQueryBuilder('walk_in_transfers')
      .where('walk_in_transfers.id = :id', { id: input.id })
      .andWhere('walk_in_transfers.hospital = :hospitalId', {
        hospitalId: mutator.hospitalId,
      })
      .getOne();

    if (!record) {
      throw new NotFoundException('Record Not Found');
    }

    validateMutator(mutator, record);

    return this.save({
      ...record,
      ...input,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  }

  async deleteWalkInTransfers(
    mutator: ProfileModel,
    recordIds: string[],
  ): Promise<WalkInTransferModel[]> {
    const records = await this.find({
      where: { id: In(recordIds) },
      relations: ['createdBy'],
    });
    const validResources = validateRecordRemover(mutator, records);
    if (validResources.length) {
      await this.remove(cloneDeep(validResources));
    }
    return validResources;
  }

  async archiveWalkInTransfers(
    mutator: ProfileModel,
    recordIds: string[],
    archive: boolean,
  ): Promise<WalkInTransferModel[]> {
    const records = await this.find({
      where: { id: In(recordIds) },
      relations: ['createdBy'],
    });
    const validResources = validateRecordRemover(mutator, records);
    if (!validResources.length) return [];
    const validIds = validResources.map((v) => v.id);

    await this.createQueryBuilder('walk_in_transfers')
      .update(WalkInTransferModel)
      .set({ archived: archive, updatedDate: () => 'updated_date' })
      .whereInIds(validIds)
      .execute();

    return validResources.map((v) => ({ ...v, archived: archive }));
  }

  async concealTransferReason(
    mutator: ProfileModel,
    recordId: string,
    concealState: boolean,
  ): Promise<WalkInTransferModel> {
    const record = await this.findOneOrFail({
      where: { id: recordId },
    }).catch(() => {
      throw new NotFoundException('Record Not Found');
    });

    await this.createQueryBuilder()
      .update(WalkInTransferModel)
      .set({
        updatedDate: () => 'updated_date',
        concealTransferReason: concealState,
      })
      .where('id = :id', { id: recordId })
      .execute();

    return { ...record, concealTransferReason: concealState };
  }
}
