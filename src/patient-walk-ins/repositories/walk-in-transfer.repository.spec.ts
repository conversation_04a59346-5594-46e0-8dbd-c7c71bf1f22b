import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { DataSource, EntityManager } from 'typeorm';
import { WalkInTransferRepository } from './walk-in-transfer.repository';
import { WalkInTransferModel } from '../models/walk-in-transfer.model';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { TestDataSourceOptions } from '@clinify/data-source';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { createWalkInTransferFixtures } from '@clinify/utils/tests/walk-in-transfer.fixtures';

const chance = new Chance();

describe('WalkInTransferRepository', () => {
  let ds: DataSource;
  let repository: WalkInTransferRepository;
  let manager: EntityManager;
  let transfers: WalkInTransferModel[];
  let transfer: WalkInTransferModel;
  let mutator: ProfileModel;
  let hospital: HospitalModel;
  let filterKeyword: string;

  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeormExtendedModule.forCustomRepository([WalkInTransferRepository]),
      ],
      providers: [],
    }).compile();

    ds = module.get(DataSource);
    manager = ds.manager;
    repository = module.get(WalkInTransferRepository);

    transfers = await createWalkInTransferFixtures(manager, 2);
    transfer = transfers[1];
    mutator = transfers[0].createdBy;
    hospital = transfers[0].hospital;
    filterKeyword = transfers[1].transferredBy;
  });

  afterAll(async () => {
    await ds.destroy();
    await module.close();
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  it('findByHospital(): should return hospital walk in transfers', async () => {
    const response = await repository.findByHospital(hospital.id, {
      skip: 0,
      take: 1,
    });

    expect(response.totalCount).toBe(2);

    const response2 = await repository.findByHospital(
      chance.guid({ version: 4 }),
      {},
    );

    expect(response2.totalCount).toBe(0);

    const response3 = await repository.findByHospital(hospital.id, {
      dateRange: { from: new Date('2021-04-14'), to: new Date('2023-04-14') },
    });

    expect(response3.totalCount).toBe(0);

    const response4 = await repository.findByHospital(hospital.id, {
      dateRange: { from: new Date('2021-04-14'), to: new Date() },
    });

    expect(response4.totalCount).toBe(2);

    const response5 = await repository.findByHospital(hospital.id, {
      keyword: filterKeyword,
    });

    expect(response5.totalCount).toBeTruthy();
  });

  it('getOneWalkInTransfer(): should fetch single walk in transfer', async () => {
    const response = await repository.getOneWalkInTransfer(
      mutator,
      transfer.id,
    );

    expect(response.transferredBy).toBe(transfer.transferredBy);
  });

  it('updateWalkInTransfer(): should update single walk in transfer', async () => {
    await repository.updateWalkInTransfer(mutator, {
      ...transfer,
      transferredBy: 'John Wick',
    });

    const response = await repository.getOneWalkInTransfer(
      mutator,
      transfer.id,
    );

    expect(response.transferredBy).toBe('John Wick');
    expect(response.lastModifierId).toBe(mutator.id);
    expect(response.lastModifierName).toBe(mutator.fullName);
  });

  it('updateWalkInTransfer(): should throw error when record is not found', async () => {
    await expect(
      repository.updateWalkInTransfer(mutator, {
        ...transfer,
        id: chance.guid({ version: 4 }),
        transferredBy: 'John Wick',
      }),
    ).rejects.toThrowError('Record Not Found');
  });

  it('archiveWalkInTransfers(): should update archive state of multiple walk in transfer', async () => {
    const response = await repository.archiveWalkInTransfers(
      mutator,
      [chance.guid({ version: 4 })],
      false,
    );
    expect(response.length).toBe(0);

    const response1 = await repository.findByHospital(hospital.id, {
      archive: false,
    });
    expect(response1.totalCount).toBe(2);

    const response2 = await repository.findByHospital(hospital.id, {
      archive: true,
    });
    expect(response2.totalCount).toBe(0);

    const ids = transfers.map(({ id }) => id);

    await repository.archiveWalkInTransfers(mutator, ids, true);

    const response3 = await repository.findByHospital(hospital.id, {
      archive: false,
    });
    expect(response3.totalCount).toBe(0);

    const response4 = await repository.findByHospital(hospital.id, {
      archive: true,
    });
    expect(response4.totalCount).toBe(2);

    await repository.archiveWalkInTransfers(mutator, ids, false);

    const response5 = await repository.findByHospital(hospital.id, {
      archive: false,
    });
    expect(response5.totalCount).toBe(2);

    const response6 = await repository.findByHospital(hospital.id, {
      archive: true,
    });
    expect(response6.totalCount).toBe(0);
  });

  it('concealTransferReason(): should throw error when record is not found', async () => {
    await expect(
      repository.concealTransferReason(
        mutator,
        chance.guid({ version: 4 }),
        false,
      ),
    ).rejects.toThrowError('Record Not Found');
  });

  it('concealTransferReason(): should toggle hide of transfer reason', async () => {
    const response1 = await repository.getOneWalkInTransfer(
      mutator,
      transfer.id,
    );
    expect(response1.concealTransferReason).toBe(true);

    await repository.concealTransferReason(mutator, transfer.id, false);

    const response2 = await repository.getOneWalkInTransfer(
      mutator,
      transfer.id,
    );
    expect(response2.concealTransferReason).toBe(false);

    await repository.concealTransferReason(mutator, transfer.id, true);

    const response3 = await repository.getOneWalkInTransfer(
      mutator,
      transfer.id,
    );
    expect(response3.concealTransferReason).toBe(true);
  });

  it('deleteWalkInTransfers(): should delete multiple walk in transfers', async () => {
    const response1 = await repository.findByHospital(hospital.id, {});
    expect(response1.totalCount).toBe(2);

    const ids = transfers.map(({ id }) => id);

    await repository.deleteWalkInTransfers(mutator, ids);

    const response2 = await repository.findByHospital(hospital.id, {});
    expect(response2.totalCount).toBe(0);
  });
});
