import { NotFoundException } from '@nestjs/common';
import cloneDeep from 'lodash.clonedeep';
import { In, Repository } from 'typeorm';
import { WalkInFilterInput } from '../inputs/walk-in-filter.input';
import { WalkInReferralInput } from '../inputs/walk-in-referral.input';
import { WalkInReferralModel } from '../models/walk-in-referral.model';
import { WalkInReferralResponse } from '../responses/walk-in-referral.response';
import { CustomRepository } from '@clinify/custom-repository/decorators/custom-repo.decorator';
import {
  validateMutator,
  validateRecordRemover,
} from '@clinify/shared/validators/validate-record-mutation.validator';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { takePaginatedResponses } from '@clinify/utils/pagination';

@CustomRepository(WalkInReferralModel)
export class WalkInReferralRepository extends Repository<WalkInReferralModel> {
  async findByHospital(
    hospitalId: string,
    filter: WalkInFilterInput,
  ): Promise<WalkInReferralResponse> {
    const { skip = 0, take = 50, dateRange, keyword, archive } = { ...filter };

    let query = this.createQueryBuilder('walk_in_referrals')
      .where('walk_in_referrals.hospital = :hospitalId', { hospitalId })
      .andWhere('walk_in_referrals.archived = :archived', {
        archived: !!archive,
      });

    if (keyword) {
      query = query.andWhere(
        `(
          walk_in_referrals.referred_by ILIKE :keyword OR
          walk_in_referrals.referral_facility_name ILIKE :keyword OR
          walk_in_referrals.patient_information::text ILIKE :keyword
        )`,
        {
          keyword: `%${keyword}%`,
        },
      );
    }

    if (dateRange?.from) {
      query = query.andWhere(
        '(walk_in_referrals.referral_date_time >= :from)',
        {
          from: dateRange.from,
        },
      );
    }

    if (dateRange?.to) {
      query = query.andWhere('(walk_in_referrals.referral_date_time < :to)', {
        to: dateRange.to,
      });
    }

    query = query
      .orderBy('walk_in_referrals.createdDate', 'DESC')
      .skip(skip)
      .take(take);

    const response = await query.getManyAndCount();

    return new WalkInReferralResponse(
      ...takePaginatedResponses(response, take),
    );
  }

  getOneWalkInReferral(
    mutator: ProfileModel,
    recordId: string,
  ): Promise<WalkInReferralModel> {
    const record = this.createQueryBuilder('walk_in_referrals').where(
      'walk_in_referrals.id = :id',
      { id: recordId },
    );

    return record.getOne();
  }

  async updateWalkInReferral(
    mutator: ProfileModel,
    input: WalkInReferralInput,
  ): Promise<WalkInReferralModel> {
    const record = await this.createQueryBuilder('walk_in_referrals')
      .where('walk_in_referrals.id = :id', { id: input.id })
      .andWhere('walk_in_referrals.hospital = :hospitalId', {
        hospitalId: mutator.hospitalId,
      })
      .getOne();

    if (!record) {
      throw new NotFoundException('Record Not Found');
    }

    validateMutator(mutator, record);

    return this.save({
      ...record,
      ...input,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  }

  async deleteWalkInReferrals(
    mutator: ProfileModel,
    recordIds: string[],
  ): Promise<WalkInReferralModel[]> {
    const records = await this.find({
      where: { id: In(recordIds) },
      relations: ['createdBy'],
    });
    const validResources = validateRecordRemover(mutator, records);
    if (validResources.length) {
      await this.remove(
        cloneDeep(validResources).map((v) => ({
          ...v,
          deletedBy: {
            id: mutator.id,
            fullName: mutator.fullName,
            entityId: v.id,
          },
        })),
      );
    }
    return validResources;
  }

  async archiveWalkInReferrals(
    mutator: ProfileModel,
    recordIds: string[],
    archive: boolean,
  ): Promise<WalkInReferralModel[]> {
    const records = await this.find({
      where: { id: In(recordIds) },
      relations: ['createdBy'],
    });
    const validResources = validateRecordRemover(mutator, records);
    if (!validResources.length) return [];
    const validIds = validResources.map((v) => v.id);

    await this.createQueryBuilder('walk_in_referrals')
      .update(WalkInReferralModel)
      .set({ archived: archive, updatedDate: () => 'updated_date' })
      .whereInIds(validIds)
      .execute();

    return validResources.map((v) => ({ ...v, archived: archive }));
  }

  async concealReferralReason(
    mutator: ProfileModel,
    recordId: string,
    concealState: boolean,
  ): Promise<WalkInReferralModel> {
    const record = await this.findOneOrFail({
      where: { id: recordId },
    }).catch(() => {
      throw new NotFoundException('Record Not Found');
    });

    await this.createQueryBuilder()
      .update(WalkInReferralModel)
      .set({
        updatedDate: () => 'updated_date',
        concealReferralReason: concealState,
      })
      .where('id = :id', { id: recordId })
      .execute();

    return { ...record, concealReferralReason: concealState };
  }
}
