import { Parent, ResolveField, Resolver } from '@nestjs/graphql';
import { OncologyTreatmentPlanModel } from '../models/oncology-treatment-plan.model';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { AppServices } from '@clinify/shared/enums/services';
import { concealRecordField } from '@clinify/shared/helper';
import { ProfileModel } from '@clinify/users/models/profile.model';

@LogService(AppServices.OncologyConsultationHistory)
@Resolver(() => OncologyTreatmentPlanModel)
export class OncologyTreatmentPlanResolver {
  @ResolveField('treatmentPlan', () => String, { nullable: true })
  getTreatmentPlan(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyTreatmentPlanModel,
  ): string {
    return concealRecordField(profile, root) && root.conceal
      ? null
      : root.treatmentPlan;
  }

  @ResolveField('observationNote', () => String, { nullable: true })
  getObservationNote(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyTreatmentPlanModel,
  ): string {
    return concealRecordField(profile, root) && root.concealObservationNote
      ? null
      : root.observationNote;
  }
}
