import { forwardR<PERSON>, Logger, Module } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OncologyConsultationHistoryModel } from './models/oncology-consultation-history.model';
import { OncologyTreatmentPlanModel } from './models/oncology-treatment-plan.model';
import { OncologyConsultationToInvestigation } from './models/oncology_consultation_investigation.model';
import { CustomOncologyConsultationHistoryRepoMethods } from './repositories/oncology-consultation-history.repository';
import { OncologyConsultationHistoryResolver } from './resolvers/oncology-consultation-history.resolver';
import { OncologyTreatmentPlanResolver } from './resolvers/oncology-treatment-plan.resolver';
import { OncologyConsultationHistoryService } from './services/oncology-consultation-history.service';
import { NotificationsService } from '../notifications/services/notifications.service';
import { SharedModule } from '../shared/module';
import { AdmissionModel } from '@clinify/admissions/models/admission.model';
import { CustomAdmissionRepoMethods } from '@clinify/admissions/repositories/admission.repository';
import { AllergyModel } from '@clinify/allergies/models/allergy.model';
import { CustomAllergyRepoMethods } from '@clinify/allergies/repositories/allergy.repository';
import { OrganisationAppointmentModel } from '@clinify/appointments/models/organisation_appointment.model';
import { AuthorizationModule } from '@clinify/authorization/authorization.module';
import { BillModule } from '@clinify/bills/bill.module';
import { extendModel } from '@clinify/database/extendModel';
import { FacilityPreferenceModule } from '@clinify/facility-preferences/facility-preference.module';
import { HmoClaimModule } from '@clinify/hmo-claims/hmo-claims.module';
import { InventoryModule } from '@clinify/inventory/inventory.module';
import { MedicationModel } from '@clinify/medications/models/medication.model';
import { CustomMedicationMethods } from '@clinify/medications/repositories/medication.repository';
import { NotificationsModel } from '@clinify/notifications/models/notifications.model';
import { NursingServiceModel } from '@clinify/nursing-services/models/nursing-services.model';
import { CustomNursingServiceMethods } from '@clinify/nursing-services/repositories/nursing-services.repository';
import { OncologyChemoDrugSubscriber } from '@clinify/oncology-consultation-history/subscribers/oncology-chemo-drug.subscriber';
import { PreauthorizationDetailsModule } from '@clinify/preauthorization-details/preauthorization-details.module';
import { SurgeryModel } from '@clinify/surgeries/models/surgery.model';
import { CustomSurgeryRepoMethods } from '@clinify/surgeries/repositories/surgery.repository';
import { ProfileModel } from '@clinify/users/models/profile.model';
import PubSub, { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { VitalModel } from '@clinify/vitals/models/vital.model';
import { CustomVitalRepoMethods } from '@clinify/vitals/repositories/vital.repository';

@Module({
  imports: [
    forwardRef(() => SharedModule),
    AuthorizationModule,
    forwardRef(() => BillModule),
    TypeOrmModule.forFeature([
      OncologyConsultationHistoryModel,
      OncologyConsultationToInvestigation,
      ProfileModel,
      NotificationsModel,
      OrganisationAppointmentModel,
    ]),
    PreauthorizationDetailsModule,
    forwardRef(() => HmoClaimModule),
    FacilityPreferenceModule,
    forwardRef(() => InventoryModule),
  ],
  providers: [
    extendModel(
      OncologyConsultationHistoryModel,
      CustomOncologyConsultationHistoryRepoMethods,
    ),
    extendModel(VitalModel, CustomVitalRepoMethods),
    extendModel(AllergyModel, CustomAllergyRepoMethods),
    extendModel(MedicationModel, CustomMedicationMethods),
    extendModel(SurgeryModel, CustomSurgeryRepoMethods),
    extendModel(AdmissionModel, CustomAdmissionRepoMethods),
    extendModel(NursingServiceModel, CustomNursingServiceMethods),
    extendModel(OncologyTreatmentPlanModel, {}),
    OncologyConsultationHistoryService,
    OncologyConsultationHistoryResolver,
    OncologyTreatmentPlanResolver,
    { provide: PUB_SUB, useFactory: () => PubSub },
    Logger,
    EventEmitter2,
    NotificationsService,
    OncologyChemoDrugSubscriber,
  ],
  exports: [OncologyConsultationHistoryService],
})
export class OncologyConsultationHistoryModule {}
