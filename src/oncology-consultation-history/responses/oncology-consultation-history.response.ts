import { Field, Int, ObjectType } from '@nestjs/graphql';
import { OncologyConsultationHistoryModel } from '../models/oncology-consultation-history.model';

@ObjectType()
export class OncologyConsultationHistoryResponse {
  constructor(
    oncologyConsultationHistory: OncologyConsultationHistoryModel[],
    totalCount: number,
  ) {
    this.list = oncologyConsultationHistory;
    this.totalCount = totalCount;
  }

  @Field(() => Int)
  totalCount: number;

  @Field(() => [OncologyConsultationHistoryModel])
  list: OncologyConsultationHistoryModel[];
}
