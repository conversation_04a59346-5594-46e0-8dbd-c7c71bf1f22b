/* eslint-disable max-lines */
import { NotFoundException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { DataSource, EntityManager } from 'typeorm';
import { OncologyConsultationHistoryService } from './oncology-consultation-history.service';
import { hmoClaimFactory } from '../../__mocks__/factories/hmo-claim.factory';
import { MockHmoClaimService } from '../../__mocks__/hmo-claim.mock';
import { HmoClaimService } from '../../hmo-claims/services/hmo-claim.service';
import { OncologyChemoDrugModel } from '../models/oncology-chemo-drug.model';
import { OncologyConsultationHistoryModel } from '../models/oncology-consultation-history.model';
import { OncologyTreatmentPlanModel } from '../models/oncology-treatment-plan.model';
import { OncologyConsultationToInvestigation } from '../models/oncology_consultation_investigation.model';
import { OncologyConsultationLinkedRecordType } from '../validators/oncology-consultation-history.input';
import { billFactory } from '@clinify/__mocks__/factories/bill.factory';
import { treatmentPlanFactory } from '@clinify/__mocks__/factories/consultation.factory';
import { investigationFactory } from '@clinify/__mocks__/factories/investigation.factory';
import { medicationFactory } from '@clinify/__mocks__/factories/medication.factory';
import {
  oncologyChemoDrugFactory,
  oncologyConsultationFactory,
} from '@clinify/__mocks__/factories/oncologyConsultation.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { loggerMock } from '@clinify/__mocks__/logger';
import { BillService } from '@clinify/bills/services/bill.service';
import * as db from '@clinify/database';
import { FacilityPreferenceService } from '@clinify/facility-preferences/services/facility-preference.service';
import { InventoryService } from '@clinify/inventory/services/inventory.service';
import { MedicationModel } from '@clinify/medications/models/medication.model';
import { BankType } from '@clinify/shared/enums/medication';
import { UserType } from '@clinify/shared/enums/users';
import { LinkService } from '@clinify/shared/services/dashboard-link.service';
import { OncologyChartType } from '@clinify/users/inputs/oncology-register.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';
import { userFactory } from '@mocks/factories/user.factory';

const chance = new Chance();

const spySlaveQuery = jest.spyOn(db, 'queryDSWithSlave');

const mockUser = userFactory.build();
const investigationData = investigationFactory.build();
const oncologyConsultationData = oncologyConsultationFactory.build();
const treatmentPlanData = treatmentPlanFactory.build();
const oncologyConsultationToInvestigationData = {
  id: chance.guid({ version: 4 }),
  oncologyConsultationId: oncologyConsultationData.id,
  investigationId: investigationData.id,
  oncologyConsultation: oncologyConsultationData,
  investigation: investigationData,
};
const oncologConsultationDataWithInvestigation =
  oncologyConsultationFactory.build();
oncologConsultationDataWithInvestigation.oncology_consultation_investigation = [
  oncologyConsultationToInvestigationData,
];

const mockEntityManagerSave = jest.fn(() => oncologyConsultationData);

const mockOncologyConsultationDelete = jest.fn(() => [
  oncologyConsultationData,
]);
const mockMedicationRepository = {
  saveOncologyPrescribeMedications: jest.fn(),
  update: jest.fn(),
  updateOncologyPrescribeMedication: jest.fn(() => [medicationFactory.build()]),
  deleteOncologyPrescribeMedicationDetails: jest.fn(() => [
    {
      details: [
        {
          id: 'medication-details-id',
          createdBy: mockUser,
        },
        {
          id: 'medication_details_id_1',
          createdBy: mockUser,
        },
      ],
    },
    [
      {
        bank: BankType.FACILITY,
        drugInventoryId: 'drug-inventory-id',
        quantityDispensed: 4,
      },
    ],
  ]),
  deleteOncologyPrescribeMedication: jest.fn(() => [
    {
      details: [
        {
          id: 'medication-details-id',
          createdBy: mockUser,
        },
      ],
    },
    [
      {
        bank: BankType.FACILITY,
        drugInventoryId: 'drug-inventory-id',
        quantityDispensed: 4,
      },
    ],
  ]),
};
const ManagerMock = {
  withRepository: jest.fn().mockReturnValue({
    save: mockEntityManagerSave,
    createOncologyRegister: jest.fn(
      () => oncologyConsultationData.oncologyRegister,
    ),
    updateOncologyHistory: jest.fn(() => [
      oncologyConsultationData,
      oncologyConsultationData,
    ]),
    deleteOncologyHistory: mockOncologyConsultationDelete,
    delete: jest.fn(),
    ...mockMedicationRepository,
  }),
  createQueryBuilder: jest.fn(() => ({
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    execute: jest.fn(),
  })),
  queryRunner: { isTransactionActive: true },
  save: jest.fn((...v) => (v.length > 1 ? v[1] : v[0])),
  findOneOrFail: jest.fn(),
  update: jest.fn(),
  find: jest.fn(),
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

const mockEventEmitter = {
  emit: jest.fn(),
};

const mockOncologyConsultationRepository = {
  manager: ManagerMock,
  findByProfile: jest.fn(() => ({
    totalCount: 1,
    list: [oncologyConsultationData],
  })),
  getOncologyHistory: jest.fn(() => oncologyConsultationData),
  updateOncologyRegister: jest.fn(
    () => oncologyConsultationData.oncologyRegister,
  ),
  archiveOncologyHistory: jest.fn(() => [oncologyConsultationData]),
  concealOncologyConsultationFields: jest.fn(),
  addChemoDrug: jest.fn(),
  updateChemoDrug: jest.fn(),
  deleteChemoDrug: jest.fn(),
  linkRecordsToOncologyConsultation: jest.fn(),
  getLinkedRadiologyInvestigationRecords: jest.fn(),
  getLinkedLaboratoryInvestigationRecords: jest.fn(),
  getLinkedInvestigationRecords: jest.fn(),
  findOneOrFail: jest.fn().mockResolvedValue(oncologyConsultationData),
  oncologyConsultationTreatmentPlans: jest.fn(() => treatmentPlanData),
  addOncologyConsultationTreatmentPlan: jest.fn(() => treatmentPlanData),
  updateOncologyConsultationTreatmentPlan: jest.fn(() => treatmentPlanData),
  deleteOncologyConsultationTreatmentPlan: jest.fn(() => treatmentPlanData),
  concealOncologyTreatmentPlanFields: jest.fn(() => treatmentPlanData),
  updateOncologyTreatmentPlanConsentSignature: jest.fn(),
  save: jest.fn(),
};

const MockProfileRepository = {
  findOne: jest.fn(() => mockUser.defaultProfile),
};

const BillServiceMock = {
  deleteAutoGeneratedBillItems: jest.fn(),
  generateMultipleBill: jest.fn(() => billFactory.build()),
  updateMultipleBill: jest.fn(() => billFactory.build()),
};

const dsMock = {
  getRepository: jest.fn(() => ({
    extend: jest.fn(),
  })),
  transaction: jest.fn((cb) => cb(ManagerMock)),
  manager: ManagerMock,
  createQueryRunner: jest.fn(() => ({
    release: jest.fn(),
    query: jest.fn(),
  })),
};

const mockOncologyConsultationToInvestigation = {
  create: jest.fn(),
  save: jest.fn().mockReturnValue(oncologyConsultationToInvestigationData),
  delete: jest.fn(),
};

const mockLinkService = {
  getLinkedConnections: jest.fn(() => ({
    oncology_consultation_investigation:
      oncologConsultationDataWithInvestigation.oncology_consultation_investigation,
  })),
};

const MockFacilityPreferenceService = {
  saveChemoDiagnosisTemplate: jest.fn(() => Promise.resolve({})),
};

const mockInventoryService = {
  updateInventoryQtys: jest.fn(),
};

const MockTreatmentPlanRepository = {
  create: jest.fn(),
  save: jest.fn((v) => v),
  delete: jest.fn(),
};

describe('OncologyConsultationHistoryService', () => {
  let service: OncologyConsultationHistoryService;
  const user = mockUser as UserModel;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OncologyConsultationHistoryService,
        {
          provide: getRepositoryToken(OncologyConsultationHistoryModel),
          useValue: mockOncologyConsultationRepository,
        },
        {
          provide: EntityManager,
          useValue: ManagerMock,
        },
        {
          provide: BillService,
          useValue: BillServiceMock,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: MockProfileRepository,
        },
        {
          provide: getRepositoryToken(OncologyConsultationToInvestigation),
          useValue: mockOncologyConsultationToInvestigation,
        },
        {
          provide: getRepositoryToken(OncologyTreatmentPlanModel),
          useValue: MockTreatmentPlanRepository,
        },
        {
          provide: DataSource,
          useValue: dsMock,
        },
        {
          provide: LinkService,
          useValue: mockLinkService,
        },
        {
          provide: HmoClaimService,
          useValue: MockHmoClaimService,
        },
        {
          provide: FacilityPreferenceService,
          useValue: MockFacilityPreferenceService,
        },
        {
          provide: InventoryService,
          useValue: mockInventoryService,
        },
        {
          provide: getRepositoryToken(MedicationModel),
          useValue: mockMedicationRepository,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        { ...loggerMock },
      ],
    }).compile();

    service = module.get<OncologyConsultationHistoryService>(
      OncologyConsultationHistoryService,
    );
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(service).toBeTruthy();
  });

  it('getOncologyHistory(): should get one oncology consultation', async () => {
    const input = oncologyConsultationFactory.build();
    delete input.profile;
    await service.getOncologyHistory(user.defaultProfile, input.id);
    expect(
      mockOncologyConsultationRepository.getOncologyHistory,
    ).toHaveBeenCalledWith(user.defaultProfile, input.id);
  });

  it('getOncologyHistory(): should throw error if oncology consultation is not found', async () => {
    jest
      .spyOn(mockOncologyConsultationRepository, 'getOncologyHistory')
      .mockReturnValue(null);

    await expect(
      service.getOncologyHistory(user.defaultProfile, 'record-id'),
    ).rejects.toThrow(NotFoundException);
  });

  it('getOncologyHistories(): should get all oncology consultation', async () => {
    const mutator = profileFactory.build();
    const input = oncologyConsultationFactory.build();
    delete input.profile;
    await service.getOncologyHistories(mutator, user.defaultProfile.id, {
      skip: 0,
      take: 50,
    });
    expect(
      mockOncologyConsultationRepository.findByProfile,
    ).toHaveBeenCalledWith(mutator, user.defaultProfile.id, {
      skip: 0,
      take: 50,
    });
  });

  it('getOncologyHistories(): should get filtered oncology consultations', async () => {
    const mutator = profileFactory.build();
    const input = oncologyConsultationFactory.build();
    delete input.profile;
    await service.getOncologyHistories(mutator, user.defaultProfile.id, {
      skip: 0,
      take: 50,
      keyword: 'Family Surgeon',
    });
    expect(
      mockOncologyConsultationRepository.findByProfile,
    ).toHaveBeenCalledWith(mutator, user.defaultProfile.id, {
      skip: 0,
      take: 50,
      keyword: 'Family Surgeon',
    });
  });

  it('saveOncologyHistory() should save oncology consultations using user profile', async () => {
    const input = oncologyConsultationFactory.build();
    await service.saveOncologyHistory(user.defaultProfile, input);
    expect(mockEntityManagerSave).toHaveBeenCalled();
  });

  it('saveOncologyHistory() should save oncology consultations with treatment plan', async () => {
    const input = oncologyConsultationFactory.build();
    const treatmentPlan = chance.word();
    const patientAdmitted = chance.bool();

    await service.saveOncologyHistory(user.defaultProfile, {
      ...input,
      treatmentPlan,
      patientAdmitted,
    });
    expect(mockEntityManagerSave).toHaveBeenCalled();
  });

  it('saveOncologyHistory() and also save chemo drug combination template', async () => {
    const input = oncologyConsultationFactory.build({
      saveAsTemplate: ['pre', 'chemo', 'post'],
      oncologyChemoDrugs: [
        {
          day: '1',
          drugName: 'Drug 1',
          dosage: '1',
          dosagePercentage: '2',
          totalDose: '1',
          quantity: '5',
          route: 'Oral',
          investigationDetails: [
            { investigationType: 'Laboratory', investigationName: 'DIMER' },
          ],
          combinationGroupName: 'Combination 1',
          cycleNumber: 1,
          section: 'pre',
        },
        {
          day: '1',
          drugName: 'Drug 2',
          dosage: '1',
          dosagePercentage: '100',
          totalDose: '1',
          quantity: '10',
          route: 'Oral',
          investigationDetails: [
            { investigationType: 'Laboratory', investigationName: 'DIMER' },
          ],
          combinationGroupName: 'Combination 2',
          cycleNumber: 1,
          section: 'chemo',
        },
        {
          day: '1',
          drugName: 'Drug 3',
          dosage: '1',
          dosagePercentage: '0',
          totalDose: '1',
          quantity: '12',
          route: 'Oral',
          investigationDetails: [
            { investigationType: 'Laboratory', investigationName: 'DIMER' },
          ],
          combinationGroupName: 'Combination 3',
          cycleNumber: 1,
          section: 'post',
        },
      ],
    });
    spySlaveQuery.mockImplementation(() =>
      Promise.resolve([{ id: 'template-id' }]),
    );
    await service.saveOncologyHistory(
      { ...user.defaultProfile, type: 'OrganizationAdmin' } as any,
      input,
    );
    expect(
      MockFacilityPreferenceService.saveChemoDiagnosisTemplate,
    ).toHaveBeenCalledTimes(3);
  });

  it('saveOncologyHistory() and also save chemo drug combination and prescribe drugs template', async () => {
    const input = oncologyConsultationFactory.build({
      saveAsTemplate: ['pre', 'chemo', 'post'],
      oncologyChemoDrugs: [
        {
          day: '1',
          drugName: 'Drug',
          dosage: '1',
          dosagePercentage: '100',
          totalDose: '1',
          quantity: '1',
          route: 'Oral',
          investigationDetails: [
            { investigationType: 'Laboratory', investigationName: 'DIMER' },
          ],
          combinationGroupName: 'NAME',
          cycleNumber: 1,
          section: 'chemo',
        },
      ],
    });
    ManagerMock.withRepository().saveOncologyPrescribeMedications = jest.fn(
      () => medicationFactory.build(),
    );
    spySlaveQuery.mockImplementation(() =>
      Promise.resolve([{ id: 'template-id' }]),
    );
    await service.saveOncologyHistory(
      { ...user.defaultProfile, type: 'OrganizationAdmin' } as any,
      input,
      true,
    );
    expect(
      MockFacilityPreferenceService.saveChemoDiagnosisTemplate,
    ).toHaveBeenCalledTimes(3);
    expect(
      ManagerMock.withRepository().saveOncologyPrescribeMedications,
    ).toHaveBeenCalledTimes(1);
  });

  it('saveOncologyHistory() should save oncology consultations using user profile', async () => {
    const input = oncologyConsultationFactory.build();
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationDoctor;
    await service.saveOncologyHistory(profile, input);
    expect(mockEntityManagerSave).toHaveBeenCalled();
    expect(BillServiceMock.generateMultipleBill).toHaveBeenCalled();
  });

  it('saveOncologyHistory() should save oncology consultations for another user', async () => {
    const newUser = mockUser as UserModel;
    const input = oncologyConsultationFactory.build();
    await service.saveOncologyHistory(
      { ...newUser.defaultProfile, id: 'different-profile-id' } as any,
      input,
    );
    expect(mockEntityManagerSave).toHaveBeenCalled();
  });
  it('saveOncologyHistory() should save oncology consultations and hmoClaim', async () => {
    const input = oncologyConsultationFactory.build();
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationDoctor;
    const hmoClaim = hmoClaimFactory.build();
    delete hmoClaim.profile;
    input.hmoClaim = hmoClaim;
    await service.saveOncologyHistory(profile, input);
    expect(MockHmoClaimService.createHmoClaimInTransaction).toHaveBeenCalled();
  });
  it('saveOncologyHistory() should save oncology consultations with bill', async () => {
    const input = oncologyConsultationFactory.build();
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationDoctor;
    input.serviceDetails = [
      {
        type: 'service type',
        name: 'service name',
        quantity: '2',
        pricePerUnit: '100',
      },
      {
        type: 'service type 1',
        name: 'service name 2',
        quantity: '1',
        pricePerUnit: '300',
      },
    ];
    await service.saveOncologyHistory(profile, input);
    expect(BillServiceMock.generateMultipleBill).toHaveBeenCalled();
  });

  it('saveOncologyHistory() should save oncology consultations with chemo drugs', async () => {
    const input = oncologyConsultationFactory.build();
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationDoctor;
    input.oncologyChemoDrugs = [
      {
        day: '1',
        cycleNumber: 1,
        drugName: 'Book',
        dosage: '50ml',
        dosagePercentage: 'full',
        route: 'mouth',
      },
    ];
    await service.saveOncologyHistory(profile, input);
    expect(ManagerMock.save).toHaveBeenCalled();
  });

  it('updateOncologyHistory(): should update oncology consultation', async () => {
    const input = oncologyConsultationFactory.build();
    delete input.profile;
    await service.updateOncologyHistory(
      user.defaultProfile,
      input.id,
      input,
      false,
    );
    expect(
      ManagerMock.withRepository().updateOncologyHistory,
    ).toHaveBeenCalled();
  });

  it('updateOncologyHistory(): should update oncology consultation with bill', async () => {
    const input = oncologyConsultationFactory.build();
    delete input.profile;
    input.serviceDetails = [
      {
        type: 'service type',
        name: 'service name',
        quantity: '2',
        pricePerUnit: '100',
      },
      {
        type: 'service type 1',
        name: 'service name 2',
        quantity: '1',
        pricePerUnit: '300',
      },
    ];
    await service.updateOncologyHistory(
      user.defaultProfile,
      input.id,
      input,
      false,
    );
    expect(
      ManagerMock.withRepository().updateOncologyHistory,
    ).toHaveBeenCalled();
  });

  it('updateOncologyHistory(): should throw error if oncology consultation not found', async () => {
    ManagerMock.withRepository().updateOncologyHistory.mockReturnValue([
      null,
      null,
    ]);

    await expect(
      service.updateOncologyHistory(
        user.defaultProfile,
        'oncology-id',
        {
          specialty: 'Nothing',
        },
        false,
      ),
    ).rejects.toThrow(NotFoundException);
  });

  it('updateOncologyHistory() should update medication details', async () => {
    const input = oncologyConsultationFactory.build();
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationDoctor;
    const oncologyChemoDrugs = [
      {
        day: '1',
        cycleNumber: 1,
        drugName: 'Book 1',
        dosage: '50ml',
        dosagePercentage: 'full',
        route: 'mouth',
        id: 'chemo-drug-id',
        medicationDetailsId: 'medication-details-id',
      },
      {
        day: '1',
        cycleNumber: 1,
        drugName: 'Book',
        dosage: '50ml',
        dosagePercentage: 'full',
        route: 'mouth',
        id: 'chemo-drug-id-2',
      },
    ];
    ManagerMock.withRepository().updateOncologyHistory.mockReturnValue([
      oncologyConsultationData,
      oncologyConsultationData,
      {
        validChemoDrugs: oncologyChemoDrugs,
        unsavedChemoDrugs: oncologyChemoDrugs,
        deletedChemoDrugs: [
          {
            day: '1',
            cycleNumber: 1,
            drugName: 'Book',
            dosage: '50ml',
            dosagePercentage: 'full',
            route: 'mouth',
            id: 'chemo-drug-id-3',
          },
        ],
      },
      true,
    ]);
    ManagerMock.withRepository().saveOncologyPrescribeMedications = jest.fn(
      () => medicationFactory.build(),
    );
    await service.updateOncologyHistory(
      profile,
      input.id,
      {
        ...input,
        oncologyChemoDrugs,
      },
      false,
      true,
    );
    expect(
      ManagerMock.withRepository().updateOncologyPrescribeMedication,
    ).toHaveBeenCalledTimes(1);
    expect(
      ManagerMock.withRepository().saveOncologyPrescribeMedications,
    ).toHaveBeenCalledTimes(0);
    expect(
      ManagerMock.withRepository().deleteOncologyPrescribeMedicationDetails,
    ).toHaveBeenCalledTimes(0);
  });

  it('updateOncologyRegister(): should update oncology register', async () => {
    let input = oncologyConsultationFactory.build();
    input = input.oncologyRegister;

    delete input.profile;
    await service.updateOncologyRegister(
      user.defaultProfile,
      input.id,
      OncologyChartType.Therapy,
      input,
    );
    expect(
      mockOncologyConsultationRepository.updateOncologyRegister,
    ).toHaveBeenCalled();
  });

  it('deleteOncologyHistory(): should delete oncology consultation', async () => {
    const input = oncologyConsultationFactory.build();
    delete input.profile;
    await service.deleteOncologyHistory(user.defaultProfile, [input.id]);
    expect(mockOncologyConsultationDelete).toHaveBeenCalledWith(
      user.defaultProfile,
      [input.id],
    );
    expect(BillServiceMock.deleteAutoGeneratedBillItems).toHaveBeenCalled();
  });
  it('deleteOncologyHistory(): should delete oncology consultation and flag hmoClaim', async () => {
    const input = oncologyConsultationFactory.build();
    delete input.profile;
    const hmoClaim = hmoClaimFactory.build();
    delete hmoClaim.profile;
    input.hmoClaim = hmoClaim;
    ManagerMock.withRepository.mockImplementationOnce(() => ({
      deleteOncologyHistory: jest.fn(() => [input]),
    }));
    await service.deleteOncologyHistory(user.defaultProfile, [input.id]);
    expect(
      MockHmoClaimService.flagHmoClaimDeletedInTransaction,
    ).toHaveBeenCalled();
  });
  it('deleteOncologyHistory(): should delete oncology consultation with sub bill', async () => {
    const input = oncologyConsultationFactory.build();
    delete input.profile;
    input.subBillRef = 'sub-bill-ref';
    ManagerMock.withRepository.mockImplementationOnce(() => ({
      deleteOncologyHistory: jest.fn(() => [input]),
    }));
    await service.deleteOncologyHistory(user.defaultProfile, [input.id]);
    expect(BillServiceMock.deleteAutoGeneratedBillItems).toHaveBeenCalled();
  });
  it('deleteOncologyHistory(): should delete oncology consultation and medication', async () => {
    const input = oncologyConsultationFactory.build();
    input.oncologyChemoDrugs = [
      {
        day: '1',
        cycleNumber: 1,
        drugName: 'Book',
        dosage: '50ml',
        dosagePercentage: 'full',
        route: 'mouth',
        id: 'chemo-drug-id',
        medicationDetailsId: 'medication-details-id',
      },
    ];
    ManagerMock.withRepository.mockImplementationOnce(() => ({
      deleteOncologyHistory: jest.fn(() => [input]),
    }));

    expect(BillServiceMock.deleteAutoGeneratedBillItems).not.toBeCalled();
    await service.deleteOncologyHistory(user.defaultProfile, [input.id]);

    expect(
      ManagerMock.withRepository().deleteOncologyPrescribeMedication,
    ).toBeCalledTimes(1);
    expect(mockInventoryService.updateInventoryQtys).toBeCalledTimes(1);
    expect(BillServiceMock.deleteAutoGeneratedBillItems).toBeCalledTimes(2);
  });
  it('archiveOncologyHistory(): should archive oncology consultation', async () => {
    const input = oncologyConsultationFactory.build();
    delete input.profile;
    await service.archiveOncologyHistory(user.defaultProfile, [input.id], true);
    expect(
      mockOncologyConsultationRepository.archiveOncologyHistory,
    ).toHaveBeenCalledWith(
      user.defaultProfile,
      [input.id],
      true,
      BillServiceMock,
    );
  });

  it('concealConsultationComplaint(): should call concealOncologyConsultationFields with concealComplaint', async () => {
    const mutator = profileFactory.build();

    await service.concealConsultationComplaint(mutator, 'oncology-id', false);

    expect(
      mockOncologyConsultationRepository.concealOncologyConsultationFields,
    ).toHaveBeenLastCalledWith(
      mutator,
      'oncology-id',
      false,
      'concealComplaint',
    );
  });

  it('concealConsultationComplaintHistory(): should call concealOncologyConsultationFields with concealComplaintHistory', async () => {
    const mutator = profileFactory.build();

    await service.concealConsultationComplaintHistory(
      mutator,
      'oncology-id',
      false,
    );

    expect(
      mockOncologyConsultationRepository.concealOncologyConsultationFields,
    ).toHaveBeenLastCalledWith(
      mutator,
      'oncology-id',
      false,
      'concealComplaintHistory',
    );
  });

  it('concealConsultationSystemReview(): should call concealOncologyConsultationFields with concealSystemReview', async () => {
    const mutator = profileFactory.build();

    await service.concealConsultationSystemReview(mutator, 'oncology-id', true);

    expect(
      mockOncologyConsultationRepository.concealOncologyConsultationFields,
    ).toHaveBeenLastCalledWith(
      mutator,
      'oncology-id',
      true,
      'concealSystemReview',
    );
  });

  it('concealConsultationPhysicalExam(): should call concealOncologyConsultationFields with concealPhysicalExam', async () => {
    const mutator = profileFactory.build();

    await service.concealConsultationPhysicalExam(mutator, 'oncology-id', true);

    expect(
      mockOncologyConsultationRepository.concealOncologyConsultationFields,
    ).toHaveBeenLastCalledWith(
      mutator,
      'oncology-id',
      true,
      'concealPhysicalExam',
    );
  });

  it('concealConsultationAudiometry(): should call concealOncologyConsultationFields with concealAudiometry', async () => {
    const mutator = profileFactory.build();

    await service.concealConsultationAudiometry(mutator, 'oncology-id', false);

    expect(
      mockOncologyConsultationRepository.concealOncologyConsultationFields,
    ).toHaveBeenLastCalledWith(
      mutator,
      'oncology-id',
      false,
      'concealAudiometry',
    );
  });

  it('concealConsultationHealthEducation(): should call concealOncologyConsultationFields with concealHealthEducation', async () => {
    const mutator = profileFactory.build();

    await service.concealConsultationHealthEducation(
      mutator,
      'oncology-id',
      false,
    );

    expect(
      mockOncologyConsultationRepository.concealOncologyConsultationFields,
    ).toHaveBeenLastCalledWith(
      mutator,
      'oncology-id',
      false,
      'concealHealthEducation',
    );
  });

  it('concealOncologyConsultationChemoNote(): should call concealOncologyConsultationFields with concealChemoNote', async () => {
    const mutator = profileFactory.build();

    await service.concealOncologyConsultationChemoNote(
      mutator,
      'oncology-id',
      false,
    );

    expect(
      mockOncologyConsultationRepository.concealOncologyConsultationFields,
    ).toHaveBeenLastCalledWith(
      mutator,
      'oncology-id',
      false,
      'concealChemoNote',
    );
  });

  it('addChemoDrug(): should call addChemoDrug', async () => {
    const input = {} as any;
    await service.addChemoDrug(user.defaultProfile, input);
    expect(mockOncologyConsultationRepository.addChemoDrug).toHaveBeenCalled();
  });

  it('updateChemoDrug(): should call updateChemoDrug', async () => {
    const input = {} as any;
    await service.updateChemoDrug(user.defaultProfile, input);
    expect(
      mockOncologyConsultationRepository.updateChemoDrug,
    ).toHaveBeenCalled();
  });

  it('deleteChemoDrug(): should call deleteChemoDrug', async () => {
    await service.deleteChemoDrug('id');
    expect(
      mockOncologyConsultationRepository.deleteChemoDrug,
    ).toHaveBeenCalled();
  });

  it('administerChemoDrug(): should call update chemo drug administered in a specific period, status is true', async () => {
    const mutator = profileFactory.build({
      id: '',
      fullName: '',
    });

    ManagerMock.findOneOrFail = jest.fn().mockResolvedValue({
      administrationRegister: [
        {
          period: 'period_0',
        },
        {
          period: 'period_1',
        },
      ],
    });

    const result = await service.administerChemoDrug(
      mutator,
      'chemo_id',
      'period_1',
      true,
    );

    expect(ManagerMock.update).toHaveBeenCalled();
    expect(result).toStrictEqual({
      administrationRegister: [
        {
          period: 'period_0',
        },
        expect.objectContaining({
          period: 'period_1',
          administeredBy: mutator.fullName,
          administratorId: mutator.id,
        }),
      ],
    });
    expect(
      result.administrationRegister[1].administrationDateTime,
    ).toBeTruthy();
  });

  it('administerChemoDrug(): should call update chemo drug administered in a specific period, status is false', async () => {
    const mutator = profileFactory.build({
      id: '',
      fullName: '',
    });

    ManagerMock.findOneOrFail = jest.fn().mockResolvedValue({
      administrationRegister: [
        {
          period: 'period_0',
        },
        {
          period: 'period_1',
        },
      ],
    });

    const result = await service.administerChemoDrug(
      mutator,
      'chemo_id',
      'period_1',
      false,
    );

    expect(ManagerMock.update).toHaveBeenCalled();
    expect(result).toStrictEqual({
      administrationRegister: [
        {
          period: 'period_0',
        },
        {
          period: 'period_1',
          administeredBy: null,
          administratorId: null,
          administrationDateTime: null,
        },
      ],
    });
  });

  it('linkRecordsToOncologyConsultation should call linkRecordsToOncologyConsultation from repo', async () => {
    await service.linkRecordsToOncologyConsultation(
      user.defaultProfile,
      'oncology-id',
      OncologyConsultationLinkedRecordType.Admission,
      ['linked-record-id'],
    );

    expect(
      mockOncologyConsultationRepository.linkRecordsToOncologyConsultation,
    ).toHaveBeenCalledWith(
      oncologyConsultationData,
      ['linked-record-id'],
      user.defaultProfile,
      OncologyConsultationLinkedRecordType.Admission,
    );
  });

  it('linkRecordsToOncologyConsultation() should link Lab investigation records to consultation', async () => {
    mockOncologyConsultationRepository.manager.find = jest
      .fn()
      .mockReturnValue([investigationData]);

    await service.linkRecordsToOncologyConsultation(
      user.defaultProfile,
      'oncology-id',
      OncologyConsultationLinkedRecordType.LabTest,
      ['lab-investigation-id'],
    );

    expect(mockOncologyConsultationToInvestigation.save).toHaveBeenCalled();
  });

  it('linkRecordsToOncologyConsultation() should link Radiology investigation records to consultation', async () => {
    mockOncologyConsultationRepository.manager.find = jest
      .fn()
      .mockReturnValue([investigationData]);

    await service.linkRecordsToOncologyConsultation(
      user.defaultProfile,
      'oncology-id',
      OncologyConsultationLinkedRecordType.Radiology,
      ['radiology-investigation-id'],
    );

    expect(mockOncologyConsultationToInvestigation.save).toHaveBeenCalled();
  });

  it('linkRecordsToOncologyConsultation() should link Radiology investigation records to consultation', async () => {
    mockOncologyConsultationRepository.manager.find = jest
      .fn()
      .mockReturnValue([investigationData]);

    await service.linkRecordsToOncologyConsultation(
      user.defaultProfile,
      'oncology-id',
      OncologyConsultationLinkedRecordType.Investigation,
      ['investigation-id'],
    );

    expect(mockOncologyConsultationToInvestigation.save).toHaveBeenCalled();
  });

  it('linkRecordsToOncologyConsultation() should throw if consultation is not found', async () => {
    mockOncologyConsultationRepository.findOneOrFail = jest
      .fn()
      .mockRejectedValue('error');

    await expect(
      service.linkRecordsToOncologyConsultation(
        user.defaultProfile,
        'non-existing-oncology-id',
        OncologyConsultationLinkedRecordType.Admission,
        ['investigation-id'],
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateOncologyHistory(): should update prescriptions', async () => {
    const input = oncologyConsultationFactory.build();
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationDoctor;
    const oncologyChemoDrugs = [
      {
        day: '1',
        cycleNumber: 1,
        drugName: 'Book 1',
        dosage: '50ml',
        dosagePercentage: 'full',
        route: 'mouth',
        id: 'chemo-drug-id',
        medicationDetailsId: 'medication-details-id',
      },
      {
        day: '1',
        cycleNumber: 1,
        drugName: 'Book',
        dosage: '50ml',
        dosagePercentage: 'full',
        route: 'mouth',
        id: 'chemo-drug-id-2',
      },
    ];
    ManagerMock.withRepository().updateOncologyHistory.mockReturnValue([
      oncologyConsultationData,
      oncologyConsultationData,
      {
        validChemoDrugs: oncologyChemoDrugs,
        unsavedChemoDrugs: oncologyChemoDrugs,
        deletedChemoDrugs: [
          {
            day: '1',
            cycleNumber: 1,
            drugName: 'Book',
            dosage: '50ml',
            dosagePercentage: 'full',
            route: 'mouth',
            id: 'chemo-drug-id-3',
            medicationDetailsId: 'medication_details_id_1',
          },
        ],
      },
      true,
    ]);
    ManagerMock.withRepository().saveOncologyPrescribeMedications = jest.fn(
      () => medicationFactory.build(),
    );
    await service.updateOncologyHistory(
      profile,
      input.id,
      {
        ...input,
        oncologyChemoDrugs,
      },
      false,
      true,
    );
    expect(
      ManagerMock.withRepository().updateOncologyPrescribeMedication,
    ).toHaveBeenCalledTimes(1);
    expect(
      ManagerMock.withRepository().deleteOncologyPrescribeMedicationDetails,
    ).toHaveBeenCalledTimes(1);
    expect(
      ManagerMock.withRepository().saveOncologyPrescribeMedications,
    ).toHaveBeenCalledTimes(0);
  });

  it('updateOncologyHistory(): should create prescriptions', async () => {
    const input = oncologyConsultationFactory.build();
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationDoctor;
    const oncologyChemoDrugs = [
      {
        day: '1',
        cycleNumber: 1,
        drugName: 'Book 1',
        dosage: '50ml',
        dosagePercentage: 'full',
        route: 'mouth',
        id: 'chemo-drug-id',
      },
      {
        day: '1',
        cycleNumber: 1,
        drugName: 'Book',
        dosage: '50ml',
        dosagePercentage: 'full',
        route: 'mouth',
        id: 'chemo-drug-id-2',
      },
    ];
    ManagerMock.withRepository().updateOncologyHistory.mockReturnValue([
      oncologyConsultationData,
      oncologyConsultationData,
      {
        validChemoDrugs: oncologyChemoDrugs,
        unsavedChemoDrugs: oncologyChemoDrugs,
        deletedChemoDrugs: [],
      },
      true,
    ]);
    ManagerMock.withRepository().saveOncologyPrescribeMedications = jest.fn(
      () => medicationFactory.build(),
    );
    await service.updateOncologyHistory(
      profile,
      input.id,
      {
        ...input,
        oncologyChemoDrugs,
      },
      false,
      true,
    );
    expect(
      ManagerMock.withRepository().saveOncologyPrescribeMedications,
    ).toHaveBeenCalledTimes(1);
    expect(
      ManagerMock.withRepository().updateOncologyPrescribeMedication,
    ).toHaveBeenCalledTimes(0);
    expect(
      ManagerMock.withRepository().deleteOncologyPrescribeMedicationDetails,
    ).toHaveBeenCalledTimes(0);
  });

  it('getChemoDrugsByMedicationDetailsId(): should call find method with correct parameters', async () => {
    const medicationDetailsId = chance.guid({ version: 4 });

    await service.getChemoDrugsByMedicationDetailsId(medicationDetailsId);

    expect(ManagerMock.find).toHaveBeenLastCalledWith(OncologyChemoDrugModel, {
      relations: ['oncologyConsultationHistory'],
      where: { medicationDetailsId },
    });
  });

  it('getLinkedRadiologyInvestigationRecords(): should call getLinkedRadiologyInvestigationRecords repo method', async () => {
    const profile = profileFactory.build();
    const oncologyId = chance.guid({ version: 4 });

    await service.getLinkedRadiologyInvestigationRecords(profile, oncologyId);

    expect(
      mockOncologyConsultationRepository.getLinkedRadiologyInvestigationRecords,
    ).toHaveBeenLastCalledWith(profile, oncologyId);
  });

  it('getLinkedLaboratoryInvestigationRecords(): should call getLinkedLaboratoryInvestigationRecords repo method', async () => {
    const profile = profileFactory.build();
    const oncologyId = chance.guid({ version: 4 });

    await service.getLinkedLaboratoryInvestigationRecords(profile, oncologyId);

    expect(
      mockOncologyConsultationRepository.getLinkedLaboratoryInvestigationRecords,
    ).toHaveBeenLastCalledWith(profile, oncologyId);
  });

  it('getLinkedInvestigationRecords(): should call getLinkedInvestigationRecords repo method', async () => {
    const profile = profileFactory.build();
    const oncologyId = chance.guid({ version: 4 });

    await service.getLinkedInvestigationRecords(profile, oncologyId);

    expect(
      mockOncologyConsultationRepository.getLinkedInvestigationRecords,
    ).toHaveBeenLastCalledWith(profile, oncologyId);
  });

  it('oncologyConsultationTreatmentPlans(): should call oncologyConsultationTreatmentPlans repository method', async () => {
    const profile = profileFactory.build();
    const oncologyId = chance.guid({ version: 4 });

    await service.oncologyConsultationTreatmentPlans(profile, oncologyId);

    expect(
      mockOncologyConsultationRepository.oncologyConsultationTreatmentPlans,
    ).toHaveBeenCalledWith(profile, oncologyId);
  });

  it('addOncologyConsultationTreatmentPlan(): should call addOncologyConsultationTreatmentPlan repository method', async () => {
    await service.addOncologyConsultationTreatmentPlan(
      user.defaultProfile,
      oncologyConsultationData.id,
      treatmentPlanData,
    );

    expect(
      mockOncologyConsultationRepository.addOncologyConsultationTreatmentPlan,
    ).toHaveBeenCalledWith(
      user.defaultProfile,
      oncologyConsultationData.id,
      treatmentPlanData,
    );
  });

  it('updateOncologyConsultationTreatmentPlan(): should call updateOncologyConsultationTreatmentPlan repository method', async () => {
    const profile = profileFactory.build();
    const treatmentPlan = treatmentPlanFactory.build();

    await service.updateOncologyConsultationTreatmentPlan(
      profile,
      treatmentPlan.id,
      treatmentPlan,
    );
    expect(
      mockOncologyConsultationRepository.updateOncologyConsultationTreatmentPlan,
    ).toHaveBeenCalledWith(profile, treatmentPlan.id, treatmentPlan);
  });

  it('deleteOncologyConsultationTreatmentPlan(): should call deleteOncologyConsultationTreatmentPlan repository method', async () => {
    const profile = profileFactory.build();

    await service.deleteOncologyConsultationTreatmentPlan(
      profile,
      treatmentPlanData.id,
    );

    expect(
      mockOncologyConsultationRepository.deleteOncologyConsultationTreatmentPlan,
    ).toHaveBeenCalledWith(profile, treatmentPlanData.id);
  });

  it('concealOncologyConsultationTreatmentPlan(): should call concealOncologyTreatmentPlanFields repository method', async () => {
    await service.concealOncologyConsultationTreatmentPlan(
      user.defaultProfile,
      'treatment-plan-id',
      true,
    );

    expect(
      mockOncologyConsultationRepository.concealOncologyTreatmentPlanFields,
    ).toHaveBeenCalledWith(
      user.defaultProfile,
      'treatment-plan-id',
      true,
      'conceal',
    );
  });

  it('concealOncologyTreatmentPlanObservationNote(): should call concealOncologyTreatmentPlanFields repository method', async () => {
    await service.concealOncologyTreatmentPlanObservationNote(
      user.defaultProfile,
      'treatment-plan-id',
      true,
    );

    expect(
      mockOncologyConsultationRepository.concealOncologyTreatmentPlanFields,
    ).toHaveBeenCalledWith(
      user.defaultProfile,
      'treatment-plan-id',
      true,
      'concealObservationNote',
    );
  });

  it(`saveOncologyTreatmentPlanConsentSignature():
    should call the updateOncologyTreatmentPlanConsentSignature repository with shouldSave true`, async () => {
    await service.saveOncologyTreatmentPlanConsentSignature(
      user.defaultProfile,
      'record-id',
      {
        patientConsentSignature: 'John Doe',
        patientConsentSignatureType: 'uploads',
      },
    );

    expect(
      mockOncologyConsultationRepository.updateOncologyTreatmentPlanConsentSignature,
    ).toHaveBeenLastCalledWith(
      user.defaultProfile,
      'record-id',
      'John Doe',
      'uploads',
      true,
    );
  });

  it(`updateOncologyTreatmentPlanConsentSignature():
    should call the updateOncologyTreatmentPlanConsentSignature repository without shouldSave true`, async () => {
    await service.updateOncologyTreatmentPlanConsentSignature(
      user.defaultProfile,
      'record-id',
      {
        patientConsentSignature: 'Mary Slice',
        patientConsentSignatureType: 'draw',
      },
    );

    expect(
      mockOncologyConsultationRepository.updateOncologyTreatmentPlanConsentSignature,
    ).toHaveBeenLastCalledWith(
      user.defaultProfile,
      'record-id',
      'Mary Slice',
      'draw',
    );
  });

  it(`removeOncologyTreatmentPlanConsentSignature():
    should call the updateOncologyTreatmentPlanConsentSignature repository without empty signature and signature type`, async () => {
    await service.removeOncologyTreatmentPlanConsentSignature(
      user.defaultProfile,
      'record-id',
    );

    expect(
      mockOncologyConsultationRepository.updateOncologyTreatmentPlanConsentSignature,
    ).toHaveBeenLastCalledWith(user.defaultProfile, 'record-id', '', '');
  });

  it('updateChemoComment(): should throw error comments', async () => {
    const mutator = profileFactory.build();

    ManagerMock.findOneOrFail = jest.fn().mockRejectedValue('');

    await expect(
      service.updateChemoComment(mutator, 'chemo-drug-id', {
        cycleNumber: 2,
        section: 'post',
        comment: 'comment',
      }),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateChemoComment(): should update chemo comment', async () => {
    const mutator = profileFactory.build();
    const chemoDrug = {
      ...oncologyChemoDrugFactory.build(),
      oncologyConsultationHistory: {
        ...oncologyConsultationFactory.build(),
        chemoComments: [],
      },
    };

    ManagerMock.findOneOrFail = jest.fn().mockResolvedValue(chemoDrug);

    // Add New Comment
    let response = await service.updateChemoComment(mutator, 'chemo-drug-id', {
      cycleNumber: 2,
      section: 'post',
      comment: '2 times a day',
    });

    let chemoComments = response?.oncologyConsultationHistory?.chemoComments;

    expect(chemoComments.length).toBe(1);
    expect(chemoComments[0].comment).toBe('2 times a day');
    expect(chemoComments[0].creatorId).toBe(mutator.id);
    expect(chemoComments[0].creatorName).toBe(mutator.fullName);
    expect(chemoComments[0].lastModifierId).toBeFalsy();
    expect(chemoComments[0].lastModifierName).toBeFalsy();
    expect(mockOncologyConsultationRepository.save).toHaveBeenCalledTimes(1);

    ManagerMock.findOneOrFail = jest.fn().mockResolvedValue({
      ...chemoDrug,
      oncologyConsultationHistory: {
        ...chemoDrug.oncologyConsultationHistory,
        chemoComments: [
          {
            cycleNumber: 2,
            section: 'post',
            comment: '2 times a day',
            creatorId: mutator.id,
            creatorName: mutator.fullName,
          },
        ],
      },
    });

    const mutator2 = profileFactory.build();

    // Update Added Comment
    response = await service.updateChemoComment(mutator2, 'chemo-drug-id', {
      cycleNumber: 2,
      section: 'post',
      comment: '2 times a day, morning and night',
    });

    chemoComments = response?.oncologyConsultationHistory?.chemoComments;

    expect(chemoComments.length).toBe(1);
    expect(chemoComments[0].comment).toBe('2 times a day, morning and night');
    expect(chemoComments[0].creatorId).toBe(mutator.id);
    expect(chemoComments[0].creatorName).toBe(mutator.fullName);
    expect(chemoComments[0].lastModifierId).toBe(mutator2.id);
    expect(chemoComments[0].lastModifierName).toBe(mutator2.fullName);
    expect(mockOncologyConsultationRepository.save).toHaveBeenCalledTimes(2);

    // Add Another Comment
    response = await service.updateChemoComment(mutator2, 'chemo-drug-id', {
      cycleNumber: 3,
      section: 'post',
      comment: 'Will need a refill',
    });

    chemoComments = response?.oncologyConsultationHistory?.chemoComments;

    expect(chemoComments.length).toBe(2);
    expect(chemoComments[1].comment).toBe('Will need a refill');
    expect(chemoComments[1].creatorId).toBe(mutator2.id);
    expect(chemoComments[1].creatorName).toBe(mutator2.fullName);
    expect(chemoComments[1].lastModifierId).toBeFalsy();
    expect(chemoComments[1].lastModifierName).toBeFalsy();
    expect(mockOncologyConsultationRepository.save).toHaveBeenCalledTimes(3);
  });
});
