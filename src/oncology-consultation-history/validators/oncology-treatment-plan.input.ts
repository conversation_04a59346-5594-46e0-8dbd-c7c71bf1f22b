import { Field, InputType } from '@nestjs/graphql';
import { IsOptional, IsUUID } from 'class-validator';

@InputType()
export class OncologyTreatmentPlanInput {
  @Field({ nullable: true })
  @IsUUID('4')
  @IsOptional()
  id?: string;

  @Field(() => String, { nullable: true })
  treatmentPlan: string;

  @Field(() => Boolean, { defaultValue: true })
  conceal?: boolean;

  @Field({ nullable: true })
  patientAdmitted?: string;

  @Field({ nullable: true })
  observationNote?: string;

  @Field(() => Boolean, { defaultValue: true })
  @IsOptional()
  concealObservationNote?: boolean;

  @Field({ nullable: true })
  admissionConsent?: string;

  @Field({ nullable: true })
  adverseEffectsFollowingTreatment: string;

  @Field({ nullable: true })
  stateEffects: string;

  @Field({ nullable: true })
  adverseEffectsInvestigated: string;

  @Field({ nullable: true })
  outcomeOfInvestigation: string;

  @Field({ nullable: true })
  treatmentGiven?: string;

  @Field(() => String, { nullable: true })
  patientConsentSignature: string;

  @Field(() => String, { nullable: true })
  patientConsentSignatureType: string;

  @Field(() => String, { nullable: true })
  patientConsentSignatureDateTime: Date;

  @Field({ nullable: true })
  treatmentStatus?: string;
}
