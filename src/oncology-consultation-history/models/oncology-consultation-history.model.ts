/* eslint-disable max-lines */
import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsUUID } from 'class-validator';
import {
  Column,
  Entity,
  OneToOne,
  PrimaryGeneratedColumn,
  JoinColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinT<PERSON>,
} from 'typeorm';
import { AuditEntitiesWithProfile } from './base-audits.entity';
import { OncologyConsultationRegisterModel } from './oncology-consultation-register.model';
import { OncologyTreatmentPlanModel } from './oncology-treatment-plan.model';
import { OncologyConsultationToInvestigation } from './oncology_consultation_investigation.model';
import { AdmissionModel } from '@clinify/admissions/models/admission.model';
import { AllergyModel } from '@clinify/allergies/models/allergy.model';
import { OrganisationAppointmentModel } from '@clinify/appointments/models/organisation_appointment.model';
import { BillModel } from '@clinify/bills/models/bill.model';
import { SelectionInput } from '@clinify/consultations/validators/consultation.input';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { MedicationModel } from '@clinify/medications/models/medication.model';
import { NursingServiceModel } from '@clinify/nursing-services/models/nursing-services.model';
import { OncologyChemoDrugModel } from '@clinify/oncology-consultation-history/models/oncology-chemo-drug.model';
import { PreauthorizationDetailsModel } from '@clinify/preauthorization-details/models/preauthorization-details.model';
import { ServiceDetailInput } from '@clinify/shared/validators/service-detail.input';
import { AsOutput } from '@clinify/shared/validators/validate.constant';
import { SurgeryModel } from '@clinify/surgeries/models/surgery.model';
import {
  ChemoCommentInput,
  TumorDetailsInput,
} from '@clinify/users/inputs/oncology-history.input';
import { VitalModel } from '@clinify/vitals/models/vital.model';

@ObjectType()
@Entity({ name: 'oncology_consultation_histories' })
export class OncologyConsultationHistoryModel extends AuditEntitiesWithProfile {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsUUID('4', AsOutput)
  id?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'consultation_date' })
  consultationDateTime: Date;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'duration' })
  duration: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'priority' })
  priority: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'category' })
  category: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'doctor_name' })
  doctorName: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'specialty' })
  specialty: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'rank' })
  rank: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'department' })
  department: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'consultation_start_date' })
  consultationStartDate: Date;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'consultation_end_date' })
  consultationEndDate: Date;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'facility_name' })
  facilityName: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'facility_address' })
  facilityAddress: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'initial_diagnosis_icd10', nullable: true })
  initialDiagnosisICD10: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'initial_diagnosis_icd11', nullable: true })
  initialDiagnosisICD11: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'initial_diagnosis_snomed', nullable: true })
  initialDiagnosisSNOMED: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'final_diagnosis_icd10', nullable: true })
  finalDiagnosisICD10: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'final_diagnosis_icd11', nullable: true })
  finalDiagnosisICD11: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'final_diagnosis_snomed', nullable: true })
  finalDiagnosisSNOMED: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'diagnosed_by', nullable: true })
  diagnosedBy: string;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'diagnosis_date_time', nullable: true })
  diagnosisDateTime: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'additional_note', nullable: true })
  additionalNote: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'stage_diagnosis_icd10', nullable: true })
  stageDiagnosisICD10: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'stage_diagnosis_icd11', nullable: true })
  stageDiagnosisICD11: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'stage_diagnosis_snomed', nullable: true })
  stageDiagnosisSNOMED: string;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'stage_diagnosis_date_time', nullable: true })
  stageDiagnosisDateTime: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'classification', nullable: true })
  classification: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'stage', nullable: true })
  stage: string;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'stage_date', nullable: true })
  stageDate: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'histopathologic_type', nullable: true })
  histopathologicType: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'stage_timing', nullable: true })
  stageTiming: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'primary_tumor', nullable: true })
  primaryTumor: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'residual_tumor', nullable: true })
  residualTumor: string;

  @Field(() => [TumorDetailsInput], { nullable: true })
  @Column({
    name: 'tumor_details',
    nullable: true,
    type: 'jsonb',
  })
  tumorDetails?: TumorDetailsInput[];

  @Field(() => String, { nullable: true })
  @Column({ name: 'lymphovascular_invasion', nullable: true })
  lymphovascularInvasion: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'regional_lymph_nodes', nullable: true })
  regionalLymphNodes: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'number_of_nodes', nullable: true })
  numberOfNodes: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'distant_metastasis', nullable: true })
  distantMetastasis: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'grade', nullable: true })
  grade: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'stage_status', nullable: true })
  stageStatus: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'cancer_type', nullable: true })
  cancerType: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'progression', nullable: true })
  progression: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'relapse', nullable: true })
  relapse: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'remission', nullable: true })
  remission: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'stage_treatment_type', nullable: true })
  stageTreatmentType: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'stage_additional_note', nullable: true })
  stageAdditionalNote: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'nuclear_grade_or_pleomorphism', nullable: true })
  nuclearGradeOrPleomorphism: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'mitotic_count_score', nullable: true })
  mitoticCountScore: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'tubule_formation', nullable: true })
  tubuleFormation: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'stage_grouping_score', nullable: true })
  stageGroupingScore: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'scarff_bloom_richardson_score', nullable: true })
  scarffBloomRichardsonScore: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'nottingham_modification_sbr_grade', nullable: true })
  nottinghamModificationSbrGrade: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'stage_lymphovascular_invasion', nullable: true })
  stageLymphovascularInvasion: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'stage_histopathologic_type', nullable: true })
  stageHistopathologicType: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'diagnostic_information', nullable: true })
  diagnosticInformation: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'type_of_specimen', nullable: true })
  typeOfSpecimen: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'staging_role', nullable: true })
  stagingRole: string;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'staging_date', nullable: true })
  stagingDate: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_type', nullable: true })
  treatmentType: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_site', nullable: true })
  treatmentSite: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'intent_of_treatment', nullable: true })
  intentOfTreatment: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'line_of_treatment', nullable: true })
  lineOfTreatment: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'concurrent_treatment', nullable: true })
  concurrentTreatment: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_plan_provider', nullable: true })
  treatmentPlanProvider: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_department', nullable: true })
  treatmentDepartment: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_status', nullable: true })
  treatmentStatus: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_priority', nullable: true })
  treatmentPriority: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_interval', nullable: true })
  treatmentInterval: string;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'treatment_start_date', nullable: true })
  treatmentStartDate: Date;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'treatment_end_date', nullable: true })
  treatmentEndDate: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_cycle_days', nullable: true })
  treatmentCycleDays: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_cycle_number', nullable: true })
  treatmentCycleNumber: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_patient_type', nullable: true })
  treatmentPatientType: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_adverse_reaction', nullable: true })
  treatmentAdverseReaction: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_specific_reaction', nullable: true })
  treatmentSpecificReaction: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_outcome', nullable: true })
  treatmentOutcome: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_response', nullable: true })
  treatmentResponse: string;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'treatment_followup_date', nullable: true })
  treatmentFollowupDate: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'treatment_additional_note', nullable: true })
  treatmentAdditionalNote: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_type', nullable: true })
  therapyType: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_site', nullable: true })
  therapySite: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'intent_of_therapy', nullable: true })
  intentOfTherapy: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'line_of_therapy', nullable: true })
  lineOfTherapy: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'concurrent_therapy', nullable: true })
  concurrentTherapy: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_plan_provider', nullable: true })
  therapyPlanProvider: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_department', nullable: true })
  therapyDepartment: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_status', nullable: true })
  therapyStatus: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_priority', nullable: true })
  therapyPriority: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_interval', nullable: true })
  therapyInterval: string;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'therapy_start_date', nullable: true })
  therapyStartDate: Date;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'therapy_end_date', nullable: true })
  therapyEndDate: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_cycle_days', nullable: true })
  therapyCycleDays: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_cycle_number', nullable: true })
  therapyCycleNumber: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_patient_type', nullable: true })
  therapyPatientType: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_adverse_reaction', nullable: true })
  therapyAdverseReaction: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_specific_reaction', nullable: true })
  therapySpecificReaction: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_outcome', nullable: true })
  therapyOutcome: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_response', nullable: true })
  therapyResponse: string;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'therapy_followup_date', nullable: true })
  therapyFollowupDate: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'therapy_additional_note', nullable: true })
  therapyAdditionalNote: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'nottingham_grade_above', nullable: true })
  nottinghamGradeAbove: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'estrogen_receptor_expression', nullable: true })
  estrogenReceptorExpression: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'er_percentage_positive', nullable: true })
  erPercentagePositive: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'er_allred_score', nullable: true })
  erAllredScore: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'progesterone_receptor', nullable: true })
  progesteroneReceptor: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'pr_percentage_positive', nullable: true })
  prPercentagePositive: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'pr_allred_score', nullable: true })
  prAllredScore: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'overall_her2_status', nullable: true })
  overallHer2Status: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'ihc_score', nullable: true })
  ihcScore: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fish_result', nullable: true })
  fishResult: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'fish_copy_number', nullable: true })
  fishCopyNumber: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'her2_or_cep17_ratio', nullable: true })
  her2OrCep17Ratio: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'circulating_tumor_cells', nullable: true })
  circulatingTumorCells: string;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'complaint_gender' })
  complaintGender: string;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'complaint_smart_text' })
  complaintSmartText: string;

  @Field(() => SelectionInput, { nullable: true })
  @Column({
    nullable: true,
    name: 'complaint_smart_selection',
    type: 'jsonb',
  })
  complaintSmartSelection: { [key: string]: any };

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'system_review_smart_text' })
  systemReviewSmartText: string;

  @Field(() => SelectionInput, { nullable: true })
  @Column({
    nullable: true,
    name: 'system_review_smart_selection',
    type: 'jsonb',
  })
  systemReviewSmartSelection: { [key: string]: any };

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'physical_exam_smart_text' })
  physicalExamSmartText: string;

  @Field(() => SelectionInput, { nullable: true })
  @Column({
    nullable: true,
    name: 'physical_exam_smart_selection',
    type: 'jsonb',
  })
  physicalExamSmartSelection: { [key: string]: any };

  @Field(() => Boolean, { nullable: true, defaultValue: true })
  @Column({
    name: 'conceal_complaint',
    nullable: true,
    type: 'boolean',
    default: true,
  })
  concealComplaint?: boolean;

  @Column({ nullable: true, name: 'complaint' })
  complaint: string;

  @Field(() => Boolean, { nullable: true, defaultValue: true })
  @Column({
    name: 'conceal_complaint_history',
    type: 'boolean',
    default: true,
    nullable: true,
  })
  concealComplaintHistory?: boolean;

  @Column({ nullable: true, name: 'complaint_history' })
  complaintHistory: string;

  @Field(() => Boolean, { nullable: true, defaultValue: true })
  @Column({
    nullable: true,
    name: 'conceal_system_review',
    default: true,
    type: 'boolean',
  })
  concealSystemReview?: boolean;

  @Column({ nullable: true, name: 'system_review' })
  systemReview: string;

  @Field(() => Boolean, { nullable: true, defaultValue: true })
  @Column({
    nullable: true,
    name: 'conceal_physical_exam',
    type: 'boolean',
    default: true,
  })
  concealPhysicalExam?: boolean;

  @Column({ nullable: true, name: 'physical_exam' })
  physicalExam: string;

  @Field(() => Boolean, { nullable: true, defaultValue: true })
  @Column({
    name: 'conceal_audiometry',
    nullable: true,
    type: 'boolean',
    default: true,
  })
  concealAudiometry?: boolean;

  @Column({ nullable: true, name: 'audiometry' })
  audiometry: string;

  @Field(() => Boolean, { nullable: true, defaultValue: true })
  @Column({
    name: 'conceal_health_education',
    nullable: true,
    type: 'boolean',
    default: true,
  })
  concealHealthEducation?: boolean;

  @Column({ nullable: true, name: 'health_education' })
  healthEducation: string;

  @Field(() => [String], { nullable: true })
  @Column({ nullable: true, name: 'document_url', type: 'text', array: true })
  documentUrl: string[];

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'provider', type: 'text' })
  provider?: string;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'provider_service_name', type: 'text' })
  providerServiceName?: string;

  @Field(() => Boolean)
  @Column({
    name: 'is_package',
    type: 'boolean',
    nullable: false,
    default: false,
  })
  isPackage: boolean;

  @Field(() => [ServiceDetailInput], { nullable: true })
  @Column({
    nullable: true,
    name: 'service_details',
    type: 'jsonb',
  })
  serviceDetails: ServiceDetailInput[];

  @Field({ nullable: true })
  @Column({ name: 'sub_bill_ref', nullable: true })
  subBillRef: string;

  @Field(() => String, { nullable: true, defaultValue: 'Pending' })
  @Column({
    nullable: false,
    name: 'bill_status',
    type: 'text',
    default: 'Pending',
  })
  billStatus?: string;

  @ManyToOne(() => BillModel, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'bill' })
  bill?: BillModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'bill', nullable: true })
  billId: string;

  @Column({ nullable: false, name: 'archived', default: false })
  archived: boolean;

  @Field(() => [ChemoCommentInput], { nullable: true })
  @Column({
    name: 'chemo_comments',
    nullable: true,
    type: 'jsonb',
  })
  chemoComments?: ChemoCommentInput[];

  @Field(() => [OncologyTreatmentPlanModel], { nullable: true })
  @OneToMany(
    () => OncologyTreatmentPlanModel,
    (treatment_plan) => treatment_plan.oncologyConsultationHistory,
    {
      eager: true,
      onDelete: 'CASCADE',
    },
  )
  treatmentPlans?: OncologyTreatmentPlanModel[];

  @Field(() => OrganisationAppointmentModel, { nullable: true })
  @OneToOne(
    () => OrganisationAppointmentModel,
    (appointment) => appointment.oncologyConsultationHistory,
    {
      onDelete: 'SET NULL',
    },
  )
  @JoinColumn({ name: 'appointment_id' })
  appointment?: OrganisationAppointmentModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'appointment_id', nullable: true })
  appointmentId: string;

  @Field(() => OncologyConsultationRegisterModel, { nullable: true })
  @OneToOne(
    () => OncologyConsultationRegisterModel,
    (register) => register.oncologyHistory,
  )
  oncologyRegister?: OncologyConsultationRegisterModel;

  @Field(() => HospitalModel, { nullable: true })
  @ManyToOne(() => HospitalModel, (hospital) => hospital.oncologyHistories)
  @JoinColumn({ name: 'hospital' })
  hospital: HospitalModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'hospital', nullable: true })
  hospitalId: string;

  @OneToOne(
    () => PreauthorizationDetailsModel,
    (preauthDetails) => preauthDetails.oncologyConsultationHistory,
    { nullable: true, onDelete: 'RESTRICT' },
  )
  preauthorizationDetails?: PreauthorizationDetailsModel;

  @ManyToOne(() => HmoProviderModel, { nullable: true })
  @JoinColumn({ name: 'hmo_provider_id' })
  hmoProvider?: HmoProviderModel;

  @OneToMany(
    () => OncologyChemoDrugModel,
    (oncologyChemoDrug) => oncologyChemoDrug.oncologyConsultationHistory,
    { onDelete: 'CASCADE', cascade: true },
  )
  @Field(() => [OncologyChemoDrugModel], { nullable: true })
  oncologyChemoDrugs?: OncologyChemoDrugModel[];

  @Field(() => Boolean, { nullable: true, defaultValue: true })
  @Column({
    name: 'conceal_chemo_note',
    nullable: true,
    type: 'boolean',
    default: true,
  })
  concealChemoNote?: boolean;

  @Column({ name: 'chemo_note', nullable: true })
  chemoNote?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'laboratory_test_verified', nullable: true })
  laboratoryTestVerified?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'radiology_examination_verified', nullable: true })
  radiologyExaminationVerified?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'hmo_provider_id', nullable: true })
  hmoProviderId?: string;

  @Column({ name: 'hmo_claim_id', nullable: true })
  hmoClaimId?: string;

  @ManyToOne(
    () => HmoClaimModel,
    (hmoClaim) => hmoClaim.oncologyConsultationHistories,
    { nullable: true, onDelete: 'SET NULL' },
  )
  @JoinColumn({ name: 'hmo_claim_id' })
  hmoClaim?: HmoClaimModel;

  @ManyToMany(
    () => AdmissionModel,
    (admission) => admission.oncologyConsultations,
  )
  admissions: AdmissionModel[];

  @ManyToMany(() => AllergyModel, (allergy) => allergy.oncologyConsultations)
  @JoinTable({
    name: 'oncology_consultation_allergies',
    joinColumns: [{ name: 'oncology_consultation_id' }],
    inverseJoinColumns: [{ name: 'allergies_id' }],
  })
  allergies: AllergyModel[];

  @OneToMany(
    () => OncologyConsultationToInvestigation,
    (oncology_consultation_investigation) =>
      oncology_consultation_investigation.oncologyConsultation,
  )
  oncology_consultation_investigation: OncologyConsultationToInvestigation[];

  @ManyToMany(
    () => MedicationModel,
    (medication) => medication.oncologyConsultations,
  )
  @JoinTable({
    name: 'oncology_consultation_medications',
    joinColumns: [{ name: 'oncology_consultation_id' }],
    inverseJoinColumns: [{ name: 'medications_id' }],
  })
  medications: MedicationModel[];

  @ManyToMany(() => SurgeryModel, (surgery) => surgery.oncologyConsultations)
  @JoinTable({
    name: 'oncology_consultation_surgery',
    joinColumns: [{ name: 'oncology_consultation_id' }],
    inverseJoinColumns: [{ name: 'surgery_id' }],
  })
  surgeries: SurgeryModel[];

  @ManyToMany(() => VitalModel, (vital) => vital.oncologyConsultations)
  @JoinTable({
    name: 'oncology_consultation_vitals',
    joinColumns: [{ name: 'oncology_consultation_id' }],
    inverseJoinColumns: [{ name: 'vitals_id' }],
  })
  vitals: VitalModel[];

  @ManyToMany(
    () => NursingServiceModel,
    (nursingService) => nursingService.oncologyConsultations,
  )
  @JoinTable({
    name: 'oncology_consultation_nursing_services',
    joinColumns: [{ name: 'oncology_consultation_id' }],
    inverseJoinColumns: [{ name: 'nursing_service_id' }],
  })
  nursingServices: NursingServiceModel[];

  constructor(
    oncologyConsultationHistory?: Partial<OncologyConsultationHistoryModel>,
  ) {
    super();
    Object.assign(this, oncologyConsultationHistory);
  }
}
