import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsEmpty, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ProfileModel } from '../../users/models/profile.model';
import { MedicationDetailsModel } from '@clinify/medications/models/medication_details.model';
import { OncologyConsultationHistoryModel } from '@clinify/oncology-consultation-history/models/oncology-consultation-history.model';
import {
  ChemoInvestigationDetails,
  OncologyDrugAdministrationRegistration,
} from '@clinify/oncology-consultation-history/validators/oncology-consultation-history.input';

@ObjectType()
export class BaseAudits {
  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Column({ name: 'updated_by', nullable: true })
  updatedById?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'created_by' })
  createdBy?: ProfileModel;

  @Column({ name: 'created_by', nullable: false })
  createdById: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'creator_name' })
  creatorName?: string;
}

@ObjectType()
@Entity({
  name: 'oncology_chemo_drug',
})
export class OncologyChemoDrugModel extends BaseAudits {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsEmpty()
  @IsUUID('4')
  id: string;

  @Field({ nullable: true })
  @Column({
    name: 'type',
    nullable: true,
  })
  type: string;

  @Field({ nullable: true })
  @Column({
    name: 'combination_name',
    nullable: true,
  })
  combinationName: string;

  @Field({ nullable: true })
  @Column({ name: 'combination_group_name', nullable: true })
  combinationGroupName: string;

  @Field({ nullable: false })
  @Column({
    name: 'cycleNumber',
    nullable: false,
  })
  cycleNumber: number;

  @Field(() => String, { nullable: false })
  @Column({
    name: 'day',
    nullable: false,
  })
  day: string;

  @Field(() => String, { nullable: false })
  @Column({
    name: 'drug_name',
    nullable: false,
  })
  drugName: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'drug_id',
    nullable: true,
  })
  drugId: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'dosage',
    nullable: true,
  })
  dosage: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'dosage_percentage',
    nullable: true,
  })
  dosagePercentage: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'total_dose',
    nullable: true,
  })
  totalDose: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'adjusted_dose',
    nullable: true,
  })
  adjustedDose?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'quantity',
    nullable: true,
  })
  quantity?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'route',
    nullable: true,
  })
  route?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'note',
    nullable: true,
  })
  note: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'infusion_used',
    nullable: true,
  })
  infusionUsed: string;

  @Field(() => OncologyConsultationHistoryModel, { nullable: true })
  @ManyToOne(
    () => OncologyConsultationHistoryModel,
    (oncology) => oncology.oncologyChemoDrugs,
  )
  @JoinColumn({ name: 'oncology_consultation_history_id' })
  oncologyConsultationHistory?: OncologyConsultationHistoryModel;

  @Column({ name: 'oncology_consultation_history_id', nullable: false })
  oncologyConsultationHistoryId: string;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true })
  frequency?: string;

  @Field(() => [OncologyDrugAdministrationRegistration], { nullable: true })
  @Column({ type: 'jsonb', nullable: true, name: 'administration_register' })
  administrationRegister?: OncologyDrugAdministrationRegistration[];

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'chemo_diagnosis' })
  chemoDiagnosis?: string;

  @Field(() => [ChemoInvestigationDetails], { nullable: true })
  @Column({ type: 'jsonb', nullable: true, name: 'investigation_details' })
  investigationDetails?: ChemoInvestigationDetails[];

  @Field()
  @Column({ name: 'section', default: 'chemo' })
  section: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'inventory_class',
    nullable: true,
    type: 'text',
    default: 'External',
  })
  inventoryClass?: string;

  @Field(() => MedicationDetailsModel, { nullable: true })
  @ManyToOne(
    () => MedicationDetailsModel,
    (medicationDetail) => medicationDetail.chemoDrugs,
    {
      nullable: true,
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    },
  )
  @JoinColumn({ name: 'medication_details_id' })
  medicationDetails?: MedicationDetailsModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'medication_details_id' })
  medicationDetailsId: string;

  constructor(partial: Partial<OncologyChemoDrugModel>) {
    super();
    Object.assign(this, partial);
  }
}
