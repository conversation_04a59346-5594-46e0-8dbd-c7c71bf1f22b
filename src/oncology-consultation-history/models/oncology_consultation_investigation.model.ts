import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { OncologyConsultationHistoryModel } from './oncology-consultation-history.model';
import { InvestigationModel } from '@clinify/investigation/models/investigation.model';
import { InvestigationRequestType } from '@clinify/shared/enums/investigation';

@Entity({ name: 'oncology_consultation_investigation' })
export class OncologyConsultationToInvestigation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  oncologyConsultationId: string;

  @Column()
  investigationId: string;

  @Column({
    type: 'enum',
    enum: InvestigationRequestType,
    name: 'type',
    nullable: false,
    default: InvestigationRequestType.Laboratory,
  })
  type: InvestigationRequestType;

  @ManyToOne(
    () => OncologyConsultationHistoryModel,
    (oncologyConsultation) =>
      oncologyConsultation.oncology_consultation_investigation,
    { onDelete: 'CASCADE' },
  )
  oncologyConsultation: OncologyConsultationHistoryModel;

  @ManyToOne(
    () => InvestigationModel,
    (investigation) => investigation.oncology_consultation_investigation,
    { onDelete: 'CASCADE' },
  )
  investigation: InvestigationModel;

  constructor(
    oncologyConsultationToInvestigation: Partial<OncologyConsultationToInvestigation>,
  ) {
    Object.assign(this, oncologyConsultationToInvestigation);
  }
}
