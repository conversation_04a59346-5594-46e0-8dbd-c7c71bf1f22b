import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsEmpty, IsUUID } from 'class-validator';
import {
  Column,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OncologyConsultationHistoryModel } from './oncology-consultation-history.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { OncologyRegisterChart } from '@clinify/users/inputs/oncology-register.input';

@ObjectType()
@Entity({ name: 'oncology_consultation_register' })
export class OncologyConsultationRegisterModel {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsEmpty()
  @IsUUID('4')
  id: string;

  @Field({ nullable: true })
  @Column({ name: 'creator_name', nullable: false })
  creatorName: string;

  @Field({ nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => OncologyRegisterChart, { nullable: true })
  @Column({ nullable: true, type: 'jsonb', name: 'treatment_chart' })
  treatmentChart: OncologyRegisterChart;

  @Field(() => OncologyRegisterChart, { nullable: true })
  @Column({ nullable: true, type: 'jsonb', name: 'therapy_chart' })
  therapyChart: OncologyRegisterChart;

  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => OncologyConsultationHistoryModel, { nullable: true })
  @OneToOne(
    () => OncologyConsultationHistoryModel,
    (oncology) => oncology.oncologyRegister,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'oncology_consultation_history' })
  oncologyHistory?: OncologyConsultationHistoryModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'oncology_consultation_history', nullable: true })
  oncologyRecordId?: string;

  @Field(() => HospitalModel, { nullable: true })
  @ManyToOne(
    () => HospitalModel,
    (hospital) => hospital.oncologyConsultationRegister,
  )
  @JoinColumn({ name: 'hospital' })
  hospital: HospitalModel;

  constructor(
    oncologyConsultationRegister?: Partial<OncologyConsultationRegisterModel>,
  ) {
    Object.assign(this, oncologyConsultationRegister);
  }
}
