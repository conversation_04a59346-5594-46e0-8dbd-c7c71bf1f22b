import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsUUID, IsDate } from 'class-validator';
import {
  Column,
  <PERSON><PERSON><PERSON>,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OncologyConsultationHistoryModel } from './oncology-consultation-history.model';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
export abstract class BaseAudits {
  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'updated_by', nullable: true })
  lastModifierId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'created_by' })
  createdBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'created_by', nullable: true })
  creatorId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'creator_name' })
  creatorName?: string;
}

@ObjectType()
@Entity({ name: 'oncology_treatment_plans' })
export class OncologyTreatmentPlanModel extends BaseAudits {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsUUID('4')
  id: string;

  @Field(() => OncologyConsultationHistoryModel, { nullable: true })
  @ManyToOne(
    () => OncologyConsultationHistoryModel,
    (oncologyConsultationHistory) => oncologyConsultationHistory.treatmentPlans,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'oncology_consultation_history_id' })
  oncologyConsultationHistory: OncologyConsultationHistoryModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'oncology_consultation_history_id', nullable: true })
  oncologyConsultationHistoryId?: string;

  @Column({ name: 'treatment_plan', nullable: true })
  treatmentPlan: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'patient_admitted' })
  patientAdmitted: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'observation_note' })
  observationNote?: string;

  @Field(() => Boolean, { nullable: true, defaultValue: true })
  @Column({
    name: 'conceal_observation_note',
    nullable: true,
    type: 'boolean',
    default: true,
  })
  concealObservationNote?: boolean;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'admission_consent' })
  admissionConsent: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'adverse_effects_following_treatment' })
  adverseEffectsFollowingTreatment: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'state_effects' })
  stateEffects: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'adverse_effects_investigated' })
  adverseEffectsInvestigated: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'outcome_of_investigation' })
  outcomeOfInvestigation: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'treatment_given' })
  treatmentGiven: string;

  @Field(() => Boolean, { nullable: true, defaultValue: true })
  @Column({ type: 'boolean', default: true, nullable: true, name: 'conceal' })
  conceal?: boolean;

  @Field(() => String, { nullable: true })
  @Column({ name: 'patient_consent_signature', nullable: true, type: 'text' })
  patientConsentSignature: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'patient_consent_signature_type',
    nullable: true,
    type: 'text',
  })
  patientConsentSignatureType: string;

  @Field(() => Date, { nullable: true })
  @Column({
    name: 'patient_consent_signature_date_time',
    nullable: true,
    type: 'timestamptz',
  })
  patientConsentSignatureDateTime: Date;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'treatment_status' })
  treatmentStatus: string;

  constructor(oncologyTreatmentPlan: Partial<OncologyTreatmentPlanModel>) {
    super();
    Object.assign(this, oncologyTreatmentPlan);
  }
}
