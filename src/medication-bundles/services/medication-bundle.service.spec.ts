import { TestingModule } from '@nestjs/testing';
import { Test } from '@nestjs/testing/test';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { MedicationBundleModel } from '../models/medication-bundle.model';
import { MedicationBundleService } from '@clinify/medication-bundles/services/medication-bundle.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import {
  medicationBundleItemMock,
  medicationBundleMock,
} from '@mocks/factories/medication-bundle.factory';
import { mockProfile } from '@mocks/factories/profile.factory';
import { loggerMock } from '@mocks/logger';

const profile = mockProfile;

const managerMock = {};

const medicationBundleRepositoryMock = {
  findByProfile: jest.fn(() => Promise.resolve(medicationBundleMock)),
  getMedicationBundle: jest.fn(() => Promise.resolve(medicationBundleMock)),
  getSavedMedicationBundles: jest.fn(() =>
    Promise.resolve([medicationBundleMock]),
  ),
  addMedicationBundle: jest.fn(() => Promise.resolve(medicationBundleMock)),
  updateMedicationBundle: jest.fn(() => Promise.resolve(medicationBundleMock)),
  deleteMedicationBundles: jest.fn(() =>
    Promise.resolve([medicationBundleMock]),
  ),
  addMedicationBundleItem: jest.fn(() =>
    Promise.resolve(medicationBundleItemMock),
  ),
  updateMedicationBundleItem: jest.fn(() =>
    Promise.resolve(medicationBundleItemMock),
  ),
  deleteMedicationBundleItem: jest.fn(() =>
    Promise.resolve(medicationBundleItemMock),
  ),
  archiveMedicationBundles: jest.fn(() =>
    Promise.resolve([medicationBundleMock]),
  ),
  getMedicationBundleItems: jest.fn(() =>
    Promise.resolve([medicationBundleItemMock]),
  ),
};

const dsMock = {
  getRepository: jest.fn(() => ({
    extend: jest.fn(),
  })),
  transaction: jest.fn((cb) => cb(managerMock)),
  manager: managerMock,
};

const profileRepositoryMock = {};

describe('MedicationBundleService', () => {
  let service: MedicationBundleService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MedicationBundleService,
        {
          provide: getRepositoryToken(MedicationBundleModel),
          useValue: medicationBundleRepositoryMock,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: profileRepositoryMock,
        },
        {
          provide: DataSource,
          useValue: dsMock,
        },
        {
          provide: EntityManager,
          useValue: managerMock,
        },
        { ...loggerMock },
      ],
    }).compile();

    service = module.get<MedicationBundleService>(MedicationBundleService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(service).toBeTruthy();
  });

  it('getAllMedicationBundles() should call findByProfile on repo and return value', async () => {
    const res = await service.getAllMedicationBundles(profile.id, {});
    expect(medicationBundleRepositoryMock.findByProfile).toHaveBeenCalledWith(
      profile.id,
      {},
    );
    expect(res).toEqual(medicationBundleMock);
  });

  it('getMedicationBundle() should call getMedicationBundle on repo and return value', async () => {
    const res = await service.getMedicationBundle(profile, 'id');
    expect(
      medicationBundleRepositoryMock.getMedicationBundle,
    ).toHaveBeenCalledWith(profile, 'id');
    expect(res).toEqual(medicationBundleMock);
  });

  it('getMedicationBundleItems() should call getMedicationBundleItems on repo', async () => {
    const res = await service.getMedicationBundleItems('id');
    expect(
      medicationBundleRepositoryMock.getMedicationBundleItems,
    ).toHaveBeenCalledWith('id');
    expect(res).toEqual([medicationBundleItemMock]);
  });

  it('getSavedMedicationBundles() should call getSavedMedicationBundles from repo and return value', async () => {
    const res = await service.getSavedMedicationBundles(profile);
    expect(
      medicationBundleRepositoryMock.getSavedMedicationBundles,
    ).toHaveBeenCalledWith(profile);
    expect(res).toEqual([medicationBundleMock]);
  });

  it('addMedicationBundle() should call addMedicationBundle on repo and return value', async () => {
    const res = await service.addMedicationBundle(
      profile,
      medicationBundleMock,
    );
    expect(
      medicationBundleRepositoryMock.addMedicationBundle,
    ).toHaveBeenCalledWith(profile, medicationBundleMock);
    expect(res).toEqual(medicationBundleMock);
  });

  it('updateMedicationBundle() should call updateMedicationBundle on repo and return value', async () => {
    const res = await service.updateMedicationBundle(
      profile,
      medicationBundleMock,
      'id',
    );
    expect(
      medicationBundleRepositoryMock.updateMedicationBundle,
    ).toHaveBeenCalledWith(profile, medicationBundleMock, 'id');
    expect(res).toEqual(medicationBundleMock);
  });

  it('deleteMedicationBundles() should call deleteMedicationBundles on repo and return value', async () => {
    const res = await service.deleteMedicationBundles(profile, ['id']);
    expect(
      medicationBundleRepositoryMock.deleteMedicationBundles,
    ).toHaveBeenCalledWith(profile, ['id']);
    expect(res).toEqual([medicationBundleMock]);
  });

  it('addMedicationBundleItem() should call addMedicationBundleItem on repo and return value', async () => {
    const res = await service.addMedicationBundleItem(
      'id',
      profile,
      medicationBundleItemMock,
    );
    expect(
      medicationBundleRepositoryMock.addMedicationBundleItem,
    ).toHaveBeenCalledWith('id', profile, medicationBundleItemMock);
    expect(res).toEqual(medicationBundleItemMock);
  });

  it('updateMedicationBundleItem() should call updateMedicationBundleItem on repo and return value', async () => {
    const res = await service.updateMedicationBundleItem(
      'id',
      medicationBundleItemMock,
      profile,
    );
    expect(
      medicationBundleRepositoryMock.updateMedicationBundleItem,
    ).toHaveBeenCalledWith('id', medicationBundleItemMock, profile);
    expect(res).toEqual(medicationBundleItemMock);
  });

  it('deleteMedicationBundleItem() should call deleteMedicationBundleItem on repo and return value', async () => {
    const res = await service.deleteMedicationBundleItem(profile, 'id');
    expect(
      medicationBundleRepositoryMock.deleteMedicationBundleItem,
    ).toHaveBeenCalledWith('id', profile);
    expect(res).toEqual(medicationBundleItemMock);
  });

  it('archiveMedicationBundles() should call archiveMedicationBundles on repo and return value', async () => {
    const res = await service.archiveMedicationBundles(profile, ['id'], true);
    expect(
      medicationBundleRepositoryMock.archiveMedicationBundles,
    ).toHaveBeenCalledWith(profile, ['id'], true);
    expect(res).toEqual([medicationBundleMock]);
  });
});
