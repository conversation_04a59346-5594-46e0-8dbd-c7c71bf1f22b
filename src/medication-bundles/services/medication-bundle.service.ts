import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MedicationBundleItemInput } from '@clinify/medication-bundles/inputs/medication-bundle-item.input';
import {
  MedicationBundleInput,
  NewMedicationBundleInput,
} from '@clinify/medication-bundles/inputs/medication-bundle.input';
import { MedicationBundleItemModel } from '@clinify/medication-bundles/models/medication-bundle-item.model';
import { MedicationBundleModel } from '@clinify/medication-bundles/models/medication-bundle.model';
import { IMedicationBundleRepository } from '@clinify/medication-bundles/repositories/medication-bundle.repository';
import { MedicationBundleResponse } from '@clinify/medication-bundles/responses/medication-bundle.response';
import { FilterInput } from '@clinify/shared/validators/filter.input';
import { ProfileModel } from '@clinify/users/models/profile.model';

@Injectable()
export class MedicationBundleService {
  constructor(
    @InjectRepository(MedicationBundleModel)
    public repository: IMedicationBundleRepository,
  ) {}

  getAllMedicationBundles(
    profileId: string,
    filter: Partial<FilterInput>,
  ): Promise<MedicationBundleResponse> {
    return this.repository.findByProfile(profileId, filter);
  }

  getMedicationBundleItems(
    medBundleId: string,
  ): Promise<MedicationBundleItemModel[]> {
    return this.repository.getMedicationBundleItems(medBundleId);
  }

  getMedicationBundle(
    mutator: ProfileModel,
    medicationBundleId: string,
  ): Promise<MedicationBundleModel> {
    return this.repository.getMedicationBundle(mutator, medicationBundleId);
  }

  getSavedMedicationBundles(
    profile: ProfileModel,
  ): Promise<MedicationBundleModel[]> {
    return this.repository.getSavedMedicationBundles(profile);
  }

  addMedicationBundle(
    mutator: ProfileModel,
    medicationBundle: NewMedicationBundleInput,
  ): Promise<MedicationBundleModel> {
    return this.repository.addMedicationBundle(mutator, medicationBundle);
  }

  updateMedicationBundle(
    mutator: ProfileModel,
    medicationBundle: MedicationBundleInput,
    medicationBundleId: string,
  ): Promise<MedicationBundleModel> {
    return this.repository.updateMedicationBundle(
      mutator,
      medicationBundle,
      medicationBundleId,
    );
  }

  deleteMedicationBundles(
    mutator: ProfileModel,
    medicationBundleIds: string[],
  ): Promise<MedicationBundleModel[]> {
    return this.repository.deleteMedicationBundles(
      mutator,
      medicationBundleIds,
    );
  }

  addMedicationBundleItem(
    medicationBundleId: string,
    item: MedicationBundleItemInput,
    mutator: ProfileModel,
  ): Promise<MedicationBundleItemModel> {
    return this.repository.addMedicationBundleItem(
      medicationBundleId,
      item,
      mutator,
    );
  }

  updateMedicationBundleItem(
    id: string,
    item: MedicationBundleItemInput,
    mutator: ProfileModel,
  ): Promise<MedicationBundleItemModel> {
    return this.repository.updateMedicationBundleItem(id, item, mutator);
  }

  deleteMedicationBundleItem(
    mutator: ProfileModel,
    id: string,
  ): Promise<MedicationBundleItemModel> {
    return this.repository.deleteMedicationBundleItem(id, mutator);
  }

  archiveMedicationBundles(
    mutator: ProfileModel,
    medicationBundleIds: string[],
    archive: boolean,
  ): Promise<MedicationBundleModel[]> {
    return this.repository.archiveMedicationBundles(
      mutator,
      medicationBundleIds,
      archive,
    );
  }
}
