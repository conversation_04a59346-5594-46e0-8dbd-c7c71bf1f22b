import { UseGuards, Inject } from '@nestjs/common';
import {
  Args,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
  Subscription,
} from '@nestjs/graphql';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { AuthorizationGuard } from '@clinify/authorization/guards/authorization.guard';
import { MedicationBundleItemInput } from '@clinify/medication-bundles/inputs/medication-bundle-item.input';
import {
  MedicationBundleInput,
  NewMedicationBundleInput,
} from '@clinify/medication-bundles/inputs/medication-bundle.input';
import { MedicationBundleItemModel } from '@clinify/medication-bundles/models/medication-bundle-item.model';
import { MedicationBundleModel } from '@clinify/medication-bundles/models/medication-bundle.model';
import { MedicationBundleService } from '@clinify/medication-bundles/services/medication-bundle.service';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { AppServices } from '@clinify/shared/enums/services';
import { ProfileModel } from '@clinify/users/models/profile.model';
import {
  filterMedicationBundleAdded,
  filterMedicationBundleUpdated,
  filterMedicationBundleRemoved,
  filterMedicationBundleArchived,
  filterMedicationBundleUnarchived,
  filterMedicationBundleItemAdded,
  filterMedicationBundleItemUpdated,
  filterMedicationBundleItemRemoved,
} from '@clinify/utils/subscriptions/filters';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const {
  MedicationBundleAdded,
  MedicationBundleUpdated,
  MedicationBundleRemoved,
  MedicationBundleArchived,
  MedicationBundleUnarchived,
  MedicationBundleItemAdded,
  MedicationBundleItemUpdated,
  MedicationBundleItemRemoved,
} = SubscriptionTypes;

@LogService(AppServices.MedicationBundle)
@UseGuards(GqlAuthGuard, AuthorizationGuard)
@Resolver(() => MedicationBundleModel)
export class MedicationBundleResolver {
  constructor(
    private readonly service: MedicationBundleService,
    @Inject(PUB_SUB) private readonly pubSub: RedisPubSub,
  ) {}

  @Query(() => MedicationBundleModel)
  medicationBundle(
    @CurrentProfile() profile: ProfileModel,
    @Args('id') medicationBundleId: string,
  ): Promise<MedicationBundleModel> {
    return this.service.getMedicationBundle(profile, medicationBundleId);
  }

  @Query(() => [MedicationBundleModel])
  getSavedMedicationBundles(
    @CurrentProfile() profile: ProfileModel,
  ): Promise<MedicationBundleModel[]> {
    return this.service.getSavedMedicationBundles(profile);
  }

  @Mutation(() => MedicationBundleModel)
  async addMedicationBundle(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'medicationBundle',
      type: () => NewMedicationBundleInput,
      nullable: false,
    })
    medicationBundle: NewMedicationBundleInput,
  ): Promise<MedicationBundleModel> {
    const newMedicationBundle = await this.service.addMedicationBundle(
      profile,
      medicationBundle,
    );
    await this.pubSub.publish(MedicationBundleAdded, {
      [MedicationBundleAdded]: newMedicationBundle,
    });

    return newMedicationBundle;
  }

  @Subscription(() => MedicationBundleModel, {
    name: MedicationBundleAdded,
    filter: filterMedicationBundleAdded,
  })
  addMedicationBundleSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(MedicationBundleAdded);
  }

  @Mutation(() => MedicationBundleModel)
  async updateMedicationBundle(
    @CurrentProfile() profile: ProfileModel,
    @Args('medicationBundle', { type: () => MedicationBundleInput })
    medicationBundle: MedicationBundleInput,
    @Args('id') medicationBundleId: string,
  ): Promise<MedicationBundleModel> {
    const updatedMedicationBundle = await this.service.updateMedicationBundle(
      profile,
      medicationBundle,
      medicationBundleId,
    );
    await this.pubSub.publish(MedicationBundleUpdated, {
      [MedicationBundleUpdated]: updatedMedicationBundle,
    });

    return updatedMedicationBundle;
  }

  @Subscription(() => MedicationBundleModel, {
    name: MedicationBundleUpdated,
    filter: filterMedicationBundleUpdated,
  })
  updateMedicationBundleSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(MedicationBundleUpdated);
  }

  @Mutation(() => [MedicationBundleModel])
  async deleteMedicationBundles(
    @CurrentProfile() profile: ProfileModel,
    @Args('ids', { type: () => [String] }) ids: string[],
  ): Promise<MedicationBundleModel[]> {
    const deletedMedications = await this.service.deleteMedicationBundles(
      profile,
      ids,
    );
    await this.pubSub.publish(MedicationBundleRemoved, {
      [MedicationBundleRemoved]: deletedMedications,
    });

    return deletedMedications;
  }

  @Subscription(() => [MedicationBundleModel], {
    name: MedicationBundleRemoved,
    filter: filterMedicationBundleRemoved,
  })
  deleteMedicationBundleSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(MedicationBundleRemoved);
  }

  @Mutation(() => [MedicationBundleModel])
  async archiveMedicationBundles(
    @CurrentProfile() profile: ProfileModel,
    @Args('ids', { type: () => [String] }) ids: string[],
    @Args('archive', { type: () => Boolean, defaultValue: true })
    archive: boolean,
  ): Promise<MedicationBundleModel[]> {
    const pubSubTrigger = archive
      ? MedicationBundleArchived
      : MedicationBundleUnarchived;
    const archivedMedications = await this.service.archiveMedicationBundles(
      profile,
      ids,
      archive,
    );
    await this.pubSub.publish(pubSubTrigger, {
      [pubSubTrigger]: archivedMedications,
    });

    return archivedMedications;
  }

  @Subscription(() => [MedicationBundleModel], {
    name: MedicationBundleArchived,
    filter: filterMedicationBundleArchived,
  })
  archiveMedicationBundleSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(MedicationBundleArchived);
  }

  @Subscription(() => [MedicationBundleModel], {
    name: MedicationBundleUnarchived,
    filter: filterMedicationBundleUnarchived,
  })
  unarchiveMedicationBundleSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(MedicationBundleUnarchived);
  }

  @Mutation(() => MedicationBundleItemModel)
  async addMedicationBundleItem(
    @CurrentProfile() profile: ProfileModel,
    @Args('medicationBundleId') medicationBundleId: string,
    @Args('input', { type: () => MedicationBundleItemInput })
    input: MedicationBundleItemInput,
  ): Promise<MedicationBundleItemModel> {
    const item = await this.service.addMedicationBundleItem(
      medicationBundleId,
      input,
      profile,
    );
    await this.pubSub.publish(MedicationBundleItemAdded, {
      [MedicationBundleItemAdded]: item,
    });

    return item;
  }

  @Subscription(() => MedicationBundleItemModel, {
    name: MedicationBundleItemAdded,
    filter: filterMedicationBundleItemAdded,
  })
  addMedicationBundleItemSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(MedicationBundleItemAdded);
  }

  @Mutation(() => MedicationBundleItemModel)
  async updateMedicationBundleItem(
    @CurrentProfile() profile: ProfileModel,
    @Args('input', { type: () => MedicationBundleItemInput })
    input: MedicationBundleItemInput,
    @Args('medicationBundleItemId') medicationBundleItemId: string,
  ): Promise<MedicationBundleItemModel> {
    const item = await this.service.updateMedicationBundleItem(
      medicationBundleItemId,
      input,
      profile,
    );
    await this.pubSub.publish(MedicationBundleItemUpdated, {
      [MedicationBundleItemUpdated]: item,
    });

    return item;
  }

  @Subscription(() => MedicationBundleItemModel, {
    name: MedicationBundleItemUpdated,
    filter: filterMedicationBundleItemUpdated,
  })
  updateMedicationBundleItemSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(MedicationBundleItemUpdated);
  }

  @Mutation(() => MedicationBundleItemModel)
  async deleteMedicationBundleItem(
    @CurrentProfile() profile: ProfileModel,
    @Args('id') id: string,
  ): Promise<MedicationBundleItemModel> {
    const item = await this.service.deleteMedicationBundleItem(profile, id);
    await this.pubSub.publish(MedicationBundleItemRemoved, {
      [MedicationBundleItemRemoved]: item,
    });

    return item;
  }

  @Subscription(() => MedicationBundleItemModel, {
    name: MedicationBundleItemRemoved,
    filter: filterMedicationBundleItemRemoved,
  })
  deleteMedicationBundleItemSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(MedicationBundleItemRemoved);
  }

  @ResolveField(() => [MedicationBundleItemModel], {
    nullable: true,
    name: 'medicationBundleItems',
  })
  getBundleItems(
    @Parent() medicationBundle: MedicationBundleModel,
  ): Promise<MedicationBundleItemModel[]> {
    return this.service.getMedicationBundleItems(medicationBundle.id);
  }
}
