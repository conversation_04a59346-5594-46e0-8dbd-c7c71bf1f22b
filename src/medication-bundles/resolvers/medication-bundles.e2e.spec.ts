import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { GraphQLModule } from '@nestjs/graphql';
import { Test } from '@nestjs/testing/test';
import { getRepositoryToken, TypeOrmModule } from '@nestjs/typeorm';
import request from 'supertest';
import { EntityManager } from 'typeorm';
import { MedicationBundleModel } from '../models/medication-bundle.model';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { TestDataSourceOptions } from '@clinify/data-source';
import { MedicationBundlesModule } from '@clinify/medication-bundles/medication-bundles.module';
import { UserType } from '@clinify/shared/enums/users';
import { ProfileModel } from '@clinify/users/models/profile.model';
import {
  medicationBundleFactory,
  medicationBundleItemMock,
} from '@mocks/factories/medication-bundle.factory';
import gqlAuthGuardMock, { gqlUserMock } from '@mocks/gqlAuthGuard.mock';

const medicationBundleData = medicationBundleFactory.build();

const medBundleRepo = {
  getMedicationBundle: jest.fn(() => medicationBundleData),
  getSavedMedicationBundles: jest.fn(() => [medicationBundleData]),
  getMedicationBundleItems: jest.fn(() => [medicationBundleItemMock]),
  addMedicationBundle: jest.fn(() => medicationBundleData),
  updateMedicationBundle: jest.fn(() => medicationBundleData),
  deleteMedicationBundles: jest.fn(() => [medicationBundleData]),
  addMedicationBundleItem: jest.fn(() => medicationBundleItemMock),
  updateMedicationBundleItem: jest.fn(() => medicationBundleItemMock),
  archiveMedicationBundles: jest.fn(() => [medicationBundleData]),
  deleteMedicationBundleItem: jest.fn(() => medicationBundleItemMock),
};

const profileRepo = {
  findOne: jest.fn(() => gqlUserMock.defaultProfile),
  findOneOrFail: jest.fn(() => gqlUserMock.defaultProfile),
};

const managerMock = {
  save: jest.fn(() => medicationBundleData),
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

describe('MedicationBundleController', () => {
  let testHttpServer: request.SuperTest<request.Test>;
  let app: INestApplication;

  beforeAll(async () => {
    const builder = await Test.createTestingModule({
      imports: [
        MedicationBundlesModule,
        TypeOrmModule.forRoot(TestDataSourceOptions),
        EventEmitterModule.forRoot(),
        GraphQLModule.forRoot<ApolloDriverConfig>({
          debug: false,
          playground: false,
          driver: ApolloDriver,
          autoSchemaFile: true,
          installSubscriptionHandlers: true,
          subscriptions: {
            'graphql-ws': {
              onConnect: (connectionParams: { [key: string]: any }) => {
                return {
                  headers: {
                    ...connectionParams,
                  },
                };
              },
            },
          },
          include: [MedicationBundlesModule],
          context: ({ req }) => ({ req }),
        }),
      ],
    })
      .overrideProvider(getRepositoryToken(MedicationBundleModel))
      .useValue(medBundleRepo)
      .overrideProvider(getRepositoryToken(ProfileModel))
      .useValue(profileRepo)
      .overrideProvider(EntityManager)
      .useValue(managerMock)
      .overrideGuard(GqlAuthGuard)
      .useValue(gqlAuthGuardMock(UserType.Doctor, gqlUserMock))
      .overrideProvider('PUB_SUB')
      .useValue(pubSubMock)
      .compile();

    app = builder.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    testHttpServer = await request(app.getHttpServer());
  });

  afterAll(async () => await app.close());

  it('medicationBundle', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          query {
              medicationBundle(id: "${medicationBundleData.id}") {
                  id
                  bundleName
                  medicationBundleItems {
                      id
                      medicationName
                  }
              }
          }
      `,
      })
      .expect(({ body }) => {
        const data = body.data.medicationBundle;
        expect(data).toEqual(
          expect.objectContaining({
            id: medicationBundleData.id,
            bundleName: medicationBundleData.bundleName,
          }),
        );
        expect(data.medicationBundleItems.length).toEqual(
          medicationBundleData.medicationBundleItems.length,
        );
      })
      .expect(200)
      .end(done);
  });

  it('getSavedMedicationBundles', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          query {
            getSavedMedicationBundles {
              id
              bundleName
            }
          }
        `,
      })
      .expect(({ body }) => {
        const data = body.data.getSavedMedicationBundles;
        expect(data.length).toEqual(1);
        expect(data).toContainEqual(
          expect.objectContaining({
            id: medicationBundleData.id,
            bundleName: medicationBundleData.bundleName,
          }),
        );
      })
      .expect(200)
      .end(done);
  });

  it('addMedicationBundle', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            addMedicationBundle(
              medicationBundle: {
                bundleName: "Test Bundle"
                clinifyId: "id"
                medicationBundleItems: [
                  { medicationName: "Test Medication", purpose: "Test Purpose", duration: "daily", frequency: "1" }
                ]
              }
            ) {
              id
              bundleName
            }
          }
        `,
      })
      .expect(({ body }) => {
        const data = body.data.addMedicationBundle;
        expect(data).toEqual(
          expect.objectContaining({
            id: medicationBundleData.id,
            bundleName: medicationBundleData.bundleName,
          }),
        );
      })
      .expect(200)
      .end(done);
  });

  it('updateMedicationBundle', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            updateMedicationBundle(
              id: "id"
              medicationBundle: {
                bundleName: "Test Bundle"
                clinifyId: "id"
              }
            ) {
              id
              bundleName
            }
          }
        `,
      })
      .expect(({ body }) => {
        const data = body.data.updateMedicationBundle;
        expect(data).toEqual(
          expect.objectContaining({
            id: medicationBundleData.id,
            bundleName: medicationBundleData.bundleName,
          }),
        );
      })
      .expect(200)
      .end(done);
  });

  it('deleteMedicationBundles', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            deleteMedicationBundles(ids: ["id"]) {
              id
            }
          }
        `,
      })
      .expect(({ body }) => {
        const data = body.data.deleteMedicationBundles;
        expect(data).toContainEqual(
          expect.objectContaining({
            id: medicationBundleData.id,
          }),
        );
      })
      .expect(200)
      .end(done);
  });

  it('archiveMedicationBundles', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            archiveMedicationBundles(ids: ["id"], archive: true) {
              id
            }
          }
        `,
      })
      .expect(({ body }) => {
        const data = body.data.archiveMedicationBundles;
        expect(data).toContainEqual(
          expect.objectContaining({
            id: medicationBundleData.id,
          }),
        );
      })
      .expect(200)
      .end(done);
  });

  it('addMedicationBundleItem', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            addMedicationBundleItem(
              medicationBundleId: "id"
              input: {
                medicationName: "Test Medication"
                purpose: "Test Purpose"
                duration: "daily"
                frequency: "1"
              }
            ) {
              id
              medicationName
            }
          }
        `,
      })
      .expect(({ body }) => {
        const data = body.data.addMedicationBundleItem;
        expect(data).toEqual(
          expect.objectContaining({
            id: medicationBundleItemMock.id,
            medicationName: medicationBundleItemMock.medicationName,
          }),
        );
      })
      .expect(200)
      .end(done);
  });

  it('updateMedicationBundleItem', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            updateMedicationBundleItem(
              medicationBundleItemId: "id"
              input: {
                medicationName: "Test Medication"
                purpose: "Test Purpose"
                duration: "daily"
                frequency: "1"
              }
            ) {
              id
              medicationName
            }
          }
        `,
      })
      .expect(({ body }) => {
        const data = body.data.updateMedicationBundleItem;
        expect(data).toEqual(
          expect.objectContaining({
            id: medicationBundleItemMock.id,
            medicationName: medicationBundleItemMock.medicationName,
          }),
        );
      })
      .expect(200)
      .end(done);
  });

  it('deleteMedicationBundleItem', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            deleteMedicationBundleItem(
              id: "id"
            ) {
              id
              medicationName
            }
          }
        `,
      })
      .expect(({ body }) => {
        const data = body.data.deleteMedicationBundleItem;
        expect(data).toEqual(
          expect.objectContaining({
            id: medicationBundleItemMock.id,
            medicationName: medicationBundleItemMock.medicationName,
          }),
        );
      })
      .expect(200)
      .end(done);
  });
});
