import { TestingModule } from '@nestjs/testing';
import { Test } from '@nestjs/testing/test';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { PermissionService } from '@clinify/authorization/services/permission.service';
import { MedicationBundleResolver } from '@clinify/medication-bundles/resolvers/medication-bundle.resolver';
import { MedicationBundleService } from '@clinify/medication-bundles/services/medication-bundle.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';
import {
  medicationBundleItemMock,
  medicationBundleMock,
} from '@mocks/factories/medication-bundle.factory';
import { mockUser } from '@mocks/factories/user.factory';

const {
  MedicationBundleAdded,
  MedicationBundleUpdated,
  MedicationBundleRemoved,
  MedicationBundleArchived,
  MedicationBundleUnarchived,
  MedicationBundleItemAdded,
  MedicationBundleItemUpdated,
  MedicationBundleItemRemoved,
} = SubscriptionTypes;

const user: UserModel = mockUser;

const mockProfileRepository = {
  findOne: jest.fn(() => user.defaultProfile),
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

const medicationBundleServiceMock = {
  getMedicationBundle: jest.fn(() => Promise.resolve(medicationBundleMock)),
  getSavedMedicationBundles: jest.fn(() =>
    Promise.resolve([medicationBundleMock]),
  ),
  getMedicationBundleItems: jest.fn(() =>
    Promise.resolve([medicationBundleItemMock]),
  ),
  addMedicationBundle: jest.fn(() => Promise.resolve(medicationBundleMock)),
  updateMedicationBundle: jest.fn(() => Promise.resolve(medicationBundleMock)),
  deleteMedicationBundles: jest.fn(() =>
    Promise.resolve([medicationBundleMock]),
  ),
  archiveMedicationBundles: jest.fn(() =>
    Promise.resolve([medicationBundleMock]),
  ),
  addMedicationBundleItem: jest.fn(() =>
    Promise.resolve(medicationBundleItemMock),
  ),
  updateMedicationBundleItem: jest.fn(() =>
    Promise.resolve(medicationBundleItemMock),
  ),
  deleteMedicationBundleItem: jest.fn(() =>
    Promise.resolve(medicationBundleItemMock),
  ),
};

const managerMock = {};

describe('MedicationBundlesResolver', () => {
  let resolver: MedicationBundleResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MedicationBundleResolver,
        PermissionService,
        {
          provide: MedicationBundleService,
          useValue: medicationBundleServiceMock,
        },
        {
          provide: getRepositoryToken(PermissionModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: mockProfileRepository,
        },
        {
          provide: DataSource,
          useValue: { manager: managerMock },
        },
        {
          provide: EntityManager,
          useValue: managerMock,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
      ],
    }).compile();

    resolver = module.get<MedicationBundleResolver>(MedicationBundleResolver);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  it('medicationBundle should call getMedicationBundle from service and return value', async () => {
    const res = await resolver.medicationBundle(user.defaultProfile, 'id');
    expect(
      medicationBundleServiceMock.getMedicationBundle,
    ).toHaveBeenCalledWith(user.defaultProfile, 'id');
    expect(res).toEqual(medicationBundleMock);
  });

  it('getSavedMedicationBundles should call getSavedMedicationBundles from services and return value', async () => {
    const res = await resolver.getSavedMedicationBundles(user.defaultProfile);
    expect(
      medicationBundleServiceMock.getSavedMedicationBundles,
    ).toHaveBeenCalledWith(user.defaultProfile);
    expect(res).toEqual([medicationBundleMock]);
  });

  it('getMedicationBundleItems should call getMedicationBundleItems from service and return value', async () => {
    const { medicationBundleItems } = medicationBundleMock;
    const res = await resolver.getBundleItems(medicationBundleMock);
    expect(
      medicationBundleServiceMock.getMedicationBundleItems,
    ).toHaveBeenCalledWith(medicationBundleMock.id);
    expect(res).toEqual(medicationBundleItems);
  });

  it('addMedicationBundle should call addMedicationBundle from services and return value', async () => {
    const res = await resolver.addMedicationBundle(
      user.defaultProfile,
      medicationBundleMock,
    );
    expect(
      medicationBundleServiceMock.addMedicationBundle,
    ).toHaveBeenCalledWith(user.defaultProfile, medicationBundleMock);
    expect(res).toEqual(medicationBundleMock);
  });

  it('updateMedicationBundle should call updateMedicationBundle from services and return value', async () => {
    const res = await resolver.updateMedicationBundle(
      user.defaultProfile,
      medicationBundleMock,
      'id',
    );
    expect(
      medicationBundleServiceMock.updateMedicationBundle,
    ).toHaveBeenCalledWith(user.defaultProfile, medicationBundleMock, 'id');
    expect(res).toEqual(medicationBundleMock);
  });

  it('deleteMedicationBundles should call deleteMedicationBundles from services and return value', async () => {
    const res = await resolver.deleteMedicationBundles(user.defaultProfile, [
      'ids',
    ]);
    expect(
      medicationBundleServiceMock.deleteMedicationBundles,
    ).toHaveBeenCalledWith(user.defaultProfile, ['ids']);
    expect(res).toEqual([medicationBundleMock]);
  });

  it('archiveMedicationBundles should call archiveMedicationBundles and return value', async () => {
    const res = await resolver.archiveMedicationBundles(
      user.defaultProfile,
      ['ids'],
      true,
    );
    expect(
      medicationBundleServiceMock.archiveMedicationBundles,
    ).toHaveBeenCalledWith(user.defaultProfile, ['ids'], true);
    expect(res).toEqual([medicationBundleMock]);

    await resolver.archiveMedicationBundles(
      user.defaultProfile,
      ['ids'],
      false,
    );
    expect(
      medicationBundleServiceMock.archiveMedicationBundles,
    ).toHaveBeenCalledWith(user.defaultProfile, ['ids'], false);
  });

  it('addMedicationBundleItem should call addMedicationBundleItem from services and return value', async () => {
    const res = await resolver.addMedicationBundleItem(
      user.defaultProfile,
      'id',
      medicationBundleItemMock,
    );
    expect(
      medicationBundleServiceMock.addMedicationBundleItem,
    ).toHaveBeenCalledWith('id', medicationBundleItemMock, user.defaultProfile);
    expect(res).toEqual(medicationBundleItemMock);
  });

  it('updateMedicationBundleItem should call updateMedicationBundleItem from services and return value', async () => {
    const res = await resolver.updateMedicationBundleItem(
      user.defaultProfile,
      medicationBundleItemMock,
      'id',
    );
    expect(
      medicationBundleServiceMock.updateMedicationBundleItem,
    ).toHaveBeenCalledWith('id', medicationBundleItemMock, user.defaultProfile);
    expect(res).toEqual(medicationBundleItemMock);
  });

  it('deleteMedicationBundleItem should call deleteMedicationBundleItem from services and return value', async () => {
    const res = await resolver.deleteMedicationBundleItem(
      user.defaultProfile,
      'id',
    );
    expect(
      medicationBundleServiceMock.deleteMedicationBundleItem,
    ).toHaveBeenCalledWith(user.defaultProfile, 'id');
    expect(res).toEqual(medicationBundleItemMock);
  });

  it('addMedicationBundleSubsHandler() should trigger MedicationBundelAdded subscription', () => {
    resolver.addMedicationBundleSubsHandler('id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      MedicationBundleAdded,
    );
  });

  it('updateMedicationBundleSubsHandler() should trigger MedicationBundleUpdated subscription', () => {
    resolver.updateMedicationBundleSubsHandler('id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      MedicationBundleUpdated,
    );
  });

  it('deleteMedicationBundleSubsHandler() should trigger MedicationBundleRemoved subscription', () => {
    resolver.deleteMedicationBundleSubsHandler('id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      MedicationBundleRemoved,
    );
  });

  it('archiveMedicationBundleSubsHandler() should trigger MedicationBundleArchived subscription', () => {
    resolver.archiveMedicationBundleSubsHandler('id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      MedicationBundleArchived,
    );
  });

  it('unarchiveMedicationBundleSubsHandler() should trigger MedicationBundleUnarchived subscription', () => {
    resolver.unarchiveMedicationBundleSubsHandler('id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      MedicationBundleUnarchived,
    );
  });

  it('addMedicationBundleItemSubsHandler() should trigger MedicationBundleItemAdded subscription', () => {
    resolver.addMedicationBundleItemSubsHandler('id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      MedicationBundleItemAdded,
    );
  });

  it('updateMedicationBundleItemSubsHandler() should trigger MedicationBundleItemUpdated subscription', () => {
    resolver.updateMedicationBundleItemSubsHandler('id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      MedicationBundleItemUpdated,
    );
  });

  it('deleteMedicationBundleItemSubsHandler() should trigger MedicationBundleItemRemoved subscription', () => {
    resolver.deleteMedicationBundleItemSubsHandler('id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      MedicationBundleItemRemoved,
    );
  });
});
