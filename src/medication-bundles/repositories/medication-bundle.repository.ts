import { NotFoundException } from '@nestjs/common';
import cloneDeep from 'lodash.clonedeep';
import moment from 'moment';
import { In, Repository, SelectQueryBuilder } from 'typeorm';
import { MedicationBundleItemInput } from '@clinify/medication-bundles/inputs/medication-bundle-item.input';
import {
  MedicationBundleInput,
  NewMedicationBundleInput,
} from '@clinify/medication-bundles/inputs/medication-bundle.input';
import { MedicationBundleItemModel } from '@clinify/medication-bundles/models/medication-bundle-item.model';
import { MedicationBundleModel } from '@clinify/medication-bundles/models/medication-bundle.model';
import { MedicationBundleResponse } from '@clinify/medication-bundles/responses/medication-bundle.response';
import { FilterInput } from '@clinify/shared/validators/filter.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { takePaginatedResponses } from '@clinify/utils/pagination';

export interface IMedicationBundleRepository
  extends Repository<MedicationBundleModel> {
  this: Repository<MedicationBundleModel>;
  buildProfileQuery(
    id: string,
    options: Partial<FilterInput>,
  ): SelectQueryBuilder<MedicationBundleModel>;
  findByProfile(
    profile: string,
    filterOptions: FilterInput,
  ): Promise<MedicationBundleResponse>;
  getMedicationBundle(
    mutator: ProfileModel,
    medicationBundleId: string,
  ): Promise<MedicationBundleModel>;
  getMedicationBundleItems(
    medBundleId: string,
  ): Promise<MedicationBundleItemModel[]>;
  getSavedMedicationBundles(
    profile: ProfileModel,
  ): Promise<MedicationBundleModel[]>;
  resolveMedicationBundleData(
    mutator: ProfileModel,
    input: NewMedicationBundleInput,
    existing?: MedicationBundleModel,
  ): Promise<MedicationBundleModel>;
  addMedicationBundle(
    mutator: ProfileModel,
    input: NewMedicationBundleInput,
  ): Promise<MedicationBundleModel>;
  updateMedicationBundle(
    mutator: ProfileModel,
    input: MedicationBundleInput,
    id: string,
  ): Promise<MedicationBundleModel>;
  deleteMedicationBundles(
    mutator: ProfileModel,
    medicationBundleIds: string[],
  ): Promise<MedicationBundleModel[]>;
  addMedicationBundleItem(
    medicationBundleId: string,
    item: MedicationBundleItemInput,
    mutator: ProfileModel,
  ): Promise<MedicationBundleItemModel>;
  updateMedicationBundleItem(
    id: string,
    item: MedicationBundleItemInput,
    mutator: ProfileModel,
  ): Promise<MedicationBundleItemModel>;
  archiveMedicationBundles(
    mutator: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<MedicationBundleModel[]>;
  deleteMedicationBundleItem(
    id: string,
    mutator: ProfileModel,
  ): Promise<MedicationBundleItemModel>;
}

export const CustomMedicationBundleRepoMethods: Pick<
  IMedicationBundleRepository,
  | 'buildProfileQuery'
  | 'findByProfile'
  | 'getMedicationBundle'
  | 'getMedicationBundleItems'
  | 'getSavedMedicationBundles'
  | 'resolveMedicationBundleData'
  | 'addMedicationBundle'
  | 'updateMedicationBundle'
  | 'deleteMedicationBundles'
  | 'addMedicationBundleItem'
  | 'updateMedicationBundleItem'
  | 'archiveMedicationBundles'
  | 'deleteMedicationBundleItem'
> = {
  buildProfileQuery(
    this: IMedicationBundleRepository,
    id: string,
    options: Partial<FilterInput>,
  ) {
    const { dateRange, keyword, archive } = { ...options };

    let query = this.createQueryBuilder('medicationBundles')
      .leftJoinAndSelect('medicationBundles.profile', 'profile')
      .leftJoinAndSelect(
        'medicationBundles.medicationBundleItems',
        'medicationBundleItems',
      )
      .where('medicationBundles.profile = :id', { id })
      .andWhere('medicationBundles.archived = :archived', {
        archived: !!archive,
      });

    if (keyword) {
      query = query.andWhere(
        `(
          "medicationBundles".bundle_name ILIKE :keyword OR
          "medicationBundleItems".medication_name ILIKE :keyword OR
          "medicationBundleItems".medication_consumables :: jsonb :: text ILIKE :keyword
        )`,
        {
          keyword: `%${keyword}%`,
        },
      );
    }

    if (dateRange?.from) {
      query = query.andWhere('medicationBundleItems.bundleDate >= :from', {
        from: dateRange.from,
      });
    }

    if (dateRange?.to) {
      query = query.andWhere('medicationBundleItems.bundleDate < :to', {
        to: dateRange.to,
      });
    }

    return query;
  },

  async findByProfile(
    this: IMedicationBundleRepository,
    profile: string,
    filterOptions: FilterInput,
  ): Promise<MedicationBundleResponse> {
    const { skip, take } = { ...filterOptions };
    const query = this.buildProfileQuery(profile, filterOptions)
      .orderBy('medicationBundles.createdDate', 'DESC')
      .skip(skip)
      .take(take);

    const response = await query.getManyAndCount();

    return new MedicationBundleResponse(
      ...takePaginatedResponses(response, take),
    );
  },

  getMedicationBundle(
    this: IMedicationBundleRepository,
    mutator: ProfileModel,
    medicationBundleId: string,
  ): Promise<MedicationBundleModel> {
    return this.findOneOrFail({
      where: {
        id: medicationBundleId,
        profile: { id: mutator.id },
      },
      relations: ['medicationBundleItems'],
      withDeleted: true,
    }).catch(() => {
      throw new NotFoundException('Medication Bundle Not Found');
    });
  },

  getMedicationBundleItems(
    this: IMedicationBundleRepository,
    medBundleId: string,
  ): Promise<MedicationBundleItemModel[]> {
    return this.manager.find(MedicationBundleItemModel, {
      where: { medicationBundle: { id: medBundleId } },
    });
  },

  getSavedMedicationBundles(
    this: IMedicationBundleRepository,
    profile: ProfileModel,
  ): Promise<MedicationBundleModel[]> {
    return this.find({
      where: {
        profile: { id: profile.id },
        archived: false,
      },
      select: ['id', 'bundleName'],
    });
  },

  async resolveMedicationBundleData(
    this: IMedicationBundleRepository,
    mutator: ProfileModel,
    input: NewMedicationBundleInput,
    existing?: MedicationBundleModel,
  ): Promise<MedicationBundleModel> {
    const bundleItems: Array<
      MedicationBundleItemInput | MedicationBundleItemModel
    > = input.medicationBundleItems.map((item) => ({
      ...item,
      createdBy: mutator,
      creatorName: mutator.fullName,
    }));
    if (existing) {
      let existingItems = existing.medicationBundleItems;
      const duplicates: string[] = [];
      bundleItems.forEach((item) => {
        const duplicateBundleItem = existingItems.find(
          ({ medicationName, medicationConsumables }) => {
            const { option } = item;
            const medicationNameExists = medicationName === item.medicationName;
            const medicationConsumablesExists =
              (medicationConsumables || [])
                .map((con) => con.name)
                .sort()
                .join(',') ===
              (item.medicationConsumables || [])
                .map((con) => con.name)
                .sort()
                .join(',');
            return option === 'C'
              ? medicationConsumablesExists
              : medicationNameExists;
          },
        );
        if (duplicateBundleItem) {
          duplicates.push(duplicateBundleItem.id);
        }
      });
      if (duplicates.length) {
        await this.createQueryBuilder()
          .delete()
          .from(MedicationBundleItemModel)
          .where('id IN (:...ids)', { ids: duplicates })
          .execute();
      }
      existingItems = existingItems.filter(
        ({ id }) =>
          duplicates.findIndex((duplicateId) => duplicateId === id) === -1,
      );
      bundleItems.push(...existingItems);
    }

    return {
      ...(existing || input),
      medicationBundleItems: [...(bundleItems as MedicationBundleItemModel[])],
      ...(existing
        ? {
            updatedBy: mutator,
            lastModifierName: mutator.fullName,
          }
        : {
            profile: mutator,
            creatorName: mutator.fullName,
            createdBy: mutator,
          }),
    };
  },

  async addMedicationBundle(
    this: IMedicationBundleRepository,
    mutator: ProfileModel,
    input: NewMedicationBundleInput,
  ): Promise<MedicationBundleModel> {
    const existing = await this.findOne({
      where: {
        profile: { id: mutator.id },
        bundleName: input.bundleName,
      },
      relations: ['medicationBundleItems'],
    });
    const bundle = await this.resolveMedicationBundleData(
      mutator,
      input,
      existing,
    );

    return this.save(bundle);
  },

  async updateMedicationBundle(
    this: IMedicationBundleRepository,
    mutator: ProfileModel,
    input: MedicationBundleInput,
    id: string,
  ): Promise<MedicationBundleModel> {
    const medicationBundle = await this.findOneOrFail({
      where: {
        id,
        profile: { id: mutator.id },
      },
      relations: ['medicationBundleItems'],
    }).catch(() => {
      throw new NotFoundException('Medication Bundle Not Found');
    });

    return this.save({
      ...medicationBundle,
      ...input,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  },

  async deleteMedicationBundles(
    this: IMedicationBundleRepository,
    mutator: ProfileModel,
    medicationBundleIds: string[],
  ): Promise<MedicationBundleModel[]> {
    const medicationBundles = await this.find({
      where: {
        id: In(medicationBundleIds),
        profile: { id: mutator.id },
      },
    });

    if (medicationBundles.length) {
      await this.remove(
        cloneDeep(medicationBundles).map((v) => ({
          ...v,
          deletedBy: {
            id: mutator.id,
            fullName: mutator.fullName,
            entityId: v.id,
          },
        })),
      );
    }

    return medicationBundles;
  },

  async addMedicationBundleItem(
    this: IMedicationBundleRepository,
    medicationBundleId: string,
    item: MedicationBundleItemInput,
    mutator: ProfileModel,
  ): Promise<MedicationBundleItemModel> {
    const medicationBundle = await this.findOneOrFail({
      where: {
        id: medicationBundleId,
        profile: { id: mutator.id },
      },
    }).catch(() => {
      throw new NotFoundException('Medication Bundle Not Found');
    });

    return this.manager.save(
      new MedicationBundleItemModel({
        ...item,
        createdBy: mutator,
        creatorName: mutator.fullName,
        medicationBundle,
      }),
    );
  },

  async updateMedicationBundleItem(
    this: IMedicationBundleRepository,
    id: string,
    item: MedicationBundleItemInput,
    mutator: ProfileModel,
  ): Promise<MedicationBundleItemModel> {
    const medicationBundleItem = await this.manager
      .findOneOrFail(MedicationBundleItemModel, {
        where: { id, createdBy: { id: mutator.id } },
        relations: ['medicationBundle'],
      })
      .catch(() => {
        throw new NotFoundException('Medication Bundle Item Not Found');
      });

    return this.manager.save(MedicationBundleItemModel, {
      ...medicationBundleItem,
      ...item,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  },

  async archiveMedicationBundles(
    this: IMedicationBundleRepository,
    mutator: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<MedicationBundleModel[]> {
    const medicationBundles = await this.find({
      where: {
        profile: { id: mutator.id },
        id: In(ids),
      },
      relations: ['medicationBundleItems'],
    });
    const validIds = medicationBundles.map(({ id }) => id);
    await this.createQueryBuilder('medicationBundles')
      .update(MedicationBundleModel)
      .set({ archived: archive, updatedDate: () => 'updated_date' })
      .whereInIds(validIds)
      .execute();

    return medicationBundles.map((medBundle) => ({
      ...medBundle,
      archived: archive,
    }));
  },

  async deleteMedicationBundleItem(
    this: IMedicationBundleRepository,
    id: string,
    mutator: ProfileModel,
  ): Promise<MedicationBundleItemModel> {
    const medicationBundleItem = await this.manager
      .findOneOrFail(MedicationBundleItemModel, {
        where: { id, createdBy: { id: mutator.id } },
        relations: ['medicationBundle'],
      })
      .catch(() => {
        throw new NotFoundException('Medication Bundle Item Not Found');
      });

    await this.manager.delete(MedicationBundleItemModel, { id });

    return medicationBundleItem;
  },
};
