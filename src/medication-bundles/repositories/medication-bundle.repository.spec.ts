import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import moment from 'moment/moment';
import { DataSource, EntityManager } from 'typeorm';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendDSRepo } from '@clinify/database/extendModel';
import { MedicationBundleModel } from '@clinify/medication-bundles/models/medication-bundle.model';
import {
  CustomMedicationBundleRepoMethods,
  IMedicationBundleRepository,
} from '@clinify/medication-bundles/repositories/medication-bundle.repository';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { createMedicationBundles } from '@fixtures/medication-bundle.fixtures';
import { createUsers } from '@fixtures/user.fixtures';
import {
  medicationBundleFactory,
  medicationBundleItemFactory,
} from '@mocks/factories/medication-bundle.factory';

describe('MedicationBundleRepository', () => {
  let ds: DataSource;
  let medicationBundles: MedicationBundleModel[];
  let medicationBundle: MedicationBundleModel;
  let manager: EntityManager;
  let repo: IMedicationBundleRepository;
  let otherUser: ProfileModel;

  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [],
    }).compile();

    ds = module.get<DataSource>(DataSource);
    manager = ds.manager;
    repo = extendDSRepo<IMedicationBundleRepository>(
      ds,
      MedicationBundleModel,
      CustomMedicationBundleRepoMethods,
    );

    const [user] = await createUsers(manager, 1);
    otherUser = user.defaultProfile;
  });

  beforeEach(async () => {
    medicationBundles = await createMedicationBundles(manager, 2);
    medicationBundle = medicationBundles[0];
  });

  afterAll(async () => {
    await ds.destroy();
    await module.close();
  });

  it('findByProfile() should find medication bundles for a particular user', async () => {
    const res = await repo.findByProfile(medicationBundle.profile.id, {
      skip: 0,
      take: 10,
    });

    expect(res).toHaveProperty('list');
    expect(res.list.length).toEqual(2);
    expect(res.list).toContainEqual(
      expect.objectContaining({
        id: medicationBundle.id,
        createdDate: medicationBundle.createdDate,
      }),
    );
  });

  it('findByProfile() should find medication bundles for a particular user using filters', async () => {
    const { bundleName: keyword } = medicationBundle;
    const { bundleDate } = medicationBundle.medicationBundleItems[0];
    const res = await repo.findByProfile(medicationBundle.profile.id, {
      skip: 0,
      take: 10,
      keyword,
      dateRange: {
        from: moment(bundleDate).startOf('day').toDate(),
        to: moment(bundleDate).endOf('day').toDate(),
      },
    });

    expect(res).toHaveProperty('list');
    expect(res.totalCount > 0).toBeTruthy();
  });

  it('getMedicationBundle() should fetch single medication bundle', async () => {
    const res = await repo.getMedicationBundle(
      medicationBundle.profile,
      medicationBundle.id,
    );
    expect(res).toEqual(
      expect.objectContaining({
        id: medicationBundle.id,
        bundleName: medicationBundle.bundleName,
      }),
    );
  });

  it('getMedicationBundle() should be unable to fetch bundle not created by self', async () => {
    await expect(
      repo.getMedicationBundle(otherUser, medicationBundle.id),
    ).rejects.toThrow('Medication Bundle Not Found');
  });

  it('getMedicationBundleItems() should fetch medication bundle items', async () => {
    const { medicationBundleItems } = medicationBundle;
    const res = await repo.getMedicationBundleItems(medicationBundle.id);
    expect(res.length).toEqual(medicationBundle.medicationBundleItems.length);
    expect(res).toContainEqual(
      expect.objectContaining({
        id: medicationBundleItems[0].id,
        medicationName: medicationBundleItems[0].medicationName,
      }),
    );
  });

  it('getSavedMedicationBundles() get all saved medication bundles', async () => {
    const res = await repo.getSavedMedicationBundles(medicationBundle.profile);
    expect(res.length).toEqual(2);
    expect(res).toContainEqual(
      expect.objectContaining({
        id: medicationBundle.id,
        bundleName: medicationBundle.bundleName,
      }),
    );
  });

  it('addMedicationBundle() add medication bundle', async () => {
    const input = medicationBundleFactory.build();
    delete input.id;
    const res = await repo.addMedicationBundle(medicationBundle.profile, input);
    expect(res.id).toBeTruthy();
    expect(res.bundleName).toEqual(input.bundleName);

    const getSavedBundlesRes = await repo.getSavedMedicationBundles(
      medicationBundle.profile,
    );
    expect(getSavedBundlesRes.length).toBeTruthy();
    expect(getSavedBundlesRes).toContainEqual(
      expect.objectContaining({
        id: res.id,
        bundleName: res.bundleName,
      }),
    );
  });

  it('addMedicationBundle() add medication bundle to existing bundle', async () => {
    const input = medicationBundleFactory.build();
    input.bundleName = medicationBundle.bundleName;
    delete input.id;
    const res = await repo.addMedicationBundle(medicationBundle.profile, input);
    expect(res.id).toEqual(medicationBundle.id);
    expect(res.bundleName).toEqual(medicationBundle.bundleName);

    const getSavedBundlesRes = await repo.getSavedMedicationBundles(
      medicationBundle.profile,
    );
    expect(getSavedBundlesRes.length).toEqual(2);
    expect(getSavedBundlesRes).toContainEqual(
      expect.objectContaining({
        id: res.id,
        bundleName: res.bundleName,
      }),
    );
  });

  it('updateMedicationBundle() update medication bundle', async () => {
    const input = medicationBundleFactory.build();
    delete input.id;
    delete input.profile;
    const res = await repo.updateMedicationBundle(
      medicationBundle.profile,
      input,
      medicationBundle.id,
    );
    expect(res).toEqual(
      expect.objectContaining({
        id: medicationBundle.id,
        bundleName: input.bundleName,
      }),
    );
  });

  it('updateMedicationBundle() should be unable to modify for others', async () => {
    const input = medicationBundleFactory.build();
    delete input.id;
    delete input.profile;
    await expect(
      repo.updateMedicationBundle(otherUser, input, medicationBundle.id),
    ).rejects.toThrow('Medication Bundle Not Found');
  });

  it('deleteMedicationBundles() delete medication bundles', async () => {
    const res = await repo.deleteMedicationBundles(medicationBundle.profile, [
      medicationBundle.id,
    ]);
    expect(res.length).toEqual(1);
    expect(res).toContainEqual(
      expect.objectContaining({
        id: medicationBundle.id,
      }),
    );

    const getSavedBundlesRes = await repo.getSavedMedicationBundles(
      medicationBundle.profile,
    );

    expect(getSavedBundlesRes.length).toEqual(1);
  });

  it('deleteMedicationBundles() should be unable to delete medication bundle not created by self', async () => {
    const res = await repo.deleteMedicationBundles(otherUser, [
      medicationBundle.id,
    ]);
    expect(res.length).toEqual(0);
  });

  it('addMedicationBundleItem() adds new item to medication bundle', async () => {
    const input = medicationBundleItemFactory.build();
    delete input.id;
    const res = await repo.addMedicationBundleItem(
      medicationBundle.id,
      input,
      medicationBundle.profile,
    );
    expect(res.id).toBeTruthy();
    expect(res).toEqual(
      expect.objectContaining({
        medicationName: input.medicationName,
      }),
    );

    const getBundleRes = await repo.getMedicationBundle(
      medicationBundle.profile,
      medicationBundle.id,
    );
    expect(getBundleRes.medicationBundleItems.length).toEqual(2);
  });

  it('addMedicationBundleItem() should be unable to add new item to medication bundle not created by self', async () => {
    const input = medicationBundleItemFactory.build();
    delete input.id;

    await expect(
      repo.addMedicationBundleItem(medicationBundle.id, input, otherUser),
    ).rejects.toThrow('Medication Bundle Not Found');
  });

  it('updateMedicationBundleItem() updates medication bundle items', async () => {
    const { medicationBundleItems } = medicationBundle;
    const updatedInput = medicationBundleItemFactory.build();
    delete updatedInput.id;
    const res = await repo.updateMedicationBundleItem(
      medicationBundleItems[0].id,
      updatedInput,
      medicationBundle.profile,
    );
    expect(res.id).toEqual(medicationBundleItems[0].id);
    expect(res.medicationName).toEqual(updatedInput.medicationName);
  });

  it('updateMedicationBundleItem() should be unable to update medication bundle items from bundle not created by self', async () => {
    const { medicationBundleItems } = medicationBundle;
    const updatedInput = medicationBundleItemFactory.build();
    delete updatedInput.id;
    await expect(
      repo.updateMedicationBundleItem(
        medicationBundleItems[0].id,
        updatedInput,
        otherUser,
      ),
    ).rejects.toThrow('Medication Bundle Item Not Found');
  });

  it('archiveMedicationBundles() should archive and unarchive medication bundles', async () => {
    let res = await repo.archiveMedicationBundles(
      medicationBundle.profile,
      [medicationBundle.id],
      true,
    );
    expect(res.length).toEqual(1);
    expect(res).toContainEqual(
      expect.objectContaining({
        id: medicationBundle.id,
        archived: true,
      }),
    );

    res = await repo.archiveMedicationBundles(
      medicationBundle.profile,
      [medicationBundle.id],
      false,
    );
    expect(res.length).toEqual(1);
    expect(res).toContainEqual(
      expect.objectContaining({
        id: medicationBundle.id,
        archived: false,
      }),
    );
  });

  it('archiveMedicationBundles() should be unable to archive and unarchive medication bundles', async () => {
    let res = await repo.archiveMedicationBundles(
      otherUser,
      [medicationBundle.id],
      true,
    );
    expect(res.length).toEqual(0);

    res = await repo.archiveMedicationBundles(
      otherUser,
      [medicationBundle.id],
      false,
    );
    expect(res.length).toEqual(0);
  });

  it('deleteMedicationBundleItem()', async () => {
    const { medicationBundleItems } = medicationBundle;

    const res = await repo.deleteMedicationBundleItem(
      medicationBundleItems[0].id,
      medicationBundle.profile,
    );
    expect(res.id).toEqual(medicationBundleItems[0].id);

    const getBundleRes = await repo.getMedicationBundle(
      medicationBundle.profile,
      medicationBundle.id,
    );
    expect(getBundleRes.medicationBundleItems.length).toEqual(0);
  });

  it('deleteMedicationBundleItem() should be unable to delete medication bundle item not deleted by self', async () => {
    const { medicationBundleItems } = medicationBundle;

    await expect(
      repo.deleteMedicationBundleItem(medicationBundleItems[0].id, otherUser),
    ).rejects.toThrow('Medication Bundle Item Not Found');
  });
});
