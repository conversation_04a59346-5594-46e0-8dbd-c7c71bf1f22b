import { Field, InputType } from '@nestjs/graphql';
import { IsOptional, IsUUID } from 'class-validator';
import { MedicationBundleItemInput } from '@clinify/medication-bundles/inputs/medication-bundle-item.input';

@InputType()
export class MedicationBundleInput {
  @Field({ nullable: true })
  @IsUUID('4')
  @IsOptional()
  id?: string;

  @Field()
  clinifyId: string;

  @Field()
  bundleName: string;

  @Field({ nullable: true })
  additionalNote?: string;

  @Field(() => [String], { nullable: true })
  documentUrl?: string[];
}

@InputType()
export class NewMedicationBundleInput extends MedicationBundleInput {
  @Field(() => [MedicationBundleItemInput], { nullable: true })
  medicationBundleItems?: MedicationBundleItemInput[];
}
