import { Field, InputType } from '@nestjs/graphql';
import { IsOptional, IsUUID } from 'class-validator';
import {
  MedicationConsumable,
  MedPriceDetailInput,
} from '@clinify/medications/validators/medication-details.input';
import {
  BankType,
  MedicationOptionType,
} from '@clinify/shared/enums/medication';
import { DiagnosisInput } from '@clinify/shared/validators/service-detail.input';

@InputType()
export class MedicationBundleItemInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsUUID('4')
  id?: string;

  @Field(() => Date, { nullable: true })
  bundleDate?: Date;

  @Field({ nullable: true })
  duration?: string;

  @Field({ nullable: true })
  medicationName?: string;

  @Field({ nullable: true })
  medicationCategory?: string;

  @Field({ nullable: true })
  medicationType?: string;

  @Field(() => [DiagnosisInput], { nullable: true })
  diagnosis: DiagnosisInput[];

  @Field({ nullable: true })
  purpose: string;

  @Field({ nullable: true })
  administrationMethod: string;

  @Field({ nullable: true })
  quantity?: string;

  @Field({ nullable: true })
  unitPrice?: string;

  @Field({ nullable: true })
  dosage?: string;

  @Field({ nullable: true })
  dosageUnit?: string;

  @Field({ nullable: true })
  frequency: string;

  @Field(() => MedicationOptionType, { nullable: true })
  option: MedicationOptionType;

  @Field(() => MedPriceDetailInput, { nullable: true })
  priceDetails: MedPriceDetailInput;

  @Field(() => BankType, { nullable: true })
  bank?: BankType;

  @Field({ nullable: true })
  provider?: string;

  @Field({ nullable: true })
  drugInventoryId?: string;

  @Field({ nullable: true })
  inventoryClass?: string;

  @Field(() => [MedicationConsumable], { nullable: true })
  medicationConsumables?: MedicationConsumable[];

  @Field({ nullable: true })
  prescriptionNote: string;
}
