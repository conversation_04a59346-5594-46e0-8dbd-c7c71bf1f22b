import { Field, Int, ObjectType } from '@nestjs/graphql';
import { MedicationBundleModel } from '@clinify/medication-bundles/models/medication-bundle.model';

@ObjectType()
export class MedicationBundleResponse {
  constructor(medicationBundles: MedicationBundleModel[], totalCount: number) {
    this.list = medicationBundles;
    this.totalCount = totalCount;
  }

  @Field(() => Int)
  totalCount: number;

  @Field(() => [MedicationBundleModel])
  list: MedicationBundleModel[];
}
