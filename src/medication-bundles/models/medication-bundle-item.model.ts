import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsString, IsUUID } from 'class-validator';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
} from 'typeorm';
import { BaseAudits } from '../interface/base-audits.entity';
import { MedicationBundleModel } from '@clinify/medication-bundles/models/medication-bundle.model';
import {
  MedicationConsumable,
  MedPriceDetailInput,
} from '@clinify/medications/validators/medication-details.input';
import {
  BankType,
  MedicationOptionType,
} from '@clinify/shared/enums/medication';
import { DiagnosisInput } from '@clinify/shared/validators/service-detail.input';

@ObjectType()
@Entity({ name: 'medication_bundle_item' })
export class MedicationBundleItemModel extends BaseAudits {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsUUID('4')
  id: string;

  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'bundle_date' })
  bundleDate?: Date;

  @Field({ nullable: true })
  @Column({ nullable: true, type: 'text' })
  duration?: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'medication_name' })
  @IsString()
  medicationName?: string;

  @Field(() => [DiagnosisInput], { nullable: true })
  @Column({
    nullable: true,
    name: 'diagnosis',
    type: 'jsonb',
  })
  diagnosis: DiagnosisInput[];

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'category' })
  medicationCategory?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'medication_type' })
  medicationType?: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true })
  purpose?: string;

  @Field({ nullable: true })
  @Column({ name: 'hospital_id', nullable: true })
  hospitalId?: string;

  @Field(() => [MedicationConsumable], { nullable: true })
  @Column({ type: 'jsonb', nullable: true, name: 'medication_consumables' })
  medicationConsumables?: MedicationConsumable[];

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'administration_method' })
  administrationMethod?: string;

  @Field(() => MedicationOptionType, { nullable: true })
  @Column({
    type: 'enum',
    nullable: true,
    name: 'option',
    enum: MedicationOptionType,
    default: MedicationOptionType.M,
  })
  option: MedicationOptionType;

  @Field(() => MedPriceDetailInput, { nullable: true })
  @Column({
    nullable: true,
    name: 'price_details',
    type: 'jsonb',
  })
  priceDetails?: MedPriceDetailInput;

  @Field({ nullable: true })
  @Column({ nullable: true, type: 'text' })
  bank?: BankType;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'dosage' })
  dosage?: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'dosage_unit' })
  dosageUnit?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'quantity' })
  quantity?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'unit_price' })
  unitPrice?: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true })
  frequency?: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'provider' })
  provider?: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'drug_inventory_id' })
  drugInventoryId: string;

  @Field(() => MedicationBundleModel)
  @ManyToOne(
    () => MedicationBundleModel,
    (medicationBundle) => medicationBundle.medicationBundleItems,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'medication_bundle' })
  medicationBundle: MedicationBundleModel;

  @Field(() => String)
  @Column({ name: 'medication_bundle', nullable: true })
  medicationBundleId?: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'prescription_note' })
  prescriptionNote?: string;

  @Field({ nullable: true })
  @Column({
    type: 'text',
    nullable: true,
    name: 'inventory_class',
    default: 'External',
  })
  inventoryClass: string;

  constructor(medicationBundleItem: Partial<MedicationBundleItemModel>) {
    super();
    Object.assign(this, medicationBundleItem);
  }
}
