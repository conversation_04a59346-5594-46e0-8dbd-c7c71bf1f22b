import { Field, ObjectType, ID } from '@nestjs/graphql';
import { IsUUID } from 'class-validator';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { AuditEntitiesWithProfile } from '@clinify/medication-bundles/interface/base-audits.entity';
import { MedicationBundleItemModel } from '@clinify/medication-bundles/models/medication-bundle-item.model';

@ObjectType()
@Entity({ name: 'medication_bundles' })
export class MedicationBundleModel extends AuditEntitiesWithProfile {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsUUID('4')
  id?: string;

  @Field()
  @Column({ name: 'bundle_name' })
  bundleName: string;

  @Field()
  @Column({ name: 'clinify_id' })
  clinifyId: string;

  @Column({ name: 'archived', default: false })
  archived?: boolean;

  @OneToMany(
    () => MedicationBundleItemModel,
    (medicationBundleItem) => medicationBundleItem.medicationBundle,
    {
      cascade: true,
      nullable: true,
    },
  )
  medicationBundleItems?: MedicationBundleItemModel[];

  @Field(() => [String], { nullable: true })
  @Column({ name: 'document_url', nullable: true, type: 'text', array: true })
  documentUrl?: string[];

  @Field({ nullable: true })
  @Column({ nullable: true })
  additionalNote?: string;

  constructor(medicationBundle: Partial<MedicationBundleModel>) {
    super();
    Object.assign(this, medicationBundle);
  }
}
