import { Logger, Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MedicationBundleModel } from './models/medication-bundle.model';
import { AuthorizationModule } from '@clinify/authorization/authorization.module';
import { extendModel } from '@clinify/database/extendModel';
import { CustomMedicationBundleRepoMethods } from '@clinify/medication-bundles/repositories/medication-bundle.repository';
import { MedicationBundleResolver } from '@clinify/medication-bundles/resolvers/medication-bundle.resolver';
import { MedicationBundleService } from '@clinify/medication-bundles/services/medication-bundle.service';
import PubSub, { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';

@Module({
  imports: [
    TypeOrmModule.forFeature([MedicationBundleModel]),
    AuthorizationModule,
  ],
  providers: [
    extendModel(MedicationBundleModel, CustomMedicationBundleRepoMethods),
    MedicationBundleService,
    MedicationBundleResolver,
    <PERSON><PERSON>,
    { provide: PUB_SUB, useFactory: () => PubSub },
  ],
  exports: [MedicationBundleService],
})
export class MedicationBundlesModule {}
