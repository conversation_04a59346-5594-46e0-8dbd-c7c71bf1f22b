import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsUUID } from 'class-validator';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AuditEntitiesWithProfile } from './base-audits.entity';
import { OrganisationAppointmentModel } from '@clinify/appointments/models/organisation_appointment.model';
import { BillModel } from '@clinify/bills/models/bill.model';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { PreauthorizationDetailsModel } from '@clinify/preauthorization-details/models/preauthorization-details.model';
import { ServiceDetailInput } from '@clinify/shared/validators/service-detail.input';
import { ProcedureTypeInput } from '@clinify/surgeries/validators/surgery.input';

@ObjectType()
@Entity({ name: 'request_procedures' })
export class RequestProcedureModel extends AuditEntitiesWithProfile {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsUUID('4')
  id: string;

  @Field({ nullable: true })
  @Column({ name: 'surgery_date', nullable: true })
  surgeryDate: Date;

  @Field({ nullable: true })
  @Column({ name: 'duration', nullable: true })
  duration: string;

  @Field(() => [ProcedureTypeInput], { nullable: false })
  @Column({
    nullable: false,
    name: 'procedure_type',
    type: 'jsonb',
  })
  procedureType: ProcedureTypeInput[];

  @Field({ nullable: true })
  @Column({ name: 'sub_bill_ref', nullable: true })
  subBillRef: string;

  @Field({ nullable: true })
  @Column({ name: 'requested_by', nullable: true })
  requestedBy: string;

  @Field({ nullable: true })
  @Column({ name: 'operated_by', nullable: true })
  operatedBy: string;

  @Field({ nullable: true })
  @Column({ name: 'specialty', nullable: true })
  specialty: string;

  @Field({ nullable: true })
  @Column({ name: 'rank', nullable: true })
  rank: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'department' })
  department: string;

  @Field({ nullable: true })
  @Column({ name: 'facility_name', nullable: true })
  facilityName: string;

  @Field({ nullable: true })
  @Column({ name: 'facility_address', nullable: true })
  facilityAddress: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'patient_consent' })
  patientConsent: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'patient_consent_signature', nullable: true, type: 'text' })
  patientConsentSignature: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'patient_consent_signature_type',
    nullable: true,
    type: 'text',
  })
  patientConsentSignatureType: string;

  @Field(() => Date, { nullable: true })
  @Column({
    name: 'patient_consent_signature_date_time',
    nullable: true,
    type: 'timestamptz',
  })
  patientConsentSignatureDateTime: Date;

  @Field(() => [ServiceDetailInput], { nullable: true })
  @Column({
    nullable: true,
    name: 'service_details',
    type: 'jsonb',
  })
  serviceDetails: ServiceDetailInput[];

  @Field(() => Boolean)
  @Column({
    name: 'is_package',
    type: 'boolean',
    nullable: false,
    default: false,
  })
  isPackage: boolean;

  @Field({ nullable: true })
  @Column({ name: 'reason', nullable: true })
  reason: string;

  @Field(() => OrganisationAppointmentModel, { nullable: true })
  @OneToOne(
    () => OrganisationAppointmentModel,
    (appointment) => appointment.requestProcedure,
    {
      onDelete: 'SET NULL',
    },
  )
  @JoinColumn({ name: 'appointment_id' })
  appointment?: OrganisationAppointmentModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'appointment_id', nullable: true })
  appointmentId: string;

  @Field({ nullable: true })
  @Column({ name: 'surgery_start_date', nullable: true })
  surgeryStartDate: Date;

  @Field({ nullable: true })
  @Column({ name: 'surgery_end_date', nullable: true })
  surgeryEndDate: Date;

  @Field(() => [String], { nullable: true })
  @Column({ name: 'document_url', nullable: true, type: 'text', array: true })
  documentUrl: string[];

  @ManyToOne(() => BillModel, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'bill' })
  bill?: BillModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'bill', nullable: true })
  billId: string;

  @Field(() => HospitalModel, { nullable: true })
  @ManyToOne(() => HospitalModel, (hospital) => hospital.requestProcedure)
  @JoinColumn({ name: 'hospital' })
  hospital: HospitalModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'hospital', nullable: true })
  hospitalId: string;

  @Column({ name: 'archived', default: false })
  archived: boolean;

  @Field(() => String, { nullable: true, defaultValue: 'Pending' })
  @Column({
    nullable: false,
    name: 'bill_status',
    type: 'text',
    default: 'Pending',
  })
  billStatus?: string;

  @OneToMany(
    () => PreauthorizationDetailsModel,
    (preauthDetails) => preauthDetails.requestProcedure,
    { nullable: true, onDelete: 'RESTRICT' },
  )
  preauthorizationDetails?: PreauthorizationDetailsModel;

  @ManyToOne(() => HmoProviderModel, { nullable: true })
  @JoinColumn({ name: 'hmo_provider_id' })
  hmoProvider?: HmoProviderModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'hmo_provider_id', nullable: true })
  hmoProviderId?: string;

  @Column({ name: 'hmo_claim_id', nullable: true })
  hmoClaimId?: string;

  @ManyToOne(() => HmoClaimModel, (hmoClaim) => hmoClaim.requestProcedures, {
    nullable: true,
  })
  @JoinColumn({ name: 'hmo_claim_id' })
  hmoClaim?: HmoClaimModel;

  constructor(requestProcedure: Partial<RequestProcedureModel>) {
    super();
    Object.assign(this, requestProcedure);
  }
}
