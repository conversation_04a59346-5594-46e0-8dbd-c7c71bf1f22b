import { Logger, Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RequestProcedureModel } from './models/request-procedures.model';
import { CustomRequestProcedureRepoMethods } from './repositories/request-procedure.repository';
import { RequestProcedureResolver } from './resolver/request-procedures.resolver';
import { RequestProcedureService } from './services/request-procedures.service';
import { AuthorizationModule } from '@clinify/authorization/authorization.module';
import { BillModule } from '@clinify/bills/bill.module';
import { extendModel } from '@clinify/database/extendModel';
import { HmoClaimModule } from '@clinify/hmo-claims/hmo-claims.module';
import { NotificationsModel } from '@clinify/notifications/models/notifications.model';
import { NotificationsService } from '@clinify/notifications/services/notifications.service';
import { PreauthorizationDetailsModule } from '@clinify/preauthorization-details/preauthorization-details.module';
import { ProfileModel } from '@clinify/users/models/profile.model';
import PubSub, { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';

@Module({
  imports: [
    forwardRef(() => BillModule),
    TypeOrmModule.forFeature([
      ProfileModel,
      RequestProcedureModel,
      NotificationsModel,
    ]),
    AuthorizationModule,
    PreauthorizationDetailsModule,
    forwardRef(() => HmoClaimModule),
  ],
  providers: [
    extendModel(RequestProcedureModel, CustomRequestProcedureRepoMethods),
    RequestProcedureService,
    RequestProcedureResolver,
    NotificationsService,
    Logger,
    NotificationsService,
    { provide: PUB_SUB, useFactory: () => PubSub },
  ],
  exports: [RequestProcedureService],
})
export class RequestProcedureModule {}
