import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { v4 as generateUUID } from 'uuid';
import { RequestProcedureFilterInput } from '../inputs/request-procedure-filter.input';
import {
  NewRequestProcedureInput,
  RequestProcedureInput,
} from '../inputs/request-procedure.input';
import { RequestProcedureModel } from '../models/request-procedures.model';
import { IRequestProcedureRepository } from '../repositories/request-procedure.repository';
import { RequestProcedureResponse } from '../responses/request-procedures.response';
import { BillService } from '@clinify/bills/services/bill.service';
import {
  customDSSerializeInTransaction,
  inTransaction,
} from '@clinify/database';
import { NewHmoClaimInput } from '@clinify/hmo-claims/inputs/hmo-claims.input';
import { HmoClaimService } from '@clinify/hmo-claims/services/hmo-claim.service';
import { ServiceType } from '@clinify/shared/enums/bill';
import { UserType } from '@clinify/shared/enums/users';
import { validateCreation } from '@clinify/shared/validators/validate-record-mutation.validator';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { IProfileRepository } from '@clinify/users/repositories/profile.repository';
import {
  getBillableStatusOf,
  handleSubBillOnCreate,
  handleSubForMultipleBill,
} from '@clinify/utils/helpers/billing.util';

@Injectable()
export class RequestProcedureService {
  constructor(
    @InjectRepository(RequestProcedureModel)
    public repository: IRequestProcedureRepository,
    @InjectRepository(ProfileModel)
    private profileRepository: IProfileRepository,
    private billService: BillService,
    private readonly entityManager: EntityManager,
    private dataSource: DataSource,
    @Inject(forwardRef(() => HmoClaimService))
    private hmoClaimService: HmoClaimService,
  ) {}

  getAllRequestProcedures(
    mutator: ProfileModel,
    profileId: string,
    filter: Partial<RequestProcedureFilterInput>,
  ): Promise<RequestProcedureResponse> {
    return this.repository.findByProfile(mutator, profileId, filter);
  }

  async saveRequestProcedure(
    mutator: ProfileModel,
    input: NewRequestProcedureInput,
  ): Promise<RequestProcedureModel> {
    const profile = await this.profileRepository.findOne({
      where: {
        clinifyId: input?.clinifyId,
      },
    });

    validateCreation(profile, input);

    const newRequest = new RequestProcedureModel({
      ...input,
      profile,
      createdBy: mutator,
      creatorName: mutator.fullName,
      hospital: mutator.hospital,
      patientConsentSignatureDateTime: input.patientConsentSignature
        ? new Date()
        : undefined,
    } as any);

    return customDSSerializeInTransaction(this.dataSource, async (manager) => {
      const requestTrxnRepo = manager.withRepository(this.repository);
      const claimInput: NewHmoClaimInput | null = input.hmoClaim;
      delete input.hmoClaim;

      const subBillServiceDetail = [];
      const serviceDetails = input.serviceDetails?.map((detail) => {
        if (detail.itemId) return detail;

        const detailToUse = {
          ...detail,
          reference: detail.reference || generateUUID(),
        };

        subBillServiceDetail.push(detailToUse);
        return detailToUse;
      });

      const savedRequest = await requestTrxnRepo.save({
        ...newRequest,
        serviceDetails,
        procedureType: newRequest.procedureType.map((detail) => ({
          ...detail,
          ref: generateUUID(),
        })),
        subBillRef: subBillServiceDetail?.length ? generateUUID() : null,
      });

      if (mutator.type === UserType.Patient) return savedRequest;

      if (claimInput) {
        savedRequest.hmoClaim =
          await this.hmoClaimService.createHmoClaimInTransaction(
            manager,
            mutator,
            {
              ...claimInput,
              clinifyId: savedRequest.profile.clinifyId,
              autoGenerated: true,
            },
            { requestProcedureId: savedRequest.id },
          );
        await manager
          .withRepository(this.repository)
          .update(savedRequest.id, { hmoClaimId: savedRequest.hmoClaim.id });
      }

      let billDetails: any[] = [...savedRequest.procedureType]
        .map((details, idx) => {
          const selectedServiceDetail = [...savedRequest.serviceDetails]?.find(
            (singleService) => singleService?.itemId === details.itemId,
          );
          if (!selectedServiceDetail) {
            return null;
          }
          const {
            pricePerUnit,
            quantity,
            type,
            name,
            patientType,
            paymentType,
          } = selectedServiceDetail;
          const billName = `${ServiceType.RequestProcedure} - ${
            details.type
          } -- ${
            [...savedRequest.procedureType]
              .slice(0, idx + 1)
              .filter((req) => req?.type === details.type).length
          }`;

          return {
            reference: details.ref,
            billName,
            serviceType: type,
            serviceName: name,
            price: pricePerUnit,
            quantity,
            patientType,
            paymentType,
            subBillsInputs: null,
          };
        })
        .filter((detail) => !!detail);

      billDetails = handleSubBillOnCreate(
        savedRequest.subBillRef,
        ServiceType.RequestProcedure,
        billDetails,
        subBillServiceDetail,
      );

      const generatedBills = await this.billService.generateMultipleBill(
        mutator,
        profile,
        savedRequest,
        ServiceType.RequestProcedure,
        billDetails,
        manager,
        undefined,
      );

      const billStatus = getBillableStatusOf(generatedBills.billStatus);

      await manager
        .createQueryBuilder()
        .update(RequestProcedureModel)
        .set({
          bill: generatedBills,
          billStatus,
          updatedDate: () => 'updated_date',
        })
        .where('id = :id', { id: savedRequest.id })
        .execute();

      return { ...savedRequest, bill: generatedBills, billStatus };
    });
  }

  getOneRequestProcedure(
    mutator: ProfileModel,
    requestId: string,
  ): Promise<RequestProcedureModel> {
    return this.repository.getOneRequestProcedure(mutator, requestId);
  }

  async updateRequestProcedure(
    mutator: ProfileModel,
    input: RequestProcedureInput,
    onlyServiceDetail?: boolean,
  ): Promise<RequestProcedureModel> {
    return customDSSerializeInTransaction(this.dataSource, async (manager) => {
      const requestTrxnRepo = manager.withRepository(this.repository);

      const serviceDetails = input.serviceDetails?.map((detail) => {
        if (detail.itemId) return detail;

        return {
          ...detail,
          reference: detail.reference || generateUUID(),
        };
      });

      const newlyAddedRefs: Record<string, any> = {};

      const [updatedRequest, oldRequest] =
        await requestTrxnRepo.updateRequestProcedure(
          mutator,
          {
            ...input,
            serviceDetails,
            ...(!onlyServiceDetail
              ? {
                  procedureType: input.procedureType.map((detail) => {
                    const generatedUUID = generateUUID();
                    !detail.ref
                      ? (newlyAddedRefs[detail?.itemId] = generatedUUID)
                      : null;
                    return {
                      ...detail,
                      ...(!detail.ref ? { ref: generatedUUID } : {}),
                    };
                  }),
                }
              : {}),
          },
          onlyServiceDetail,
        );

      if (mutator.type === UserType.Patient) return updatedRequest;

      const billRefsToDelete = [...(input?.billRefsToDelete || [])];

      const billDetails: any[] = input?.billInfo?.map((info) => ({
        ...info,
        serviceType: info.type,
        serviceName: info.name,
        recordServiceName: info.recordName,
        ...(newlyAddedRefs[info?.itemId]
          ? { ref: newlyAddedRefs[info?.itemId] }
          : {}),
      }));

      handleSubForMultipleBill(
        updatedRequest,
        oldRequest,
        ServiceType.RequestProcedure,
        billRefsToDelete,
        billDetails,
      );

      const updatedBill = await this.billService.updateMultipleBill(
        mutator,
        updatedRequest.profile,
        updatedRequest,
        ServiceType.RequestProcedure,
        updatedRequest.billId,
        billDetails,
        billRefsToDelete,
        manager,
        true,
      );

      return {
        ...updatedRequest,
        updatedBy: mutator,
        ...(updatedBill
          ? {
              bill: updatedBill,
              billStatus: getBillableStatusOf(updatedBill.billStatus),
            }
          : {}),
      };
    });
  }

  async deleteRequestProcedures(
    mutator: ProfileModel,
    requestIds: string[],
  ): Promise<RequestProcedureModel[]> {
    return inTransaction(this.entityManager, async (manager) => {
      const repo = manager.withRepository(this.repository);
      const deletedRequests = await repo.deleteRequestProcedures(
        mutator,
        requestIds,
      );
      const billItemsRefsToDelete: string[] = [];
      const claimsToDelete: string[] = [];
      deletedRequests.forEach((request) => {
        if (request?.subBillRef) {
          billItemsRefsToDelete.push(request.subBillRef);
        }
        billItemsRefsToDelete.push(
          ...[...request.procedureType].map(({ ref }) => ref),
        );
        if (request.hmoClaim) {
          claimsToDelete.push(request.hmoClaim.id);
        }
      });
      if (claimsToDelete.length) {
        claimsToDelete.map((claimId) =>
          this.hmoClaimService.flagHmoClaimDeletedInTransaction(
            manager,
            mutator,
            claimId,
          ),
        );
      }
      await this.billService.deleteAutoGeneratedBillItems(
        mutator,
        RequestProcedureModel,
        billItemsRefsToDelete,
        manager,
        ServiceType.RequestProcedure,
      );
      return deletedRequests;
    });
  }

  async archiveRequestProcedures(
    profile: ProfileModel,
    requestIds: string[],
    archive: boolean,
  ): Promise<RequestProcedureModel[]> {
    const requests = await this.repository.archiveRequestProcedures(
      profile,
      requestIds,
      archive,
    );
    return requests;
  }
}
