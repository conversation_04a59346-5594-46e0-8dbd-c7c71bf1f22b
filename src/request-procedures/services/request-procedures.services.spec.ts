import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { RequestProcedureService } from './request-procedures.service';
import { RequestProcedureModel } from '../models/request-procedures.model';
import { billFactory } from '@clinify/__mocks__/factories/bill.factory';
import { hmoClaimFactory } from '@clinify/__mocks__/factories/hmo-claim.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { requestProcedureFactory } from '@clinify/__mocks__/factories/request-procedure.factory';
import { MockHmoClaimService } from '@clinify/__mocks__/hmo-claim.mock';
import { BillService } from '@clinify/bills/services/bill.service';
import { HmoClaimService } from '@clinify/hmo-claims/services/hmo-claim.service';
import { ServiceType } from '@clinify/shared/enums/bill';
import { UserType } from '@clinify/shared/enums/users';
import { ProfileModel } from '@clinify/users/models/profile.model';

const procedureData = requestProcedureFactory.build();
const mockBill = billFactory.build();
const profile = profileFactory.build();

const mockedUuid = '86e7623d-031c-4ebd-b606-70584833577b';

jest.mock('uuid', () => ({
  v4: () => mockedUuid,
}));

const mockRequestProcedureRepository = {
  findByProfile: jest.fn(),
  getOneRequestProcedure: jest.fn(),
  archiveRequestProcedures: jest.fn(),
};

const mockProfileRepository = {
  findOne: jest.fn().mockResolvedValue(profile),
};

const surgeryManagerSave = jest.fn(() => procedureData);

const mockProcedureDelete = jest.fn(() => [
  {
    ...procedureData,
    serviceDetails: [
      {
        hmoClaimId: '3',
        itemId: '1',
      },
      {
        hmoClaimId: '1',
        itemId: '2',
      },
    ],
  },
]);

const commonManagerMethods = {
  save: surgeryManagerSave,
  update: jest.fn(),
  deleteRequestProcedures: mockProcedureDelete,
  updateRequestProcedure: jest.fn(() => [
    {
      ...procedureData,
      requestType: ServiceType.RequestProcedure,
      bill: {
        ...mockBill,
        details: [
          {
            ...mockBill?.details[0],
            amountPaid: 0,
            serviceType: ServiceType.RequestProcedure,
            subServiceType: `${procedureData.procedureType[0].type} -- 1`,
          },
        ],
      },
    },
    procedureData,
  ]),
};

const entityManagerMock = {
  getCustomRepository: jest.fn().mockReturnValue({
    save: surgeryManagerSave,
    deleteRequestProcedures: mockProcedureDelete,
  }),
  createQueryBuilder: jest.fn(() => ({
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    execute: jest.fn(),
  })),
  queryRunner: { isTransactionActive: true },
  withRepository: jest.fn().mockReturnValue(commonManagerMethods),
};

const mockBillService = {
  deleteAutoGeneratedBillItems: jest.fn(),
  generateMultipleBill: jest.fn(() => mockBill),
};

describe('RequestProcedureService', () => {
  let service: RequestProcedureService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [],
      providers: [
        RequestProcedureService,
        {
          provide: getRepositoryToken(RequestProcedureModel),
          useValue: mockRequestProcedureRepository,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: mockProfileRepository,
        },
        {
          provide: BillService,
          useValue: mockBillService,
        },
        {
          provide: DataSource,
          useValue: {
            manager: entityManagerMock,
            transaction: jest.fn((cb) => cb(entityManagerMock)),
          },
        },
        {
          provide: EntityManager,
          useValue: entityManagerMock,
        },
        {
          provide: HmoClaimService,
          useValue: MockHmoClaimService,
        },
      ],
    }).compile();

    service = module.get<RequestProcedureService>(RequestProcedureService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(service).toBeTruthy();
  });

  it('getAllRequestProcedures(): should call the findByProfile repository method', async () => {
    const mutator = profileFactory.build();
    await service.getAllRequestProcedures(mutator, 'profile-id', {});

    expect(mockRequestProcedureRepository.findByProfile).toHaveBeenCalledWith(
      mutator,
      'profile-id',
      {},
    );
  });

  it('getOneRequestProcedure(): should call the getOneRequestProcedure repository method', async () => {
    const mutator = profileFactory.build();

    await service.getOneRequestProcedure(mutator, 'request-id');

    expect(
      mockRequestProcedureRepository.getOneRequestProcedure,
    ).toHaveBeenCalledWith(mutator, 'request-id');
  });

  it('archiveRequestProcedures(): should call the archiveRequestProcedures repository method', async () => {
    const mutator = profileFactory.build();

    await service.archiveRequestProcedures(mutator, ['request-id'], false);

    expect(
      mockRequestProcedureRepository.archiveRequestProcedures,
    ).toHaveBeenCalledWith(mutator, ['request-id'], false);

    await service.archiveRequestProcedures(mutator, ['request-id'], true);

    expect(
      mockRequestProcedureRepository.archiveRequestProcedures,
    ).toHaveBeenLastCalledWith(mutator, ['request-id'], true);
  });

  it('saveRequestProcedure(): should save procedure', async () => {
    const mutator = profileFactory.build();
    const input = requestProcedureFactory.build({
      clinifyId: 'clinify-id',
      serviceDetails: [
        {
          type: 'service-type-1',
          name: 'service-name-1',
          quantity: '1',
          pricePerUnit: '14000',
          itemId: 'dasw223ew',
        },
        {
          type: 'service-type-2',
          name: 'service-name-2',
          quantity: '2',
          pricePerUnit: '1100',
          itemId: '',
        },
        {
          type: 'service-type-3',
          name: 'service-name-3',
          quantity: '4',
          pricePerUnit: '300',
          itemId: '',
          reference: 'sub-bill-reference',
        },
      ],
    });
    await service.saveRequestProcedure(mutator, input);
    expect(surgeryManagerSave).toHaveBeenCalled();
  });

  it('saveRequestProcedure(): should create bill with procedure record', async () => {
    const input = requestProcedureFactory.build({ clinifyId: 'clinify-id' });
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationDoctor;
    await service.saveRequestProcedure(profile, input);

    expect(surgeryManagerSave).toHaveBeenCalled();
    expect(mockBillService.generateMultipleBill).toHaveBeenCalled();
  });

  it('saveRequestProcedure(): should create bill with procedure record and save hmoClaim', async () => {
    const input = requestProcedureFactory.build({ clinifyId: 'clinify-id' });
    input.serviceDetails[0].itemId = 'item-id';
    const hmoClaim = hmoClaimFactory.build();
    delete hmoClaim.profile;
    hmoClaim.itemId = 'item-id';
    input.hmoClaim = hmoClaim;
    entityManagerMock.getCustomRepository().save = jest
      .fn()
      .mockReturnValue(input);
    MockHmoClaimService.createHmoClaimInTransaction.mockReturnValue(hmoClaim);
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationDoctor;
    await service.saveRequestProcedure(profile, input);
    expect(MockHmoClaimService.createHmoClaimInTransaction).toHaveBeenCalled();
  });

  it('updateRequestProcedure(): should update procedure', async () => {
    const mutator = profileFactory.build();
    const input = requestProcedureFactory.build({
      serviceDetails: [
        {
          type: 'service-type-1',
          name: 'service-name-1',
          quantity: '1',
          pricePerUnit: '14000',
          itemId: 'dasw223ew',
        },
        {
          type: 'service-type-2',
          name: 'service-name-2',
          quantity: '2',
          pricePerUnit: '1100',
          itemId: '',
        },
        {
          type: 'service-type-3',
          name: 'service-name-3',
          quantity: '4',
          pricePerUnit: '300',
          itemId: '',
          reference: 'sub-bill-reference',
        },
      ],
    });
    delete input.profile;

    await service.updateRequestProcedure(mutator, input);

    input.serviceDetails = [
      {
        type: 'service-type-1',
        name: 'service-name-1',
        quantity: '1',
        pricePerUnit: '14000',
        itemId: 'dasw223ew',
      },
      {
        type: 'service-type-2',
        name: 'service-name-2',
        quantity: '2',
        pricePerUnit: '1100',
        itemId: '',
        reference: mockedUuid,
      },
      {
        type: 'service-type-3',
        name: 'service-name-3',
        quantity: '4',
        pricePerUnit: '300',
        itemId: '',
        reference: 'sub-bill-reference',
      },
    ];
    expect(
      entityManagerMock.withRepository().updateRequestProcedure,
    ).toHaveBeenCalledWith(mutator, input, undefined);
  });

  it('updateRequestProcedure(): should update procedure', async () => {
    const mutator = profileFactory.build();
    const input = requestProcedureFactory.build({
      serviceDetails: [
        {
          type: 'service-type-1',
          name: 'service-name-1',
          quantity: '1',
          pricePerUnit: '14000',
          itemId: 'dasw223ew',
        },
        {
          type: 'service-type-2',
          name: 'service-name-2',
          quantity: '2',
          pricePerUnit: '1100',
          itemId: '',
        },
        {
          type: 'service-type-3',
          name: 'service-name-3',
          quantity: '4',
          pricePerUnit: '300',
          itemId: '',
          reference: 'sub-bill-reference',
        },
      ],
    });
    delete input.profile;

    await service.updateRequestProcedure(mutator, input, true);

    input.serviceDetails = [
      {
        type: 'service-type-1',
        name: 'service-name-1',
        quantity: '1',
        pricePerUnit: '14000',
        itemId: 'dasw223ew',
      },
      {
        type: 'service-type-2',
        name: 'service-name-2',
        quantity: '2',
        pricePerUnit: '1100',
        itemId: '',
        reference: mockedUuid,
      },
      {
        type: 'service-type-3',
        name: 'service-name-3',
        quantity: '4',
        pricePerUnit: '300',
        itemId: '',
        reference: 'sub-bill-reference',
      },
    ];
    expect(
      entityManagerMock.withRepository().updateRequestProcedure,
    ).toHaveBeenCalledWith(mutator, input, true);
  });

  it('updateRequestProcedure(): should update procedure', async () => {
    const mutator = profileFactory.build();
    const data = requestProcedureFactory.build();
    let input = requestProcedureFactory.build();
    input = {
      ...input,
      procedureType: [{ type: data.procedureType[0].type }],
      serviceDetails: [
        {
          type: 'service-type-1',
          name: 'service-name-1',
          quantity: '1',
          pricePerUnit: '14000',
          itemId: 'dasw223ew',
        },
        {
          type: 'service-type-2',
          name: 'service-name-2',
          quantity: '2',
          pricePerUnit: '1100',
          itemId: '',
        },
        {
          type: 'service-type-3',
          name: 'service-name-3',
          quantity: '4',
          pricePerUnit: '300',
          itemId: '',
          reference: 'sub-bill-reference',
        },
      ],
    };
    delete input.profile;

    entityManagerMock.withRepository().updateRequestProcedure = jest.fn(() => [
      {
        ...data,
        procedureType: [{ type: data.procedureType[0].type, ref: mockedUuid }],
        bill: {
          ...mockBill,
          requestType: ServiceType.RequestProcedure,
          details: [
            {
              ...mockBill?.details[0],
              reference: mockedUuid,
              amountPaid: 0,
              serviceType: ServiceType.RequestProcedure,
              subServiceType: `${data.procedureType[0].type} -- 1`,
            },
          ],
        },
      },
      data,
    ]);

    await service.updateRequestProcedure(mutator, input);
    expect(
      entityManagerMock.withRepository().updateRequestProcedure,
    ).toHaveBeenCalledWith(
      mutator,
      {
        ...input,
        procedureType: [
          expect.objectContaining({ type: data.procedureType[0].type }),
        ],
        serviceDetails: [
          {
            type: 'service-type-1',
            name: 'service-name-1',
            quantity: '1',
            pricePerUnit: '14000',
            itemId: 'dasw223ew',
          },
          {
            type: 'service-type-2',
            name: 'service-name-2',
            quantity: '2',
            pricePerUnit: '1100',
            itemId: '',
            reference: mockedUuid,
          },
          {
            type: 'service-type-3',
            name: 'service-name-3',
            quantity: '4',
            pricePerUnit: '300',
            itemId: '',
            reference: 'sub-bill-reference',
          },
        ],
      },
      undefined,
    );
  });

  it('updateRequestProcedure(): resolver for patient', async () => {
    const mutator = profileFactory.build();
    const data = requestProcedureFactory.build();
    entityManagerMock.withRepository().updateRequestProcedure = jest.fn(() => [
      {
        ...data,
      },
      data,
    ]);
    const input = requestProcedureFactory.build();
    const res = await service.updateRequestProcedure(
      { ...mutator, type: 'Patient' },
      input,
    );
    expect(res).toEqual(data);
  });

  it('deleteRequestProcedures(): should delete procedure', async () => {
    const mutator = profileFactory.build();
    const input = requestProcedureFactory.build();
    delete input.profile;
    await service.deleteRequestProcedures(mutator, [input.id]);
    expect(mockProcedureDelete).toHaveBeenCalledWith(mutator, [input.id]);
  });

  it('deleteRequestProcedures(): should delete procedure and flad hmoClaim', async () => {
    const mutator = profileFactory.build();
    const data = requestProcedureFactory.build();
    entityManagerMock.getCustomRepository().deleteRequestProcedures = jest.fn(
      () => [{ ...data, hmoClaim: hmoClaimFactory.build() }],
    );
    const input = requestProcedureFactory.build();
    delete input.profile;

    await service.deleteRequestProcedures(mutator, [input.id]);

    expect(
      MockHmoClaimService.flagHmoClaimDeletedInTransaction,
    ).toHaveBeenCalled();
  });
});
