import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { RequestProcedureResolver } from './request-procedures.resolver';
import { RequestProcedureService } from '../services/request-procedures.service';
import {
  billDetailsFactory,
  billFactory,
} from '@clinify/__mocks__/factories/bill.factory';
import { hmoClaimFactory } from '@clinify/__mocks__/factories/hmo-claim.factory';
import { hospitalFactory } from '@clinify/__mocks__/factories/hospital.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { requestProcedureFactory } from '@clinify/__mocks__/factories/request-procedure.factory';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { PermissionService } from '@clinify/authorization/services/permission.service';
import { HmoClaimService } from '@clinify/hmo-claims/services/hmo-claim.service';
import { NotificationsService } from '@clinify/notifications/services/notifications.service';
import { PreauthorizationDetailsModel } from '@clinify/preauthorization-details/models/preauthorization-details.model';
import { PreauthorizationDetailsService } from '@clinify/preauthorization-details/services/preauthorization-details.service';
import { ProfileModel } from '@clinify/users/models/profile.model';

const handleNoticationEventAction = jest.fn();

const mockRequestProcedureService = {
  getOneRequestProcedure: jest.fn(),
  saveRequestProcedure: jest.fn(),
  updateRequestProcedure: jest.fn(),
  deleteRequestProcedures: jest.fn(),
  archiveRequestProcedures: jest.fn(),
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

const dsMock = {};

const mockPreauthorizationDetailsModel = {
  find: jest.fn(),
};

const mockHmoClaimService = {
  getHmoClaim: jest.fn(),
};

describe('RequestProcedureResolver', () => {
  let resolver: RequestProcedureResolver;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RequestProcedureResolver,
        RequestProcedureService,
        PermissionService,
        PreauthorizationDetailsService,
        {
          provide: RequestProcedureService,
          useValue: mockRequestProcedureService,
        },
        {
          provide: getRepositoryToken(PreauthorizationDetailsModel),
          useValue: mockPreauthorizationDetailsModel,
        },
        {
          provide: getRepositoryToken(PermissionModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: {},
        },
        {
          provide: DataSource,
          useValue: dsMock,
        },
        {
          provide: EntityManager,
          useValue: {},
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
        {
          provide: NotificationsService,
          useValue: {
            handleNoticationEvent: handleNoticationEventAction,
          },
        },
        {
          provide: HmoClaimService,
          useValue: mockHmoClaimService,
        },
      ],
    }).compile();

    resolver = module.get<RequestProcedureResolver>(RequestProcedureResolver);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('requestProcedure(): should call getOneRequestProcedure service method', async () => {
    const mutator: ProfileModel = profileFactory.build();

    await resolver.requestProcedure(mutator, 'request-id', 'clinify-id');

    expect(
      mockRequestProcedureService.getOneRequestProcedure,
    ).toHaveBeenCalledWith(mutator, 'request-id');
  });

  it('addRequestProcedure(): should save procedure', async () => {
    const mutator = profileFactory.build();
    const input = requestProcedureFactory.build();
    delete input.profile;

    const response = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
    };

    mockRequestProcedureService.saveRequestProcedure = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.addRequestProcedure(mutator, input);
    expect(
      mockRequestProcedureService.saveRequestProcedure,
    ).toHaveBeenCalledWith(mutator, input);
    expect(handleNoticationEventAction).toHaveBeenLastCalledWith({
      profile: mutator,
      details: {
        modelName: 'Request Procedure',
        action: 'Requested',
        item: response,
      },
    });
    expect(pubSubMock.publish).toHaveBeenCalledTimes(2);
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(1, 'ConsultationEvent', {
      consultation: response,
      ConsultationEvent: mutator.id,
    });
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(
      2,
      'RequestProcedureAdded',
      {
        RequestProcedureAdded: response,
      },
    );
  });

  it('addRequestProcedure(): should save procedure with provided ID', async () => {
    const mutator = profileFactory.build();
    const input = requestProcedureFactory.build();
    const recordId = 'request-id';
    delete input.profile;

    const response = {
      ...input,
      createdBy: mutator,
      creatorId: mutator.id,
    };

    mockRequestProcedureService.saveRequestProcedure = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.addRequestProcedure(mutator, input, recordId);
    expect(
      mockRequestProcedureService.saveRequestProcedure,
    ).toHaveBeenCalledWith(mutator, { ...input, id: recordId });
    expect(handleNoticationEventAction).toHaveBeenLastCalledWith({
      profile: mutator,
      details: {
        modelName: 'Request Procedure',
        action: 'Requested',
        item: response,
      },
    });
    expect(pubSubMock.publish).toHaveBeenCalledTimes(2);
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(1, 'ConsultationEvent', {
      consultation: response,
      ConsultationEvent: mutator.id,
    });
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(
      2,
      'RequestProcedureAdded',
      {
        RequestProcedureAdded: response,
      },
    );
  });

  it('addRequestProcedure(): should save procedure with bill', async () => {
    const mutator = profileFactory.build();
    const bill = billFactory.build();
    const input = requestProcedureFactory.build();
    delete input.profile;

    const response = {
      ...input,
      bill,
      createdBy: mutator,
      creatorId: mutator.id,
    };

    mockRequestProcedureService.saveRequestProcedure = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.addRequestProcedure(mutator, input);
    expect(
      mockRequestProcedureService.saveRequestProcedure,
    ).toHaveBeenCalledWith(mutator, input);
    expect(handleNoticationEventAction).toHaveBeenLastCalledWith({
      profile: mutator,
      details: {
        modelName: 'Request Procedure',
        action: 'Requested',
        item: response,
      },
    });
    expect(pubSubMock.publish).toHaveBeenCalledTimes(4);
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(1, 'ConsultationEvent', {
      consultation: response,
      ConsultationEvent: mutator.id,
    });
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(
      2,
      'RequestProcedureAdded',
      {
        RequestProcedureAdded: response,
      },
    );
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(3, 'OrgBillAdded', {
      OrgBillAdded: bill,
    });
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(4, 'BillingEvent', {
      billing: response,
      BillingEvent: 'Added',
    });
  });

  it('updateRequestProcedure(): should update procedure', async () => {
    const mutator = profileFactory.build();
    const input = requestProcedureFactory.build();
    delete input.profile;
    const requestId = 'request-id';

    const response = {
      ...input,
      id: requestId,
      createdBy: mutator,
      updatedBy: mutator,
    };
    mockRequestProcedureService.updateRequestProcedure = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.updateRequestProcedure(mutator, input, requestId);
    expect(
      mockRequestProcedureService.updateRequestProcedure,
    ).toHaveBeenCalledWith(mutator, {
      ...input,
      id: requestId,
    });
    expect(handleNoticationEventAction).toHaveBeenLastCalledWith({
      profile: mutator,
      details: {
        modelName: 'Request Procedure',
        action: 'Updated',
        item: response,
      },
    });
    expect(pubSubMock.publish).toHaveBeenCalledTimes(2);
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(1, 'ConsultationEvent', {
      consultation: response,
      ConsultationEvent: 'Updated',
    });
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(
      2,
      'RequestProcedureUpdated',
      {
        RequestProcedureUpdated: response,
      },
    );
  });

  it('updateRequestProcedure(): should update procedure with bill', async () => {
    const mutator = profileFactory.build();
    const bill = billFactory.build();
    const input = requestProcedureFactory.build();
    delete input.profile;
    const requestId = 'request-id';

    const response = {
      ...input,
      id: requestId,
      bill,
      createdBy: mutator,
      updatedBy: mutator,
    };
    mockRequestProcedureService.updateRequestProcedure = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.updateRequestProcedure(mutator, input, requestId);
    expect(
      mockRequestProcedureService.updateRequestProcedure,
    ).toHaveBeenCalledWith(mutator, {
      ...input,
      id: requestId,
    });
    expect(handleNoticationEventAction).toHaveBeenLastCalledWith({
      profile: mutator,
      details: {
        modelName: 'Request Procedure',
        action: 'Updated',
        item: response,
      },
    });
    expect(pubSubMock.publish).toHaveBeenCalledTimes(3);
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(1, 'ConsultationEvent', {
      consultation: response,
      ConsultationEvent: 'Updated',
    });
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(
      2,
      'RequestProcedureUpdated',
      {
        RequestProcedureUpdated: response,
      },
    );
    expect(pubSubMock.publish).toBeCalledWith('OrgBillUpdated', {
      OrgBillUpdated: bill,
    });
  });

  it('updateRequestProcedureBill(): should update procedure with bill data', async () => {
    const mutator = profileFactory.build();
    const bill = billFactory.build();
    const input = {
      ...requestProcedureFactory.build(),
      clinifyId: 'clinify-id',
      billInfo: [],
      billRefsToDelete: [],
      serviceDetails: [
        {
          type: 'Service Type',
          name: 'Service Name',
          quantity: '2',
          pricePerUnit: '3000',
        },
      ],
    };
    delete input.profile;
    const requestId = 'request-id';

    const response = {
      ...input,
      bill,
      createdBy: mutator,
      updatedBy: mutator,
    };
    mockRequestProcedureService.updateRequestProcedure = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.updateRequestProcedureBill(mutator, input, requestId);
    expect(
      mockRequestProcedureService.updateRequestProcedure,
    ).toHaveBeenCalledWith(
      mutator,
      {
        id: requestId,
        clinifyId: 'clinify-id',
        billInfo: [],
        billRefsToDelete: [],
        serviceDetails: [
          {
            type: 'Service Type',
            name: 'Service Name',
            quantity: '2',
            pricePerUnit: '3000',
          },
        ],
      },
      true,
    );
    expect(handleNoticationEventAction).toHaveBeenLastCalledWith({
      profile: mutator,
      details: {
        modelName: 'Request Procedure',
        action: 'Updated',
        item: response,
      },
    });
    expect(pubSubMock.publish).toHaveBeenCalledTimes(3);
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(1, 'ConsultationEvent', {
      consultation: response,
      ConsultationEvent: 'Updated',
    });
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(
      2,
      'RequestProcedureUpdated',
      {
        RequestProcedureUpdated: response,
      },
    );
    expect(pubSubMock.publish).toBeCalledWith('OrgBillUpdated', {
      OrgBillUpdated: bill,
    });
  });

  it('deleteRequestProcedures(): should delete procedures', async () => {
    const mutator = profileFactory.build();
    const input = requestProcedureFactory.buildList(2, { profile: null });
    const ids = [input[0].id, input[1].id];

    const response = input.map((item) => ({
      ...item,
      createdBy: mutator,
    }));

    mockRequestProcedureService.deleteRequestProcedures = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.deleteRequestProcedures(mutator, ids, 'clinify-id');
    expect(
      mockRequestProcedureService.deleteRequestProcedures,
    ).toHaveBeenCalledWith(mutator, ids);
    expect(handleNoticationEventAction).toHaveBeenLastCalledWith({
      profile: mutator,
      details: {
        modelName: 'Request Procedure',
        action: 'Deleted',
        item: response,
      },
    });
    expect(pubSubMock.publish).toHaveBeenCalledTimes(2);
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(1, 'ConsultationEvent', {
      consultation: response[0],
      ConsultationEvent: 'Deleted',
    });
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(
      2,
      'RequestProcedureRemoved',
      {
        RequestProcedureRemoved: response,
      },
    );
  });

  it('deleteRequestProcedures(): should delete procedures with bill', async () => {
    const mutator = profileFactory.build();
    const input = requestProcedureFactory.buildList(2, { profile: null });
    const ids = [input[0].id, input[1].id];
    const mockedBill = billFactory.build({ details: [] });
    const anotherMockedBill = billFactory.build();

    const response = input.map((item, index) => ({
      ...item,
      createdBy: mutator,
      subBillRef: 'sub-bill-ref',
      bill: index ? anotherMockedBill : mockedBill,
    }));

    mockRequestProcedureService.deleteRequestProcedures = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.deleteRequestProcedures(mutator, ids, 'clinify-id');
    expect(
      mockRequestProcedureService.deleteRequestProcedures,
    ).toHaveBeenCalledWith(mutator, ids);
    expect(handleNoticationEventAction).toHaveBeenLastCalledWith({
      profile: mutator,
      details: {
        modelName: 'Request Procedure',
        action: 'Deleted',
        item: response,
      },
    });
    expect(pubSubMock.publish).toHaveBeenCalledTimes(4);
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(1, 'ConsultationEvent', {
      consultation: response[0],
      ConsultationEvent: 'Deleted',
    });
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(
      2,
      'RequestProcedureRemoved',
      {
        RequestProcedureRemoved: response,
      },
    );
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(
      3,
      'OrgBillRemoved',
      expect.objectContaining({}),
    );
    expect(pubSubMock.publish).toHaveBeenNthCalledWith(
      4,
      'OrgBillUpdated',
      expect.objectContaining({}),
    );
  });

  it('archiveRequestProcedures(): should archive procedure', async () => {
    const mutator = profileFactory.build();
    const input = requestProcedureFactory.build();
    delete input.profile;

    const response = {
      ...input,
      createdBy: mutator,
      updatedBy: mutator,
    };

    mockRequestProcedureService.archiveRequestProcedures = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.archiveRequestProcedures(
      mutator,
      [input.id],
      true,
      'clinify-id',
    );
    expect(
      mockRequestProcedureService.archiveRequestProcedures,
    ).toHaveBeenCalledWith(mutator, [input.id], true);
    expect(pubSubMock.publish).toHaveBeenCalledTimes(1);
    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'RequestProcedureArchived',
      {
        RequestProcedureArchived: response,
      },
    );
  });

  it('archiveRequestProcedures(): should unarchive procedure', async () => {
    const mutator = profileFactory.build();
    const input = requestProcedureFactory.build();
    delete input.profile;

    const response = {
      ...input,
      createdBy: mutator,
      updatedBy: mutator,
    };

    mockRequestProcedureService.archiveRequestProcedures = jest
      .fn()
      .mockResolvedValue(response);

    await resolver.archiveRequestProcedures(
      mutator,
      [input.id],
      false,
      'clinify-id',
    );
    expect(
      mockRequestProcedureService.archiveRequestProcedures,
    ).toHaveBeenCalledWith(mutator, [input.id], false);
    expect(pubSubMock.publish).toHaveBeenCalledTimes(1);
    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'RequestProcedureUnarchived',
      {
        RequestProcedureUnarchived: response,
      },
    );
  });

  it('addRequestProcedureSubsHandler(): should call pubsub asyncIterator with RequestProcedureAdded', () => {
    resolver.addRequestProcedureSubsHandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'RequestProcedureAdded',
    );
  });

  it('updateRequestProcedureSubsHandler(): should call pubsub asyncIterator with RequestProcedureUpdated', () => {
    resolver.updateRequestProcedureSubsHandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'RequestProcedureUpdated',
    );
  });

  it('removeRequestProcedureSubsHandler(): should call pubsub asyncIterator with RequestProcedureRemoved', () => {
    resolver.removeRequestProcedureSubsHandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'RequestProcedureRemoved',
    );
  });

  it('archiveRequestProcedureSubsHandler(): should call pubsub asyncIterator with RequestProcedureArchived', () => {
    resolver.archiveRequestProcedureSubsHandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'RequestProcedureArchived',
    );
  });

  it('unArchiveRequestProcedureSubsHandler(): should call pubsub asyncIterator with RequestProcedureUnarchived', () => {
    resolver.unArchiveRequestProcedureSubsHandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'RequestProcedureUnarchived',
    );
  });

  it('getBill() should get procedure bills', () => {
    const mutator = profileFactory.build();
    const newHospitals = hospitalFactory.buildList(2);
    const newBill = billDetailsFactory.build();
    const data = requestProcedureFactory.build();

    const response = resolver.getBill(
      {
        ...mutator,
        hospital: newHospitals[0],
      },
      {
        ...data,
        mutator,
        hospital: newHospitals[0],
        createdBy: {
          ...data.createdBy,
          hospital: newHospitals[0],
          hospitalId: mutator.hospitalId,
        },
        bill: newBill,
      },
    );

    expect(response).toStrictEqual(newBill);
  });

  it('getBill() should get procedure bills', () => {
    const mutator = profileFactory.build();
    const newHospitals = hospitalFactory.buildList(2);
    const newBill = billDetailsFactory.build();
    const data = requestProcedureFactory.build();

    const response = resolver.getBill(
      {
        ...mutator,
        hospitalId: newHospitals[0].id,
        defaultProfile: {
          ...mutator,
          hospital: newHospitals[0],
        },
      },
      {
        ...data,
        profile: profileFactory.build(),
        profileId: profileFactory.build().id,
        hospital: newHospitals[1],
        hospitalId: newHospitals[1].id,
        createdBy: {
          ...data.createdBy,
          hospital: newHospitals[1],
          hospitalId: mutator.hospitalId,
        },
        bill: newBill,
      },
    );

    expect(response).toStrictEqual(null);
  });

  it('getPreauthorizationDetails() should get preauthorization details', async () => {
    const mutator = profileFactory.build();
    const data = requestProcedureFactory.build();
    const response = {
      ...data,
      hospitalId: mutator.hospitalId,
    };
    mockPreauthorizationDetailsModel.find.mockReturnValue([response]);

    const res2 = await resolver.getPreauthorizationDetails(mutator, response);
    expect(res2[0].id).toEqual(response.id);
  });

  it('getHmoClaim() should call getRecordHmoClaim service', async () => {
    const mutator = profileFactory.build();
    const data = requestProcedureFactory.build();
    const hmoClaimData = hmoClaimFactory.build();

    mockHmoClaimService.getHmoClaim = jest.fn().mockResolvedValue(hmoClaimData);

    const response = await resolver.getHmoClaim(mutator, {
      ...data,
      hospitalId: mutator.hospitalId,
      hmoClaimId: 'id',
    });
    expect(mutator.hospitalId).toBeTruthy();
    expect(mutator.hospitalId.length).toBeGreaterThan(10);
    expect(mockHmoClaimService.getHmoClaim).toHaveBeenCalledWith(mutator, 'id');
    expect(response).toEqual(hmoClaimData);
  });

  it('getHmoClaim() should not call getRecordHmoClaim service', async () => {
    const mutator = profileFactory.build();
    const data = requestProcedureFactory.build();

    const response = await resolver.getHmoClaim(mutator, data);
    expect(mutator.hospitalId).toBeTruthy();
    expect(mutator.hospitalId.length).toBeGreaterThan(10);
    expect(mockHmoClaimService.getHmoClaim).not.toHaveBeenCalled();
    expect(response).toEqual(null);
  });
});
