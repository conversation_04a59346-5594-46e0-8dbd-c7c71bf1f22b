import { Inject, UseGuards } from '@nestjs/common';
import {
  Args,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
  Subscription,
} from '@nestjs/graphql';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import {
  NewRequestProcedureInput,
  RequestProcedureInput,
} from '../inputs/request-procedure.input';
import { RequestProcedureModel } from '../models/request-procedures.model';
import { RequestProcedureService } from '../services/request-procedures.service';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { PinAuthGuard } from '@clinify/authentication/guards/pin.guard';
import { ProfileDataAccessGuard } from '@clinify/authentication/guards/profile-data-acess.guard';
import { Permissions } from '@clinify/authorization/decorators/top-level-permissions.decorator';
import { AuthorizationGuard } from '@clinify/authorization/guards/authorization.guard';
import {
  OrganizationAction,
  PatientAction,
  Subject,
} from '@clinify/authorization/types/permission.type';
import { BillModel } from '@clinify/bills/models/bill.model';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { HmoClaimService } from '@clinify/hmo-claims/services/hmo-claim.service';
import { NotificationsService } from '@clinify/notifications/services/notifications.service';
import { PreauthorizationDetailsModel } from '@clinify/preauthorization-details/models/preauthorization-details.model';
import { PreauthorizationDetailsService } from '@clinify/preauthorization-details/services/preauthorization-details.service';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { BillableRecords } from '@clinify/shared/enums/billable-records';
import { EventType } from '@clinify/shared/enums/events';
import {
  DashboardIcon,
  NotificationTag,
} from '@clinify/shared/enums/notifications';
import { AppServices } from '@clinify/shared/enums/services';
import { resolveBillForRecordDelete } from '@clinify/shared/helper/billing';
import { ProfileModel } from '@clinify/users/models/profile.model';
import {
  filterRequestProcedureAdded,
  filterRequestProcedureArchived,
  filterRequestProcedureRemoved,
  filterRequestProcedureUnarchived,
  filterRequestProcedureUpdated,
} from '@clinify/utils/subscriptions/filters';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const {
  BillingEvent,
  OrgBillAdded,
  OrgBillUpdated,
  OrgBillRemoved,
  ConsultationEvent,
  RequestProcedureAdded,
  RequestProcedureUpdated,
  RequestProcedureRemoved,
  RequestProcedureArchived,
  RequestProcedureUnarchived,
} = SubscriptionTypes;

@LogService(AppServices.RequestProcedure)
@UseGuards(GqlAuthGuard, AuthorizationGuard)
@Resolver(() => RequestProcedureModel)
export class RequestProcedureResolver {
  constructor(
    private service: RequestProcedureService,
    private readonly preauthDetailsService: PreauthorizationDetailsService,
    private notificationService: NotificationsService,
    private readonly hmoClaimService: HmoClaimService,
    @Inject(PUB_SUB) private pubSub: RedisPubSub,
  ) {}

  @Permissions(
    { action: PatientAction.CreateOwn, subject: Subject.ConsultationProcedure },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.ConsultationProcedure,
    },
    { action: PatientAction.Manage, subject: Subject.ConsultationProcedure },
  )
  @UseGuards(PinAuthGuard, ProfileDataAccessGuard('input.clinifyId'))
  @Mutation(() => RequestProcedureModel)
  async addRequestProcedure(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => NewRequestProcedureInput,
      nullable: false,
    })
    input: NewRequestProcedureInput,
    @Args({ name: 'id', nullable: true }) id?: string,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<RequestProcedureModel> {
    const item = await this.service.saveRequestProcedure(profile, {
      ...input,
      ...(id && { id }),
    });
    const details = {
      modelName: DashboardIcon.RequestProcedure,
      action: NotificationTag.Requested,
      item,
    };
    this.notificationService.handleNoticationEvent({ profile, details });

    await this.pubSub.publish(ConsultationEvent, {
      consultation: item,
      [ConsultationEvent]: item?.creatorId,
    });
    await this.pubSub.publish(RequestProcedureAdded, {
      [RequestProcedureAdded]: item,
    });
    if (!!item.bill) {
      await this.pubSub.publish(OrgBillAdded, {
        [OrgBillAdded]: item.bill,
      });
      await this.pubSub.publish(BillingEvent, {
        billing: item,
        [BillingEvent]: EventType.ADDED,
      });
    }

    if (item.hmoClaim && !item.hmoClaim.lastModifierId) {
      this.hmoClaimService.handleAddClaimSubscription(item.hmoClaim);
    } else if (item.hmoClaim && item.hmoClaim.lastModifierId) {
      this.hmoClaimService.handleUpdateHmoClaimSubscription(item.hmoClaim);
    }

    return item;
  }

  @Subscription(() => RequestProcedureModel, {
    name: RequestProcedureAdded,
    filter: filterRequestProcedureAdded,
  })
  addRequestProcedureSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(RequestProcedureAdded);
  }

  @Permissions(
    { action: PatientAction.ReadOwn, subject: Subject.ConsultationProcedure },
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.ConsultationProcedure,
    },
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.ConsultationProcedure,
    },
    { action: PatientAction.Manage, subject: Subject.ConsultationProcedure },
  )
  @UseGuards(ProfileDataAccessGuard('clinifyId'))
  @Query(() => RequestProcedureModel)
  async requestProcedure(
    @CurrentProfile() profile: ProfileModel,
    @Args('id') requestId: string,
    @Args('clinifyId') _clinifyId: string,
  ): Promise<RequestProcedureModel> {
    return await this.service.getOneRequestProcedure(profile, requestId);
  }

  @Permissions(
    { action: PatientAction.UpdateOwn, subject: Subject.ConsultationProcedure },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationProcedure,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationProcedure,
    },
    { action: PatientAction.Manage, subject: Subject.ConsultationProcedure },
  )
  @UseGuards(PinAuthGuard, ProfileDataAccessGuard('input.clinifyId'))
  @Mutation(() => RequestProcedureModel)
  async updateRequestProcedure(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => RequestProcedureInput,
      nullable: false,
    })
    input: RequestProcedureInput,
    @Args('id') requestId: string,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<RequestProcedureModel> {
    const item = await this.service.updateRequestProcedure(profile, {
      ...input,
      id: requestId,
    } as any);
    const details = {
      modelName: DashboardIcon.RequestProcedure,
      action: NotificationTag.Updated,
      item,
    };
    this.notificationService.handleNoticationEvent({ profile, details });

    await this.pubSub.publish(ConsultationEvent, {
      consultation: item,
      [ConsultationEvent]: EventType.UPDATED,
    });
    await this.pubSub.publish(RequestProcedureUpdated, {
      [RequestProcedureUpdated]: item,
    });

    if (item.bill) {
      await this.pubSub.publish(OrgBillUpdated, {
        [OrgBillUpdated]: item.bill,
      });
    }

    return item;
  }

  @Permissions(
    { action: PatientAction.UpdateOwn, subject: Subject.ConsultationProcedure },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationProcedure,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationProcedure,
    },
    { action: OrganizationAction.UpdateTeamAny, subject: Subject.RecordBill },
  )
  @UseGuards(ProfileDataAccessGuard('input.clinifyId'))
  @Mutation(() => RequestProcedureModel)
  async updateRequestProcedureBill(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => RequestProcedureInput,
      nullable: false,
    })
    input: RequestProcedureInput,
    @Args('id') requestId: string,
  ): Promise<RequestProcedureModel> {
    const { clinifyId, billInfo, billRefsToDelete, serviceDetails } = input;
    const item = await this.service.updateRequestProcedure(
      profile,
      {
        id: requestId,
        clinifyId,
        billInfo,
        billRefsToDelete,
        serviceDetails,
      } as any,
      true,
    );
    const details = {
      modelName: DashboardIcon.RequestProcedure,
      action: NotificationTag.Updated,
      item,
    };
    this.notificationService.handleNoticationEvent({ profile, details });

    await this.pubSub.publish(ConsultationEvent, {
      consultation: item,
      [ConsultationEvent]: EventType.UPDATED,
    });
    await this.pubSub.publish(RequestProcedureUpdated, {
      [RequestProcedureUpdated]: item,
    });

    if (item.bill) {
      await this.pubSub.publish(OrgBillUpdated, {
        [OrgBillUpdated]: item.bill,
      });
    }
    return item;
  }

  @Subscription(() => RequestProcedureModel, {
    name: RequestProcedureUpdated,
    filter: filterRequestProcedureUpdated,
  })
  updateRequestProcedureSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(RequestProcedureUpdated);
  }

  @Permissions(
    { action: PatientAction.DeleteOwn, subject: Subject.ConsultationProcedure },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.ConsultationProcedure,
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.ConsultationProcedure,
    },
    { action: PatientAction.Manage, subject: Subject.ConsultationProcedure },
  )
  @UseGuards(ProfileDataAccessGuard('clinifyId'))
  @Mutation(() => [RequestProcedureModel])
  async deleteRequestProcedures(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) requestIds: string[],
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    _clinifyId: string,
  ): Promise<RequestProcedureModel[]> {
    const items = await this.service.deleteRequestProcedures(
      profile,
      requestIds,
    );

    const details = {
      modelName: DashboardIcon.Consultation,
      action: NotificationTag.Deleted,
      item: items,
    };
    this.notificationService.handleNoticationEvent({ profile, details });

    await this.pubSub.publish(ConsultationEvent, {
      consultation: items[0],
      [ConsultationEvent]: EventType.DELETED,
    });
    await this.pubSub.publish(RequestProcedureRemoved, {
      [RequestProcedureRemoved]: items,
    });
    if (items?.length) {
      const details = {
        modelName: DashboardIcon.RequestProcedure,
        action: NotificationTag.Deleted,
        item: items,
      };
      this.notificationService.handleNoticationEvent({ profile, details });

      items.map(async (item) => {
        let resolvedBill;
        const references: string[] = [];
        if (!item.bill) return;

        if (item.bill.details?.length) {
          item.subBillRef ? references.push(item.subBillRef) : null;
          item.procedureType.map(({ ref }) => {
            references.push(ref);
          });

          resolvedBill = resolveBillForRecordDelete(references, {
            ...item.bill,
            updatedBy: profile,
            senderHospital: profile.hospital,
          });
        }

        if (resolvedBill?.details.length) {
          await this.pubSub.publish(OrgBillUpdated, {
            [OrgBillUpdated]: resolvedBill,
          });
        } else {
          await this.pubSub.publish(OrgBillRemoved, {
            [OrgBillRemoved]: [item.bill],
          });
        }
      });
    }

    return items;
  }

  @Subscription(() => [RequestProcedureModel], {
    name: RequestProcedureRemoved,
    filter: filterRequestProcedureRemoved,
  })
  removeRequestProcedureSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(RequestProcedureRemoved);
  }

  @Permissions(
    {
      action: PatientAction.ArchiveOwn,
      subject: Subject.ConsultationProcedure,
    },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.ConsultationProcedure,
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.ConsultationProcedure,
    },
    { action: PatientAction.Manage, subject: Subject.ConsultationProcedure },
  )
  @UseGuards(ProfileDataAccessGuard('clinifyId'))
  @Mutation(() => [RequestProcedureModel])
  async archiveRequestProcedures(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) requestIds: string[],
    @Args({
      name: 'archive',
      type: () => Boolean,
      defaultValue: true,
    })
    archive: boolean,
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    _clinifyId: string,
  ): Promise<RequestProcedureModel[]> {
    const items = await this.service.archiveRequestProcedures(
      profile,
      requestIds,
      archive,
    );
    const pubSubPath = archive
      ? RequestProcedureArchived
      : RequestProcedureUnarchived;

    await this.pubSub.publish(pubSubPath, { [pubSubPath]: items });
    return items;
  }

  @Subscription(() => [RequestProcedureModel], {
    name: RequestProcedureArchived,
    filter: filterRequestProcedureArchived,
  })
  archiveRequestProcedureSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(RequestProcedureArchived);
  }

  @Subscription(() => [RequestProcedureModel], {
    name: RequestProcedureUnarchived,
    filter: filterRequestProcedureUnarchived,
  })
  unArchiveRequestProcedureSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(RequestProcedureUnarchived);
  }

  @ResolveField('bill', () => BillModel, { nullable: true })
  getBill(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: RequestProcedureModel,
  ): BillModel {
    return profile.hospitalId === root.createdBy?.hospitalId ||
      profile.id === root.profileId
      ? root.bill
      : null;
  }

  @ResolveField(() => [PreauthorizationDetailsModel], {
    nullable: true,
    name: 'preauthorizationDetails',
  })
  getPreauthorizationDetails(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: RequestProcedureModel,
  ): Promise<PreauthorizationDetailsModel[]> {
    if (profile.hospitalId !== root.hospitalId) return null;
    return this.preauthDetailsService.getPreauthorizationDetails(
      BillableRecords.RequestProcedure,
      root.id,
    );
  }

  @ResolveField(() => HmoClaimModel, { name: 'hmoClaim', nullable: true })
  getHmoClaim(
    @CurrentProfile() mutator: ProfileModel,
    @Parent() root: RequestProcedureModel,
  ): Promise<HmoClaimModel> {
    if (mutator.hospitalId !== root.hospitalId || !root.hmoClaimId) return null;
    return this.hmoClaimService.getHmoClaim(mutator, root.hmoClaimId);
  }
}
