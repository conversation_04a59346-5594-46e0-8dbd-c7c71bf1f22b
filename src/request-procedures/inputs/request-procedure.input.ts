import { Field, InputType, OmitType } from '@nestjs/graphql';
import { IsOptional, IsUUID, Validate } from 'class-validator';
import { NewHmoClaimInput } from '../../hmo-claims/inputs/hmo-claims.input';
import {
  DurationType,
  IsDuration,
} from '@clinify/shared/validators/duration.validator';
import {
  BillInfoUpdateInput,
  ServiceDetailInput,
} from '@clinify/shared/validators/service-detail.input';
import { ProcedureTypeInput } from '@clinify/surgeries/validators/surgery.input';

@InputType()
export class NewRequestProcedureInput {
  @Field({ nullable: true })
  @IsUUID('4')
  @IsOptional()
  id?: string;

  @Field(() => String, { nullable: false })
  clinifyId: string;

  @Field(() => Date, { nullable: true })
  surgeryDate: Date;

  @Field(() => String, { nullable: true })
  @Validate(IsDuration, [DurationType.Daily])
  @IsOptional()
  duration: string;

  @Field(() => String, { nullable: true })
  provider?: string;

  @Field(() => [ProcedureTypeInput], { nullable: false })
  procedureType: ProcedureTypeInput[];

  @Field(() => String, { nullable: true })
  requestedBy: string;

  @Field(() => String)
  operatedBy: string;

  @Field(() => String, { nullable: true })
  specialty: string;

  @Field(() => String, { nullable: true })
  rank: string;

  @Field(() => String, { nullable: true })
  department: string;

  @Field(() => String, { nullable: true })
  facilityName: string;

  @Field(() => String, { nullable: true })
  facilityAddress: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  patientConsent: string;

  @Field(() => String, { nullable: true })
  patientConsentSignature: string;

  @Field(() => String, { nullable: true })
  patientConsentSignatureType: string;

  @Field(() => Date, { nullable: true })
  patientConsentSignatureDateTime: Date;

  @Field(() => [ServiceDetailInput], { nullable: true })
  @IsOptional()
  serviceDetails: ServiceDetailInput[];

  @Field(() => String, { nullable: true })
  reason: string;

  @Field(() => Date, { nullable: true })
  surgeryStartDate: Date;

  @Field(() => Date, { nullable: true })
  surgeryEndDate: Date;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  nextAppointmentDateTime: Date;

  @Field({ nullable: true })
  nextAppointmentEndDateTime: Date;

  @Field(() => String, { nullable: true })
  nextAppointmentDuration?: string;

  @Field(() => String, { nullable: true })
  nextAppointmentSpecialty: string;

  @Field(() => String, { nullable: true })
  nextAppointmentRole: string;

  @Field(() => String, { nullable: true })
  nextAppointmentCategory: string;

  @Field(() => String, { nullable: true })
  nextAppointmentSpecialistId?: string;

  @Field(() => String, { nullable: true })
  nextAppointmentSpecialistName?: string;

  @Field(() => String, { nullable: true })
  nextAppointmentUrgency?: string;

  @Field(() => String, { nullable: true })
  nextAppointmentDeliveryMethod: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  documentUrl: string[];

  @Field(() => String, { nullable: true })
  @IsUUID('4')
  @IsOptional()
  hmoProviderId?: string;

  @Field(() => NewHmoClaimInput, { nullable: true })
  @IsOptional()
  hmoClaim?: NewHmoClaimInput;
}

@InputType()
export class RequestProcedureInput extends OmitType(NewRequestProcedureInput, [
  'hmoClaim',
]) {
  @Field(() => [BillInfoUpdateInput], { nullable: true })
  @IsOptional()
  billInfo?: BillInfoUpdateInput[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  billRefsToDelete?: string[];
}
