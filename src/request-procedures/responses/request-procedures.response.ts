import { Field, Int, ObjectType } from '@nestjs/graphql';
import { RequestProcedureModel } from '../models/request-procedures.model';

@ObjectType()
export class RequestProcedureResponse {
  constructor(procedures: RequestProcedureModel[], totalCount: number) {
    this.list = procedures;
    this.totalCount = totalCount;
  }

  @Field(() => Int)
  totalCount: number;

  @Field(() => [RequestProcedureModel])
  list: RequestProcedureModel[];
}
