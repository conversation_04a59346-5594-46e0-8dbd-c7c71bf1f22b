import { NotFoundException } from '@nestjs/common';
import cloneDeep from 'lodash.clonedeep';
import { In, Repository } from 'typeorm';
import { RequestProcedureFilterInput } from '../inputs/request-procedure-filter.input';
import { RequestProcedureInput } from '../inputs/request-procedure.input';
import { RequestProcedureModel } from '../models/request-procedures.model';
import { RequestProcedureResponse } from '../responses/request-procedures.response';
import { ServiceType } from '@clinify/shared/enums/bill';
import { UserType } from '@clinify/shared/enums/users';
import { RecordCreator } from '@clinify/shared/validators/filter.input';
import {
  validateRecordArchiver,
  validateRecordRemover,
  validateUpdateBillableRecords,
} from '@clinify/shared/validators/validate-record-mutation.validator';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { resolveSubBillRef } from '@clinify/utils/helpers/billing.util';
import { takePaginatedResponses } from '@clinify/utils/pagination';

export interface IRequestProcedureRepository
  extends Repository<RequestProcedureModel> {
  this: Repository<RequestProcedureModel>;
  findByProfile(
    mutator: ProfileModel,
    profileId: string,
    filterOptions: RequestProcedureFilterInput,
  ): Promise<RequestProcedureResponse>;
  getOneRequestProcedure(
    mutator: ProfileModel,
    requestPackageId: string,
  ): Promise<RequestProcedureModel>;
  updateRequestProcedure(
    mutator: ProfileModel,
    input: RequestProcedureInput,
    onlyServiceDetail?: boolean,
  ): Promise<[RequestProcedureModel, RequestProcedureModel]>;
  deleteRequestProcedures(
    mutator: ProfileModel,
    requestPackageIds: string[],
  ): Promise<RequestProcedureModel[]>;
  archiveRequestProcedures(
    mutator: ProfileModel,
    requestPackageIds: string[],
    archive: boolean,
  ): Promise<RequestProcedureModel[]>;
}

export const CustomRequestProcedureRepoMethods: Pick<
  IRequestProcedureRepository,
  | 'findByProfile'
  | 'getOneRequestProcedure'
  | 'updateRequestProcedure'
  | 'deleteRequestProcedures'
  | 'archiveRequestProcedures'
> = {
  async findByProfile(
    this: IRequestProcedureRepository,
    mutator: ProfileModel,
    profileId: string,
    filterOptions: RequestProcedureFilterInput,
  ): Promise<RequestProcedureResponse> {
    const { skip, take, dateRange, keyword, archive, creator } = {
      ...filterOptions,
    };

    let query = this.createQueryBuilder('request_procedures')
      .innerJoinAndSelect('request_procedures.profile', 'profile')
      .innerJoinAndSelect('request_procedures.createdBy', 'createdBy')
      .leftJoinAndSelect('request_procedures.bill', 'requestProceduresBill')
      .leftJoinAndSelect('request_procedures.hospital', 'hospital')
      .leftJoinAndSelect('requestProceduresBill.details', 'billDetails')
      .leftJoinAndSelect('profile.coverageDetails', 'coverageDetails')
      .leftJoinAndSelect('coverageDetails.hmoProfile', 'hmoProfile')
      .leftJoinAndSelect('hmoProfile.provider', 'provider')
      .where('request_procedures.profile = :profileId', { profileId })
      .andWhere('request_procedures.archived = :archived', {
        archived: !!archive,
      });

    if (mutator.branchIds?.length)
      query = query.andWhere(
        `((hospital.facility_created_visibility = false OR hospital.facility_created_visibility IS NULL) 
        OR request_procedures.hospital IN(:...branchIds))`,
        {
          branchIds: mutator.branchIds,
        },
      );

    if (creator) {
      query = query
        .withDeleted()
        .andWhere(
          creator === RecordCreator.SELF
            ? 'createdBy.type = :patient'
            : 'createdBy.type != :patient',
          { patient: UserType.Patient },
        );
    }

    if (dateRange?.from) {
      query = query.andWhere('request_procedures.surgeryDate >= :from', {
        from: dateRange.from,
      });
    }

    if (dateRange?.to) {
      query = query.andWhere('request_procedures.surgeryDate < :to', {
        to: dateRange.to,
      });
    }

    if (keyword) {
      query = query.andWhere(
        `(
          request_procedures.procedure_type :: jsonb @> '[{ "type": "${keyword}" }]' OR
          request_procedures.specialty ILIKE :keyword OR
          request_procedures.duration ILIKE :keyword OR
          "requestProceduresBill".id::text ILIKE :keyword OR
          request_procedures.rank ILIKE :keyword OR
          request_procedures.facility_name ILIKE :keyword OR
          request_procedures.procedure_type::text ILIKE :keyword OR
          coverageDetails.coverageType ILIKE :keyword OR
          coverageDetails.name ILIKE :keyword OR
          coverageDetails.familyName ILIKE :keyword OR
          coverageDetails.companyName ILIKE :keyword OR
          provider.name ILIKE :keyword OR
          request_procedures.operated_by ILIKE :keyword
        )`,
        {
          keyword: `%${keyword}%`,
        },
      );
    }

    query = query
      .orderBy('request_procedures.createdDate', 'DESC')
      .skip(skip)
      .take(take);

    const response = await query.getManyAndCount();

    return new RequestProcedureResponse(
      ...takePaginatedResponses(response, take),
    );
  },

  async getOneRequestProcedure(
    this: IRequestProcedureRepository,
    mutator: ProfileModel,
    requestId: string,
  ): Promise<RequestProcedureModel> {
    return this.findOneOrFail({
      where: { id: requestId },
      relations: ['createdBy', 'updatedBy'],
      withDeleted: true,
    }).catch(() => {
      throw new NotFoundException('Record Not Found');
    });
  },

  async updateRequestProcedure(
    this: IRequestProcedureRepository,
    profile: ProfileModel,
    input: RequestProcedureInput,
    onlyServiceDetail?: boolean,
  ): Promise<[RequestProcedureModel, RequestProcedureModel]> {
    const requestInfo = await this.createQueryBuilder('request_procedures')
      .innerJoinAndSelect('request_procedures.createdBy', 'createdBy')
      .leftJoinAndSelect('request_procedures.profile', 'profile')
      .where('request_procedures.id = :id', { id: input.id })
      .andWhere('profile.clinifyId = :clinifyId', {
        clinifyId: input?.clinifyId,
      })
      .getOne();

    if (!requestInfo) throw new NotFoundException('Record Not Found');

    validateUpdateBillableRecords(profile, requestInfo, onlyServiceDetail);

    requestInfo.updatedBy = profile;

    const updateData = { ...requestInfo, ...input };
    const subBillServiceDetail = updateData.serviceDetails?.filter(
      (detail) => !detail.itemId,
    );
    const subBillRef = resolveSubBillRef(subBillServiceDetail, requestInfo);

    const updatedRecord = await this.save({
      ...updateData,
      subBillRef,
      lastModifierName: profile.fullName,
    });

    return [updatedRecord, requestInfo];
  },

  async deleteRequestProcedures(
    this: IRequestProcedureRepository,
    profile: ProfileModel,
    requestIds: string[],
  ): Promise<RequestProcedureModel[]> {
    const requests = await this.find({
      join: {
        alias: 'request_procedures',
        leftJoinAndSelect: {
          bill: 'request_procedures.bill',
          receiverProfile: 'bill.receiverProfile',
          billDetails: 'bill.details',
          subBills: 'billDetails.subBills',
          billDetailsCreatedBy: 'billDetails.createdBy',
          hmoClaim: 'request_procedures.hmoClaim',
        },
      },
      where: { id: In(requestIds) },
    });
    const validResources = validateRecordRemover(
      profile,
      requests,
      undefined,
      true,
      ServiceType.RequestProcedure,
    );
    if (validResources.length) {
      await this.remove(
        cloneDeep(validResources).map((v) => ({
          ...v,
          deletedBy: {
            id: profile.id,
            fullName: profile.fullName,
            entityId: v.id,
          },
        })),
      );
    }
    return validResources;
  },

  async archiveRequestProcedures(
    this: IRequestProcedureRepository,
    profile: ProfileModel,
    requestIds: string[],
    archive: boolean,
  ): Promise<RequestProcedureModel[]> {
    const requests = await this.find({
      relations: ['createdBy'],
      where: { id: In(requestIds) },
    });
    const validResources = validateRecordArchiver(profile, requests);

    const validIds = validResources.map((v) => v.id);
    if (!validIds.length) return [];

    await this.createQueryBuilder('request_procedures')
      .update(RequestProcedureModel)
      .set({ archived: archive, updatedDate: () => 'updated_date' })
      .whereInIds(validIds)
      .execute();

    return validResources.map((v) => ({ ...v, archived: archive }));
  },
};
