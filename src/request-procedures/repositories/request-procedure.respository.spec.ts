import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import moment from 'moment';
import { DataSource, EntityManager } from 'typeorm';
import {
  CustomRequestProcedureRepoMethods,
  IRequestProcedureRepository,
} from './request-procedure.repository';
import { RequestProcedureModel } from '../models/request-procedures.model';
import { requestProcedureFactory } from '@clinify/__mocks__/factories/request-procedure.factory';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendDSRepo, extendModel } from '@clinify/database/extendModel';
import { UserType } from '@clinify/shared/enums/users';
import { RecordCreator } from '@clinify/shared/validators/filter.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { CustomProfileRepoMethods } from '@clinify/users/repositories/profile.repository';
import { createHospitals } from '@clinify/utils/tests/hospital.fixtures';
import { createProcedures } from '@clinify/utils/tests/procedure.fixtures';
import { createUsers } from '@clinify/utils/tests/user.fixtures';

const chance = Chance();

describe('RequestProcedureRepository', () => {
  let procedures: RequestProcedureModel[];
  let profile: ProfileModel;
  let procedure: RequestProcedureModel;
  let dataSource: DataSource;
  let manager: EntityManager;
  let repo: IRequestProcedureRepository;

  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeOrmModule.forFeature([RequestProcedureModel, ProfileModel]),
      ],
      providers: [
        extendModel(RequestProcedureModel, CustomRequestProcedureRepoMethods),
        extendModel(ProfileModel, CustomProfileRepoMethods),
      ],
    }).compile();

    dataSource = module.get<DataSource>(DataSource);
    manager = dataSource.manager;
    repo = extendDSRepo<IRequestProcedureRepository>(
      dataSource,
      RequestProcedureModel,
      CustomRequestProcedureRepoMethods,
    );
  });

  beforeEach(async () => {
    procedures = await createProcedures(manager, 3);
    procedure = procedures[0];
    profile = procedure.profile;
  });

  afterAll(async () => {
    await dataSource.destroy();
    await module.close();
  });

  it('findByProfile(): should find procedures for a particular user', async () => {
    const { id, procedureType, requestedBy, createdBy } = procedure;
    const records = await repo.findByProfile(createdBy, procedure.profile.id, {
      skip: 0,
      take: 50,
    });
    expect(records).toHaveProperty('list');
    expect(records.list.length).toEqual(3);
    expect(records.list).toContainEqual(
      expect.objectContaining({ id, procedureType, requestedBy }),
    );
  });

  it('findByProfile(): should find procedures for a particular user using filters', async () => {
    const [{ specialty: keyword, procedureType, id, surgeryDate, createdBy }] =
      procedures;
    const record = await repo.findByProfile(createdBy, profile.id, {
      skip: 0,
      take: 10,
      keyword,
      dateRange: {
        from: moment(surgeryDate).startOf('day').toDate(),
        to: moment(surgeryDate).endOf('day').toDate(),
      },
    });

    expect(record).toHaveProperty('list');
    expect(record.list).toContainEqual(
      expect.objectContaining({ id, specialty: keyword, procedureType }),
    );
  });

  it('findByProfile(): should find procedures for a particular user created by them', async () => {
    const record = await repo.findByProfile(profile, profile.id, {
      creator: RecordCreator.SELF,
    });
    expect(record).toHaveProperty('list');
    expect(
      record.list.every((item) => item.createdBy.id === profile.id),
    ).toBeTruthy();
  });

  it('findByProfile(): should find procedures for a particular user created by others', async () => {
    const record = await repo.findByProfile(profile, profile.id, {
      creator: RecordCreator.OTHERS,
    });
    expect(record).toHaveProperty('list');
    expect(
      record.list.every((item) => item.createdBy.id !== profile.id),
    ).toBeTruthy();
  });

  it('getOneRequestProcedure(): should throw error when procedure is not found', async () => {
    await expect(
      repo.getOneRequestProcedure(
        procedure.profile,
        chance.guid({ version: 4 }),
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('getOneRequestProcedure(): should get one procedure', async () => {
    const record = await repo.getOneRequestProcedure(
      procedure.profile,
      procedure.id,
    );
    expect(record.id).toEqual(procedure.id);
    expect(record.procedureType).toEqual(procedure.procedureType);
  });

  it('updateRequestProcedure(): should throw error if record Not Found', async () => {
    const input = requestProcedureFactory.build();
    delete input.profile;

    await expect(
      repo.updateRequestProcedure(procedure.profile, input),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateRequestProcedure(): should update an procedure', async () => {
    const input = requestProcedureFactory.build();
    delete input.profile;
    input.id = procedure.id;
    input.clinifyId = profile.clinifyId;

    const updatedProcedure = await repo.updateRequestProcedure(profile, input);

    expect(updatedProcedure[0].procedureType).toEqual(input.procedureType);
    expect(updatedProcedure[0].requestedBy).toEqual(input.requestedBy);
  });

  it('updateRequestProcedure(): should not allow patient to update another patient procedure', async () => {
    const input = requestProcedureFactory.build();

    const [anotherPatientRecord] = await createProcedures(manager, 1);

    input.id = anotherPatientRecord.id;
    input.clinifyId = anotherPatientRecord.profile.clinifyId;
    delete input.profile;

    await expect(
      repo.updateRequestProcedure(procedure.profile, input),
    ).rejects.toThrow('Not Authorized To Modify This Record');
  });

  it('updateRequestProcedure(): should not allow organization doctors to update surgerys for another hospital', async () => {
    const input = requestProcedureFactory.build();

    const [hospitalCreator, hospital] = await createHospitals(manager, 2);
    const [doctor] = await createUsers(manager, 1, hospitalCreator);
    const [anotherDoctorInAnotherHospital] = await createUsers(
      manager,
      1,
      hospital,
    );

    const [procedureByDoctor] = await createProcedures(
      manager,
      1,
      doctor.defaultProfile,
    );

    input.id = procedureByDoctor.id;
    input.createdBy = procedureByDoctor.createdBy;
    input.clinifyId = procedureByDoctor.profile.clinifyId;
    delete input.profile;

    await expect(
      repo.updateRequestProcedure(
        anotherDoctorInAnotherHospital.defaultProfile,
        input,
      ),
    ).rejects.toThrow('Not Authorized To Modify This Record');
  });

  it('updateRequestProcedure(): should not allow organization billing officer to update records', async () => {
    const input = requestProcedureFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [billingOfficer] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.OrganizationBillingOfficer,
    );

    const [procedureByDoctor] = await createProcedures(
      manager,
      1,
      doctor.defaultProfile,
      undefined,
      undefined,
      hospital,
    );

    input.id = procedureByDoctor.id;
    input.createdBy = procedureByDoctor.createdBy;
    input.clinifyId = procedureByDoctor.profile.clinifyId;
    delete input.profile;

    await expect(
      repo.updateRequestProcedure(billingOfficer.defaultProfile, input),
    ).rejects.toThrow('Not Authorized To Modify This Record');
  });

  it('updateRequestProcedure(): should not allow organization billing officer to update records', async () => {
    const input = requestProcedureFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [billingOfficer] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.OrganizationBillingOfficer,
    );

    const [procedureByDoctor] = await createProcedures(
      manager,
      1,
      doctor.defaultProfile,
      undefined,
      undefined,
      hospital,
    );

    input.id = procedureByDoctor.id;
    input.createdBy = procedureByDoctor.createdBy;
    input.clinifyId = procedureByDoctor.profile.clinifyId;
    delete input.profile;

    const [response] = await repo.updateRequestProcedure(
      billingOfficer.defaultProfile,
      input,
      true,
    );

    expect(response.procedureType).toEqual(input.procedureType);
    expect(response.requestedBy).toEqual(input.requestedBy);
  });

  it('updateRequestProcedure(): should update a procedure', async () => {
    const input = requestProcedureFactory.build();
    delete input.profile;
    const record = await repo.updateRequestProcedure(procedure.profile, {
      ...input,
      id: procedure.id,
      clinifyId: profile.clinifyId,
    });

    expect(record[0].procedureType).toEqual(input.procedureType);
    expect(record[0].requestedBy).toEqual(input.requestedBy);
  });

  it('deleteRequestProcedures(): should delete procedures by ids', async () => {
    const record = await repo.deleteRequestProcedures(procedure.profile, [
      procedure.id,
    ]);
    expect(record[0].id).toEqual(procedure.id);
    expect(record[0].procedureType).toEqual(procedure.procedureType);
  });

  it('deleteRequestProcedures(): should only delete record they have access to (created by me)', async () => {
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [procedureByDoctor, nextProcedureByDoctor] = await createProcedures(
      manager,
      2,
      doctor.defaultProfile,
    );
    const record = await repo.deleteRequestProcedures(procedure.profile, [
      procedure.id,
      procedureByDoctor.id,
      nextProcedureByDoctor.id,
    ]);
    expect(record.length).toEqual(1);
  });

  it('deleteRequestProcedures(): should only delete record they created (created by them/organization)', async () => {
    const fakeProcedure = requestProcedureFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);

    const [procedureByDoctor, nextProcedureByDoctor] = await createProcedures(
      manager,
      2,
      doctor.defaultProfile,
    );
    const record = await repo.deleteRequestProcedures(doctor.defaultProfile, [
      procedure.id,
      procedureByDoctor.id,
      nextProcedureByDoctor.id,
      fakeProcedure.id,
    ]);
    expect(record.length).toEqual(2);
  });

  it('deleteRequestProcedures(): should not delete record they did not create', async () => {
    const fakeProcedure = requestProcedureFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [anotherDoctorInSameHospital] = await createUsers(
      manager,
      2,
      hospital,
    );

    const [procedureByDoctor, nextProcedureByDoctor] = await createProcedures(
      manager,
      2,
      doctor.defaultProfile,
    );
    const record = await repo.deleteRequestProcedures(
      anotherDoctorInSameHospital.defaultProfile,
      [
        procedure.id,
        procedureByDoctor.id,
        nextProcedureByDoctor.id,
        fakeProcedure.id,
      ],
    );
    expect(record.length).toEqual(0);
  });

  it('deleteRequestProcedures(): should only delete record they have access to (created by them/organization)', async () => {
    const [hospital, clinic] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [anotherDoctorInAnotherHospital] = await createUsers(
      manager,
      1,
      clinic,
    );

    const [procedureByDoctor] = await createProcedures(
      manager,
      2,
      doctor.defaultProfile,
    );
    const record = await repo.deleteRequestProcedures(
      anotherDoctorInAnotherHospital.defaultProfile,
      [procedure.id, procedureByDoctor.id],
    );
    expect(record.length).toBeFalsy();
  });

  it('archiveRequestProcedures(): should only archive/unarchive record created by me', async () => {
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [procedureByDoctor, nextProcedureByDoctor] = await createProcedures(
      manager,
      2,
      doctor.defaultProfile,
    );
    let record = await repo.archiveRequestProcedures(
      procedure.profile,
      [procedure.id, procedureByDoctor.id, nextProcedureByDoctor.id],
      true,
    );
    expect(record.length).toEqual(1);
    expect(record).toContainEqual(expect.objectContaining({ archived: true }));

    record = await repo.archiveRequestProcedures(
      procedure.profile,
      [procedure.id, procedureByDoctor.id, nextProcedureByDoctor.id],
      false,
    );
    expect(record.length).toEqual(1);
    expect(record).toContainEqual(expect.objectContaining({ archived: false }));
  });

  it('archiveRequestProcedures(): should only archive record created by me (organization)', async () => {
    const fakeProcedure = requestProcedureFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);

    const [procedureByDoctor, nextProcedureByDoctor] = await createProcedures(
      manager,
      2,
      doctor.defaultProfile,
    );
    let record = await repo.archiveRequestProcedures(
      doctor.defaultProfile,
      [
        procedure.id,
        procedureByDoctor.id,
        nextProcedureByDoctor.id,
        fakeProcedure.id,
      ],
      true,
    );
    expect(record.length).toEqual(2);
    expect(record).toContainEqual(expect.objectContaining({ archived: true }));

    record = await repo.archiveRequestProcedures(
      doctor.defaultProfile,
      [
        procedure.id,
        procedureByDoctor.id,
        nextProcedureByDoctor.id,
        fakeProcedure.id,
      ],
      false,
    );
    expect(record.length).toEqual(2);
    expect(record).toContainEqual(expect.objectContaining({ archived: false }));
  });

  it('archiveRequestProcedures(): should archive record created by another person with same facility', async () => {
    const fakeProcedure = requestProcedureFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [anotherDoctorInSameHospital] = await createUsers(
      manager,
      2,
      hospital,
    );

    const [procedureByDoctor, nextProcedureByDoctor] = await createProcedures(
      manager,
      2,
      doctor.defaultProfile,
    );
    let record = await repo.archiveRequestProcedures(
      anotherDoctorInSameHospital.defaultProfile,
      [
        procedure.id,
        procedureByDoctor.id,
        nextProcedureByDoctor.id,
        fakeProcedure.id,
      ],
      true,
    );
    expect(record.length).toEqual(2);

    record = await repo.archiveRequestProcedures(
      anotherDoctorInSameHospital.defaultProfile,
      [
        procedure.id,
        procedureByDoctor.id,
        nextProcedureByDoctor.id,
        fakeProcedure.id,
      ],
      false,
    );
    expect(record.length).toEqual(2);
  });

  it('findByProfile(): should find archived procedures for a particular user using filters', async () => {
    await repo.archiveRequestProcedures(
      profile,
      [procedure.id, procedures[2].id],
      true,
    );
    const record = await repo.findByProfile(profile, profile.id, {
      archive: true,
    });
    expect(record).toHaveProperty('list');
    expect(record.list).toContainEqual(
      expect.objectContaining({ archived: true }),
    );
  });
});
