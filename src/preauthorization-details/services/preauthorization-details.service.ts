import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { customDSSerializeInTransaction } from '@clinify/database';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { InvestigationModel } from '@clinify/investigation/models/investigation.model';
import { PreauthorizationDetailsModel } from '@clinify/preauthorization-details/models/preauthorization-details.model';
import { IPreauthorizationDetailsRepository } from '@clinify/preauthorization-details/repositories/preauthorization-details.repository';
import { BillableRecords } from '@clinify/shared/enums/billable-records';
import { PreauthorizationDetailsUpdate } from '@clinify/shared/validators/preauthorization-details.input';
import { SurgeryModel } from '@clinify/surgeries/models/surgery.model';
import { ProfileModel } from '@clinify/users/models/profile.model';

@Injectable()
export class PreauthorizationDetailsService {
  constructor(
    @InjectRepository(PreauthorizationDetailsModel)
    private readonly repository: IPreauthorizationDetailsRepository,
    private readonly entityManager: EntityManager,
    private readonly dataSource: DataSource,
  ) {}

  private resolveModelPathObject(record: BillableRecords, recordId: string) {
    switch (record) {
      case BillableRecords.Admission:
        return { admissionId: recordId };
      case BillableRecords.Antenatal:
        return { antenatalDetailsId: recordId };
      case BillableRecords.Consultation:
        return { consultationId: recordId };
      case BillableRecords.Immunization:
        return { immunizationDetailId: recordId };
      case BillableRecords.Laboratory:
      case BillableRecords.Radiology:
        return { investigationId: recordId };
      case BillableRecords.Medication:
        return { medicationDetailsId: recordId };
      case BillableRecords.Surgery:
        return { surgeryId: recordId };
      case BillableRecords.Registration:
        return { registrationProfileId: recordId };
      case BillableRecords.NursingService:
        return { nursingServiceId: recordId };
      case BillableRecords.Postnatal:
        return { postnatalId: recordId };
      case BillableRecords.LabourDelivery:
        return { labourDeliveryId: recordId };
      case BillableRecords.OncologyConsultationHistory:
        return { oncologyConsultationHistoryId: recordId };
      case BillableRecords.RequestProcedure:
        return { requestProcedureId: recordId };
    }
  }

  getPreauthorizationDetails(
    record: BillableRecords,
    recordId: string,
  ): Promise<PreauthorizationDetailsModel[]> {
    return this.repository.find({
      where: this.resolveModelPathObject(record, recordId),
    });
  }

  async updatePreauthorizationDetailsInTransaction(
    manager: EntityManager,
    mutator: ProfileModel,
    input: PreauthorizationDetailsUpdate,
  ): Promise<PreauthorizationDetailsModel> {
    const preauthDetailsTrxnRepo = manager.getRepository(
      PreauthorizationDetailsModel,
    );
    const { id, model, modelId, ref, providerCode } = input;
    let providerId: string;
    const existing = !!id
      ? await preauthDetailsTrxnRepo.findOne({ where: { id } })
      : undefined;
    if (providerCode) {
      const provider = await manager.findOne(HmoProviderModel, {
        where: {
          providerCode,
        },
        select: ['id'],
      });
      providerId = provider.id;
    }
    if (existing) {
      if (existing.hospitalId !== mutator.hospitalId) {
        throw new ForbiddenException('Not Authorized To Modify This Record');
      }
      return this.repository.save({
        ...existing,
        recordType: input.model,
        paStatus: input.paStatus,
        paCode: input.paCode,
        updatedBy: mutator,
        providerId,
        lastModifierName: mutator.fullName,
        ref,
        ...this.resolveModelPathObject(model, modelId),
      });
    }
    const preauthorizationDetails = await preauthDetailsTrxnRepo.save({
      recordType: input.model,
      paStatus: input.paStatus,
      paCode: input.paCode,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
      hospital: mutator.hospital,
      providerId,
      ref,
      ...this.resolveModelPathObject(input.model, input.modelId),
    });

    if (input.model === BillableRecords.Surgery) {
      const surgery = await this.entityManager
        .findOneOrFail(SurgeryModel, { where: { id: modelId } })
        .catch(() => {
          throw new NotFoundException('Procedure Not Found');
        });

      const index = surgery.procedureType.findIndex((type) => type.ref === ref);
      if (index === -1) {
        throw new NotFoundException('Procedure Type Not Found');
      }
      surgery.procedureType[index].preauthorizationDetailsId =
        preauthorizationDetails.id;
      await this.entityManager.save(SurgeryModel, { ...surgery });
    }

    if (
      [BillableRecords.Laboratory, BillableRecords.Radiology].includes(model)
    ) {
      const isLab = model === BillableRecords.Laboratory;
      const investigation = await this.entityManager
        .findOneOrFail(InvestigationModel, { where: { id: modelId } })
        .catch(() => {
          throw new NotFoundException('Investigation Not Found');
        });

      const index = investigation[
        isLab ? 'testInfo' : 'examinationType'
      ].findIndex((type) => type.ref === ref);
      if (index === -1) {
        throw new NotFoundException('Investigation Type Not Found');
      }
      investigation[isLab ? 'testInfo' : 'examinationType'][
        index
      ].preauthorizationDetailsId = preauthorizationDetails.id;
      await this.entityManager.save(InvestigationModel, investigation);
    }

    return preauthorizationDetails;
  }

  async updatePreauthorizationDetails(
    mutator: ProfileModel,
    input: PreauthorizationDetailsUpdate,
  ): Promise<PreauthorizationDetailsModel> {
    return customDSSerializeInTransaction(this.dataSource, (manager) =>
      this.updatePreauthorizationDetailsInTransaction(manager, mutator, input),
    );
  }
}
