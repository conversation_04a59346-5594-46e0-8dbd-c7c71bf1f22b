import { HttpService } from '@nestjs/axios';
import { ForbiddenException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { DataSource, EntityManager } from 'typeorm';
import { PreauthorizationDetailsService } from './preauthorization-details.service';
import { PreauthorizationDetailsModel } from '../models/preauthorization-details.model';
import { preauthorizationDetailsFactory } from '@clinify/__mocks__/factories/preauthorizationdetails.factory';
import { loggerMock } from '@clinify/__mocks__/logger';
import { BillableRecords } from '@clinify/shared/enums/billable-records';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';
import { createAdmissions } from '@clinify/utils/tests/admission.fixtures';
import { createInvestigations } from '@clinify/utils/tests/investigations.fixtures';
import { createOncologyConsultations } from '@clinify/utils/tests/oncologyConsultation.fixtures';
import { createPreauthorizationDetails } from '@clinify/utils/tests/preauthdetails.fixtures';
import { createProcedures } from '@clinify/utils/tests/procedure.fixtures';
import { createSurgeries } from '@clinify/utils/tests/surgery.fixtures';
import { mockUser } from '@mocks/factories/user.factory';

const chance = new Chance();
const user: UserModel = mockUser;
const preauthDetailsData = preauthorizationDetailsFactory.build();
const profile = user.defaultProfile;
const paCode = chance.word();
const paStatus = 'Accepted';

const MockProfileRepository = {
  findOne: jest.fn(() => profile),
};

const MockPreauthorizationDetailsRepository = {
  findOne: jest.fn(() => preauthDetailsData),
  save: jest.fn((v) => v),
};

const ManagerMock = {
  getRepository: jest.fn().mockReturnValue({
    save: jest.fn((v) => v),
    findOne: jest.fn(() => preauthDetailsData),
  }),
  createQueryBuilder: jest.fn(() => ({
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    execute: jest.fn(),
  })),
  queryRunner: { isTransactionActive: true },
  save: jest.fn((v) => v),
  findOneOrFail: jest.fn(() => Promise.resolve(preauthDetailsData)),
};

const dsMock = {
  getRepository: jest.fn(() => ({
    extend: jest.fn(),
  })),
  transaction: jest.fn((cb) => cb(ManagerMock)),
  manager: ManagerMock,
};

describe('PreAuthorizationDetailsService', () => {
  let service: PreauthorizationDetailsService;
  let manager: EntityManager;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PreauthorizationDetailsService,
        HttpService,
        {
          provide: getRepositoryToken(PreauthorizationDetailsModel),
          useValue: MockPreauthorizationDetailsRepository,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: MockProfileRepository,
        },
        {
          provide: HttpService,
          useValue: {},
        },
        {
          provide: DataSource,
          useValue: dsMock,
        },
        {
          provide: EntityManager,
          useValue: ManagerMock,
        },
        { ...loggerMock },
      ],
    }).compile();

    manager = module.get<EntityManager>(EntityManager);
    service = module.get<PreauthorizationDetailsService>(
      PreauthorizationDetailsService,
    );
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(service).toBeTruthy();
  });

  it('updatePreauthorizationDetails() should update preauthorization details', async () => {
    const admissionData = await createAdmissions(manager, 1);
    const response = await service.updatePreauthorizationDetails(profile, {
      model: BillableRecords.Admission,
      modelId: admissionData[0].id,
    });
    expect(response.admissionId).toEqual(admissionData[0].id);
    expect(response.paCode).toBeUndefined();
    expect(response.paStatus).toBeUndefined();
  });

  it('updatePreauthorizationDetails() should update preauthorization details with pa code and status', async () => {
    const admissionData = await createAdmissions(manager, 1);
    const response = await service.updatePreauthorizationDetails(profile, {
      model: BillableRecords.Admission,
      modelId: admissionData[0].id,
      paCode,
      paStatus,
    });
    expect(response.admissionId).toEqual(admissionData[0].id);
    expect(response.paCode).toEqual(paCode);
    expect(response.paStatus).toEqual(paStatus);
  });

  it('updatePreauthorizationDetails() should fail for wrong permission', async () => {
    const paCode = chance.word();
    const paStatus = 'Accepted';
    const preauthDetailData = await createPreauthorizationDetails(manager, 1);
    const admissionData = await createAdmissions(manager, 1);
    await expect(
      service.updatePreauthorizationDetails(profile, {
        model: BillableRecords.Admission,
        modelId: admissionData[0].id,
        paCode,
        paStatus,
        id: preauthDetailData[0].id,
      }),
    ).rejects.toThrow(
      new ForbiddenException('Not Authorized To Modify This Record'),
    );
  });

  it('updatePreauthorizationDetails() should update with existing preauthorization details', async () => {
    const preauthDetailData = await createPreauthorizationDetails(
      manager,
      1,
      undefined,
      undefined,
      undefined,
      profile?.hospital,
    );
    profile.hospitalId = profile.hospital.id;
    const admissionData = await createAdmissions(manager, 1);
    const response = await service.updatePreauthorizationDetails(profile, {
      model: BillableRecords.Admission,
      modelId: admissionData[0].id,
      paCode: '1234',
      paStatus,
      id: preauthDetailData[0].id,
    });
    expect(MockPreauthorizationDetailsRepository.save).toHaveBeenCalled();
    expect(response.admissionId).toEqual(admissionData[0].id);
    expect(response.paCode).toEqual('1234');
    expect(response.paStatus).toEqual(paStatus);
    expect(response.recordType).toEqual('Admission');
  });

  it('updatePreauthorizationDetails() should fail for Procedure Type Not Found', async () => {
    const surgeryData = await createSurgeries(manager, 1);
    ManagerMock.findOneOrFail.mockReturnValue(Promise.resolve(surgeryData[0]));
    await expect(
      service.updatePreauthorizationDetails(profile, {
        model: BillableRecords.Surgery,
        modelId: surgeryData[0].id,
        paCode,
        paStatus,
      }),
    ).rejects.toThrow(new ForbiddenException('Procedure Type Not Found'));
  });

  it('updatePreauthorizationDetails() should update preauthorization details for surgery', async () => {
    const [surgeryData] = await createSurgeries(manager, 1);
    const ref = surgeryData.procedureType[0].ref;
    ManagerMock.findOneOrFail.mockReturnValue(Promise.resolve(surgeryData));
    const response = await service.updatePreauthorizationDetails(profile, {
      model: BillableRecords.Surgery,
      modelId: surgeryData.id,
      paCode,
      paStatus,
      ref,
    });
    expect(response.paCode).toEqual(paCode);
    expect(response.paStatus).toEqual(paStatus);
    expect(response.surgeryId).toEqual(surgeryData.id);
  });

  it('updatePreauthorizationDetails() should fail for lab type not found', async () => {
    const [investigationData] = await createInvestigations(manager, 1);
    ManagerMock.findOneOrFail.mockReturnValue(
      Promise.resolve(investigationData),
    );
    await expect(
      service.updatePreauthorizationDetails(profile, {
        model: BillableRecords.Laboratory,
        modelId: investigationData.id,
        paCode,
        paStatus,
        ref: 'bad-ref',
      }),
    ).rejects.toThrow(new ForbiddenException('Investigation Type Not Found'));
  });

  it('updatePreauthorizationDetails() should fail for radiology type not found', async () => {
    const [investigationData] = await createInvestigations(manager, 1);
    ManagerMock.findOneOrFail.mockReturnValue(
      Promise.resolve(investigationData),
    );
    await expect(
      service.updatePreauthorizationDetails(profile, {
        model: BillableRecords.Radiology,
        modelId: investigationData.id,
        paCode,
        paStatus,
      }),
    ).rejects.toThrow(new ForbiddenException('Investigation Type Not Found'));
  });

  it('updatePreauthorizationDetails() should update preauthorization details for lab', async () => {
    const [investigationData] = await createInvestigations(manager, 1);
    const ref = investigationData.testInfo[0].ref;
    ManagerMock.findOneOrFail.mockReturnValue(
      Promise.resolve(investigationData),
    );
    const response = await service.updatePreauthorizationDetails(profile, {
      model: BillableRecords.Laboratory,
      modelId: investigationData.id,
      paCode,
      paStatus,
      ref,
    });
    expect(response.paCode).toEqual(paCode);
    expect(response.paStatus).toEqual(paStatus);
    expect(response.investigationId).toEqual(investigationData.id);
  });

  it('updatePreauthorizationDetails() should update preauthorization details for radiology', async () => {
    const [investigationData] = await createInvestigations(manager, 1);
    const ref = investigationData.examinationType[0].ref;
    ManagerMock.findOneOrFail.mockReturnValue(
      Promise.resolve(investigationData),
    );
    const response = await service.updatePreauthorizationDetails(profile, {
      model: BillableRecords.Radiology,
      modelId: investigationData.id,
      paCode,
      paStatus,
      ref,
    });
    expect(response.paCode).toEqual(paCode);
    expect(response.paStatus).toEqual(paStatus);
    expect(response.investigationId).toEqual(investigationData.id);
  });

  it('updatePreauthorizationDetails() should update preauthorization details', async () => {
    const oncologyConsultationData = await createOncologyConsultations(
      manager,
      1,
    );
    const response = await service.updatePreauthorizationDetails(profile, {
      model: BillableRecords.OncologyConsultationHistory,
      modelId: oncologyConsultationData[0].id,
    });
    expect(response.oncologyConsultationHistoryId).toEqual(
      oncologyConsultationData[0].id,
    );
    expect(response.paCode).toBeUndefined();
    expect(response.paStatus).toBeUndefined();
  });

  it('updatePreauthorizationDetails() should update preauthorization details', async () => {
    const procedureData = await createProcedures(manager, 1);
    const response = await service.updatePreauthorizationDetails(profile, {
      model: BillableRecords.RequestProcedure,
      modelId: procedureData[0].id,
    });
    expect(response.requestProcedureId).toEqual(procedureData[0].id);
    expect(response.paCode).toBeUndefined();
    expect(response.paStatus).toBeUndefined();
  });
});
