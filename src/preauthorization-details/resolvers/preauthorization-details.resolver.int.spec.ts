import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { PreauthorizationDetailsResolver } from './preauthorization-details.resolver';
import { PreauthorizationDetailsService } from '../services/preauthorization-details.service';
import { admissionFactory } from '@clinify/__mocks__/factories/admission.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { PermissionService } from '@clinify/authorization/services/permission.service';
import { BillableRecords } from '@clinify/shared/enums/billable-records';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { HmoProviderFactory } from '@mocks/factories/hmo-provider.factory';
import { preauthorizationFactory } from '@mocks/factories/preauthorization.factory';
import { preauthorizationDetailsFactory } from '@mocks/factories/preauthorizationdetails.factory';
import { mockUser } from '@mocks/factories/user.factory';

const profile = profileFactory.build();
const admission = admissionFactory.build();
const hmoProvider = HmoProviderFactory.build();
const user = mockUser;

const list = preauthorizationFactory.buildList(4);
const preauthorizationDetailsData = preauthorizationDetailsFactory.build();

const PreauthorizationServiceMock = {
  updatePreauthorizationDetails: jest.fn(() => admission),
  updatePreauthorizationDetailsHandler: jest.fn(),
  findByHospital: jest.fn(() => ({ itemCount: 3, list })),
  findByProfile: jest.fn(() => ({ itemCount: 3, list })),
};

const ManagerMock = {
  findOneOrFail: jest.fn(() => user.defaultProfile),
  findOne: jest.fn().mockResolvedValue(hmoProvider),
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

describe('PreauthorizationDetailsResolver', () => {
  let resolver: PreauthorizationDetailsResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PreauthorizationDetailsResolver,
        PermissionService,
        { provide: getRepositoryToken(PermissionModel), useValue: {} },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: {},
        },
        {
          provide: PreauthorizationDetailsService,
          useValue: PreauthorizationServiceMock,
        },
        {
          provide: EntityManager,
          useValue: ManagerMock,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
      ],
    }).compile();

    resolver = module.get<PreauthorizationDetailsResolver>(
      PreauthorizationDetailsResolver,
    );
    jest.clearAllMocks();
  });

  it('is defined', () => {
    expect(resolver).toBeDefined();
    expect(resolver).toBeTruthy();
  });

  it('updatePreauthorizationDetails()', async () => {
    const response = await resolver.updatePreauthorizationDetails(profile, {
      model: BillableRecords.Admission,
      modelId: admission.id,
    });
    expect(response).toEqual(admission);
  });

  it('updatePreauthorizationDetails() - publishes subscription', () => {
    resolver.updatePreauthorizationDetailsHandler(profile.hospitalId);
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'PreauthorizationDetailsUpdated',
    );
  });

  it('getHmoProvider() should get hmo provider', async () => {
    const res = await resolver.getHmoProvider(preauthorizationDetailsData);
    expect(res).toEqual(hmoProvider);
  });

  it('getHmoProvider() should return undefined on no providerId', async () => {
    const _preauthorizationDetails = { ...preauthorizationDetailsData };
    delete _preauthorizationDetails.providerId;
    const res = await resolver.getHmoProvider(_preauthorizationDetails);
    expect(res).toBeUndefined();
  });
});
