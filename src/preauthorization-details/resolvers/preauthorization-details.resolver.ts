import { Inject, UseGuards } from '@nestjs/common';
import {
  Args,
  Mutation,
  Parent,
  ResolveField,
  Resolver,
  Subscription,
} from '@nestjs/graphql';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { EntityManager } from 'typeorm';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { PinAuthGuard } from '@clinify/authentication/guards/pin.guard';
import { AuthorizationGuard } from '@clinify/authorization/guards/authorization.guard';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { PreauthorizationDetailsModel } from '@clinify/preauthorization-details/models/preauthorization-details.model';
import { PreauthorizationDetailsService } from '@clinify/preauthorization-details/services/preauthorization-details.service';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { AppServices } from '@clinify/shared/enums/services';
import { PreauthorizationDetailsUpdate } from '@clinify/shared/validators/preauthorization-details.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { filterPreauthorizationDetailsUpdated } from '@clinify/utils/subscriptions/filters';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const { PreauthorizationDetailsUpdated } = SubscriptionTypes;

@LogService(AppServices.PreauthorizationDetails)
@UseGuards(GqlAuthGuard, AuthorizationGuard)
@Resolver(() => PreauthorizationDetailsModel)
export class PreauthorizationDetailsResolver {
  constructor(
    private service: PreauthorizationDetailsService,
    @Inject(EntityManager) readonly entityManager: EntityManager,
    @Inject(PUB_SUB) private readonly pubSub: RedisPubSub,
  ) {}

  @UseGuards(PinAuthGuard)
  @Mutation(() => PreauthorizationDetailsModel)
  async updatePreauthorizationDetails(
    @CurrentProfile() profile: ProfileModel,
    @Args('input', { type: () => PreauthorizationDetailsUpdate })
    input: PreauthorizationDetailsUpdate,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<PreauthorizationDetailsModel> {
    const item = await this.service.updatePreauthorizationDetails(
      profile,
      input,
    );
    await this.pubSub.publish(PreauthorizationDetailsUpdated, {
      [PreauthorizationDetailsUpdated]: item,
    });

    return item;
  }

  @Subscription(() => PreauthorizationDetailsModel, {
    name: PreauthorizationDetailsUpdated,
    filter: filterPreauthorizationDetailsUpdated,
  })
  updatePreauthorizationDetailsHandler(
    @Args('hospitalId', { type: () => String }) _hospitalId: string,
  ): AsyncIterator<any> {
    return this.pubSub.asyncIterator(PreauthorizationDetailsUpdated);
  }

  @ResolveField(() => HmoProviderModel, { nullable: true, name: 'provider' })
  getHmoProvider(
    @Parent() root: PreauthorizationDetailsModel,
  ): Promise<HmoProviderModel> {
    if (!root.providerId) return;
    return this.entityManager.findOne(HmoProviderModel, {
      where: { id: root.providerId },
    });
  }
}
