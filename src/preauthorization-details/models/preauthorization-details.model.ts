import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate } from 'class-validator';
import {
  Column,
  CreateDate<PERSON>olumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AdmissionModel } from '@clinify/admissions/models/admission.model';
import { ConsultationModel } from '@clinify/consultations/models/consultation.model';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { ImmunizationDetailModel } from '@clinify/immunizations/models/immunization-details.model';
import { InvestigationModel } from '@clinify/investigation/models/investigation.model';
import { MedicationDetailsModel } from '@clinify/medications/models/medication_details.model';
import { NursingServiceModel } from '@clinify/nursing-services/models/nursing-services.model';
import { OncologyConsultationHistoryModel } from '@clinify/oncology-consultation-history/models/oncology-consultation-history.model';
import { AntenatalDetailsModel } from '@clinify/pregnancy-care/models/antenatal-details.model';
import { LabourDeliveryModel } from '@clinify/pregnancy-care/models/labour-delivery.model';
import { PostnatalModel } from '@clinify/pregnancy-care/models/postnatal.model';
import { RequestProcedureModel } from '@clinify/request-procedures/models/request-procedures.model';
import { BillableRecords } from '@clinify/shared/enums/billable-records';
import { SurgeryModel } from '@clinify/surgeries/models/surgery.model';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
@Entity('preauthorization_details')
export class PreauthorizationDetailsModel {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @ManyToOne(() => HmoProviderModel, (provider) => provider, {
    onDelete: 'SET NULL',
    nullable: true,
  })
  @JoinColumn({ name: 'provider_id' })
  provider?: HmoProviderModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'provider_id', nullable: true })
  providerId?: string;

  @Field({ nullable: true })
  @Column({ name: 'pa_code', nullable: true })
  paCode?: string;

  @Field({ nullable: true })
  @Column({ name: 'pa_status', nullable: true })
  paStatus?: string;

  @Field(() => BillableRecords)
  @Column({ name: 'record_type' })
  recordType: BillableRecords;

  @Field(() => String, { nullable: true })
  @Column({ name: 'ref', nullable: true })
  ref?: string;

  @IsDate()
  @Field({ nullable: true })
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: true })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Field({ nullable: true })
  @Column({ name: 'updated_by' })
  lastModifierId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @ManyToOne(() => HospitalModel, (hospital) => hospital, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'hospital_id' })
  hospital: HospitalModel;

  @Field(() => String)
  @Column({ name: 'hospital_id' })
  hospitalId: string;

  @OneToOne(
    () => AdmissionModel,
    (admission) => admission.preauthorizationDetails,
    { nullable: true, onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'admission_id' })
  admission?: AdmissionModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'admission_id', nullable: true })
  admissionId?: string;

  @OneToOne(
    () => AntenatalDetailsModel,
    (antenatal) => antenatal.preauthorizationDetails,
    { nullable: true, onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'antenatal_details_id' })
  antenatalDetails?: AntenatalDetailsModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'antenatal_details_id', nullable: true })
  antenatalDetailsId?: string;

  @OneToOne(
    () => ConsultationModel,
    (consultation) => consultation.preauthorizationDetails,
    { nullable: true, onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'consultation_id' })
  consultation?: ConsultationModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'consultation_id', nullable: true })
  consultationId?: string;

  @OneToOne(
    () => ImmunizationDetailModel,
    (immunizDetail) => immunizDetail.preauthorizationDetails,
    { nullable: true, onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'immunization_detail_id' })
  immunizationDetail?: ImmunizationDetailModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'immunization_detail_id', nullable: true })
  immunizationDetailId?: string;

  @OneToOne(() => ProfileModel, (profile) => profile.preauthorizationDetails, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'registration_profile_id' })
  registrationProfile?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'registration_profile_id', nullable: true })
  registrationProfileId?: string;

  @ManyToOne(
    () => InvestigationModel,
    (investigation) => investigation.preauthorizationDetails,
    { nullable: true, onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'investigation_id' })
  investigation?: InvestigationModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'investigation_id', nullable: true })
  investigationId?: string;

  @OneToOne(
    () => MedicationDetailsModel,
    (medication) => medication.preauthorizationDetails,
    { nullable: true, onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'medication_details_id' })
  medicationDetails?: MedicationDetailsModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'medication_details_id', nullable: true })
  medicationDetailsId?: string;

  @ManyToOne(() => SurgeryModel, (surgery) => surgery.preauthorizationDetails, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'surgery_id' })
  surgery?: SurgeryModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'surgery_id', nullable: true })
  surgeryId?: string;

  @OneToOne(
    () => NursingServiceModel,
    (nursingService) => nursingService.preauthorizationDetails,
    { nullable: true, onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'nursing_service_id' })
  nursingService?: NursingServiceModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'nursing_service_id', nullable: true })
  nursingServiceId?: string;

  @OneToOne(
    () => PostnatalModel,
    (postnatal) => postnatal.preauthorizationDetails,
    { nullable: true, onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'postnatal_id' })
  postnatal?: PostnatalModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'postnatal_id', nullable: true })
  postnatalId?: string;

  @OneToOne(
    () => LabourDeliveryModel,
    (labourDelivery) => labourDelivery.preauthorizationDetails,
    { nullable: true, onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'labour_delivery_id' })
  labourDelivery?: LabourDeliveryModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'labour_delivery_id', nullable: true })
  labourDeliveryId?: string;

  @OneToOne(
    () => OncologyConsultationHistoryModel,
    (oncologyConsultationHistory) =>
      oncologyConsultationHistory.preauthorizationDetails,
    { nullable: true, onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'oncology_consultation_history_id' })
  oncologyConsultationHistory?: OncologyConsultationHistoryModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'oncology_consultation_history_id', nullable: true })
  oncologyConsultationHistoryId?: string;

  @ManyToOne(
    () => RequestProcedureModel,
    (requestProcedure) => requestProcedure.preauthorizationDetails,
    {
      nullable: true,
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'request_procedure_id' })
  requestProcedure?: RequestProcedureModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'request_procedure_id', nullable: true })
  requestProcedureId?: string;

  constructor(preauthDetails: Partial<PreauthorizationDetailsModel>) {
    Object.assign(this, preauthDetails);
  }
}
