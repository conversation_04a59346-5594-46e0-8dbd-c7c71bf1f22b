import { Lo<PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PreauthorizationDetailsModel } from './models/preauthorization-details.model';
import { AuthorizationModule } from '@clinify/authorization/authorization.module';
import { PreauthorizationDetailsResolver } from '@clinify/preauthorization-details/resolvers/preauthorization-details.resolver';
import { PreauthorizationDetailsService } from '@clinify/preauthorization-details/services/preauthorization-details.service';
import PubSub, { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';

@Module({
  imports: [
    AuthorizationModule,
    TypeOrmModule.forFeature([PreauthorizationDetailsModel]),
  ],
  providers: [
    PreauthorizationDetailsService,
    PreauthorizationDetailsResolver,
    Logger,
    { provide: PUB_SUB, useFactory: () => PubSub },
  ],
  exports: [PreauthorizationDetailsService],
})
export class PreauthorizationDetailsModule {}
