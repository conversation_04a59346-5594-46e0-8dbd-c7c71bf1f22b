import { Field, InputType, registerEnumType } from '@nestjs/graphql';
import { IsOptional } from 'class-validator';
import { BenefitCategory } from '@clinify/hmo-providers/inputs/hmo-plan.input';
import { TimeSortOrder } from '@clinify/pre-authorisations/inputs/pre-authorisation-filter.input';
import { FilterInput } from '@clinify/shared/validators/filter.input';

export enum HmoClaimDateFilterType {
  CreatedDate = 'CreatedDate',
  SubmissionDate = 'SubmissionDate',
  ClaimDate = 'ClaimDate',
}

registerEnumType(HmoClaimDateFilterType, { name: 'HmoClaimDateFilterType' });

@InputType()
export class HmoClaimFilterInput extends FilterInput {
  @Field(() => String, { nullable: true })
  @IsOptional()
  status?: string;

  @Field(() => String, { nullable: true })
  providerId?: string;

  @Field(() => String, { nullable: true })
  hospitalId?: string;

  @Field({ defaultValue: false, nullable: true })
  providerInsight?: boolean;

  @Field({ nullable: true })
  profileId?: string;

  @Field(() => String, { nullable: true })
  providerType?: string;

  @Field({ nullable: true })
  flag?: string;

  @Field(() => HmoClaimDateFilterType, {
    defaultValue: HmoClaimDateFilterType.ClaimDate,
  })
  filterDateField?: HmoClaimDateFilterType;

  @Field({ nullable: true })
  showCompleted?: boolean;

  @Field({ nullable: true })
  showNotCompleted?: boolean;

  @Field(() => BenefitCategory, { nullable: true })
  paymentModel?: BenefitCategory;

  @Field(() => TimeSortOrder, { nullable: true })
  timeSortOrder?: TimeSortOrder;
}
