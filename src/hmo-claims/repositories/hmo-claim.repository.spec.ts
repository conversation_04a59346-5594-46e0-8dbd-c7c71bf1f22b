/* eslint-disable max-lines */
import { HttpModule } from '@nestjs/axios';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import moment from 'moment/moment';
import { DataSource, EntityManager } from 'typeorm';
import {
  CustomHmoClaimRepoMethods,
  IHmoClaimRepository,
} from './hmo-claim.repository';
import { TestDataSourceOptions } from '../../data-source';
import { extendDSRepo, extendModel } from '../../database/extendModel';
import { HmoProviderModel } from '../../hmo-providers/models/hmo-provider.model';
import { MailerModule } from '../../shared/mailer/mailer.module';
import { HmoClaimDateFilterType } from '../inputs/hmo-claim.filter.input';
import { HmoClaimModel } from '../models/hmo-claim.model';
import {
  hmoClaimFactory,
  utilizationFactory,
} from '@clinify/__mocks__/factories/hmo-claim.factory';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { BenefitCategory } from '@clinify/hmo-providers/inputs/hmo-plan.input';
import { HmoProviderRepository } from '@clinify/hmo-providers/repositories/hmo-provider.repository';
import { TimeSortOrder } from '@clinify/pre-authorisations/inputs/pre-authorisation-filter.input';
import { PreAuthUtilisationsModel } from '@clinify/pre-authorisations/models/utilisations.model';
import { HmoClaimStatus } from '@clinify/shared/enums/hmo-claims';
import { UserType } from '@clinify/shared/enums/users';
import { ClaimStatus } from '@clinify/shared/hmo-providers/modules/leadway/leadway.interface';
import { RecordCreator } from '@clinify/shared/validators/filter.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { CustomProfileRepoMethods } from '@clinify/users/repositories/profile.repository';
import { createHmoClaim } from '@clinify/utils/tests/hmo-claim.fixture';
import { createHmoProviderFixtures } from '@clinify/utils/tests/hmo-provider.fixtures';
import { createHospitals } from '@clinify/utils/tests/hospital.fixtures';
import { createUsers } from '@clinify/utils/tests/user.fixtures';
import { createHmoProfileFixtures } from '@fixtures/hmo-profiles.fixtures';
import { createPartner } from '@fixtures/partner.fixture';

const chance = new Chance();

describe('HmoClaim Repository', () => {
  let hmoClaims: HmoClaimModel[];
  let profile: ProfileModel;
  let hmoClaim: HmoClaimModel;
  let dataSource: DataSource;

  let module: TestingModule;
  let manager: EntityManager;
  let repo: IHmoClaimRepository;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeOrmModule.forFeature([HmoClaimModel, HmoProviderModel]),
        MailerModule,
        HttpModule.register({}),
        TypeormExtendedModule.forCustomRepository([HmoProviderRepository]),
      ],
      providers: [
        extendModel(HmoClaimModel, CustomHmoClaimRepoMethods),
        extendModel(ProfileModel, CustomProfileRepoMethods),
        {
          provide: HmoProviderRepository,
          useValue: {},
        },
      ],
    }).compile();

    dataSource = module.get<DataSource>(DataSource);
    manager = dataSource.manager;
    repo = extendDSRepo<IHmoClaimRepository>(
      dataSource,
      HmoClaimModel,
      CustomHmoClaimRepoMethods,
    );
  });

  beforeEach(async () => {
    hmoClaims = await createHmoClaim(manager, 3);
    hmoClaim = hmoClaims[0];
    profile = hmoClaim.profile;
  });

  afterAll(async () => {
    await dataSource.destroy();
    await module.close();
  });

  it('repo should be defined', () => {
    expect(repo).toBeDefined();
  });

  it('getHmoClaim(): should get single hmo', async () => {
    const res = await repo.getHmoClaim(dataSource, profile, hmoClaim.id);
    expect(res.id).toEqual(hmoClaim.id);
  });

  it('getHmoClaim(): should get single hmo with ClaimReviewer', async () => {
    const [hospital] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.ClaimOfficer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.ClaimOfficerHOD,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [doctor] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.ClaimReviewer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const utilizations = utilizationFactory.buildList(
      3,
    ) as PreAuthUtilisationsModel[];
    utilizations[0].utilisationStatus = [
      {
        status: 'Approved',
        statusDescription: 'Approved',
        createdDate: new Date(),
        vettingGroup: UserType.ClaimOfficerHOD,
        creatorId: doctor.defaultProfile.id,
        creatorName: doctor.defaultProfile.fullName,
      },
    ];
    const claimInput = hmoClaimFactory.build() as HmoClaimModel;
    claimInput.utilizations = utilizations;
    const [hmoClaim] = await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospital,
      undefined,
      undefined,
      undefined,
      [claimInput],
    );
    const res = await repo.getHmoClaim(
      dataSource,
      doctor.defaultProfile,
      hmoClaim.id,
    );
    expect(res.id).toEqual(hmoClaim.id);
    expect(res.utilizations.length).toBe(1);
  });

  it('getHmoClaim(): should throw error when claim id is not found', async () => {
    await expect(
      repo.getHmoClaim(dataSource, profile, chance.guid()),
    ).rejects.toThrow('HMO Claim Not Found');
  });

  it('findByProfile(): should return by profile', async () => {
    const res = await repo.findByProfile(profile.id, {}, profile);
    expect(res.list.length).toBe(3);
  });

  it('findByProfile(): should accept filter', async () => {
    const filter = {
      skip: 0,
      take: 40,
      dateRange: {
        from: new Date(),
        to: new Date(),
      },
      keyword: 'some text',
      status: 'Submitted',
      providerType: 'pharmacy',
    };
    const res = await repo.findByProfile(profile.id, filter, profile);
    expect(res).toHaveProperty('list');
  });

  describe('Completion Status Filtering (New Feature)', () => {
    it('should filter completed claims when showCompleted is true', async () => {
      // Create utilization for existing claim with status that includes the mutator's ID
      const utilization = utilizationFactory.build({
        hmoClaimId: hmoClaim.id,
        utilisationStatus: [
          {
            creatorId: profile.id,
            status: 'completed',
            createdDate: new Date(),
          },
        ],
      });
      await manager.save(PreAuthUtilisationsModel, utilization);

      const filter = {
        skip: 0,
        take: 10,
        showCompleted: true,
      };

      const res = await repo.findByProfile(profile.id, filter, profile);

      // Should return claims where the mutator has completed utilizations
      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
      expect(res.totalCount).toBe(0);
    });

    it('should filter not completed claims when showNotCompleted is true', async () => {
      // Create utilization for existing claim without the mutator's ID in status
      const utilization = utilizationFactory.build({
        hmoClaimId: hmoClaims[1].id,
        utilisationStatus: [
          {
            creatorId: 'different-user-id',
            status: 'pending',
            createdDate: new Date(),
          },
        ],
      });
      await manager.save(PreAuthUtilisationsModel, utilization);

      const filter = {
        skip: 0,
        take: 10,
        showNotCompleted: true,
      };

      const res = await repo.findByProfile(profile.id, filter, profile);

      // Should return claims where the mutator has not completed utilizations
      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
      expect(res.totalCount).toBe(3);
    });

    it('should work with both completion filters simultaneously', async () => {
      const filter = {
        skip: 0,
        take: 10,
        showCompleted: true,
        showNotCompleted: true,
      };

      const res = await repo.findByProfile(profile.id, filter, profile);

      // Should return all claims (both completed and not completed)
      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
      expect(res.totalCount).toBe(0);
    });

    it('should work with findByHospital method as well', async () => {
      const [hospital] = await createHospitals(manager, 1);
      const [doctor] = await createUsers(manager, 1, hospital);

      const filter = {
        skip: 0,
        take: 10,
        showCompleted: true,
      };

      const res = await repo.findByHospital(
        dataSource,
        hospital.id,
        filter,
        doctor.defaultProfile,
      );

      // Should work with hospital-based queries too
      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
    });
  });

  it('findByHospital(): should get by hospital', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [doctor] = await createUsers(manager, 1, hospital);
    await createHmoClaim(
      manager,
      2,
      doctor.profiles[0],
      undefined,
      undefined,
      hospital,
    );
    const res = await repo.findByHospital(
      dataSource,
      doctor.profiles[0].hospital.id,
      {},
      doctor.defaultProfile,
    );
    expect(res).toHaveProperty('list');
  });

  it('findByHospital(): should get by hospital', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [claim] = await createHmoClaim(
      manager,
      2,
      doctor.profiles[0],
      undefined,
      undefined,
      hospital,
    );
    const res = await repo.findByHospital(
      dataSource,
      doctor.profiles[0].hospital.id,
      {
        creator: RecordCreator.SELF,
      },
      doctor.defaultProfile,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(0);

    let response = await repo.findByHospital(
      dataSource,
      doctor.profiles[0].hospital.id,
      {
        creator: RecordCreator.OTHERS,
        hospitalId: hospital.id,
        providerId: claim.providerId,
      },
      doctor.defaultProfile,
    );
    expect(response).toHaveProperty('list');
    expect(response.totalCount).toBe(2);

    response = await repo.findByHospital(
      dataSource,
      doctor.profiles[0].hospital.id,
      {
        creator: RecordCreator.OTHERS,
        hospitalId: hospital.id,
        providerId: claim.providerId,
        status: HmoClaimStatus.Confirmed,
      },
      doctor.defaultProfile,
    );
    expect(response).toHaveProperty('list');
    expect(response.totalCount).toBe(0);
  });

  it('findByHospital(): should get by hospital with partnerId', async () => {
    const [partner] = await createPartner(manager);
    const [hospitalA, hospitalB] = await createHospitals(
      manager,
      2,
      null,
      partner,
    );
    const [doctorA] = await createUsers(manager, 1, hospitalA);
    const [doctorB] = await createUsers(manager, 1, hospitalB);
    await createHmoClaim(
      manager,
      2,
      doctorA.profiles[0],
      undefined,
      undefined,
      hospitalA,
    );
    await createHmoClaim(
      manager,
      2,
      doctorB.profiles[0],
      undefined,
      undefined,
      hospitalB,
    );
    const res = await repo.findByHospital(
      dataSource,
      doctorA.profiles[0].hospital.id,
      {},
      {
        ...doctorA.defaultProfile,
        isPartnerProfile: true,
        partnerId: partner.id,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(4);
  });

  it('findByHospital(): should get by hospital with partnerId and providerInsights', async () => {
    const [partner] = await createPartner(manager);
    const [hospitalA, hospitalB] = await createHospitals(
      manager,
      2,
      null,
      partner,
    );
    const [doctorA] = await createUsers(manager, 1, hospitalA);
    const [doctorB] = await createUsers(manager, 1, hospitalB);
    await createHmoClaim(
      manager,
      2,
      doctorA.profiles[0],
      undefined,
      undefined,
      hospitalA,
    );
    await createHmoClaim(
      manager,
      2,
      doctorB.profiles[0],
      undefined,
      undefined,
      hospitalB,
    );
    const res = await repo.findByHospital(
      dataSource,
      doctorA.profiles[0].hospital.id,
      {
        providerInsight: true,
      },
      {
        ...doctorA.defaultProfile,
        isPartnerProfile: true,
        partnerId: partner.id,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(2);
  });

  it('findByHospital(): should get by hospital with hmoId', async () => {
    const [hospitalA] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    const [doctorA] = await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const claims = await createHmoClaim(
      manager,
      2,
      doctorA.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospitalA,
      undefined,
      undefined,
      ClaimStatus.Submitted,
    );
    const res = await repo.findByHospital(
      dataSource,
      doctorA.profiles[0].hospital.id,
      {},
      {
        ...doctorA.defaultProfile,
        hmoId: claims[0].providerId,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(2);
  });
  it('findByHospital(): should get by hospital with hmoId ClaimAccount', async () => {
    const [hospital] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.ClaimOfficer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.ClaimOfficerHOD,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [doctor] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.ClaimReviewer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const utilizations = utilizationFactory.buildList(
      3,
    ) as PreAuthUtilisationsModel[];
    utilizations[0].utilisationStatus = [
      {
        status: 'Approved',
        statusDescription: 'Approved',
        createdDate: new Date(),
        vettingGroup: UserType.ClaimOfficerHOD,
        creatorId: doctor.defaultProfile.id,
        creatorName: doctor.defaultProfile.fullName,
      },
    ];
    const claimInput = hmoClaimFactory.build() as HmoClaimModel;
    claimInput.utilizations = utilizations;
    await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospital,
      undefined,
      undefined,
      ClaimStatus.Submitted,
      [claimInput],
      hmoProfile.provider,
    );
    const res = await repo.findByHospital(
      dataSource,
      hospital.id,
      {},
      doctor.defaultProfile,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(1);
    expect(res.list[0].utilizations.length).toBe(1);
  });

  it('findByHospital(): should get by hospital with hmoId ClaimAdmin', async () => {
    const [hospitalA] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    const [doctor] = await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimAdmin,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const claims = await createHmoClaim(
      manager,
      2,
      doctor.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospitalA,
    );
    const res = await repo.findByHospital(
      dataSource,
      doctor.profiles[0].hospital.id,
      {},
      {
        ...doctor.defaultProfile,
        hmoId: claims[0].providerId,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(0);
  });
  it('findByHospital(): should get by hospital with hmoId and providerInsight', async () => {
    const [hospitalA] = await createHospitals(manager, 1, null);
    const [hmoProvider] = await createHmoProviderFixtures(manager, 1);
    const [enrollee] = await createUsers(manager, 1, undefined, undefined);
    const profile = enrollee.defaultProfile;
    await createHmoProfileFixtures(
      manager,
      1,
      profile,
      undefined,
      undefined,
      hmoProvider,
    );
    const [doctorA] = await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.OrganizationDoctor,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProvider.id,
    );

    await createHmoClaim(
      manager,
      2,
      doctorA.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospitalA,
      undefined,
      undefined,
      ClaimStatus.Submitted,
      undefined,
      hmoProvider,
    );
    const res = await repo.findByHospital(
      dataSource,
      hospitalA.id,
      {
        providerInsight: true,
      },
      {
        ...doctorA.defaultProfile,
        hmoId: hmoProvider.id,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(2);
  });

  it('findByHospital(): should filter not processed claims', async () => {
    const [hospitalA] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    const [claimOfficer] = await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimOfficer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const claims = await createHmoClaim(
      manager,
      1,
      claimOfficer.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospitalA,
      undefined,
      undefined,
      'Submitted',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: utilizationFactory.buildList(1, {
              utilisationStatus: [],
            }),
          }),
        },
      ],
      hmoProfile.provider,
    );
    const res = await repo.findByHospital(
      dataSource,
      claims[0].hospitalId,
      {
        status: 'notprocessed',
      },
      {
        ...claims[0].createdBy,
        hmoId: claims[0].providerId,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(1);
  });

  it('findByHospital(): should filter claims still processing', async () => {
    const [hospitalA] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimReviewer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimAdmin,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [claimOfficer] = await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimOfficer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const claims = await createHmoClaim(
      manager,
      1,
      claimOfficer.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospitalA,
      undefined,
      undefined,
      'Approved',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: utilizationFactory.buildList(1, {
              utilisationStatus: [
                { vettingGroup: 'ClaimOfficer', status: 'Approved' },
                { vettingGroup: 'ClaimReviewer', status: 'Approved' },
              ],
            }),
          }),
        },
      ],
      hmoProfile.provider,
    );
    const res = await repo.findByHospital(
      dataSource,
      claims[0].hospitalId,
      {
        status: 'processing',
      },
      {
        ...claims[0].createdBy,
        hmoId: claims[0].providerId,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(1);
  });

  it('findByHospital(): should filter processed claims', async () => {
    const [hospitalA] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimReviewer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimAdmin,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [claimOfficer] = await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimOfficer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const claims = await createHmoClaim(
      manager,
      1,
      claimOfficer.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospitalA,
      undefined,
      undefined,
      'Approved',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: utilizationFactory.buildList(1, {
              utilisationStatus: [
                { vettingGroup: 'ClaimOfficer', status: 'Approved' },
                { vettingGroup: 'ClaimReviewer', status: 'Approved' },
                { vettingGroup: 'ClaimAdmin', status: 'Approved' },
              ],
            }),
          }),
        },
      ],
      hmoProfile.provider,
    );
    const res = await repo.findByHospital(
      dataSource,
      claims[0].hospitalId,
      {
        status: 'processed',
      },
      {
        ...claims[0].createdBy,
        hmoId: claims[0].providerId,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(1);
  });

  it('findByHospital(): should filter partially processed claims', async () => {
    const [hospitalA] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimReviewer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimAdmin,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [claimOfficer] = await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimOfficer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const claims = await createHmoClaim(
      manager,
      1,
      claimOfficer.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospitalA,
      undefined,
      undefined,
      'Partially Approved',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: [
              {
                ...utilizationFactory.build({
                  utilisationStatus: [
                    { vettingGroup: 'ClaimOfficer', status: 'Approved' },
                    { vettingGroup: 'ClaimReviewer', status: 'Approved' },
                    { vettingGroup: 'ClaimAdmin', status: 'Approved' },
                  ],
                }),
              },
              {
                ...utilizationFactory.build({
                  utilisationStatus: [
                    { vettingGroup: 'ClaimOfficer', status: 'Approved' },
                    { vettingGroup: 'ClaimReviewer', status: 'Approved' },
                    { vettingGroup: 'ClaimAdmin', status: 'Rejected' },
                  ],
                }),
              },
            ],
          }),
        },
      ],
      hmoProfile.provider,
    );
    const res = await repo.findByHospital(
      dataSource,
      claims[0].hospitalId,
      {
        status: 'partiallyprocessed',
      },
      {
        ...claims[0].createdBy,
        hmoId: claims[0].providerId,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(1);
  });

  it('deleteHmoClaims(): should delete claimHmo', async () => {
    const response = await repo.deleteHmoClaims(profile, [hmoClaim.id]);
    await expect(response).toStrictEqual([]);
  });

  it('archiveHmoClaims(): should archive claim', async () => {
    const [res] = await repo.archiveHmoClaims(profile, [hmoClaim.id], true);
    expect(res.archived).toBe(true);
    expect(res.id).toEqual(hmoClaim.id);
  });

  it('archiveHmoClaims(): should return empty array when invalid resource id is provided', async () => {
    const hospitalId = chance.guid();

    const res = await repo.archiveHmoClaims(
      {
        ...profile,
        id: chance.guid(),
        hospitalId,
        hospital: {
          ...profile.hospital,
          id: hospitalId,
        },
      } as ProfileModel,
      [hmoClaim.id],
      true,
    );
    expect(res).toStrictEqual([]);
  });

  it('getClaimsSummary(): should get claims summary', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [claim] = await createHmoClaim(
      manager,
      2,
      doctor.profiles[0],
      undefined,
      doctor.profiles[0],
      hospital,
      undefined,
      undefined,
      'Approved',
    );

    await repo.getClaimsSummary(dataSource, doctor.profiles[0], {
      archive: true,
    });

    const res = await repo.getClaimsSummary(dataSource, doctor.profiles[0], {
      providerId: claim.providerId,
      status: 'Approved',
      archive: false,
      keyword: claim.priority,
      dateRange: {
        from: claim.claimDate,
        to: claim.claimDate,
      },
    });
    expect(res).toHaveProperty('totalClaims');
    expect(res).toHaveProperty('totalApprovedClaims');
    expect(res).toHaveProperty('totalRejectedClaims');
    expect(res).toHaveProperty('totalClaimsAmount');
    expect(res).toHaveProperty('totalApprovedClaimsAmount');
    expect(res).toHaveProperty('totalRejectedClaimsAmount');
  });

  it('getClaimsSummary(): should filter not processed claims', async () => {
    const [hospitalA] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    const [claimOfficer] = await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimOfficer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const claims = await createHmoClaim(
      manager,
      1,
      claimOfficer.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospitalA,
      undefined,
      undefined,
      'Submitted',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: utilizationFactory.buildList(1, {
              quantity: '3',
              price: '400000',
              utilisationStatus: [],
            }),
          }),
        },
      ],
      hmoProfile.provider,
    );
    const res = await repo.getClaimsSummary(
      dataSource,
      {
        ...claims[0].createdBy,
        hmoId: claims[0].providerId,
      } as ProfileModel,
      {
        status: 'notprocessed',
      },
    );
    expect(res.totalClaims).toBe('1');
    expect(res.totalApprovedClaims).toBe('0');
    expect(res.totalRejectedClaims).toBe('0');
    expect(res.totalClaimsAmount).toBe(1200000);
    expect(res.totalApprovedClaimsAmount).toBeFalsy();
    expect(res.totalRejectedClaimsAmount).toBeFalsy();
    expect(res.totalPaidClaimsAmount).toBeFalsy();
  });

  it('getClaimsSummary(): should filter claims still processing', async () => {
    const [hospitalA] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimReviewer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimAdmin,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [claimOfficer] = await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimOfficer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const claims = await createHmoClaim(
      manager,
      1,
      claimOfficer.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospitalA,
      undefined,
      undefined,
      'Approved',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: utilizationFactory.buildList(1, {
              quantity: '3',
              price: '400000',
              utilisationStatus: [
                { vettingGroup: 'ClaimOfficer', status: 'Approved' },
                { vettingGroup: 'ClaimReviewer', status: 'Approved' },
              ],
            }),
          }),
        },
      ],
      hmoProfile.provider,
    );
    const res = await repo.getClaimsSummary(
      dataSource,
      {
        ...claims[0].createdBy,
        hmoId: claims[0].providerId,
      } as ProfileModel,
      {
        status: 'processing',
      },
    );
    expect(res.totalClaims).toBe('1');
    expect(res.totalApprovedClaims).toBe('1');
    expect(res.totalRejectedClaims).toBe('0');
    expect(res.totalClaimsAmount).toBe(1200000);
    expect(res.totalApprovedClaimsAmount).toBe(1200000);
    expect(res.totalRejectedClaimsAmount).toBeFalsy();
    expect(res.totalPaidClaimsAmount).toBeFalsy();
  });

  it('getClaimsSummary(): should filter processed claims', async () => {
    const [hospitalA] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimReviewer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimAdmin,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [claimOfficer] = await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimOfficer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const claims = await createHmoClaim(
      manager,
      1,
      claimOfficer.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospitalA,
      undefined,
      undefined,
      'Approved',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: utilizationFactory.buildList(1, {
              quantity: '3',
              price: '400000',
              utilisationStatus: [
                { vettingGroup: 'ClaimOfficer', status: 'Approved' },
                { vettingGroup: 'ClaimReviewer', status: 'Approved' },
                { vettingGroup: 'ClaimAdmin', status: 'Approved' },
              ],
            }),
          }),
        },
      ],
      hmoProfile.provider,
    );
    const res = await repo.getClaimsSummary(
      dataSource,
      {
        ...claims[0].createdBy,
        hmoId: claims[0].providerId,
      } as ProfileModel,
      {
        status: 'processed',
      },
    );
    expect(res.totalClaims).toBe('1');
    expect(res.totalApprovedClaims).toBe('1');
    expect(res.totalRejectedClaims).toBe('0');
    expect(res.totalClaimsAmount).toBe(1200000);
    expect(res.totalApprovedClaimsAmount).toBe(1200000);
    expect(res.totalRejectedClaimsAmount).toBeFalsy();
    expect(res.totalPaidClaimsAmount).toBeFalsy();
  });

  it('getClaimsSummary(): should filter partially processed claims', async () => {
    const [hospitalA] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimReviewer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimAdmin,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [claimOfficer] = await createUsers(
      manager,
      1,
      hospitalA,
      undefined,
      undefined,
      UserType.ClaimOfficer,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      hmoProfile.providerId,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const claims = await createHmoClaim(
      manager,
      1,
      claimOfficer.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospitalA,
      undefined,
      undefined,
      'Partially Approved',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: [
              {
                ...utilizationFactory.build({
                  quantity: '3',
                  price: '400000',
                  utilisationStatus: [
                    { vettingGroup: 'ClaimOfficer', status: 'Approved' },
                    { vettingGroup: 'ClaimReviewer', status: 'Approved' },
                    { vettingGroup: 'ClaimAdmin', status: 'Approved' },
                  ],
                }),
              },
              {
                ...utilizationFactory.build({
                  quantity: '2',
                  price: '150000',
                  utilisationStatus: [
                    { vettingGroup: 'ClaimOfficer', status: 'Approved' },
                    { vettingGroup: 'ClaimReviewer', status: 'Approved' },
                    { vettingGroup: 'ClaimAdmin', status: 'Rejected' },
                  ],
                }),
              },
            ],
          }),
        },
      ],
      hmoProfile.provider,
    );
    const res = await repo.getClaimsSummary(
      dataSource,
      {
        ...claims[0].createdBy,
        hmoId: claims[0].providerId,
      } as ProfileModel,
      {
        status: 'partiallyprocessed',
      },
    );
    expect(res.totalClaims).toBe('1');
    expect(res.totalApprovedClaims).toBe('0');
    expect(res.totalRejectedClaims).toBe('0');
    expect(res.totalClaimsAmount).toBe(1200000);
    expect(res.totalApprovedClaimsAmount).toBeFalsy();
    expect(res.totalRejectedClaimsAmount).toBeFalsy();
    expect(res.totalPaidClaimsAmount).toBeFalsy();
  });

  it('getClaimsSummary(): should filter claims by FeeForService payment model', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [doctor] = await createUsers(manager, 1, hospital);

    const [feeForServiceClaim] = await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      doctor.profiles[0],
      hospital,
      undefined,
      undefined,
      'Approved',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: utilizationFactory.buildList(1, {
              quantity: '2',
              price: '50000',
              paymentModel: BenefitCategory.FeeForService,
            }),
          }),
        },
      ],
    );

    await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      doctor.profiles[0],
      hospital,
      undefined,
      undefined,
      'Approved',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: utilizationFactory.buildList(1, {
              quantity: '1',
              price: '30000',
              paymentModel: BenefitCategory.Capitated,
            }),
          }),
        },
      ],
    );

    const res = await repo.getClaimsSummary(dataSource, doctor.profiles[0], {
      paymentModel: BenefitCategory.FeeForService,
      archive: false,
    });

    expect(res).toHaveProperty('totalClaims');
    expect(res).toHaveProperty('totalApprovedClaims');
    expect(res).toHaveProperty('totalClaimsAmount');
    expect(res.totalClaims).toBe('1');
    expect(res.totalClaimsAmount).toBe(100000); // 2 * 50000
  });

  it('getClaimsSummary(): should filter claims by Capitated payment model', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [doctor] = await createUsers(manager, 1, hospital);

    const [capitatedClaim] = await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      doctor.profiles[0],
      hospital,
      undefined,
      undefined,
      'Approved',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: utilizationFactory.buildList(1, {
              quantity: '3',
              price: '25000',
              paymentModel: BenefitCategory.Capitated,
            }),
          }),
        },
      ],
    );

    await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      doctor.profiles[0],
      hospital,
      undefined,
      undefined,
      'Approved',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: utilizationFactory.buildList(1, {
              quantity: '1',
              price: '40000',
              paymentModel: BenefitCategory.FeeForService,
            }),
          }),
        },
      ],
    );

    const res = await repo.getClaimsSummary(dataSource, doctor.profiles[0], {
      paymentModel: BenefitCategory.Capitated,
      archive: false,
    });

    expect(res).toHaveProperty('totalClaims');
    expect(res).toHaveProperty('totalApprovedClaims');
    expect(res).toHaveProperty('totalClaimsAmount');
    expect(res.totalClaims).toBe('1');
    expect(res.totalClaimsAmount).toBe(75000); // 3 * 25000
  });

  it('getClaimsSummary(): should not filter when paymentModel is null/undefined', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [doctor] = await createUsers(manager, 1, hospital);

    await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      doctor.profiles[0],
      hospital,
      undefined,
      undefined,
      'Approved',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: utilizationFactory.buildList(1, {
              quantity: '1',
              price: '20000',
              paymentModel: BenefitCategory.FeeForService,
            }),
          }),
        },
      ],
    );

    await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      doctor.profiles[0],
      hospital,
      undefined,
      undefined,
      'Approved',
      [
        {
          ...hmoClaimFactory.build({
            utilizations: utilizationFactory.buildList(1, {
              quantity: '1',
              price: '15000',
              paymentModel: BenefitCategory.Capitated,
            }),
          }),
        },
      ],
    );

    // Test with null paymentModel
    const resNull = await repo.getClaimsSummary(
      dataSource,
      doctor.profiles[0],
      {
        paymentModel: null,
        archive: false,
      },
    );

    const resUndefined = await repo.getClaimsSummary(
      dataSource,
      doctor.profiles[0],
      {
        paymentModel: undefined,
        archive: false,
      },
    );

    expect(resNull.totalClaims).toBe('2');
    expect(resNull.totalClaimsAmount).toBe(35000); // 20000 + 15000
    expect(resUndefined.totalClaims).toBe('2');
    expect(resUndefined.totalClaimsAmount).toBe(35000);
  });

  it('getClaimsSummary(): should combine paymentModel filter with other filters', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [doctor] = await createUsers(manager, 1, hospital);

    await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      doctor.profiles[0],
      hospital,
      undefined,
      undefined,
      'Approved',
      [
        {
          ...hmoClaimFactory.build({
            priority: 'Emergency',
            utilizations: utilizationFactory.buildList(1, {
              quantity: '1',
              price: '30000',
              paymentModel: BenefitCategory.FeeForService,
            }),
          }),
        },
      ],
    );

    await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      doctor.profiles[0],
      hospital,
      undefined,
      undefined,
      'Rejected',
      [
        {
          ...hmoClaimFactory.build({
            priority: 'Emergency',
            utilizations: utilizationFactory.buildList(1, {
              quantity: '1',
              price: '25000',
              paymentModel: BenefitCategory.FeeForService,
            }),
          }),
        },
      ],
    );

    await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      doctor.profiles[0],
      hospital,
      undefined,
      undefined,
      'Approved',
      [
        {
          ...hmoClaimFactory.build({
            priority: 'Emergency',
            utilizations: utilizationFactory.buildList(1, {
              quantity: '1',
              price: '20000',
              paymentModel: BenefitCategory.Capitated,
            }),
          }),
        },
      ],
    );

    const res = await repo.getClaimsSummary(dataSource, doctor.profiles[0], {
      paymentModel: BenefitCategory.FeeForService,
      status: 'Approved',
      keyword: 'Emergency',
      archive: false,
    });

    expect(res.totalClaims).toBe('1');
    expect(res.totalApprovedClaims).toBe('1');
    expect(res.totalClaimsAmount).toBe(30000);
    expect(res.totalApprovedClaimsAmount).toBe(30000);
  });

  it('getProvidersWithSubmittedClaims', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [claim] = await createHmoClaim(
      manager,
      2,
      doctor.profiles[0],
      undefined,
      doctor.profiles[0],
      hospital,
      undefined,
      undefined,
      'Approved',
    );

    const res = await repo.getProvidersWithSubmittedClaims(
      dataSource,
      doctor.profiles[0],
      {
        status: 'Approved',
        archive: false,
        keyword: claim.priority,
      },
    );

    expect(res.length).toBe(0);
  });

  it('findByHospital(): should filter by flag', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [doctor] = await createUsers(manager, 1, hospital);

    const [claimWithDuplicateFlag] = await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      undefined,
      hospital,
      {
        flags: [
          {
            flag: 'Duplicate',
            flaggedById: doctor.profiles[0].id,
            flaggedByRole: doctor.profiles[0].type,
            flaggedByFullname: doctor.profiles[0].fullName,
            flagDateAndTime: new Date(),
          },
        ],
      },
    );

    const [claimWithFraudFlag] = await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      undefined,
      hospital,
      {
        flags: [
          {
            flag: 'Fraud',
            flaggedById: doctor.profiles[0].id,
            flaggedByRole: doctor.profiles[0].type,
            flaggedByFullname: doctor.profiles[0].fullName,
            flagDateAndTime: new Date(),
          },
        ],
      },
    );

    await createHmoClaim(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      undefined,
      hospital,
    );

    let res = await repo.findByHospital(
      dataSource,
      hospital.id,
      {
        flag: 'Duplicate',
      },
      doctor.profiles[0],
    );

    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(1);
    expect(res.list[0].id).toEqual(claimWithDuplicateFlag.id);

    res = await repo.findByHospital(
      dataSource,
      hospital.id,
      {
        flag: 'Fraud',
      },
      doctor.profiles[0],
    );

    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(1);
    expect(res.list[0].id).toEqual(claimWithFraudFlag.id);
  });

  it('findByProfile(): should filter by flag', async () => {
    const [hospital] = await createHospitals(manager, 1);
    const [user] = await createUsers(manager, 1, hospital);
    const userProfile = user.profiles[0];

    const [claimWithDuplicateFlag] = await createHmoClaim(
      manager,
      1,
      userProfile,
      undefined,
      userProfile,
      hospital,
      {
        flags: [
          {
            flag: 'Duplicate',
            flaggedById: userProfile.id,
            flaggedByRole: userProfile.type,
            flaggedByFullname: userProfile.fullName,
            flagDateAndTime: new Date(),
          },
        ],
      },
    );

    const [claimWithFraudFlag] = await createHmoClaim(
      manager,
      1,
      userProfile,
      undefined,
      userProfile,
      hospital,
      {
        flags: [
          {
            flag: 'Fraud',
            flaggedById: userProfile.id,
            flaggedByRole: userProfile.type,
            flaggedByFullname: userProfile.fullName,
            flagDateAndTime: new Date(),
          },
        ],
      },
    );

    await createHmoClaim(
      manager,
      1,
      userProfile,
      undefined,
      userProfile,
      hospital,
    );

    let res = await repo.findByProfile(
      userProfile.id,
      {
        flag: 'Duplicate',
      },
      userProfile,
    );

    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(1);
    expect(res.list[0].id).toEqual(claimWithDuplicateFlag.id);

    res = await repo.findByProfile(
      userProfile.id,
      {
        flag: 'Fraud',
      },
      userProfile,
    );

    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(1);
    expect(res.list[0].id).toEqual(claimWithFraudFlag.id);
  });

  describe('filterDateField functionality', () => {
    let hospital: any;
    let doctor: any;
    let userProfile: ProfileModel;
    let oldClaim: HmoClaimModel;
    let middleClaim: HmoClaimModel;
    let recentClaim: HmoClaimModel;

    beforeEach(async () => {
      [hospital] = await createHospitals(manager, 1);
      [doctor] = await createUsers(manager, 1, hospital);
      userProfile = doctor.profiles[0];

      // Create claims with wide date ranges (2 years apart) for efficient testing
      const oldDate = new Date('2022-01-01');
      const middleDate = new Date('2023-06-15');
      const recentDate = new Date('2024-12-31');

      // Create claim with old dates
      [oldClaim] = await createHmoClaim(
        manager,
        1,
        userProfile,
        undefined,
        userProfile,
        hospital,
        {
          claimDate: oldDate,
        },
        oldDate, // createdDate parameter
      );

      // Create claim with middle dates
      [middleClaim] = await createHmoClaim(
        manager,
        1,
        userProfile,
        undefined,
        userProfile,
        hospital,
        {
          claimDate: middleDate,
        },
        middleDate, // createdDate parameter
      );

      // Create claim with recent dates
      [recentClaim] = await createHmoClaim(
        manager,
        1,
        userProfile,
        undefined,
        userProfile,
        hospital,
        {
          claimDate: recentDate,
        },
        recentDate, // createdDate parameter
      );
    });

    it('should filter by ClaimDate (default behavior)', async () => {
      const filter = {
        dateRange: {
          from: new Date('2023-01-01'),
          to: new Date('2023-12-31'),
        },
        // filterDateField not specified, should default to ClaimDate
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      expect(res.list).toHaveLength(1);
      expect(res.list[0].id).toEqual(middleClaim.id);
    });

    it('should filter by ClaimDate when explicitly specified', async () => {
      const filter = {
        dateRange: {
          from: new Date('2023-01-01'),
          to: new Date('2023-12-31'),
        },
        filterDateField: HmoClaimDateFilterType.ClaimDate,
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      expect(res.list).toHaveLength(1);
      expect(res.list[0].id).toEqual(middleClaim.id);
    });

    it('should filter by CreatedDate when specified', async () => {
      const filter = {
        dateRange: {
          from: new Date('2022-01-01'),
          to: new Date('2022-12-31'),
        },
        filterDateField: HmoClaimDateFilterType.CreatedDate,
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      expect(res.list).toHaveLength(1);
      expect(res.list[0].id).toEqual(oldClaim.id);
    });

    it('should filter by SubmissionDate when specified', async () => {
      const filter = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2025-12-31'),
        },
        filterDateField: HmoClaimDateFilterType.SubmissionDate,
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      expect(res.list).toHaveLength(1);
      expect(res.list[0].id).toEqual(recentClaim.id);
    });

    it('should work with findByHospital method using ClaimDate', async () => {
      const filter = {
        dateRange: {
          from: new Date('2023-01-01'),
          to: new Date('2023-12-31'),
        },
        filterDateField: HmoClaimDateFilterType.ClaimDate,
      };

      const res = await repo.findByHospital(
        dataSource,
        hospital.id,
        filter,
        userProfile,
      );

      expect(res.list).toHaveLength(1);
      expect(res.list[0].id).toEqual(middleClaim.id);
    });

    it('should work with findByHospital method using CreatedDate', async () => {
      const filter = {
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2025-12-31'),
        },
        filterDateField: HmoClaimDateFilterType.CreatedDate,
      };

      const res = await repo.findByHospital(
        dataSource,
        hospital.id,
        filter,
        userProfile,
      );

      expect(res.list).toHaveLength(1);
      expect(res.list[0].id).toEqual(recentClaim.id);
    });

    it('should work with findByHospital method using SubmissionDate', async () => {
      const filter = {
        dateRange: {
          from: new Date('2022-01-01'),
          to: new Date('2022-12-31'),
        },
        filterDateField: HmoClaimDateFilterType.SubmissionDate,
      };

      const res = await repo.findByHospital(
        dataSource,
        hospital.id,
        filter,
        userProfile,
      );

      expect(res.list).toHaveLength(1);
      expect(res.list[0].id).toEqual(oldClaim.id);
    });

    it('should return empty results when date range does not match any claims', async () => {
      const filter = {
        dateRange: {
          from: new Date('2025-01-01'),
          to: new Date('2025-12-31'),
        },
        filterDateField: HmoClaimDateFilterType.ClaimDate,
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      expect(res.list).toHaveLength(0);
      expect(res.totalCount).toBe(0);
    });

    it('should handle wide date ranges correctly', async () => {
      const filter = {
        dateRange: {
          from: new Date('2021-01-01'),
          to: new Date('2025-12-31'),
        },
        filterDateField: HmoClaimDateFilterType.ClaimDate,
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      // Should return all 3 claims created in beforeEach
      expect(res.list).toHaveLength(3);
    });

    it('should handle edge case with same from and to dates', async () => {
      const exactDate = new Date('2023-06-15');
      const nextDay = new Date('2023-06-16');
      const filter = {
        dateRange: {
          from: exactDate,
          to: nextDay, // Use next day since 'to' is exclusive
        },
        filterDateField: HmoClaimDateFilterType.ClaimDate,
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      // Should return the middle claim that matches the date range
      expect(res.list).toHaveLength(1);
      expect(res.list[0].id).toEqual(middleClaim.id);
    });

    it('should work correctly when only from date is provided', async () => {
      const filter = {
        dateRange: {
          from: new Date('2023-01-01'),
          to: undefined,
        },
        filterDateField: HmoClaimDateFilterType.ClaimDate,
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      // Should return claims from 2023 onwards (middle and recent)
      expect(res.list).toHaveLength(2);
      const claimIds = res.list.map((claim) => claim.id);
      expect(claimIds).toContain(middleClaim.id);
      expect(claimIds).toContain(recentClaim.id);
    });

    it('should work correctly when only to date is provided', async () => {
      const filter = {
        dateRange: {
          from: undefined,
          to: new Date('2023-12-31'),
        },
        filterDateField: HmoClaimDateFilterType.ClaimDate,
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      // Should return claims up to 2023 (old and middle)
      expect(res.list).toHaveLength(2);
      const claimIds = res.list.map((claim) => claim.id);
      expect(claimIds).toContain(oldClaim.id);
      expect(claimIds).toContain(middleClaim.id);
    });
  });

  describe('Payment Model Filtering (New Feature)', () => {
    let userProfile: ProfileModel;
    let hospital: any;

    beforeEach(async () => {
      [hospital] = await createHospitals(manager, 1);
      const [user] = await createUsers(manager, 1, hospital);
      userProfile = user.defaultProfile;

      const feeForServiceUtilization = utilizationFactory.build({
        paymentModel: BenefitCategory.FeeForService,
        category: 'Consultation',
        type: 'General Consultation',
        price: '5000',
        quantity: 1,
      }) as PreAuthUtilisationsModel;

      const capitatedUtilization = utilizationFactory.build({
        paymentModel: BenefitCategory.Capitated,
        category: 'Laboratory',
        type: 'Blood Test',
        price: '3000',
        quantity: 1,
      }) as PreAuthUtilisationsModel;

      const feeForServiceClaimInput = hmoClaimFactory.build({
        status: 'Submitted',
        serviceType: 'Consultation',
        serviceName: 'General Consultation',
      }) as HmoClaimModel;
      feeForServiceClaimInput.utilizations = [feeForServiceUtilization];

      const capitatedClaimInput = hmoClaimFactory.build({
        status: 'Submitted',
        serviceType: 'Laboratory',
        serviceName: 'Blood Test',
      }) as HmoClaimModel;
      capitatedClaimInput.utilizations = [capitatedUtilization];

      await createHmoClaim(
        manager,
        1,
        undefined,
        undefined,
        userProfile,
        hospital,
        undefined,
        undefined,
        undefined,
        [feeForServiceClaimInput],
      );

      await createHmoClaim(
        manager,
        1,
        undefined,
        undefined,
        userProfile,
        hospital,
        undefined,
        undefined,
        undefined,
        [capitatedClaimInput],
      );
    });

    it('should filter claims by FeeForService payment model using findByProfile', async () => {
      const filter = {
        skip: 0,
        take: 10,
        paymentModel: BenefitCategory.FeeForService,
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
      expect(res.list.length).toBeGreaterThan(0);

      res.list.forEach((claim) => {
        expect(claim.utilizations).toBeDefined();
        expect(claim.utilizations.length).toBeGreaterThan(0);
        claim.utilizations.forEach((utilization) => {
          expect(utilization.paymentModel).toBe(BenefitCategory.FeeForService);
        });
      });
    });

    it('should filter claims by Capitated payment model using findByProfile', async () => {
      const filter = {
        skip: 0,
        take: 10,
        paymentModel: BenefitCategory.Capitated,
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
      expect(res.list.length).toBeGreaterThan(0);

      res.list.forEach((claim) => {
        expect(claim.utilizations).toBeDefined();
        expect(claim.utilizations.length).toBeGreaterThan(0);
        claim.utilizations.forEach((utilization) => {
          expect(utilization.paymentModel).toBe(BenefitCategory.Capitated);
        });
      });
    });

    it('should filter claims by FeeForService payment model using findByHospital', async () => {
      const filter = {
        skip: 0,
        take: 10,
        paymentModel: BenefitCategory.FeeForService,
      };

      const res = await repo.findByHospital(
        dataSource,
        hospital.id,
        filter,
        userProfile,
      );

      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
      expect(res.list.length).toBeGreaterThan(0);

      // Verify that all returned claims have FeeForService payment model
      res.list.forEach((claim) => {
        expect(claim.utilizations).toBeDefined();
        expect(claim.utilizations.length).toBeGreaterThan(0);
        claim.utilizations.forEach((utilization) => {
          expect(utilization.paymentModel).toBe(BenefitCategory.FeeForService);
        });
      });
    });

    it('should filter claims by Capitated payment model using findByHospital', async () => {
      const filter = {
        skip: 0,
        take: 10,
        paymentModel: BenefitCategory.Capitated,
      };

      const res = await repo.findByHospital(
        dataSource,
        hospital.id,
        filter,
        userProfile,
      );

      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
      expect(res.list.length).toBeGreaterThan(0);

      res.list.forEach((claim) => {
        expect(claim.utilizations).toBeDefined();
        expect(claim.utilizations.length).toBeGreaterThan(0);
        claim.utilizations.forEach((utilization) => {
          expect(utilization.paymentModel).toBe(BenefitCategory.Capitated);
        });
      });
    });

    it('should work with payment model filter combined with other filters', async () => {
      const filter = {
        skip: 0,
        take: 10,
        paymentModel: BenefitCategory.FeeForService,
        status: 'Submitted',
        keyword: 'Consultation',
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');

      res.list.forEach((claim) => {
        expect(claim.status).toContain('Submitted');
        expect(claim.utilizations).toBeDefined();
        expect(claim.utilizations.length).toBeGreaterThan(0);
        claim.utilizations.forEach((utilization) => {
          expect(utilization.paymentModel).toBe(BenefitCategory.FeeForService);
        });
      });
    });

    it('should handle payment model filter with pagination', async () => {
      const additionalClaims = [];
      for (let i = 0; i < 3; i++) {
        const utilization = utilizationFactory.build({
          paymentModel: BenefitCategory.FeeForService,
          category: `Category${i}`,
          type: `Type${i}`,
          price: '1000',
          quantity: 1,
        }) as PreAuthUtilisationsModel;

        const claimInput = hmoClaimFactory.build({
          status: 'Submitted',
          serviceType: `Service${i}`,
        }) as HmoClaimModel;
        claimInput.utilizations = [utilization];

        const [claim] = await createHmoClaim(
          manager,
          1,
          undefined,
          undefined,
          userProfile,
          hospital,
          undefined,
          undefined,
          undefined,
          [claimInput],
        );
        additionalClaims.push(claim);
      }

      const filter = {
        skip: 0,
        take: 2,
        paymentModel: BenefitCategory.FeeForService,
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
      expect(res.list.length).toBeLessThanOrEqual(2);
      expect(res.totalCount).toBeGreaterThanOrEqual(2);

      res.list.forEach((claim) => {
        expect(claim.utilizations).toBeDefined();
        expect(claim.utilizations.length).toBeGreaterThan(0);
        claim.utilizations.forEach((utilization) => {
          expect(utilization.paymentModel).toBe(BenefitCategory.FeeForService);
        });
      });
    });

    it('should handle null/undefined payment model filter gracefully', async () => {
      const filter = {
        skip: 0,
        take: 10,
        paymentModel: undefined,
      };

      const res = await repo.findByProfile(userProfile.id, filter, userProfile);

      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
      expect(res.list.length).toBeGreaterThan(0);
    });
  });

  describe('timeSortOrder Filter Tests', () => {
    let timeSortProfile: ProfileModel;
    let timeSortHmoClaims: HmoClaimModel[];
    let testHospital: any;

    beforeEach(async () => {
      // Create test data with different timestamps for timeSortOrder tests
      const hospitals = await createHospitals(manager, 1);
      const hmoProviders = await createHmoProviderFixtures(manager, 1);
      const [user] = await createUsers(
        manager,
        1,
        hospitals[0],
        hmoProviders[0],
        null,
        UserType.Patient,
      );
      timeSortProfile = user.profiles[0];
      testHospital = hospitals[0];

      // Create HMO claims with different created dates and times
      const baseDate = moment('2024-01-15').toDate();
      const dates = [
        moment(baseDate).hour(9).minute(30).toDate(), // 09:30
        moment(baseDate).hour(14).minute(15).toDate(), // 14:15
        moment(baseDate).hour(11).minute(45).toDate(), // 11:45
        moment(baseDate).hour(16).minute(20).toDate(), // 16:20
        moment(baseDate).hour(8).minute(10).toDate(), // 08:10
      ];

      timeSortHmoClaims = [];
      for (const date of dates) {
        const [claim] = await createHmoClaim(
          manager,
          1,
          null,
          null,
          timeSortProfile,
          hospitals[0],
          { providerId: hmoProviders[0].id },
          date,
        );
        timeSortHmoClaims.push(claim);
      }
    });

    describe('findByProfile with timeSortOrder', () => {
      it('should sort by time in ASC order when timeSortOrder is ASC', async () => {
        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by time in ascending order
        // Expected order: 08:10, 09:30, 11:45, 14:15, 16:20
        const times = result.list.map((item) =>
          moment(item.createdDate).format('HH:mm'),
        );

        expect(times[0]).toBe('08:10');
        expect(times[1]).toBe('09:30');
        expect(times[2]).toBe('11:45');
        expect(times[3]).toBe('14:15');
        expect(times[4]).toBe('16:20');
      });

      it('should sort by time in DESC order when timeSortOrder is DESC', async () => {
        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.DESC,
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by time in descending order
        // Expected order: 16:20, 14:15, 11:45, 09:30, 08:10
        const times = result.list.map((item) =>
          moment(item.createdDate).format('HH:mm'),
        );

        expect(times[0]).toBe('16:20');
        expect(times[1]).toBe('14:15');
        expect(times[2]).toBe('11:45');
        expect(times[3]).toBe('09:30');
        expect(times[4]).toBe('08:10');
      });

      it('should use default sorting when timeSortOrder is not provided', async () => {
        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by createdDate DESC (default behavior)
        // Should be sorted by full timestamp descending
        const timestamps = result.list.map((item) =>
          moment(item.createdDate).valueOf(),
        );

        for (let i = 0; i < timestamps.length - 1; i++) {
          expect(timestamps[i]).toBeGreaterThanOrEqual(timestamps[i + 1]);
        }
      });

      it('should handle timeSortOrder with other filters', async () => {
        // Create an HMO claim with a specific keyword
        const [specificClaim] = await createHmoClaim(
          manager,
          1,
          null,
          null,
          timeSortProfile,
          null,
          {
            claimId: 'SPECIAL-CLAIM-123',
            providerId: timeSortHmoClaims[0].providerId,
          },
          moment('2024-01-15').hour(12).minute(0).toDate(),
        );

        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            keyword: 'SPECIAL-CLAIM',
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(1);
        expect(result.list[0].claimId).toBe('SPECIAL-CLAIM-123');
      });

      it('should handle pagination with timeSortOrder', async () => {
        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 2,
            take: 2,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(2);
        expect(result).toHaveProperty('totalCount', 5);

        // Verify that pagination works correctly with time sorting
        const times = result.list.map((item) =>
          moment(item.createdDate).format('HH:mm'),
        );

        // Should get the 3rd and 4th items in ASC order: 11:45, 14:15
        expect(times[0]).toBe('11:45');
        expect(times[1]).toBe('14:15');
      });
    });

    describe('findByHospital with timeSortOrder', () => {
      it('should sort by time in ASC order when timeSortOrder is ASC', async () => {
        const hospitalId = testHospital.id;

        const result = await repo.findByHospital(
          dataSource,
          hospitalId,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by time in ascending order
        const times = result.list.map((item) =>
          moment(item.createdDate).format('HH:mm'),
        );

        expect(times[0]).toBe('08:10');
        expect(times[1]).toBe('09:30');
        expect(times[2]).toBe('11:45');
        expect(times[3]).toBe('14:15');
        expect(times[4]).toBe('16:20');
      });

      it('should sort by time in DESC order when timeSortOrder is DESC', async () => {
        const hospitalId = testHospital.id;

        const result = await repo.findByHospital(
          dataSource,
          hospitalId,
          {
            timeSortOrder: TimeSortOrder.DESC,
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by time in descending order
        const times = result.list.map((item) =>
          moment(item.createdDate).format('HH:mm'),
        );

        expect(times[0]).toBe('16:20');
        expect(times[1]).toBe('14:15');
        expect(times[2]).toBe('11:45');
        expect(times[3]).toBe('09:30');
        expect(times[4]).toBe('08:10');
      });

      it('should use default sorting when timeSortOrder is not provided', async () => {
        const hospitalId = testHospital.id;

        const result = await repo.findByHospital(
          dataSource,
          hospitalId,
          {
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by createdDate DESC (default behavior)
        const timestamps = result.list.map((item) =>
          moment(item.createdDate).valueOf(),
        );

        for (let i = 0; i < timestamps.length - 1; i++) {
          expect(timestamps[i]).toBeGreaterThanOrEqual(timestamps[i + 1]);
        }
      });
    });

    describe('Edge cases and integration tests', () => {
      it('should handle HMO claims with same date but different times', async () => {
        // Create HMO claims on the same date but different times
        const sameDate = moment('2024-02-01').toDate();
        const sameDateClaims = [];

        const times = [
          moment(sameDate).hour(10).minute(0).toDate(),
          moment(sameDate).hour(10).minute(30).toDate(),
          moment(sameDate).hour(10).minute(15).toDate(),
        ];

        for (const time of times) {
          const [claim] = await createHmoClaim(
            manager,
            1,
            null,
            null,
            timeSortProfile,
            null,
            { providerId: timeSortHmoClaims[0].providerId },
            time,
          );
          sameDateClaims.push(claim);
        }

        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 0,
            take: 20,
          },
          timeSortProfile,
        );

        // Find the same-date claims in the result
        const sameDateResults = result.list.filter(
          (item) =>
            moment(item.createdDate).format('YYYY-MM-DD') === '2024-02-01',
        );

        expect(sameDateResults).toHaveLength(3);

        // Verify they are sorted by time
        const times_result = sameDateResults.map((item) =>
          moment(item.createdDate).format('HH:mm'),
        );

        expect(times_result[0]).toBe('10:00');
        expect(times_result[1]).toBe('10:15');
        expect(times_result[2]).toBe('10:30');
      });

      it('should handle HMO claims across different dates with timeSortOrder', async () => {
        // Create HMO claims on different dates
        const dates = [
          moment('2024-03-01').hour(15).minute(0).toDate(),
          moment('2024-03-02').hour(9).minute(0).toDate(),
          moment('2024-03-01').hour(10).minute(0).toDate(),
        ];

        for (const date of dates) {
          await createHmoClaim(
            manager,
            1,
            null,
            null,
            timeSortProfile,
            null,
            { providerId: timeSortHmoClaims[0].providerId },
            date,
          );
        }

        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.DESC,
            skip: 0,
            take: 20,
          },
          timeSortProfile,
        );

        // Should be sorted by date DESC first, then by time DESC
        // Expected order: 2024-03-02 09:00, 2024-03-01 15:00, 2024-03-01 10:00, ...
        const march2Results = result.list.filter(
          (item) =>
            moment(item.createdDate).format('YYYY-MM-DD') === '2024-03-02',
        );
        const march1Results = result.list.filter(
          (item) =>
            moment(item.createdDate).format('YYYY-MM-DD') === '2024-03-01',
        );

        expect(march2Results).toHaveLength(1);
        expect(march1Results).toHaveLength(2);

        // March 2 should come first
        const march2Index = result.list.findIndex(
          (item) =>
            moment(item.createdDate).format('YYYY-MM-DD') === '2024-03-02',
        );
        const march1Index = result.list.findIndex(
          (item) =>
            moment(item.createdDate).format('YYYY-MM-DD') === '2024-03-01',
        );

        expect(march2Index).toBeLessThan(march1Index);

        // Within March 1, times should be sorted DESC (15:00 before 10:00)
        const march1Times = march1Results.map((item) =>
          moment(item.createdDate).format('HH:mm'),
        );
        expect(march1Times[0]).toBe('15:00');
        expect(march1Times[1]).toBe('10:00');
      });

      it('should handle empty results with timeSortOrder', async () => {
        // Create a new profile with no HMO claims
        const hospitals = await createHospitals(manager, 1);
        const hmoProviders = await createHmoProviderFixtures(manager, 1);
        const [newUser] = await createUsers(
          manager,
          1,
          hospitals[0],
          hmoProviders[0],
          null,
          UserType.Patient,
        );
        const newProfile = newUser.profiles[0];

        const result = await repo.findByProfile(
          newProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 0,
            take: 10,
          },
          newProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(0);
        expect(result).toHaveProperty('totalCount', 0);
      });
    });
  });
});
