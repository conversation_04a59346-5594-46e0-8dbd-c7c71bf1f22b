import { Field, InputType, ObjectType } from '@nestjs/graphql';
import { PhoneNumberInput } from '@clinify/shared/validators/phone-number.input';

@ObjectType()
export class EnrollmentAgencyDto {
  @Field(() => String, { nullable: true })
  administrationAgency: string;

  @Field(() => [String], { nullable: true })
  agencies: string[];
}

@ObjectType()
@InputType('EnrollmentAgentInput')
export class EnrollmentAgentDto {
  @Field(() => String, { nullable: true })
  profileId: string;

  @Field(() => String, { nullable: true })
  administrationAgency: string;

  @Field(() => String, { nullable: true })
  enrollmentTpa: string;

  @Field(() => String, { nullable: true })
  enrollmentAgency: string;

  @Field(() => String, { nullable: true })
  accountNumber: string;

  @Field(() => String, { nullable: true })
  accountName: string;

  @Field(() => String, { nullable: true })
  bankName: string;

  @Field(() => String, { nullable: true })
  bvn: string;

  @Field(() => String, { nullable: true })
  branchName: string;

  @Field(() => String, { nullable: true })
  status: string;
}

@ObjectType()
@InputType('FieldOfficerResponseInput')
export class FieldOfficerResponseDto {
  @Field(() => String, { nullable: true })
  profileId: string;

  @Field(() => String, { nullable: true })
  administrationAgency: string;

  @Field(() => String, { nullable: true })
  enrollmentAgency: string;

  @Field(() => String, { nullable: true })
  accountNumber: string;

  @Field(() => String, { nullable: true })
  accountName: string;

  @Field(() => String, { nullable: true })
  bankName: string;

  @Field(() => String, { nullable: true })
  bvn: string;

  @Field(() => String, { nullable: true })
  branchName: string;

  @Field(() => String, { nullable: true })
  status: string;
}

@ObjectType()
@InputType('SponsorResponseInput')
export class SponsorResponseDto {
  @Field(() => String, { nullable: true })
  ref: string;

  @Field(() => String, { nullable: true })
  sponsorName: string;

  @Field(() => String, { nullable: true })
  sponsorType: string;

  @Field(() => String, { nullable: true })
  sponsorLives: string;

  @Field(() => String, { nullable: true })
  agencyLives: string;

  @Field(() => String, { nullable: true })
  amountDue: string;

  @Field(() => String, { nullable: true })
  paymentFrequency: string;

  @Field(() => Date, { nullable: true })
  nextRenewalDate?: Date;

  @Field(() => String, { nullable: true })
  renewalCount: string;

  @Field(() => String, { nullable: true })
  paymentStatus: string;

  @Field(() => Date, { nullable: true })
  paymentDateTime?: Date;

  @Field(() => String, { nullable: true })
  sponsoredPremiumPerLife?: string;

  @Field(() => String, { nullable: true })
  totalSponsoredPremium?: string;

  @Field(() => String, { nullable: true })
  status: string;
}

@ObjectType()
@InputType('EnrolleeReferralResponseInput')
export class EnrolleeReferralResponseDto {
  @Field(() => String, { nullable: true })
  name: string;

  @Field(() => String, { nullable: true })
  referrerCode: string;

  @Field(() => String, { nullable: true })
  accountNumber: string;

  @Field(() => String, { nullable: true })
  accountName: string;

  @Field(() => String, { nullable: true })
  bankName: string;

  @Field(() => String, { nullable: true })
  bvn: string;

  @Field(() => String, { nullable: true })
  branchName: string;

  @Field(() => PhoneNumberInput, { nullable: true })
  phoneNumber: PhoneNumberInput;

  @Field(() => String, { nullable: true })
  email: string;

  @Field(() => String, { nullable: true })
  status: string;
}

@ObjectType()
@InputType('TpaResponseInput')
export class TpaResponseDto {
  @Field(() => String, { nullable: true })
  ref?: string;

  @Field(() => String, { nullable: true })
  name?: string;

  @Field(() => String, { nullable: true })
  address?: string;

  @Field(() => Boolean, { nullable: true })
  isTpa?: boolean;

  @Field(() => String, { nullable: true })
  country?: string;

  @Field(() => String, { nullable: true })
  state?: string;

  @Field(() => String, { nullable: true })
  localGovernmentArea?: string;

  @Field(() => PhoneNumberInput, { nullable: true })
  primaryPhoneNumber?: PhoneNumberInput;

  @Field(() => String, { nullable: true })
  primaryEmailAddress?: string;

  @Field(() => PhoneNumberInput, { nullable: true })
  secondaryPhoneNumber?: PhoneNumberInput;

  @Field(() => String, { nullable: true })
  secondaryEmailAddress?: string;

  @Field(() => Date, { nullable: true })
  startDate?: Date;

  @Field(() => Date, { nullable: true })
  endDate?: Date;

  @Field(() => Date, { nullable: true })
  renewalDate?: Date;

  @Field(() => String, { nullable: true })
  contactPersonTitle?: string;

  @Field(() => String, { nullable: true })
  contactPersonFirstName?: string;

  @Field(() => String, { nullable: true })
  contactPersonMiddleName?: string;

  @Field(() => String, { nullable: true })
  contactPersonLastName?: string;

  @Field(() => PhoneNumberInput, { nullable: true })
  contactPersonPhoneNumber?: PhoneNumberInput;

  @Field(() => String, { nullable: true })
  contactPersonEmailAddress?: string;

  @Field(() => String, { nullable: true })
  contactPersonAltTitle?: string;

  @Field(() => String, { nullable: true })
  contactPersonAltFirstName?: string;

  @Field(() => String, { nullable: true })
  contactPersonAltMiddleName?: string;

  @Field(() => String, { nullable: true })
  contactPersonAltLastName?: string;

  @Field(() => PhoneNumberInput, { nullable: true })
  contactPersonAltPhoneNumber?: PhoneNumberInput;

  @Field(() => String, { nullable: true })
  contactPersonAltEmailAddress?: string;

  @Field(() => String, { nullable: true })
  tpaNumber?: string;

  @Field(() => String, { nullable: true })
  tpaCode?: string;

  @Field(() => String, { nullable: true })
  accountNumber?: string;

  @Field(() => String, { nullable: true })
  accountName?: string;

  @Field(() => String, { nullable: true })
  bankName?: string;

  @Field(() => String, { nullable: true })
  bvn?: string;

  @Field(() => String, { nullable: true })
  branchName?: string;

  @Field(() => String, { nullable: true })
  status?: string;
}
