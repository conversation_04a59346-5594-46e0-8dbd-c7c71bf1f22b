import { Field, InputType, ObjectType } from '@nestjs/graphql';
import { IsNotEmpty, IsUUID } from 'class-validator';
import { ChemoInvestigationDetails } from '@clinify/oncology-consultation-history/validators/oncology-consultation-history.input';

@InputType('ChemoDiagnosisDrugTemplateInput')
@ObjectType()
export class ChemoDiagnosisDrugTemplate {
  @Field(() => String)
  ref: string;

  @Field(() => String, { nullable: false })
  day: string;

  @Field(() => String, { nullable: false })
  @IsNotEmpty()
  drugName: string;

  @Field(() => String, { nullable: true })
  drugId?: string;

  @Field(() => String, { nullable: true })
  dosage?: string;

  @Field(() => String, { nullable: true })
  dosagePercentage?: string;

  @Field(() => String, { nullable: true })
  totalDose?: string;

  @Field(() => String, { nullable: true })
  adjustedDose?: string;

  @Field(() => String, { nullable: true })
  quantity?: string;

  @Field(() => String, { nullable: true })
  route?: string;

  @Field(() => String, { nullable: true })
  infusionUsed?: string;

  @Field(() => String, { nullable: true })
  frequency?: string;

  @Field(() => String, { nullable: true })
  note: string;

  @Field(() => String, { nullable: true, defaultValue: 'External' })
  inventoryClass?: string;
}

@InputType('ChemoDiagnosisCycleTemplateInput')
export class ChemoDiagnosisCycleTemplate {
  @Field(() => String, { nullable: true })
  id?: string;

  @Field(() => Number, { nullable: false })
  cycleNumber: number;

  @Field(() => [ChemoDiagnosisDrugTemplate], { nullable: false })
  drugs: ChemoDiagnosisDrugTemplate[];

  @Field(() => [ChemoInvestigationDetails], { nullable: true })
  investigationDetails?: ChemoInvestigationDetails[];
}

@InputType('NewChemoDiagnosisTemplateInput')
export class NewChemoDiagnosisTemplate {
  @Field({ nullable: false })
  @IsUUID()
  facilityPreferenceId: string;

  @Field({ nullable: false })
  type: string;

  @Field({ nullable: false })
  @IsNotEmpty()
  combinationName: string;

  @Field(() => [ChemoDiagnosisCycleTemplate], { nullable: false })
  cycles: ChemoDiagnosisCycleTemplate[];

  @Field()
  section: string;
}

@InputType('ChemoDiagnosisTemplateInput')
export class ChemoDiagnosisTemplate extends NewChemoDiagnosisTemplate {}
