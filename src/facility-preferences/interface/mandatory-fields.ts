import { Field, InputType, ObjectType, OmitType } from '@nestjs/graphql';

@InputType('MandatoryFieldsInput')
@ObjectType()
export class MandatoryFields {
  @Field(() => [String], { nullable: true })
  admission?: string[];

  @Field(() => [String], { nullable: true })
  allergy?: string[];

  @Field(() => [String], { nullable: true })
  antenatal?: string[];

  @Field(() => [String], { nullable: true })
  antenatalDetails?: string;

  @Field(() => [String], { nullable: true })
  consultation?: string[];

  @Field(() => [String], { nullable: true })
  immunization?: string[];

  @Field(() => [String], { nullable: true })
  medication?: string[];

  @Field(() => [String], { nullable: true })
  medicationDetails?: string[];

  @Field(() => [String], { nullable: true })
  procedure?: string[];

  @Field(() => [String], { nullable: true })
  dischargePatient?: string[];

  @Field(() => [String], { nullable: true })
  transferPatient?: string[];

  @Field(() => [String], { nullable: true })
  bloodTransfusion?: string[];

  @Field(() => [String], { nullable: true })
  admissionInput?: string[];

  @Field(() => [String], { nullable: true })
  admissionOutput?: string[];

  @Field(() => [String], { nullable: true })
  admissionLine?: string[];

  @Field(() => [String], { nullable: true })
  treatmentPlan?: string[];

  @Field(() => [String], { nullable: true })
  vitals?: string[];

  @Field(() => [String], { nullable: true })
  anthropometry?: string[];

  @Field(() => [String], { nullable: true })
  bloodGlucose?: string[];

  @Field(() => [String], { nullable: true })
  bloodPressure?: string[];

  @Field(() => [String], { nullable: true })
  pain?: string[];

  @Field(() => [String], { nullable: true })
  respiratoryRate?: string[];

  @Field(() => [String], { nullable: true })
  temperature?: string[];

  @Field(() => [String], { nullable: true })
  urineDipstick?: string[];

  @Field(() => [String], { nullable: true })
  visualAcuity?: string[];

  @Field(() => [String], { nullable: true })
  pulseRate?: string[];

  @Field(() => [String], { nullable: true })
  hmoClaims?: string[];

  @Field(() => [String], { nullable: true })
  investigation?: string[];

  @Field(() => [String], { nullable: true })
  radiologyExam?: string[];

  @Field(() => [String], { nullable: true })
  postnatal?: string[];

  @Field(() => [String], { nullable: true })
  labourAndDelivery?: string[];

  @Field(() => [String], { nullable: true })
  nextOfKin?: string[];

  @Field(() => [String], { nullable: true })
  dependents?: string[];

  @Field(() => [String], { nullable: true })
  medicalReport?: string[];

  @Field(() => [String], { nullable: true })
  admissionNotes?: string[];

  @Field(() => [String], { nullable: true })
  nursingServices?: string[];

  @Field(() => [String], { nullable: true })
  oncology?: string[];

  @Field(() => [String], { nullable: true })
  requestProcedure?: string[];

  @Field(() => [String], { nullable: true })
  laboratory?: string[];

  @Field(() => [String], { nullable: true })
  radiology?: string[];

  @Field(() => [String], { nullable: true })
  postOperationChecklist?: string[];

  @Field(() => [String], { nullable: true })
  preChemoEducation?: string[];

  @Field(() => [String], { nullable: true })
  cancerScreening?: string[];
}

@InputType('NewFindingsTemplateInput')
@ObjectType()
export class NewFindingsTemplateInput {
  @Field(() => String)
  name: string;

  @Field(() => String)
  facilityPreferenceId: string;

  @Field(() => String)
  findings: string;

  @Field(() => String, { nullable: true })
  impression?: string;
}

@InputType('FindingsTemplateInput')
@ObjectType()
export class FindingsTemplateInput extends NewFindingsTemplateInput {
  @Field(() => String)
  id: string;
}

@InputType('NewLabCommentsTemplateInput')
@ObjectType()
export class NewLabCommentsTemplateInput {
  @Field(() => String)
  name: string;

  @Field(() => String)
  facilityPreferenceId: string;

  @Field(() => String)
  comment: string;
}

@InputType('LabCommentsTemplateInput')
@ObjectType()
export class LabCommentsTemplateInput extends NewLabCommentsTemplateInput {
  @Field(() => String)
  id: string;
}

@InputType('NewMedicalReportTemplateInput')
@ObjectType()
export class NewMedicalReportTemplateInput {
  @Field(() => String)
  name: string;

  @Field(() => String)
  facilityPreferenceId: string;

  @Field(() => String)
  template: string;
}

@InputType('MedicalReportTemplateInput')
export class MedicalReportTemplateInput extends OmitType(
  NewMedicalReportTemplateInput,
  ['name'],
) {
  @Field(() => String)
  id: string;
}

@InputType('RadiologyReportTemplateInput')
export class RadiologyReportTemplateInput {
  @Field(() => String)
  radiologyExamSource: string;

  @Field(() => [String])
  examTypes: string[];
}

@InputType('NewConsultationsTemplateInput')
export class NewConsultationsTemplateInput {
  @Field({ nullable: false })
  name: string;

  @Field(() => String)
  facilityPreferenceId: string;

  @Field(() => String, { nullable: true })
  complaints?: string;

  @Field(() => String, { nullable: true })
  historyComplaints?: string;

  @Field(() => String, { nullable: true })
  reviewSystems?: string;

  @Field(() => String, { nullable: true })
  physicalExamination?: string;

  @Field(() => String, { nullable: true })
  audiometry?: string;

  @Field(() => String, { nullable: true })
  healthEducation?: string;

  @Field(() => String, { nullable: true })
  treatmentPlan?: string;
}

@InputType('ConsultationsTemplateInput')
export class ConsultationsTemplateInput extends NewConsultationsTemplateInput {
  @Field(() => String)
  id: string;
}

@InputType('NewDischargeSummaryTemplateInput')
@ObjectType()
export class NewDischargeSummaryTemplateInput {
  @Field(() => String)
  name: string;

  @Field(() => String)
  facilityPreferenceId: string;

  @Field(() => String)
  summary: string;
}

@InputType('DischargeSummaryTemplateInput')
@ObjectType()
export class DischargeSummaryTemplateInput extends NewDischargeSummaryTemplateInput {
  @Field(() => String)
  id: string;
}

@InputType('NewOperationNoteTemplateInput')
@ObjectType()
export class NewOperationNoteTemplateInput {
  @Field(() => String)
  name: string;

  @Field(() => String)
  facilityPreferenceId: string;

  @Field(() => String)
  note: string;

  @Field(() => String)
  postNote: string;
}

@InputType('OperationNoteTemplateInput')
@ObjectType()
export class OperationNoteTemplateInput extends NewOperationNoteTemplateInput {
  @Field(() => String)
  id: string;
}
