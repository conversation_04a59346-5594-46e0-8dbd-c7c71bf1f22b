/* eslint-disable max-len, max-lines */

import { Test, TestingModule } from '@nestjs/testing';
import { Promise } from 'bluebird';
import { CommissionPayer } from '@clinify/facility-preferences/enums/commission-payer';
import { DefaultPatientAccessType } from '@clinify/facility-preferences/enums/patient-lookup';
import { FacilityPreferenceResolver } from '@clinify/facility-preferences/resolvers/facility-preference.resolver';
import { CommissionPayerResponse } from '@clinify/facility-preferences/responses/commission-payer.response';
import { FacilityPreferenceService } from '@clinify/facility-preferences/services/facility-preference.service';
import { facilityPreferenceMock } from '@mocks/factories/facility-preference.factory';
import { mockProfile } from '@mocks/factories/profile.factory';

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};
const specialistAccessMock = {
  id: 'id',
  specialistIds: ['specialistId'],
};

const facilityPreferenceServiceMock = {
  getFacilityPreference: jest.fn(() => Promise.resolve(facilityPreferenceMock)),
  updateWelcomeMailTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateBookAppointmentMailTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updatePatientAccessType: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateFormsMandatoryFields: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  saveFindingsTemplate: jest.fn(() => Promise.resolve(facilityPreferenceMock)),
  updateFindingsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  deleteFindingsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  findByFacilityPreferenceIdForFindingsTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  findByHospitalIdForFindingsTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  saveLabCommentsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateLabCommentsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  deleteLabCommentsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  findByFacilityPreferenceIdForLabCommentsTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  findByHospitalIdForLabCommentsTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  findByHospitalIdForMedicalReportTemplate: jest.fn(() => Promise.resolve([])),
  findByFacilityPreferenceIdForMedicalReportTemplate: jest.fn(() =>
    Promise.resolve([]),
  ),
  saveMedicalReportTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateMedicalReportTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  deleteMedicalReportTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateRadiologyContrastMode: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateDashboardColourMode: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateShowServiceDetails: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateRolesServiceDetailsIsHidden: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateDefaultCommissionPayer: jest.fn(
    (): Promise<CommissionPayerResponse> =>
      Promise.resolve({
        commissionPayer: CommissionPayer.Patient,
        facilityPreferenceId: 'id',
        hospitalId: 'hospital-id',
      }),
  ),
  updateFileNumberGenerate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  saveConsultationsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateConsultationsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  deleteConsultationsTemplate: jest.fn(() => Promise.resolve()),
  findByHospitalIdConsultationsTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  findByFacilityPreferenceIdConsultationsTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  updateReceiptSize: jest.fn(() => Promise.resolve({})),
  saveDischargeSummaryTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateDischargeSummaryTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  deleteDischargeSummaryTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  findByFacilityPreferenceIdDischargeSummaryTemplates: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  findByHospitalIdDischargeSummaryTemplates: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  saveChemoDiagnosisTemplate: jest.fn(),
  updateChemoDiagnosisTemplate: jest.fn(),
  deleteChemoDiagnosisTemplate: jest.fn(),
  findByHospitalIdChemoDiagnosisTemplates: jest.fn(),
  findByFacilityPreferenceIdChemoDiagnosisTemplates: jest.fn(),
  getOneChemoDiagnosisTemplate: jest.fn(),
  updateTariffsToUse: jest.fn(() => Promise.resolve(facilityPreferenceMock)),
  updateInventoryToUse: jest.fn(() => Promise.resolve(facilityPreferenceMock)),
  updateSpecialistAccess: jest.fn(() => Promise.resolve(specialistAccessMock)),
  getSpecialistAccess: jest.fn(() => Promise.resolve(specialistAccessMock)),
  findByHospitalIdSpecialistAccess: jest.fn(() =>
    Promise.resolve([specialistAccessMock]),
  ),
  updatePatientSurveyLinks: jest.fn(),
  saveOperationNoteTemplate: jest.fn(),
  updateOperationNoteTemplate: jest.fn(),
  deleteOperationNoteTemplate: jest.fn(),
  findByFacilityPreferenceIdOperationNoteTemplates: jest.fn(),
  findByHospitalIdOperationNoteTemplates: jest.fn(),
  updateInventoryClass: jest.fn(),
  updatePatientRegistrationFee: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateEnrolleeCapitationAmount: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateAutoProcessClaims: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateEnrollmentAgentAssignments: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateEnrollmentAgency: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateEnrolleeSponsors: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateFieldOfficer: jest.fn(() => Promise.resolve(facilityPreferenceMock)),
  updateEnrolleeReferral: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateSponsorAssignment: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateEnrolleeCapitationAmountByPlanType: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateEnrolleeCapitationAmountPerPlan: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateEnrolleeTpaAssignment: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
};

const profile = mockProfile;

describe('FacilityPreferenceResolver', () => {
  let resolver: FacilityPreferenceResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FacilityPreferenceResolver,
        {
          provide: FacilityPreferenceService,
          useValue: facilityPreferenceServiceMock,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
      ],
    }).compile();

    resolver = module.get<FacilityPreferenceResolver>(
      FacilityPreferenceResolver,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('getFacilityPreference() should call getFacilityPreference from service and return value', async () => {
    const response = await resolver.getFacilityPreference('id');
    expect(
      facilityPreferenceServiceMock.getFacilityPreference,
    ).toHaveBeenCalledWith('id');
    expect(response).toEqual(facilityPreferenceMock);
  });

  it('updateBookAppointmentMailTemplate () should call getFacilityPreference from service and return value', async () => {
    const response = await resolver.updateWelcomeMailTemplate(
      profile,
      'id',
      facilityPreferenceMock,
    );
    expect(
      facilityPreferenceServiceMock.updateWelcomeMailTemplate,
    ).toHaveBeenCalledWith(profile, 'id', facilityPreferenceMock);
    expect(response).toEqual(facilityPreferenceMock);
  });

  it('updatePatientAccessType() should call getFacilityPreference from service and return value', async () => {
    const response = await resolver.updatePatientAccessType(
      profile,
      DefaultPatientAccessType.AllPatients,
    );
    expect(
      facilityPreferenceServiceMock.updatePatientAccessType,
    ).toHaveBeenCalledWith(
      profile.hospitalId,
      DefaultPatientAccessType.AllPatients,
    );
    expect(response).toEqual(facilityPreferenceMock);
  });

  it('updateFormsMandatoryFields() should call updateFormsMandatoryFields from service', async () => {
    const res = await resolver.updateFormsMandatoryFields(
      profile,
      profile.hospitalId,
      {},
    );
    expect(
      facilityPreferenceServiceMock.updateFormsMandatoryFields,
    ).toHaveBeenCalledWith(profile, profile.hospitalId, {});
    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'FacilityPreferenceUpdated',
      {
        FacilityPreferenceUpdated: res,
      },
    );
  });

  it('updateFacilityPreferenceHandler() should call updateFacilityPreferenceHandler from service', () => {
    resolver.updateFacilityPreferenceHandler('id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'FacilityPreferenceUpdated',
    );
  });

  it('saveFindingsTemplate() should call saveFindingsTemplate from service', async () => {
    await resolver.saveFindingsTemplate(profile, {} as any);
    expect(
      facilityPreferenceServiceMock.saveFindingsTemplate,
    ).toHaveBeenCalledWith(profile, {});
  });

  it('updateFindingsTemplate() should call updateFindingsTemplate from service', async () => {
    await resolver.updateFindingsTemplate(profile, {} as any);
    expect(
      facilityPreferenceServiceMock.updateFindingsTemplate,
    ).toHaveBeenCalledWith(profile, {});
  });

  it('deleteFindingsTemplate() should call deleteFindingsTemplate from service', async () => {
    await resolver.deleteFindingsTemplate('id');
    expect(
      facilityPreferenceServiceMock.deleteFindingsTemplate,
    ).toHaveBeenCalledWith('id');
  });

  it('findFindingsTemplateByFacilityPreferenceId() should call findByFacilityPreferenceIdForFindingsTemplate from service', async () => {
    await resolver.fetchFindingsTemplates(profile, 'id');
    expect(
      facilityPreferenceServiceMock.findByFacilityPreferenceIdForFindingsTemplate,
    ).toHaveBeenCalledWith('id');
    await resolver.fetchFindingsTemplates(profile);
    expect(
      facilityPreferenceServiceMock.findByHospitalIdForFindingsTemplate,
    ).toHaveBeenCalledWith(profile.hospitalId);
  });

  it('saveLabCommentsTemplate() should call saveLabCommentsTemplate from service', async () => {
    await resolver.saveLabCommentsTemplate(profile, {} as any);
    expect(
      facilityPreferenceServiceMock.saveLabCommentsTemplate,
    ).toHaveBeenCalledWith(profile, {});
  });

  it('updateLabCommentsTemplate() should call updateLabCommentsTemplate from service', async () => {
    await resolver.updateLabCommentsTemplate(profile, {} as any);
    expect(
      facilityPreferenceServiceMock.updateLabCommentsTemplate,
    ).toHaveBeenCalledWith(profile, {});
  });

  it('deleteLabCommentsTemplate() should call deleteLabCommentsTemplate from service', async () => {
    await resolver.deleteLabCommentsTemplate('id');
    expect(
      facilityPreferenceServiceMock.deleteLabCommentsTemplate,
    ).toHaveBeenCalledWith('id');
  });

  it('findLabCommentsTemplateByFacilityPreferenceId() should call findByFacilityPreferenceIdForLabCommentsTemplate from service', async () => {
    await resolver.fetchLabCommentsTemplates(profile, 'id');
    expect(
      facilityPreferenceServiceMock.findByFacilityPreferenceIdForLabCommentsTemplate,
    ).toHaveBeenCalledWith('id');
    await resolver.fetchLabCommentsTemplates(profile);
    expect(
      facilityPreferenceServiceMock.findByHospitalIdForLabCommentsTemplate,
    ).toHaveBeenCalledWith(profile.hospitalId);
  });

  it('fetchMedicalTemplates', async () => {
    await resolver.fetchMedicalReportTemplates(profile);
    expect(
      facilityPreferenceServiceMock.findByHospitalIdForMedicalReportTemplate,
    ).toHaveBeenNthCalledWith(1, profile.hospitalId);
    await resolver.fetchMedicalReportTemplates(profile, 'id');
    expect(
      facilityPreferenceServiceMock.findByFacilityPreferenceIdForMedicalReportTemplate,
    ).toHaveBeenNthCalledWith(1, 'id');
  });

  it('saveMedicalReportTemplate', async () => {
    const input = {
      name: 'Name',
      facilityPreferenceId: 'id',
      template: 'template',
    };
    const res = await resolver.saveMedicalReportTemplate(profile, input);
    expect(
      facilityPreferenceServiceMock.saveMedicalReportTemplate,
    ).toHaveBeenCalledWith(profile, input);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateMedicalReportTemplate', async () => {
    const input = {
      id: 'id',
      template: 'update',
      facilityPreferenceId: 'id',
    };
    const res = await resolver.updateMedicalReportTemplate(profile, input);
    expect(
      facilityPreferenceServiceMock.updateMedicalReportTemplate,
    ).toHaveBeenCalledWith(profile, input);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('deleteMedicalReportTemplate', async () => {
    const res = await resolver.deleteMedicalReportTemplate('id');
    expect(
      facilityPreferenceServiceMock.deleteMedicalReportTemplate,
    ).toHaveBeenCalledWith('id');
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateRadiologyContrastMode(): should call updateRadiologyContrastMode service method', async () => {
    const res = await resolver.updateRadiologyContrastMode(profile, true);
    expect(
      facilityPreferenceServiceMock.updateRadiologyContrastMode,
    ).toHaveBeenCalledWith(profile.hospitalId, true);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateDashboardColourMode(): should call updateDashboardColourMode service method', async () => {
    const res = await resolver.updateDashboardColourMode(profile, false);
    expect(
      facilityPreferenceServiceMock.updateDashboardColourMode,
    ).toHaveBeenCalledWith(profile.hospitalId, false);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateDefaultCommissionPayer', async () => {
    const res = await resolver.updateDefaultCommissionPayer(
      profile,
      'id',
      CommissionPayer.Patient,
    );
    expect(
      facilityPreferenceServiceMock.updateDefaultCommissionPayer,
    ).toBeCalledWith(profile.hospitalId, 'id', CommissionPayer.Patient);
    expect(res).toEqual(
      expect.objectContaining({
        commissionPayer: CommissionPayer.Patient,
      }),
    );
  });

  it('updateFileNumberGenerate(): should call the service method to update file number generation mode', async () => {
    await resolver.updateFileNumberGenerate(profile, false);
    expect(
      facilityPreferenceServiceMock.updateFileNumberGenerate,
    ).toBeCalledWith(profile.hospital, false);
  });

  it('saveConsultationsTemplate(): should call the service method to save consultations template', async () => {
    const input = {} as any;
    await resolver.saveConsultationsTemplate(profile, input);
    expect(
      facilityPreferenceServiceMock.saveConsultationsTemplate,
    ).toBeCalledWith(profile, input);
  });

  it('updateConsultationsTemplate(): should call the service method to update consultations template', async () => {
    const input = {} as any;
    await resolver.updateConsultationsTemplate(profile, input);
    expect(
      facilityPreferenceServiceMock.updateConsultationsTemplate,
    ).toBeCalledWith(profile, input);
  });

  it('deleteConsultationsTemplate(): should call the service method to delete consultations template', async () => {
    await resolver.deleteConsultationsTemplate(profile, 'id');
    expect(
      facilityPreferenceServiceMock.deleteConsultationsTemplate,
    ).toBeCalledWith(profile, 'id');
  });

  it('findByHospitalIdConsultationsTemplate(): should call the service method to find consultations template', async () => {
    await resolver.findByHospitalIdConsultationsTemplates(profile, 'id');
    expect(
      facilityPreferenceServiceMock.findByHospitalIdConsultationsTemplate,
    ).toBeCalledWith(profile, 'id');
  });

  it('findByFacilityPreferenceIdConsultationsTemplate(): should call the service method to find consultations template', async () => {
    await resolver.findByFacilityPreferenceIdConsultationsTemplates(
      profile,
      'id',
    );
    expect(
      facilityPreferenceServiceMock.findByFacilityPreferenceIdConsultationsTemplate,
    ).toBeCalledWith(profile, 'id');
  });

  it('updateReceiptSize(): should call the service method to update receipt size', async () => {
    await resolver.updateReceiptSize(profile, 'id', 'A4');
    expect(facilityPreferenceServiceMock.updateReceiptSize).toBeCalledWith(
      profile,
      'id',
      'A4',
    );
  });

  it('saveDischargeSummaryTemplate(): should call the service method to save discharge summary template', async () => {
    const input = {} as any;
    await resolver.saveDischargeSummaryTemplate(profile, input);
    expect(
      facilityPreferenceServiceMock.saveDischargeSummaryTemplate,
    ).toBeCalledWith(profile, input);
  });

  it('updateDischargeSummaryTemplate(): should call the service method to update discharge summary template', async () => {
    const input = {} as any;
    await resolver.updateDischargeSummaryTemplate(profile, input);
    expect(
      facilityPreferenceServiceMock.updateDischargeSummaryTemplate,
    ).toBeCalledWith(profile, input);
  });

  it('deleteDischargeSummaryTemplate(): should call the service method to delete discharge summary template', async () => {
    await resolver.deleteDischargeSummaryTemplate('id');
    expect(
      facilityPreferenceServiceMock.deleteDischargeSummaryTemplate,
    ).toBeCalledWith('id');
  });

  it('fetchDischargeSummaryTemplates(): should call the service method to fetch discharge summary templates', async () => {
    await resolver.fetchDischargeSummaryTemplates(profile, 'id');
    expect(
      facilityPreferenceServiceMock.findByFacilityPreferenceIdDischargeSummaryTemplates,
    ).toBeCalledWith('id');
    await resolver.fetchDischargeSummaryTemplates(profile);
    expect(
      facilityPreferenceServiceMock.findByHospitalIdDischargeSummaryTemplates,
    ).toBeCalledWith(profile.hospitalId);
  });

  it('saveChemoDiagnosisTemplate(): should call the service method to save chemo diagnosis template', async () => {
    const input = {} as any;
    await resolver.saveChemoDiagnosisTemplate(profile, input);
    expect(
      facilityPreferenceServiceMock.saveChemoDiagnosisTemplate,
    ).toBeCalledWith(profile, input);
  });

  it('updateChemoDiagnosisTemplate(): should call the service method to update chemo diagnosis template', async () => {
    const input = {} as any;
    await resolver.updateChemoDiagnosisTemplate(profile, 'id', input);
    expect(
      facilityPreferenceServiceMock.updateChemoDiagnosisTemplate,
    ).toBeCalledWith(profile, 'id', input);
  });

  it('deleteChemoDiagnosisTemplate(): should call the service method to delete chemo diagnosis template', async () => {
    await resolver.deleteChemoDiagnosisTemplate('id');
    expect(
      facilityPreferenceServiceMock.deleteChemoDiagnosisTemplate,
    ).toBeCalledWith('id');
  });

  it('fetchChemoDiagnosisTemplates(): should call the service method to fetch chemo diagnosis templates', async () => {
    await resolver.fetchChemoDiagnosisTemplates(
      profile,
      'chemo',
      'diagnosis',
      'id',
    );
    expect(
      facilityPreferenceServiceMock.findByFacilityPreferenceIdChemoDiagnosisTemplates,
    ).toBeCalledWith('id', 'chemo', 'diagnosis');
    await resolver.fetchChemoDiagnosisTemplates(profile, 'chemo', 'diagnosis');
    expect(
      facilityPreferenceServiceMock.findByHospitalIdChemoDiagnosisTemplates,
    ).toBeCalledWith(profile.hospitalId, 'chemo', 'diagnosis');
  });

  it('fetchChemoDiagnosisTemplate(): should call the service method to fetch single chemo diagnosis template', async () => {
    await resolver.fetchChemoDiagnosisTemplate('id');
    expect(
      facilityPreferenceServiceMock.getOneChemoDiagnosisTemplate,
    ).toBeCalledWith('id');
  });

  it('updateTariffsToUse(): should call the service method to update tariffs', async () => {
    expect(await resolver.updateTariffsToUse('test', true, profile)).toBe(
      facilityPreferenceMock,
    );
    expect(facilityPreferenceServiceMock.updateTariffsToUse).toBeCalledWith(
      'test',
      true,
      profile,
    );
  });

  it('updateInventoryToUse(): should call the service method to update inventory', async () => {
    expect(await resolver.updateInventoryToUse('test', true, profile)).toBe(
      facilityPreferenceMock,
    );
    expect(facilityPreferenceServiceMock.updateInventoryToUse).toBeCalledWith(
      'test',
      true,
      profile,
    );
  });

  it('updateSpecialistAccess(): should call the service method to update specialist access', async () => {
    expect(
      await resolver.updateSpecialistAccess(profile, 'id', ['specialistId']),
    ).toBe(specialistAccessMock);
    expect(facilityPreferenceServiceMock.updateSpecialistAccess).toBeCalledWith(
      profile,
      'id',
      ['specialistId'],
    );
  });

  it('getSpecialistAccess(): should call the service method to get specialist access', async () => {
    expect(await resolver.getSpecialistAccess(profile, 'id')).toBe(
      specialistAccessMock,
    );
    expect(facilityPreferenceServiceMock.getSpecialistAccess).toBeCalledWith(
      profile,
      'id',
    );
  });

  it('findByHospitalIdSpecialistAccess(): should call the service method to find specialist access', async () => {
    expect(await resolver.findByHospitalIdSpecialistAccess(profile)).toEqual([
      specialistAccessMock,
    ]);
    expect(
      facilityPreferenceServiceMock.findByHospitalIdSpecialistAccess,
    ).toBeCalledWith(profile.hospitalId);

    expect(
      await resolver.findByHospitalIdSpecialistAccess(profile, 'id'),
    ).toEqual([specialistAccessMock]);
    expect(
      facilityPreferenceServiceMock.findByHospitalIdSpecialistAccess,
    ).toBeCalledWith('id');
  });

  it('saveOperationNoteTemplate(): should call the service method to save operation note template', async () => {
    const input = {} as any;
    await resolver.saveOperationNoteTemplate(profile, input);
    expect(
      facilityPreferenceServiceMock.saveOperationNoteTemplate,
    ).toBeCalledWith(profile, input);
  });

  it('updateOperationNoteTemplate(): should call the service method to update operation note template', async () => {
    const input = {} as any;
    await resolver.updateOperationNoteTemplate(profile, input);
    expect(
      facilityPreferenceServiceMock.updateOperationNoteTemplate,
    ).toBeCalledWith(profile, input);
  });

  it('deleteOperationNoteTemplate(): should call the service method to delete operation note template', async () => {
    await resolver.deleteOperationNoteTemplate('id');
    expect(
      facilityPreferenceServiceMock.deleteOperationNoteTemplate,
    ).toBeCalledWith('id');
  });

  it('fetchOperationNoteTemplates(): should call the service method to fetch operation note template', async () => {
    await resolver.fetchOperationNoteTemplates(profile, 'id');
    expect(
      facilityPreferenceServiceMock.findByFacilityPreferenceIdOperationNoteTemplates,
    ).toBeCalledWith('id');

    await resolver.fetchOperationNoteTemplates(profile);
    expect(
      facilityPreferenceServiceMock.findByHospitalIdOperationNoteTemplates,
    ).toBeCalledWith(profile.hospitalId);
  });

  it('updateInventoryClass(): should call the service method to update inventory class', async () => {
    await resolver.updateInventoryClass(profile, 'class');
    expect(facilityPreferenceServiceMock.updateInventoryClass).toBeCalledWith(
      profile,
      'class',
    );
  });

  it('updatePatientRegistrationFee(): should call the service method to update patient registration fee', async () => {
    const registrationFee = 5000;
    const result = await resolver.updatePatientRegistrationFee(
      profile,
      registrationFee,
    );
    expect(
      facilityPreferenceServiceMock.updatePatientRegistrationFee,
    ).toBeCalledWith(profile.hospitalId, registrationFee);
    expect(result).toEqual(facilityPreferenceMock);
  });

  it('updateEnrolleeCapitationAmount(): should call the service method to update enrollee capitation amount', async () => {
    const enrolleeCapitationAmount = 5000;
    const result = await resolver.updateEnrolleeCapitationAmount(
      profile,
      enrolleeCapitationAmount,
    );
    expect(
      facilityPreferenceServiceMock.updateEnrolleeCapitationAmount,
    ).toBeCalledWith(profile.hospitalId, enrolleeCapitationAmount);
    expect(result).toEqual(facilityPreferenceMock);
  });

  it('updateAutoProcessClaims(): should call the service method to update auto process claims', async () => {
    const mode = true;
    const result = await resolver.updateAutoProcessClaims(profile, mode);
    expect(
      facilityPreferenceServiceMock.updateAutoProcessClaims,
    ).toBeCalledWith(profile.hospitalId, mode, profile);
    expect(result).toEqual(facilityPreferenceMock);
  });

  it('updateEnrollmentAgentAssignments(): should call the service method to update enrollment agent assignments', async () => {
    const mockAgent = {
      profileId: 'profile-id',
      administrationAgency: 'admin-agency',
      enrollmentAgency: 'enrollment-agency',
      accountNumber: '*********',
      accountName: 'Test Account',
      bankName: 'Test Bank',
      bvn: '452153',
      branchName: 'Branch Name',
      status: 'Inactive',
      enrollmentTpa: null,
    };
    const result = await resolver.updateEnrollmentAgentAssignments(
      profile,
      mockAgent,
    );
    expect(
      facilityPreferenceServiceMock.updateEnrollmentAgentAssignments,
    ).toBeCalledWith(profile, mockAgent);
    expect(result).toEqual(facilityPreferenceMock);
  });
  it('updateEnrollmentAgency(): should call the service method to update enrollment agency', async () => {
    const result = await resolver.updateEnrollmentAgency(profile, 'id', []);
    expect(facilityPreferenceServiceMock.updateEnrollmentAgency).toBeCalledWith(
      profile,
      'id',
      [],
    );
    expect(result).toEqual(facilityPreferenceMock);
  });

  it('updateEnrolleeSponsors(): should call the service method to update enrollee sponsors', async () => {
    const sponsors = ['sponsor1', 'sponsor2'];
    const result = await resolver.updateEnrolleeSponsors(profile, sponsors);
    expect(facilityPreferenceServiceMock.updateEnrolleeSponsors).toBeCalledWith(
      profile,
      sponsors,
    );
    expect(result).toEqual(facilityPreferenceMock);
  });

  it('updateFieldOfficer(): should call the service method to update field officer', async () => {
    const mockFieldOfficer = {
      profileId: 'profile-1',
      administrationAgency: 'admin-agency',
      enrollmentAgency: 'enrollment-agency',
      accountNumber: '*********',
      accountName: 'Account Name',
      bankName: 'Bank Name',
      bvn: '452153',
      branchName: 'Branch Name',
      status: 'Active',
    };
    const result = await resolver.updateFieldOfficer(profile, mockFieldOfficer);
    expect(facilityPreferenceServiceMock.updateFieldOfficer).toBeCalledWith(
      profile,
      mockFieldOfficer,
    );
    expect(result).toEqual(facilityPreferenceMock);
  });

  it('updateEnrolleeReferral(): should call the service method to update enrollee referral', async () => {
    const mockReferral = {
      name: 'Referral Name',
      referrerCode: 'REF001',
      accountNumber: '*********',
      accountName: 'Referral Account',
      bankName: 'Referral Bank',
      bvn: '452153',
      branchName: 'Branch Name',
      phoneNumber: null,
      email: '<EMAIL>',
      status: 'Active',
    };
    const result = await resolver.updateEnrolleeReferral(profile, mockReferral);
    expect(facilityPreferenceServiceMock.updateEnrolleeReferral).toBeCalledWith(
      profile,
      mockReferral,
    );
    expect(result).toEqual(facilityPreferenceMock);
  });

  it('updateSponsorAssignment(): should call the service method to sponsorship details', async () => {
    const mockSponsorshipInfo = {
      ref: 'sponsor-ref',
      sponsorName: 'sponsor-name',
      sponsorType: 'sponsor-type',
      sponsorLives: '750',
      agencyLives: '250',
      amountDue: '3000',
      paymentFrequency: 'annual',
      nextRenewalDate: null,
      renewalCount: '3',
      paymentStatus: 'Paid',
      paymentDateTime: null,
      status: 'Inactive',
    };
    const result = await resolver.updateSponsorAssignment(
      profile,
      mockSponsorshipInfo,
    );
    expect(
      facilityPreferenceServiceMock.updateSponsorAssignment,
    ).toBeCalledWith(profile, mockSponsorshipInfo);
    expect(result).toEqual(facilityPreferenceMock);
  });

  it('updateEnrolleeCapitationAmountByPlanType(): should call the service method to update enrollee capitation amount by plan type', async () => {
    const planTypeId = 'plan-type-123';
    const planTypeName = 'Plan Type Name';
    const capitationAmount = 7500;
    const result = await resolver.updateEnrolleeCapitationAmountByPlanType(
      profile,
      planTypeId,
      planTypeName,
      capitationAmount,
    );
    expect(
      facilityPreferenceServiceMock.updateEnrolleeCapitationAmountByPlanType,
    ).toBeCalledWith(profile, planTypeId, planTypeName, capitationAmount);
    expect(result).toEqual(facilityPreferenceMock);
  });

  it('updateEnrolleeCapitationAmountPerPlan(): should call the service method to toggle enrollee capitation amount per plan', async () => {
    const enabled = true;
    const result = await resolver.updateEnrolleeCapitationAmountPerPlan(
      profile,
      enabled,
    );
    expect(
      facilityPreferenceServiceMock.updateEnrolleeCapitationAmountPerPlan,
    ).toBeCalledWith(profile, enabled);
    expect(result).toEqual(facilityPreferenceMock);
  });

  it('updateEnrolleeTpaAssignment(): should call updateEnrolleeTpaAssignment service method', async () => {
    const mockTpaDetailInput = {
      ref: 'tpa-ref',
      name: 'TPA NAME',
      address: null,
      isTpa: true,
      country: 'Nigeria',
      state: 'Lagos State',
      localGovernmentArea: 'Ikeja',
      primaryEmailAddress: null,
      secondaryEmailAddress: null,
      startDate: null,
      endDate: null,
      renewalDate: null,
      tpaNumber: '12',
      tpaCode: 'TPC',
      accountName: null,
      accountNumber: null,
      bankName: null,
      bvn: null,
      branchName: null,
      status: 'Active',
    };
    const result = await resolver.updateEnrolleeTpaAssignment(
      profile,
      mockTpaDetailInput,
    );
    expect(
      facilityPreferenceServiceMock.updateEnrolleeTpaAssignment,
    ).toBeCalledWith(profile, mockTpaDetailInput);
    expect(result).toEqual(facilityPreferenceMock);
  });
});
