import { <PERSON><PERSON>, ResolveField, Resolver } from '@nestjs/graphql';
import { FindingsTemplateModel } from '@clinify/facility-preferences/models/findings-template.model';

@Resolver(() => FindingsTemplateModel)
export class FindingsTemplateResolver {
  @ResolveField(() => String, { nullable: true, name: 'findings' })
  resolveFindings(@Parent() root: FindingsTemplateModel) {
    return typeof root.findings !== 'string'
      ? JSON.stringify(root.findings)
      : root.findings;
  }

  @ResolveField(() => String, { name: 'impression', nullable: true })
  resolveImpression(@Parent() root: FindingsTemplateModel) {
    return typeof root.impression !== 'string'
      ? JSON.stringify(root.impression)
      : root.impression;
  }
}
