import { Test } from '@nestjs/testing';
import { LabCommentTemplateResolver } from '@clinify/facility-preferences/resolvers/lab-comment-template.resolver';

const labCommentsTemplateMock: any = {
  name: 'test',
  comment: 'My lab comments',
  facilityPreferenceId: '1a971260-af87-4205-af08-d9bdf9f0f806',
  createdDate: new Date('2023-09-19T20:24:56.057Z'),
  updatedDate: new Date('2023-09-19T20:24:56.057Z'),
  id: '27dca10a-3d1e-480f-a35b-541f71e250a4',
  createdById: 'createdById',
};

describe('LabCommentTemplateResolver', () => {
  let resolver: LabCommentTemplateResolver;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [LabCommentTemplateResolver],
    }).compile();

    resolver = module.get(LabCommentTemplateResolver);
    jest.clearAllMocks();
  });

  it('resolveComment', () => {
    let result = resolver.resolveComment(labCommentsTemplateMock);
    expect(result).toEqual('My lab comments');

    result = resolver.resolveComment({
      ...labCommentsTemplateMock,
      comment: { object: 'object' },
    });
    expect(result).toEqual('{"object":"object"}');
  });
});
