import { Test } from '@nestjs/testing';
import { FindingsTemplateResolver } from '@clinify/facility-preferences/resolvers/findings-template.resolver';

const findingsTemplateMock: any = {
  name: 'test',
  findings: 'My findings',
  impression: 'My impression',
  facilityPreferenceId: '1a971260-af87-4205-af08-d9bdf9f0f806',
  createdDate: new Date('2023-09-19T20:24:56.057Z'),
  updatedDate: new Date('2023-09-19T20:24:56.057Z'),
  id: '27dca10a-3d1e-480f-a35b-541f71e250a4',
  createdById: 'createdById',
};

describe('FindingsTemplateResolver', () => {
  let resolver: FindingsTemplateResolver;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [FindingsTemplateResolver],
    }).compile();

    resolver = module.get<FindingsTemplateResolver>(FindingsTemplateResolver);
    jest.clearAllMocks();
  });

  it('resolveFindings', () => {
    let findings = resolver.resolveFindings(findingsTemplateMock);
    expect(findings).toEqual('My findings');

    findings = resolver.resolveFindings({
      ...findingsTemplateMock,
      findings: { object: 'object' },
    });
    expect(findings).toEqual('{"object":"object"}');
  });

  it('resolveImpression', () => {
    let impression = resolver.resolveImpression(findingsTemplateMock);
    expect(impression).toEqual('My impression');

    impression = resolver.resolveImpression({
      ...findingsTemplateMock,
      impression: { object: 'object' },
    });
    expect(impression).toEqual('{"object":"object"}');
  });
});
