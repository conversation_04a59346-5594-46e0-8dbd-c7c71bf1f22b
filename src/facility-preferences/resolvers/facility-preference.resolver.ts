/* eslint-disable max-lines */
import { Inject, UseGuards } from '@nestjs/common';
import { Args, Mutation, Query, Resolver, Subscription } from '@nestjs/graphql';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { RolesAuthGuard } from '@clinify/authentication/guards/roles.guard';
import {
  CommissionPayer,
  PayoutCommissionPayer,
} from '@clinify/facility-preferences/enums/commission-payer';
import { DefaultPatientAccessType } from '@clinify/facility-preferences/enums/patient-lookup';
import {
  ChemoDiagnosisTemplate,
  NewChemoDiagnosisTemplate,
} from '@clinify/facility-preferences/interface/chemo-diagnosis.interface';
import {
  FieldOfficerResponseDto,
  EnrolleeReferralResponseDto,
  EnrollmentAgentDto,
  SponsorResponseDto,
  TpaResponseDto,
} from '@clinify/facility-preferences/interface/enrollment-agent.dto';
import {
  ConsultationsTemplateInput,
  DischargeSummaryTemplateInput,
  FindingsTemplateInput,
  LabCommentsTemplateInput,
  MandatoryFields,
  MedicalReportTemplateInput,
  NewConsultationsTemplateInput,
  NewDischargeSummaryTemplateInput,
  NewFindingsTemplateInput,
  NewLabCommentsTemplateInput,
  NewMedicalReportTemplateInput,
  NewOperationNoteTemplateInput,
  OperationNoteTemplateInput,
} from '@clinify/facility-preferences/interface/mandatory-fields';
import { ChemoDiagnosisTemplateModel } from '@clinify/facility-preferences/models/chemo-diagnosis-template.model';
import { ConsultationsTemplateModel } from '@clinify/facility-preferences/models/consultations-template.model';
import { DischargeSummaryTemplateModel } from '@clinify/facility-preferences/models/discharge-summary-template.model';
import { FacilityPreferenceModel } from '@clinify/facility-preferences/models/facility-preference.model';
import { FindingsTemplateModel } from '@clinify/facility-preferences/models/findings-template.model';
import { LabCommentsTemplateModel } from '@clinify/facility-preferences/models/lab-comments-template.model';
import { MedicalReportTemplateModel } from '@clinify/facility-preferences/models/medical-report-template.model';
import { OperationNoteTemplateModel } from '@clinify/facility-preferences/models/operation-note-template.model';
import { SpecialistAccessModel } from '@clinify/facility-preferences/models/specialist-access.model';
import { CommissionPayerResponse } from '@clinify/facility-preferences/responses/commission-payer.response';
import { FacilityPreferenceService } from '@clinify/facility-preferences/services/facility-preference.service';
import { MailTemplate } from '@clinify/facility-preferences/validators/mail-template.input';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { AppServices } from '@clinify/shared/enums/services';
import { UserType } from '@clinify/shared/enums/users';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { filterFacilityPreferenceUpdated } from '@clinify/utils/subscriptions/filters';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const { FacilityPreferenceUpdated } = SubscriptionTypes;

@LogService(AppServices.FacilityPreference)
@UseGuards(GqlAuthGuard)
@Resolver(() => FacilityPreferenceModel)
export class FacilityPreferenceResolver {
  constructor(
    private readonly facilityPreferenceService: FacilityPreferenceService,
    @Inject(PUB_SUB) private readonly pubSub: RedisPubSub,
  ) {}

  @UseGuards(GqlAuthGuard)
  @Query(() => FacilityPreferenceModel)
  getFacilityPreference(
    @Args({ name: 'hospitalId' }) hospitalId: string,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.getFacilityPreference(hospitalId);
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  updateWelcomeMailTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'hospitalId', type: () => String }) hospitalId: string,
    @Args({ name: 'input', type: () => MailTemplate, nullable: true })
    input: MailTemplate,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateWelcomeMailTemplate(
      profile,
      hospitalId,
      input,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  updatePatientAccessType(
    @CurrentProfile() profile: ProfileModel,
    @Args('mode', { type: () => DefaultPatientAccessType })
    mode: DefaultPatientAccessType,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updatePatientAccessType(
      profile.hospitalId,
      mode,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  async updateFormsMandatoryFields(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'hospitalId', type: () => String }) hospitalId: string,
    @Args({ name: 'input', type: () => MandatoryFields, nullable: true })
    input: MandatoryFields,
  ): Promise<FacilityPreferenceModel> {
    const item =
      await this.facilityPreferenceService.updateFormsMandatoryFields(
        profile,
        hospitalId,
        input,
      );
    this.pubSub.publish(FacilityPreferenceUpdated, {
      [FacilityPreferenceUpdated]: item,
    });

    return item;
  }

  @Subscription(() => FacilityPreferenceModel, {
    name: FacilityPreferenceUpdated,
    filter: filterFacilityPreferenceUpdated,
  })
  updateFacilityPreferenceHandler(
    @Args('hospitalId') _hospitalId: string,
  ): AsyncIterator<any> {
    return this.pubSub.asyncIterator(FacilityPreferenceUpdated);
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationRadiographer,
      UserType.OrganizationRadiologist,
    ),
  )
  @Mutation(() => FindingsTemplateModel)
  async saveFindingsTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => NewFindingsTemplateInput,
      nullable: true,
    })
    input: NewFindingsTemplateInput,
  ): Promise<FindingsTemplateModel> {
    return this.facilityPreferenceService.saveFindingsTemplate(profile, input);
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationRadiographer,
      UserType.OrganizationRadiologist,
    ),
  )
  @Mutation(() => FindingsTemplateModel)
  async updateFindingsTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => FindingsTemplateInput,
      nullable: true,
    })
    input: FindingsTemplateInput,
  ): Promise<FindingsTemplateModel> {
    return this.facilityPreferenceService.updateFindingsTemplate(
      profile,
      input,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationRadiographer,
      UserType.OrganizationRadiologist,
    ),
  )
  @Mutation(() => FindingsTemplateModel)
  async deleteFindingsTemplate(
    @Args({
      name: 'id',
      type: () => String,
      nullable: false,
    })
    id: string,
  ): Promise<FindingsTemplateModel> {
    return this.facilityPreferenceService.deleteFindingsTemplate(id);
  }

  @Query(() => [FindingsTemplateModel])
  fetchFindingsTemplates(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'facilityPreferenceId',
      type: () => String,
      nullable: true,
    })
    facilityPreferenceId?: string,
  ): Promise<FindingsTemplateModel[]> {
    if (!facilityPreferenceId) {
      return this.facilityPreferenceService.findByHospitalIdForFindingsTemplate(
        profile.hospitalId,
      );
    }
    return this.facilityPreferenceService.findByFacilityPreferenceIdForFindingsTemplate(
      facilityPreferenceId,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationLabTechnician,
    ),
  )
  @Mutation(() => LabCommentsTemplateModel)
  async saveLabCommentsTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => NewLabCommentsTemplateInput,
      nullable: true,
    })
    input: NewLabCommentsTemplateInput,
  ): Promise<LabCommentsTemplateModel> {
    return this.facilityPreferenceService.saveLabCommentsTemplate(
      profile,
      input,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationLabTechnician,
    ),
  )
  @Mutation(() => LabCommentsTemplateModel)
  async updateLabCommentsTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => LabCommentsTemplateInput,
      nullable: true,
    })
    input: LabCommentsTemplateInput,
  ): Promise<LabCommentsTemplateModel> {
    return this.facilityPreferenceService.updateLabCommentsTemplate(
      profile,
      input,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationLabTechnician,
    ),
  )
  @Mutation(() => LabCommentsTemplateModel)
  async deleteLabCommentsTemplate(
    @Args({
      name: 'id',
      type: () => String,
      nullable: false,
    })
    id: string,
  ): Promise<LabCommentsTemplateModel> {
    return this.facilityPreferenceService.deleteLabCommentsTemplate(id);
  }

  @Query(() => [LabCommentsTemplateModel])
  fetchLabCommentsTemplates(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'facilityPreferenceId',
      type: () => String,
      nullable: true,
    })
    facilityPreferenceId?: string,
  ): Promise<LabCommentsTemplateModel[]> {
    if (!facilityPreferenceId) {
      return this.facilityPreferenceService.findByHospitalIdForLabCommentsTemplate(
        profile.hospitalId,
      );
    }
    return this.facilityPreferenceService.findByFacilityPreferenceIdForLabCommentsTemplate(
      facilityPreferenceId,
    );
  }

  @Query(() => [MedicalReportTemplateModel])
  fetchMedicalReportTemplates(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'facilityPreferenceId',
      type: () => String,
      nullable: true,
    })
    facilityPreferenceId?: string,
  ): Promise<MedicalReportTemplateModel[]> {
    if (!facilityPreferenceId)
      return this.facilityPreferenceService.findByHospitalIdForMedicalReportTemplate(
        profile.hospitalId,
      );
    return this.facilityPreferenceService.findByFacilityPreferenceIdForMedicalReportTemplate(
      facilityPreferenceId,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationDoctor,
    ),
  )
  @Mutation(() => MedicalReportTemplateModel)
  saveMedicalReportTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => NewMedicalReportTemplateInput,
      nullable: true,
    })
    input: NewMedicalReportTemplateInput,
  ): Promise<MedicalReportTemplateModel> {
    return this.facilityPreferenceService.saveMedicalReportTemplate(
      profile,
      input,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationDoctor,
    ),
  )
  @Mutation(() => MedicalReportTemplateModel)
  updateMedicalReportTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => MedicalReportTemplateInput,
      nullable: true,
    })
    input: MedicalReportTemplateInput,
  ): Promise<MedicalReportTemplateModel> {
    return this.facilityPreferenceService.updateMedicalReportTemplate(
      profile,
      input,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationDoctor,
    ),
  )
  @Mutation(() => MedicalReportTemplateModel)
  deleteMedicalReportTemplate(
    @Args({
      name: 'id',
      type: () => String,
      nullable: false,
    })
    id: string,
  ): Promise<MedicalReportTemplateModel> {
    return this.facilityPreferenceService.deleteMedicalReportTemplate(id);
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationRadiologist,
      UserType.OrganizationRadiographer,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  updateRadiologyContrastMode(
    @CurrentProfile() profile: ProfileModel,
    @Args('mode', { type: () => Boolean })
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateRadiologyContrastMode(
      profile.hospitalId,
      mode,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  updateDashboardColourMode(
    @CurrentProfile() profile: ProfileModel,
    @Args('mode', { type: () => Boolean })
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateDashboardColourMode(
      profile.hospitalId,
      mode,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  updateShowServiceDetails(
    @CurrentProfile() profile: ProfileModel,
    @Args('mode', { type: () => Boolean })
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateShowServiceDetails(
      profile.hospitalId,
      mode,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  updateInventoryClass(
    @CurrentProfile() profile: ProfileModel,
    @Args('inventoryClass', { type: () => String })
    inventoryClass: string,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateInventoryClass(
      profile,
      inventoryClass,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  updateRolesServiceDetailsIsHidden(
    @CurrentProfile() profile: ProfileModel,
    @Args('roles', { type: () => [String] })
    roles: string[],
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateRolesServiceDetailsIsHidden(
      profile.hospitalId,
      roles,
    );
  }

  @UseGuards(RolesAuthGuard(UserType.OrganizationAdmin))
  @Mutation(() => CommissionPayerResponse)
  updateDefaultCommissionPayer(
    @CurrentProfile() mutator: ProfileModel,
    @Args('facilityPreferenceId') facilityPreferenceId: string,
    @Args('commissionPayer', { type: () => CommissionPayer })
    commissionPayer: CommissionPayer,
  ): Promise<CommissionPayerResponse> {
    return this.facilityPreferenceService.updateDefaultCommissionPayer(
      mutator.hospitalId,
      facilityPreferenceId,
      commissionPayer,
    );
  }

  @UseGuards(RolesAuthGuard(UserType.OrganizationAdmin))
  @Mutation(() => FacilityPreferenceModel)
  updatePayoutCommissionPayer(
    @CurrentProfile() mutator: ProfileModel,
    @Args('payoutCommissionPayer', { type: () => PayoutCommissionPayer })
    payoutCommissionPayer: PayoutCommissionPayer,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updatePayoutCommissionPayer(
      mutator,
      payoutCommissionPayer,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.Admin,
      UserType.OrganizationStaffAdmin,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  updateFileNumberGenerate(
    @CurrentProfile() profile: ProfileModel,
    @Args('mode', { type: () => Boolean })
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateFileNumberGenerate(
      profile.hospital,
      mode,
    );
  }

  @UseGuards(RolesAuthGuard(UserType.OrganizationAdmin))
  @Mutation(() => FacilityPreferenceModel)
  updatePatientSurveyLinks(
    @CurrentProfile() profile: ProfileModel,
    @Args('facilityPreferenceId') facilityPreferenceId: string,
    @Args('outPatientLink', { type: () => String, nullable: true })
    outPatientLink: string,
    @Args('inPatientLink', { type: () => String, nullable: true })
    inPatientLink: string,
  ) {
    return this.facilityPreferenceService.updatePatientSurveyLinks(
      profile,
      facilityPreferenceId,
      outPatientLink,
      inPatientLink,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationDoctor,
      UserType.OrganizationNurse,
      UserType.Pharmacist,
    ),
  )
  @Mutation(() => ConsultationsTemplateModel)
  saveConsultationsTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: NewConsultationsTemplateInput,
  ): Promise<ConsultationsTemplateModel> {
    return this.facilityPreferenceService.saveConsultationsTemplate(
      profile,
      input,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationDoctor,
      UserType.OrganizationNurse,
      UserType.Pharmacist,
    ),
  )
  @Mutation(() => ConsultationsTemplateModel)
  updateConsultationsTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: ConsultationsTemplateInput,
  ): Promise<ConsultationsTemplateModel> {
    return this.facilityPreferenceService.updateConsultationsTemplate(
      profile,
      input,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationDoctor,
      UserType.OrganizationNurse,
      UserType.Pharmacist,
    ),
  )
  @Mutation(() => ConsultationsTemplateModel)
  deleteConsultationsTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args('id') id: string,
  ): Promise<ConsultationsTemplateModel> {
    return this.facilityPreferenceService.deleteConsultationsTemplate(
      profile,
      id,
    );
  }

  @Query(() => [ConsultationsTemplateModel])
  findByHospitalIdConsultationsTemplates(
    @CurrentProfile() profile: ProfileModel,
    @Args('hospitalId') hospitalId: string,
  ) {
    return this.facilityPreferenceService.findByHospitalIdConsultationsTemplate(
      profile,
      hospitalId,
    );
  }

  @Query(() => [ConsultationsTemplateModel])
  findByFacilityPreferenceIdConsultationsTemplates(
    @CurrentProfile() profile: ProfileModel,
    @Args('facilityPreferenceId') facilityPreferenceId: string,
  ) {
    return this.facilityPreferenceService.findByFacilityPreferenceIdConsultationsTemplate(
      profile,
      facilityPreferenceId,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationBillingOfficer,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationCashier,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  updateReceiptSize(
    @CurrentProfile() mutator: ProfileModel,
    @Args('facilityPreferenceId') facilityPreferenceId: string,
    @Args('receiptSize') receiptSize: string,
  ) {
    return this.facilityPreferenceService.updateReceiptSize(
      mutator,
      facilityPreferenceId,
      receiptSize,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationDoctor,
      UserType.OrganizationNurse,
    ),
  )
  @Mutation(() => DischargeSummaryTemplateModel)
  saveDischargeSummaryTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: NewDischargeSummaryTemplateInput,
  ): Promise<DischargeSummaryTemplateModel> {
    return this.facilityPreferenceService.saveDischargeSummaryTemplate(
      profile,
      input,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationDoctor,
      UserType.OrganizationNurse,
    ),
  )
  @Mutation(() => DischargeSummaryTemplateModel)
  updateDischargeSummaryTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: DischargeSummaryTemplateInput,
  ): Promise<DischargeSummaryTemplateModel> {
    return this.facilityPreferenceService.updateDischargeSummaryTemplate(
      profile,
      input,
    );
  }

  @Mutation(() => DischargeSummaryTemplateModel)
  deleteDischargeSummaryTemplate(
    @Args('id') id: string,
  ): Promise<DischargeSummaryTemplateModel> {
    return this.facilityPreferenceService.deleteDischargeSummaryTemplate(id);
  }

  @Query(() => [DischargeSummaryTemplateModel])
  fetchDischargeSummaryTemplates(
    @CurrentProfile() profile: ProfileModel,
    @Args('facilityPreferenceId', { nullable: true })
    facilityPreferenceId?: string,
  ) {
    if (!facilityPreferenceId)
      return this.facilityPreferenceService.findByHospitalIdDischargeSummaryTemplates(
        profile.hospitalId,
      );
    return this.facilityPreferenceService.findByFacilityPreferenceIdDischargeSummaryTemplates(
      facilityPreferenceId,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationDoctor,
      UserType.OrganizationNurse,
    ),
  )
  @Mutation(() => ChemoDiagnosisTemplateModel)
  saveChemoDiagnosisTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: NewChemoDiagnosisTemplate,
  ): Promise<ChemoDiagnosisTemplateModel> {
    return this.facilityPreferenceService.saveChemoDiagnosisTemplate(
      profile,
      input,
    );
  }

  @Mutation(() => ChemoDiagnosisTemplateModel)
  updateChemoDiagnosisTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args('id') id: string,
    @Args('input') input: ChemoDiagnosisTemplate,
  ): Promise<ChemoDiagnosisTemplateModel> {
    return this.facilityPreferenceService.updateChemoDiagnosisTemplate(
      profile,
      id,
      input,
    );
  }
  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationDoctor,
      UserType.OrganizationNurse,
    ),
  )
  @Mutation(() => ChemoDiagnosisTemplateModel)
  deleteChemoDiagnosisTemplate(
    @Args('id') id: string,
  ): Promise<ChemoDiagnosisTemplateModel> {
    return this.facilityPreferenceService.deleteChemoDiagnosisTemplate(id);
  }

  @Query(() => [ChemoDiagnosisTemplateModel])
  fetchChemoDiagnosisTemplates(
    @CurrentProfile() profile: ProfileModel,
    @Args('section', { nullable: true }) section?: string,
    @Args('chemoDiagnosis', { nullable: true }) chemoDiagnosis?: string,
    @Args('facilityPreferenceId', { nullable: true })
    facilityPreferenceId?: string,
  ) {
    if (facilityPreferenceId)
      return this.facilityPreferenceService.findByFacilityPreferenceIdChemoDiagnosisTemplates(
        facilityPreferenceId,
        section,
        chemoDiagnosis,
      );
    return this.facilityPreferenceService.findByHospitalIdChemoDiagnosisTemplates(
      profile?.hospital?.hqFacilityId || profile.hospitalId,
      section,
      chemoDiagnosis,
    );
  }

  @Query(() => ChemoDiagnosisTemplateModel)
  fetchChemoDiagnosisTemplate(
    @Args('id', { description: 'Template ID' }) id: string,
  ): Promise<ChemoDiagnosisTemplateModel> {
    return this.facilityPreferenceService.getOneChemoDiagnosisTemplate(id);
  }

  @Mutation(() => FacilityPreferenceModel)
  updateTariffsToUse(
    @Args('facilityPreferenceId') facilityPreferenceId: string,
    @Args('useHQFacilityTariffs') useHQFacilityTariffs: boolean,
    @CurrentProfile() mutator: ProfileModel,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateTariffsToUse(
      facilityPreferenceId,
      useHQFacilityTariffs,
      mutator,
    );
  }

  @Mutation(() => FacilityPreferenceModel)
  updateInventoryToUse(
    @Args('facilityPreferenceId') facilityPreferenceId: string,
    @Args('useHQFacilityInventory') useHQFacilityInventory: boolean,
    @CurrentProfile() mutator: ProfileModel,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateInventoryToUse(
      facilityPreferenceId,
      useHQFacilityInventory,
      mutator,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationRecordOfficer,
    ),
  )
  @Mutation(() => SpecialistAccessModel)
  updateSpecialistAccess(
    @CurrentProfile() profile: ProfileModel,
    @Args('patientId') patientId: string,
    @Args('specialistIds', { type: () => [String] }) specialistIds: string[],
  ): Promise<SpecialistAccessModel> {
    return this.facilityPreferenceService.updateSpecialistAccess(
      profile,
      patientId,
      specialistIds,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationRecordOfficer,
    ),
  )
  @Query(() => SpecialistAccessModel, {
    nullable: true,
  })
  getSpecialistAccess(
    @CurrentProfile() profile: ProfileModel,
    @Args('patientId') patientId: string,
  ): Promise<SpecialistAccessModel> {
    return this.facilityPreferenceService.getSpecialistAccess(
      profile,
      patientId,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationStaffAdmin,
      UserType.OrganizationRecordOfficer,
    ),
  )
  @Query(() => [SpecialistAccessModel])
  findByHospitalIdSpecialistAccess(
    @CurrentProfile() profile: ProfileModel,
    @Args('hospitalId', { nullable: true }) hospitalId?: string,
  ): Promise<SpecialistAccessModel[]> {
    return this.facilityPreferenceService.findByHospitalIdSpecialistAccess(
      hospitalId || profile.hospitalId,
    );
  }

  @Mutation(() => OperationNoteTemplateModel)
  saveOperationNoteTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: NewOperationNoteTemplateInput,
  ): Promise<OperationNoteTemplateModel> {
    return this.facilityPreferenceService.saveOperationNoteTemplate(
      profile,
      input,
    );
  }

  @Mutation(() => OperationNoteTemplateModel)
  updateOperationNoteTemplate(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: OperationNoteTemplateInput,
  ): Promise<OperationNoteTemplateModel> {
    return this.facilityPreferenceService.updateOperationNoteTemplate(
      profile,
      input,
    );
  }

  @Mutation(() => OperationNoteTemplateModel)
  deleteOperationNoteTemplate(
    @Args('id') id: string,
  ): Promise<OperationNoteTemplateModel> {
    return this.facilityPreferenceService.deleteOperationNoteTemplate(id);
  }

  @Query(() => [OperationNoteTemplateModel])
  fetchOperationNoteTemplates(
    @CurrentProfile() profile: ProfileModel,
    @Args('facilityPreferenceId', { nullable: true })
    facilityPreferenceId?: string,
  ): Promise<OperationNoteTemplateModel[]> {
    if (!facilityPreferenceId)
      return this.facilityPreferenceService.findByHospitalIdOperationNoteTemplates(
        profile.hospitalId,
      );
    return this.facilityPreferenceService.findByFacilityPreferenceIdOperationNoteTemplates(
      facilityPreferenceId,
    );
  }

  @Mutation(() => FacilityPreferenceModel)
  updateHmoSingleVisitPACode(
    @CurrentProfile() profile: ProfileModel,
    @Args('mode') mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateHmoSingleVisitPACode(
      profile.hospitalId,
      mode,
    );
  }

  @Mutation(() => FacilityPreferenceModel)
  updateCustomPaFormatType(
    @CurrentProfile() profile: ProfileModel,
    @Args('mode') mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateCustomPaFormatType(
      profile.hospitalId,
      mode,
    );
  }

  @Mutation(() => FacilityPreferenceModel)
  updateEnableBusinessRulePreventSubmit(
    @CurrentProfile() profile: ProfileModel,
    @Args('mode') mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateEnableBusinessRulePreventSubmit(
      profile.hospitalId,
      mode,
    );
  }

  @Mutation(() => FacilityPreferenceModel)
  updatePatientRegistrationFee(
    @CurrentProfile() mutator: ProfileModel,
    @Args('registrationFee') registrationFee: number,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updatePatientRegistrationFee(
      mutator.hospitalId,
      registrationFee,
    );
  }

  @Mutation(() => FacilityPreferenceModel)
  updateEnrolleeCapitationAmount(
    @CurrentProfile() mutator: ProfileModel,
    @Args('enrolleeCapitationAmount') enrolleeCapitationAmount: number,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateEnrolleeCapitationAmount(
      mutator.hospitalId,
      enrolleeCapitationAmount,
    );
  }

  @UseGuards(RolesAuthGuard(UserType.OrganizationAdmin, UserType.ClaimAdmin))
  @Mutation(() => FacilityPreferenceModel)
  updateAutoProcessClaims(
    @CurrentProfile() mutator: ProfileModel,
    @Args('mode') mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateAutoProcessClaims(
      mutator.hospitalId,
      mode,
      mutator,
    );
  }

  @UseGuards(RolesAuthGuard(UserType.OrganizationAdmin, UserType.ClaimAdmin))
  @Mutation(() => FacilityPreferenceModel)
  updateAutoProcessPreauthorizations(
    @CurrentProfile() mutator: ProfileModel,
    @Args('mode') mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateAutoProcessPreauthorizations(
      mutator.hospitalId,
      mode,
      mutator,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationStaffAdmin,
      UserType.EnrollmentAdmin,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  async updateEnrollmentAgentAssignments(
    @CurrentProfile() profile: ProfileModel,
    @Args('agent', { type: () => EnrollmentAgentDto })
    agent: EnrollmentAgentDto,
  ): Promise<FacilityPreferenceModel> {
    const item =
      await this.facilityPreferenceService.updateEnrollmentAgentAssignments(
        profile,
        agent,
      );
    this.pubSub.publish(FacilityPreferenceUpdated, {
      [FacilityPreferenceUpdated]: item,
    });

    return item;
  }
  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationStaffAdmin,
      UserType.EnrollmentAdmin,
      UserType.FieldManager,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  async updateEnrollmentAgency(
    @CurrentProfile() profile: ProfileModel,
    @Args('administrationAgency') administrationAgency: string,
    @Args('enrollmentAgency', { type: () => [String] }) agency: string[],
  ): Promise<FacilityPreferenceModel> {
    const item = await this.facilityPreferenceService.updateEnrollmentAgency(
      profile,
      administrationAgency,
      agency,
    );
    this.pubSub.publish(FacilityPreferenceUpdated, {
      [FacilityPreferenceUpdated]: item,
    });

    return item;
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationStaffAdmin,
      UserType.FieldManager,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  async updateEnrolleeSponsors(
    @CurrentProfile() profile: ProfileModel,
    @Args('sponsors', { type: () => [String] }) sponsors: string[],
  ): Promise<FacilityPreferenceModel> {
    const item = await this.facilityPreferenceService.updateEnrolleeSponsors(
      profile,
      sponsors,
    );
    this.pubSub.publish(FacilityPreferenceUpdated, {
      [FacilityPreferenceUpdated]: item,
    });

    return item;
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationStaffAdmin,
      UserType.FieldManager,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  async updateFieldOfficer(
    @CurrentProfile() profile: ProfileModel,
    @Args('fieldOfficer', { type: () => FieldOfficerResponseDto })
    fieldOfficer: FieldOfficerResponseDto,
  ): Promise<FacilityPreferenceModel> {
    const item = await this.facilityPreferenceService.updateFieldOfficer(
      profile,
      fieldOfficer,
    );
    this.pubSub.publish(FacilityPreferenceUpdated, {
      [FacilityPreferenceUpdated]: item,
    });

    return item;
  }

  @UseGuards(
    RolesAuthGuard(UserType.OrganizationAdmin, UserType.OrganizationStaffAdmin),
  )
  @Mutation(() => FacilityPreferenceModel)
  async updateEnrolleeReferral(
    @CurrentProfile() profile: ProfileModel,
    @Args('enrolleeReferral', { type: () => EnrolleeReferralResponseDto })
    enrolleeReferral: EnrolleeReferralResponseDto,
  ): Promise<FacilityPreferenceModel> {
    const item = await this.facilityPreferenceService.updateEnrolleeReferral(
      profile,
      enrolleeReferral,
    );
    this.pubSub.publish(FacilityPreferenceUpdated, {
      [FacilityPreferenceUpdated]: item,
    });

    return item;
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationStaffAdmin,
      UserType.FieldManager,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  async updateSponsorAssignment(
    @CurrentProfile() profile: ProfileModel,
    @Args('sponsor', { type: () => SponsorResponseDto })
    sponsor: SponsorResponseDto,
  ): Promise<FacilityPreferenceModel> {
    const item = await this.facilityPreferenceService.updateSponsorAssignment(
      profile,
      sponsor,
    );
    this.pubSub.publish(FacilityPreferenceUpdated, {
      [FacilityPreferenceUpdated]: item,
    });

    return item;
  }

  @Mutation(() => FacilityPreferenceModel)
  updateEnrolleeCapitationAmountByPlanType(
    @CurrentProfile() mutator: ProfileModel,
    @Args('planTypeId') planTypeId: string,
    @Args('planTypeName') planTypeName: string,
    @Args('capitationAmount') capitationAmount: number,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateEnrolleeCapitationAmountByPlanType(
      mutator,
      planTypeId,
      planTypeName,
      capitationAmount,
    );
  }

  @Mutation(() => FacilityPreferenceModel)
  updateEnrolleeCapitationAmountPerPlan(
    @CurrentProfile() mutator: ProfileModel,
    @Args('enabled') enabled: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.facilityPreferenceService.updateEnrolleeCapitationAmountPerPlan(
      mutator,
      enabled,
    );
  }

  @UseGuards(
    RolesAuthGuard(
      UserType.OrganizationAdmin,
      UserType.OrganizationStaffAdmin,
      UserType.EnrollmentAdmin,
    ),
  )
  @Mutation(() => FacilityPreferenceModel)
  async updateEnrolleeTpaAssignment(
    @CurrentProfile() profile: ProfileModel,
    @Args('tpaInput', { type: () => TpaResponseDto })
    tpaInput: TpaResponseDto,
  ): Promise<FacilityPreferenceModel> {
    const item =
      await this.facilityPreferenceService.updateEnrolleeTpaAssignment(
        profile,
        tpaInput,
      );
    this.pubSub.publish(FacilityPreferenceUpdated, {
      [FacilityPreferenceUpdated]: item,
    });

    return item;
  }
}
