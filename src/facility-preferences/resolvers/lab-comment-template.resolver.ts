import { <PERSON><PERSON>, ResolveField, Resolver } from '@nestjs/graphql';
import { LabCommentsTemplateModel } from '@clinify/facility-preferences/models/lab-comments-template.model';

@Resolver(() => LabCommentsTemplateModel)
export class LabCommentTemplateResolver {
  @ResolveField(() => String, { name: 'comment', nullable: true })
  resolveComment(@Parent() root: LabCommentsTemplateModel) {
    return typeof root.comment !== 'string'
      ? JSON.stringify(root.comment)
      : root.comment;
  }
}
