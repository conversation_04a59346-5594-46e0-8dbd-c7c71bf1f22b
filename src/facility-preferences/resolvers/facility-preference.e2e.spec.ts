/* eslint-disable max-len */
/* eslint-disable max-lines */
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { GraphQLModule } from '@nestjs/graphql';
import { getModelToken } from '@nestjs/mongoose';
import { Test } from '@nestjs/testing';
import { getRepositoryToken, TypeOrmModule } from '@nestjs/typeorm';
import request from 'supertest';
import { FacilityPreferenceModel } from '../models/facility-preference.model';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { TestDataSourceOptions } from '@clinify/data-source';
import { FacilityPreferenceModule } from '@clinify/facility-preferences/facility-preference.module';
import { NewChemoDiagnosisTemplate } from '@clinify/facility-preferences/interface/chemo-diagnosis.interface';
import {
  DefaultInvImage,
  InventoryUpload,
} from '@clinify/integrations/inventory-blk/entities/FileUpload.entity';
import { PriceUpload } from '@clinify/integrations/price-blk/entities/PriceFileUpload.entity';
import { UserType } from '@clinify/shared/enums/users';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';
import { facilityPreferenceMock } from '@mocks/factories/facility-preference.factory';
import gqlAuthGuardMock, { gqlUserMock } from '@mocks/gqlAuthGuard.mock';

const findingsTemplateMock = {
  name: 'test',
  findings: 'My findings',
  impression: 'My impression',
  facilityPreferenceId: '1a971260-af87-4205-af08-d9bdf9f0f806',
  createdDate: new Date('2023-09-19T20:24:56.057Z'),
  updatedDate: new Date('2023-09-19T20:24:56.057Z'),
  id: '27dca10a-3d1e-480f-a35b-541f71e250a4',
};
const labCommentsTemplateMock = {
  name: 'test',
  comment: 'My lab comments',
  facilityPreferenceId: '1a971260-af87-4205-af08-d9bdf9f0f806',
  createdDate: new Date('2023-09-19T20:24:56.057Z'),
  updatedDate: new Date('2023-09-19T20:24:56.057Z'),
  id: '27dca10a-3d1e-480f-a35b-541f71e250a4',
};
const richtext =
  '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"1","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}';
const MockConsultationTemplate = {
  complaints: richtext,
  historyComplaints: richtext,
  healthEducation: richtext,
  reviewSystems: richtext,
  physicalExamination: richtext,
  audiometry: richtext,
  treatmentPlan: richtext,
  name: 'Template 2',
  id: 'a511960b-140d-4b75-9278-c6a0055bacbf',
};
const dischargeSummaryTemplateMock = {
  name: 'test',
  summary: richtext,
  facilityPreferenceId: '1a971260-af87-4205-af08-d9bdf9f0f806',
  createdDate: new Date('2023-09-19T20:24:56.057Z'),
  updatedDate: new Date('2023-09-19T20:24:56.057Z'),
  id: '27dca10a-3d1e-480f-a35b-541f71e250a4',
};

const inputData: NewChemoDiagnosisTemplate = {
  facilityPreferenceId: '',
  type: 'PreChemotherapy',
  combinationName: 'Test',
  section: 'chemo',
  cycles: [
    {
      cycleNumber: 1,
      drugs: [
        {
          day: '1',
          drugName: 'Test',
          drugId: '1',
          dosage: '50ml',
          dosagePercentage: 'full',
          route: 'Infusion',
          infusionUsed: 'No',
          ref: 'some-ref',
          note: 'some note',
        },
      ],
    },
  ],
};
const chemoDiagnosisData = {
  id: 'e11db251-f9b2-450b-93df-dbdb56d0b295',
  type: 'PreChemotherapy',
  combinationName: 'Test',
  cycles: [
    {
      id: 'b49e0029-46bb-47e6-a16d-5bd04e12b4ad',
      ...inputData.cycles[0],
    },
  ],
};
const specialistAccessMock = {
  id: 'id',
  specialistIds: ['specialistId'],
  profile: gqlUserMock.defaultProfile,
};
const operationNoteTemplateMock = {
  name: 'test',
  note: 'My note',
  postNote: 'My post note',
  facilityPreferenceId: '1a971260-af87-4205-af08-d9bdf9f0f806',
  createdDate: new Date('2023-09-19T20:24:56.057Z'),
  updatedDate: new Date('2023-09-19T20:24:56.057Z'),
  id: '27dca10a-3d1e-480f-a35b-541f71e250a4',
};
const facilityPreferenceRepositoryMock = {
  getFacilityPreference: jest.fn(() => Promise.resolve(facilityPreferenceMock)),
  updateWelcomeMailTemplate: jest.fn(() =>
    Promise.resolve({
      ...facilityPreferenceMock,
      welcomeMailTemplate: { body: 'body', subject: 'subject' },
    }),
  ),
  updateBookAppointmentMailTemplate: jest.fn(() =>
    Promise.resolve({
      ...facilityPreferenceMock,
      bookAppointmentMailTemplate: { body: 'body', subject: 'subject' },
    }),
  ),
  saveFindingsTemplate: jest.fn(() => Promise.resolve(findingsTemplateMock)),
  updateFindingsTemplate: jest.fn(() =>
    Promise.resolve({
      ...findingsTemplateMock,
      findings: 'My findings 1',
      impression: 'My impression 1',
    }),
  ),
  deleteFindingsTemplate: jest.fn(() => Promise.resolve(findingsTemplateMock)),
  findByFacilityPreferenceIdForFindingsTemplate: jest.fn(() =>
    Promise.resolve([findingsTemplateMock]),
  ),
  findByHospitalIdForFindingsTemplate: jest.fn(() =>
    Promise.resolve([findingsTemplateMock]),
  ),
  saveLabCommentsTemplate: jest.fn(() =>
    Promise.resolve(labCommentsTemplateMock),
  ),
  updateLabCommentsTemplate: jest.fn(() =>
    Promise.resolve({
      ...labCommentsTemplateMock,
      comment: 'My lab comments 1',
    }),
  ),
  deleteLabCommentsTemplate: jest.fn(() =>
    Promise.resolve(labCommentsTemplateMock),
  ),
  findByFacilityPreferenceIdForLabCommentsTemplate: jest.fn(() =>
    Promise.resolve([labCommentsTemplateMock]),
  ),
  findByHospitalIdForLabCommentsTemplate: jest.fn(() =>
    Promise.resolve([labCommentsTemplateMock]),
  ),
  saveConsultationsTemplate: jest.fn(() =>
    Promise.resolve(MockConsultationTemplate),
  ),
  updateConsultationsTemplate: jest.fn(() =>
    Promise.resolve(MockConsultationTemplate),
  ),
  deleteConsultationsTemplate: jest.fn(() =>
    Promise.resolve(MockConsultationTemplate),
  ),
  findByHospitalIdConsultationsTemplate: jest.fn(() =>
    Promise.resolve([MockConsultationTemplate]),
  ),
  findByFacilityPreferenceIdConsultationsTemplate: jest.fn(() =>
    Promise.resolve([MockConsultationTemplate]),
  ),
  saveDischargeSummaryTemplate: jest.fn(() =>
    Promise.resolve(dischargeSummaryTemplateMock),
  ),
  updateDischargeSummaryTemplate: jest.fn(() =>
    Promise.resolve(dischargeSummaryTemplateMock),
  ),
  deleteDischargeSummaryTemplate: jest.fn(() =>
    Promise.resolve(dischargeSummaryTemplateMock),
  ),
  findByFacilityPreferenceIdDischargeSummaryTemplates: jest.fn(() =>
    Promise.resolve([dischargeSummaryTemplateMock]),
  ),
  findByHospitalIdDischargeSummaryTemplates: jest.fn(() =>
    Promise.resolve([dischargeSummaryTemplateMock]),
  ),
  saveChemoDiagnosisTemplate: jest.fn(() =>
    Promise.resolve(chemoDiagnosisData),
  ),
  updateChemoDiagnosisTemplate: jest.fn(() =>
    Promise.resolve(chemoDiagnosisData),
  ),
  deleteChemoDiagnosisTemplate: jest.fn(() =>
    Promise.resolve(chemoDiagnosisData),
  ),
  findByFacilityPreferenceIdChemoDiagnosisTemplates: jest.fn(() =>
    Promise.resolve([chemoDiagnosisData]),
  ),
  findByHospitalIdChemoDiagnosisTemplates: jest.fn(() =>
    Promise.resolve([chemoDiagnosisData]),
  ),
  updateSpecialistAccess: jest.fn(() => Promise.resolve(specialistAccessMock)),
  getSpecialistAccess: jest.fn(() => Promise.resolve(specialistAccessMock)),
  findByHospitalIdSpecialistAccess: jest.fn(() =>
    Promise.resolve([specialistAccessMock]),
  ),
  saveOperationNoteTemplate: jest.fn(() =>
    Promise.resolve(operationNoteTemplateMock),
  ),
  updateOperationNoteTemplate: jest.fn(() => {
    return Promise.resolve({
      id: operationNoteTemplateMock.id,
      name: 'test',
      note: 'My note 1',
      postNote: 'My post note 1',
    });
  }),
  deleteOperationNoteTemplate: jest.fn(() =>
    Promise.resolve(operationNoteTemplateMock),
  ),
  findByFacilityPreferenceIdOperationNoteTemplates: jest.fn(() =>
    Promise.resolve([operationNoteTemplateMock]),
  ),
  findByHospitalIdOperationNoteTemplates: jest.fn(() =>
    Promise.resolve([operationNoteTemplateMock]),
  ),
};

const profileRepo = {
  findOne: jest.fn(() => gqlUserMock.defaultProfile),
  findOneOrFail: jest.fn(() => gqlUserMock.defaultProfile),
};

describe('FacilityPreferenceController', () => {
  let testHttpServer: request.SuperTest<request.Test>;
  let app: INestApplication;

  beforeAll(async () => {
    const builder = await Test.createTestingModule({
      imports: [
        FacilityPreferenceModule,
        TypeOrmModule.forRoot(TestDataSourceOptions),
        EventEmitterModule.forRoot(),
        GraphQLModule.forRoot<ApolloDriverConfig>({
          debug: false,
          playground: false,
          driver: ApolloDriver,
          autoSchemaFile: true,
          installSubscriptionHandlers: true,
          subscriptions: {
            'graphql-ws': {
              onConnect: (connectionParams: { [key: string]: any }) => {
                return {
                  headers: {
                    ...connectionParams,
                  },
                };
              },
            },
          },
          include: [FacilityPreferenceModule],
          context: ({ req }) => ({ req }),
        }),
      ],
    })
      .overrideProvider(getRepositoryToken(FacilityPreferenceModel))
      .useValue(facilityPreferenceRepositoryMock)
      .overrideProvider(getRepositoryToken(ProfileModel))
      .useValue(profileRepo)
      .overrideGuard(GqlAuthGuard)
      .useValue(
        gqlAuthGuardMock(UserType.OrganizationAdmin, {
          ...gqlUserMock,
          profiles: [
            {
              ...gqlUserMock.profiles[0],
              hospitalId: facilityPreferenceMock.hospitalId,
            },
          ],
          defaultProfile: {
            ...gqlUserMock.profiles[0],
            hospitalId: facilityPreferenceMock.hospitalId,
          },
        } as UserModel),
      )
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .compile();

    app = builder.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    testHttpServer = await request(app.getHttpServer());
  });

  afterAll(async () => await app.close());

  it('getFacilityPreference', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          query {
            getFacilityPreference(hospitalId: "${facilityPreferenceMock.id}") {
              id
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.getFacilityPreference;
        expect(data).toEqual(
          expect.objectContaining({ id: facilityPreferenceMock.id }),
        );
      })
      .end(done);
  });

  it('updateWelcomeMailTemplate', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            updateWelcomeMailTemplate(hospitalId: "${facilityPreferenceMock.hospitalId}", input: {body: "body", subject: "subject"}) {
              id
              welcomeMailTemplate {
                body
                subject
              }
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.updateWelcomeMailTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: facilityPreferenceMock.id,
            welcomeMailTemplate: {
              subject: 'subject',
              body: 'body',
            },
          }),
        );
      })
      .end(done);
  });

  it('saveFindingsTemplate()', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            saveFindingsTemplate(input: {name: "test", findings: "My findings", impression: "My impression", facilityPreferenceId: "${facilityPreferenceMock.id}"}) {
              id
              name
              findings
              impression
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.saveFindingsTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: findingsTemplateMock.id,
            name: 'test',
            findings: 'My findings',
            impression: 'My impression',
          }),
        );
      })
      .end(done);
  });

  it('updateFindingsTemplate()', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            updateFindingsTemplate(input: {name: "test", findings: "My findings 1", impression: "My impression 1", facilityPreferenceId: "${facilityPreferenceMock.id}", id: "${findingsTemplateMock.id}"}) {
              id
              name
              findings
              impression
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.updateFindingsTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: findingsTemplateMock.id,
            name: 'test',
            findings: 'My findings 1',
            impression: 'My impression 1',
          }),
        );
      })
      .end(done);
  });
  it('deleteFindingsTemplate()', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            deleteFindingsTemplate(id: "${findingsTemplateMock.id}") {
              id
              name
              findings
              impression
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.deleteFindingsTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: findingsTemplateMock.id,
            name: 'test',
            findings: 'My findings',
            impression: 'My impression',
          }),
        );
      })
      .end(done);
  });

  it('fetchFindingsTemplates() with facilityPreferenceId', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          query {
            fetchFindingsTemplates(facilityPreferenceId: "${facilityPreferenceMock.id}") {
              id
              name
              findings
              impression
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.fetchFindingsTemplates;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: findingsTemplateMock.id,
              name: 'test',
              findings: 'My findings',
              impression: 'My impression',
            }),
          ]),
        );
      })
      .end(done);
  });

  it('fetchFindingsTemplates() without facilityPreferenceId', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          query {
            fetchFindingsTemplates {
              id
              name
              findings
              impression
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.fetchFindingsTemplates;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: findingsTemplateMock.id,
              name: 'test',
              findings: 'My findings',
              impression: 'My impression',
            }),
          ]),
        );
      })
      .end(done);
  });

  it('saveLabCommentsTemplate()', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            saveLabCommentsTemplate(input: {name: "test", comment: "My lab comments", facilityPreferenceId: "${facilityPreferenceMock.id}"}) {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.saveLabCommentsTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: labCommentsTemplateMock.id,
            name: 'test',
          }),
        );
      })
      .end(done);
  });

  it('updateLabCommentsTemplate()', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            updateLabCommentsTemplate(input: {name: "test", comment: "My lab comments 1", facilityPreferenceId: "${facilityPreferenceMock.id}", id: "${labCommentsTemplateMock.id}"}) {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.updateLabCommentsTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: labCommentsTemplateMock.id,
            name: 'test',
          }),
        );
      })
      .end(done);
  });
  it('deleteLabCommentsTemplate()', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            deleteLabCommentsTemplate(id: "${labCommentsTemplateMock.id}") {
              id
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.deleteLabCommentsTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: labCommentsTemplateMock.id,
          }),
        );
      })
      .end(done);
  });

  it('fetchLabCommentsTemplates() with facilityPreferenceId', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          query {
            fetchLabCommentsTemplates(facilityPreferenceId: "${facilityPreferenceMock.id}") {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.fetchLabCommentsTemplates;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: labCommentsTemplateMock.id,
              name: 'test',
            }),
          ]),
        );
      })
      .end(done);
  });

  it('fetchLabCommentsTemplates() without facilityPreferenceId', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          query {
            fetchLabCommentsTemplates {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.fetchLabCommentsTemplates;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: labCommentsTemplateMock.id,
              name: 'test',
            }),
          ]),
        );
      })
      .end(done);
  });

  it('saveConsultationsTemplate() should save consultations template', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            saveConsultationsTemplate(input: { name: "Template 2", facilityPreferenceId: "id", complaints: "[]", historyComplaints: "[]",  healthEducation: "[]", reviewSystems: "[]", physicalExamination: "[]", audiometry: "[]", treatmentPlan: "[]"}) {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.saveConsultationsTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: MockConsultationTemplate.id,
            name: 'Template 2',
          }),
        );
      })
      .end(done);
  });

  it('updateConsultationsTemplate() should update consultations template', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            updateConsultationsTemplate(input: { id: "${MockConsultationTemplate.id}", name: "Template 2", facilityPreferenceId: "id", complaints: "[]", historyComplaints: "[]",  healthEducation: "[]", reviewSystems: "[]", physicalExamination: "[]", audiometry: "[]", treatmentPlan: "[]"}) {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.updateConsultationsTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: MockConsultationTemplate.id,
            name: 'Template 2',
          }),
        );
      })
      .end(done);
  });

  it('deleteConsultationsTemplate() should delete consultations template', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            deleteConsultationsTemplate( id: "${MockConsultationTemplate.id}") {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.deleteConsultationsTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: MockConsultationTemplate.id,
            name: 'Template 2',
          }),
        );
      })
      .end(done);
  });

  it('findByHospitalIdConsultationsTemplates() should find consultations template', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          query {
            findByHospitalIdConsultationsTemplates(hospitalId: "id") {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.findByHospitalIdConsultationsTemplates;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: MockConsultationTemplate.id,
              name: 'Template 2',
            }),
          ]),
        );
      })
      .end(done);
  });

  it('findByFacilityPreferenceIdConsultationsTemplates() should find consultations template', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          query {
            findByFacilityPreferenceIdConsultationsTemplates(facilityPreferenceId: "id") {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.findByFacilityPreferenceIdConsultationsTemplates;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({ id: MockConsultationTemplate.id }),
          ]),
        );
      })
      .end(done);
  });

  it('saveDischargeSummaryTemplate() should save discharge summary template', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            saveDischargeSummaryTemplate(input: { name: "test", summary: "[]", facilityPreferenceId: "id"}) {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.saveDischargeSummaryTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: dischargeSummaryTemplateMock.id,
            name: 'test',
          }),
        );
      })
      .end(done);
  });

  it('updateDischargeSummaryTemplate() should update discharge summary template', (done) => {
    jest
      .spyOn(facilityPreferenceRepositoryMock, 'updateDischargeSummaryTemplate')
      .mockResolvedValueOnce({
        ...dischargeSummaryTemplateMock,
        name: 'test 1',
      });
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            updateDischargeSummaryTemplate(input: { id: "${dischargeSummaryTemplateMock.id}", name: "test 1", summary: "[]", facilityPreferenceId: "id"}) {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.updateDischargeSummaryTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: dischargeSummaryTemplateMock.id,
            name: 'test 1',
          }),
        );
      })
      .end(done);
  });

  it('deleteDischargeSummaryTemplate() should delete discharge summary template', (done) => {
    jest
      .spyOn(facilityPreferenceRepositoryMock, 'deleteDischargeSummaryTemplate')
      .mockResolvedValueOnce(dischargeSummaryTemplateMock);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          mutation {
            deleteDischargeSummaryTemplate(id: "${dischargeSummaryTemplateMock.id}") {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.deleteDischargeSummaryTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: dischargeSummaryTemplateMock.id,
            name: 'test',
          }),
        );
      })
      .end(done);
  });

  it('fetchDischargeSummaryTemplates() should find discharge summary template with hospital Id', (done) => {
    jest
      .spyOn(
        facilityPreferenceRepositoryMock,
        'findByHospitalIdDischargeSummaryTemplates',
      )
      .mockResolvedValueOnce([
        { ...dischargeSummaryTemplateMock, name: 'Hospital' },
      ]);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          query {
            fetchDischargeSummaryTemplates {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.fetchDischargeSummaryTemplates;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: dischargeSummaryTemplateMock.id,
              name: 'Hospital',
            }),
          ]),
        );
      })
      .end(done);
  });

  it('fetchDischargeSummaryTemplates() should find discharge summary template with facilityPreferenceId', (done) => {
    jest
      .spyOn(
        facilityPreferenceRepositoryMock,
        'findByFacilityPreferenceIdDischargeSummaryTemplates',
      )
      .mockResolvedValueOnce([
        { ...dischargeSummaryTemplateMock, name: 'Facility' },
      ]);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
          query {
            fetchDischargeSummaryTemplates(facilityPreferenceId: "${facilityPreferenceMock.id}") {
              id
              name
            }
          }
        `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.fetchDischargeSummaryTemplates;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: dischargeSummaryTemplateMock.id,
              name: 'Facility',
            }),
          ]),
        );
      })
      .end(done);
  });

  it('saveChemoDiagnosisTemplate() should save chemo diagnosis template', (done) => {
    jest
      .spyOn(facilityPreferenceRepositoryMock, 'saveChemoDiagnosisTemplate')
      .mockResolvedValueOnce(chemoDiagnosisData);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
        mutation {
          saveChemoDiagnosisTemplate(input: {facilityPreferenceId: "${facilityPreferenceMock.id}", type: "PreChemotherapy", combinationName: "Test", section: "chemo", cycles: [{cycleNumber: 1, drugs: [{day: "1", drugName: "Test", drugId: "1", dosage: "50ml", dosagePercentage: "full", route: "Infusion", infusionUsed: "No", ref: "some-ref"}]}]}) {
            id
            type
            combinationName
            cycles {
              cycleNumber
              drugs {
                day
                drugName
                drugId
                dosage
                dosagePercentage
                route
                infusionUsed
              }
            }
          }
        }
      `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.saveChemoDiagnosisTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: chemoDiagnosisData.id,
            type: 'PreChemotherapy',
            combinationName: 'Test',
            cycles: [
              {
                cycleNumber: 1,
                drugs: [
                  {
                    day: '1',
                    drugName: 'Test',
                    drugId: '1',
                    dosage: '50ml',
                    dosagePercentage: 'full',
                    route: 'Infusion',
                    infusionUsed: 'No',
                  },
                ],
              },
            ],
          }),
        );
      })
      .end(done);
  });

  it('updateChemoDiagnosisTemplate() should update chemo diagnosis template', (done) => {
    jest
      .spyOn(facilityPreferenceRepositoryMock, 'updateChemoDiagnosisTemplate')
      .mockResolvedValueOnce(chemoDiagnosisData);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
        mutation {
          updateChemoDiagnosisTemplate(id: "${chemoDiagnosisData.id}",input: {facilityPreferenceId: "${facilityPreferenceMock.id}", type: "PreChemotherapy", combinationName: "Test", section: "chemo", cycles: [{cycleNumber: 1, drugs: [{day: "1", drugName: "Test", drugId: "1", dosage: "50ml", dosagePercentage: "full", route: "Infusion", infusionUsed: "No", ref: "some-ref"}]}]}) {
            id
            type
            combinationName
            cycles {
              cycleNumber
              drugs {
                day
                drugName
                drugId
                dosage
                dosagePercentage
                route
                infusionUsed
              }
            }
          }
        }
      `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.updateChemoDiagnosisTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: chemoDiagnosisData.id,
            type: 'PreChemotherapy',
            combinationName: 'Test',
            cycles: [
              {
                cycleNumber: 1,
                drugs: [
                  {
                    day: '1',
                    drugName: 'Test',
                    drugId: '1',
                    dosage: '50ml',
                    dosagePercentage: 'full',
                    route: 'Infusion',
                    infusionUsed: 'No',
                  },
                ],
              },
            ],
          }),
        );
      })
      .end(done);
  });

  it('deleteChemoDiagnosisTemplate() should delete chemo diagnosis template', (done) => {
    jest
      .spyOn(facilityPreferenceRepositoryMock, 'deleteChemoDiagnosisTemplate')
      .mockResolvedValueOnce(chemoDiagnosisData);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
        mutation {
          deleteChemoDiagnosisTemplate(id: "${chemoDiagnosisData.id}") {
            id
            type
            combinationName
            cycles {
              cycleNumber
              drugs {
                day
                drugName
                drugId
                dosage
                dosagePercentage
                route
                infusionUsed
              }
            }
          }
        }
      `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.deleteChemoDiagnosisTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: chemoDiagnosisData.id,
            type: 'PreChemotherapy',
            combinationName: 'Test',
            cycles: [
              {
                cycleNumber: 1,
                drugs: [
                  {
                    day: '1',
                    drugName: 'Test',
                    drugId: '1',
                    dosage: '50ml',
                    dosagePercentage: 'full',
                    route: 'Infusion',
                    infusionUsed: 'No',
                  },
                ],
              },
            ],
          }),
        );
      })
      .end(done);
  });

  it('fetchChemoDiagnosisTemplates() should find chemo diagnosis template with hospital Id', (done) => {
    jest
      .spyOn(
        facilityPreferenceRepositoryMock,
        'findByHospitalIdChemoDiagnosisTemplates',
      )
      .mockResolvedValueOnce([
        { ...chemoDiagnosisData, combinationName: 'Hospital' },
      ]);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
        query {
          fetchChemoDiagnosisTemplates {
            id
            combinationName
          }
        }
      `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.fetchChemoDiagnosisTemplates;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: chemoDiagnosisData.id,
              combinationName: 'Hospital',
            }),
          ]),
        );
      })
      .end(done);
  });

  it('fetchChemoDiagnosisTemplates() should find chemo diagnosis template with facilityPreferenceId', (done) => {
    jest
      .spyOn(
        facilityPreferenceRepositoryMock,
        'findByFacilityPreferenceIdChemoDiagnosisTemplates',
      )
      .mockResolvedValueOnce([
        { ...chemoDiagnosisData, combinationName: 'Facility' },
      ]);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
        query {
          fetchChemoDiagnosisTemplates(facilityPreferenceId: "${facilityPreferenceMock.id}") {
            id
            combinationName
          }
        }
      `,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.fetchChemoDiagnosisTemplates;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: chemoDiagnosisData.id,
              combinationName: 'Facility',
            }),
          ]),
        );
      })
      .end(done);
  });

  it('updateSpecialistAccess() should update specialist access', (done) => {
    jest
      .spyOn(facilityPreferenceRepositoryMock, 'updateSpecialistAccess')
      .mockResolvedValueOnce(specialistAccessMock);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
          updateSpecialistAccess(patientId: "patientId", specialistIds: ["specialistId"]) {
            id
            specialistIds
            profile {
              id
            }
          }
        }`,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.updateSpecialistAccess;
        expect(data).toEqual(
          expect.objectContaining({
            id: specialistAccessMock.id,
            specialistIds: ['specialistId'],
            profile: { id: gqlUserMock.defaultProfile.id },
          }),
        );
      })
      .end(done);
  });

  it('getSpecialistAccess() should get specialist access', (done) => {
    jest
      .spyOn(facilityPreferenceRepositoryMock, 'getSpecialistAccess')
      .mockResolvedValueOnce(specialistAccessMock);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query {
          getSpecialistAccess(patientId: "patientId") {
            id
            specialistIds
            profile {
              id
            }
          }
        }`,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.getSpecialistAccess;
        expect(data).toEqual(
          expect.objectContaining({
            id: specialistAccessMock.id,
            specialistIds: ['specialistId'],
            profile: { id: gqlUserMock.defaultProfile.id },
          }),
        );
      })
      .end(done);
  });

  it('findByHospitalIdSpecialistAccess() should find specialist access with hospital Id', (done) => {
    jest
      .spyOn(
        facilityPreferenceRepositoryMock,
        'findByHospitalIdSpecialistAccess',
      )
      .mockResolvedValueOnce([
        { ...specialistAccessMock, id: 'id', specialistIds: ['specialistId'] },
      ]);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query {
          findByHospitalIdSpecialistAccess(hospitalId: "id") {
            id
            specialistIds
            profile {
              id
            }
          }
        }`,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.findByHospitalIdSpecialistAccess;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: 'id',
              specialistIds: ['specialistId'],
              profile: { id: gqlUserMock.defaultProfile.id },
            }),
          ]),
        );
      })
      .end(done);
  });

  it('saveOperationNoteTemplate() should save operation note template', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
          saveOperationNoteTemplate(input: {name: "test", note: "My note", postNote: "My post note", facilityPreferenceId: "id"}) {
            id
            name
            note
            postNote
          }
        }`,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.saveOperationNoteTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: operationNoteTemplateMock.id,
            name: 'test',
            note: 'My note',
            postNote: 'My post note',
          }),
        );
      })
      .end(done);
  });

  it('updateOperationNoteTemplate() should update operation note template', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
          updateOperationNoteTemplate(input: {id: "${operationNoteTemplateMock.id}", name: "test", note: "My note 1", postNote: "My post note 1", facilityPreferenceId: "id"}) {
            id
            name
            note
            postNote
          }
        }`,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.updateOperationNoteTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: operationNoteTemplateMock.id,
            name: 'test',
            note: 'My note 1',
            postNote: 'My post note 1',
          }),
        );
      })
      .end(done);
  });

  it('deleteOperationNoteTemplate() should delete operation note template', (done) => {
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation {
          deleteOperationNoteTemplate(id: "${operationNoteTemplateMock.id}") {
            id
            name
            note
            postNote
          }
        }`,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.deleteOperationNoteTemplate;
        expect(data).toEqual(
          expect.objectContaining({
            id: operationNoteTemplateMock.id,
            name: 'test',
            note: 'My note',
            postNote: 'My post note',
          }),
        );
      })
      .end(done);
  });

  it('fetchOperationNoteTemplates() should find operation note template with hospital Id', (done) => {
    jest
      .spyOn(
        facilityPreferenceRepositoryMock,
        'findByHospitalIdOperationNoteTemplates',
      )
      .mockResolvedValueOnce([
        { ...operationNoteTemplateMock, name: 'Hospital' },
      ]);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query {
          fetchOperationNoteTemplates {
            id
            name
          }
        }`,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.fetchOperationNoteTemplates;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: operationNoteTemplateMock.id,
              name: 'Hospital',
            }),
          ]),
        );
      })
      .end(done);
  });

  it('fetchOperationNoteTemplates() should find operation note template with facilityPreferenceId', (done) => {
    jest
      .spyOn(
        facilityPreferenceRepositoryMock,
        'findByFacilityPreferenceIdOperationNoteTemplates',
      )
      .mockResolvedValueOnce([
        { ...operationNoteTemplateMock, name: 'Facility' },
      ]);
    return testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query {
          fetchOperationNoteTemplates(facilityPreferenceId: "id") {
            id
            name
          }
        }`,
      })
      .expect(200)
      .expect(({ body }) => {
        const data = body.data.fetchOperationNoteTemplates;
        expect(data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: operationNoteTemplateMock.id,
              name: 'Facility',
            }),
          ]),
        );
      })
      .end(done);
  });
});
