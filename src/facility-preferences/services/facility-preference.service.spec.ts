/* eslint-disable max-len */
/* eslint-disable max-lines */
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EntityManager, In } from 'typeorm';
import { FacilityPreferenceModel } from '../models/facility-preference.model';
import { hospitalFactory } from '@clinify/__mocks__/factories/hospital.factory';
import { CommissionPayer } from '@clinify/facility-preferences/enums/commission-payer';
import { DefaultPatientAccessType } from '@clinify/facility-preferences/enums/patient-lookup';
import { MedicalReportTemplateModel } from '@clinify/facility-preferences/models/medical-report-template.model';
import { CommissionPayerResponse } from '@clinify/facility-preferences/responses/commission-payer.response';
import { FacilityPreferenceService } from '@clinify/facility-preferences/services/facility-preference.service';
import { ProfilePreferenceService } from '@clinify/profile-preferences/services/profile-preference.service';
import { UserType } from '@clinify/shared/enums/users';
import { managerMock } from '@mocks/database.mock';
import {
  facilityPreferenceFactory,
  facilityPreferenceMock,
} from '@mocks/factories/facility-preference.factory';
import { mockProfile } from '@mocks/factories/profile.factory';

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};
const specialistAccessMock = {
  id: 'id',
  specialistIds: ['specialistId'],
};
const facilityPreferenceRepositoryMock = {
  findOneOrFail: jest.fn(),
  find: jest.fn(),
  save: jest.fn(),
  getFacilityPreference: jest.fn(() => Promise.resolve(facilityPreferenceMock)),
  updateWelcomeMailTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateBookAppointmentMailTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updatePatientAccessType: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateFormsMandatoryFields: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  saveFindingsTemplate: jest.fn(() => Promise.resolve(facilityPreferenceMock)),
  updateFindingsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  deleteFindingsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  findByFacilityPreferenceIdForFindingsTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  findByHospitalIdForFindingsTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  saveLabCommentsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateLabCommentsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  deleteLabCommentsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  findByFacilityPreferenceIdForLabCommentsTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  findByFacilityPreferenceIdForMedicalReportTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  findByHospitalIdForMedicalReportTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  updateRadiologyContrastMode: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateDashboardColourMode: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateShowServiceDetails: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateRolesServiceDetailsIsHidden: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  update: jest.fn(() => Promise.resolve({})),
  saveConsultationsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateConsultationsTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  deleteConsultationsTemplate: jest.fn(() => Promise.resolve()),
  findByHospitalIdConsultationsTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  findByFacilityPreferenceIdConsultationsTemplate: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),

  saveDischargeSummaryTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateDischargeSummaryTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  deleteDischargeSummaryTemplate: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  findByFacilityPreferenceIdDischargeSummaryTemplates: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  findByHospitalIdDischargeSummaryTemplates: jest.fn(() =>
    Promise.resolve([facilityPreferenceMock]),
  ),
  saveChemoDiagnosisTemplate: jest.fn(),
  updateChemoDiagnosisTemplate: jest.fn(),
  deleteChemoDiagnosisTemplate: jest.fn(),
  findByHospitalIdChemoDiagnosisTemplates: jest.fn(),
  findByFacilityPreferenceIdChemoDiagnosisTemplates: jest.fn(),
  getOneChemoDiagnosisTemplate: jest.fn(),
  updateSpecialistAccess: jest.fn(() => Promise.resolve(specialistAccessMock)),
  getSpecialistAccess: jest.fn(() => Promise.resolve(specialistAccessMock)),
  findByHospitalIdSpecialistAccess: jest.fn(() =>
    Promise.resolve([specialistAccessMock]),
  ),
  updatePatientSurveyLinks: jest.fn(),
  saveOperationNoteTemplate: jest.fn(),
  updateOperationNoteTemplate: jest.fn(),
  deleteOperationNoteTemplate: jest.fn(),
  findByFacilityPreferenceIdOperationNoteTemplates: jest.fn(),
  findByHospitalIdOperationNoteTemplates: jest.fn(),
  updateInventoryClass: jest.fn(),
  updateHmoSingleVisitPACode: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateCustomPaFormatType: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updatePatientRegistrationFee: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateEnrolleeCapitationAmount: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateAutoProcessClaims: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateEnrollmentAgentAssignments: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
  updateEnrollmentAgency: jest.fn(() =>
    Promise.resolve(facilityPreferenceMock),
  ),
};

const MockProfilePreferenceService = {
  updateProfilePreference: jest.fn(),
};

const profile = mockProfile;

describe('FacilityPreferenceService', () => {
  let service: FacilityPreferenceService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FacilityPreferenceService,
        {
          provide: getRepositoryToken(FacilityPreferenceModel),
          useValue: facilityPreferenceRepositoryMock,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
        {
          provide: EntityManager,
          useValue: managerMock,
        },
        {
          provide: ProfilePreferenceService,
          useValue: MockProfilePreferenceService,
        },
      ],
    }).compile();

    service = module.get<FacilityPreferenceService>(FacilityPreferenceService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('FacilityPreferenceService should be defined', () => {
    expect(service).toBeDefined();
    expect(service).toBeTruthy();
  });

  it('getFacilityPreference() should call getFacilityPreference from repo and return value', async () => {
    const res = await service.getFacilityPreference('id');
    expect(
      facilityPreferenceRepositoryMock.getFacilityPreference,
    ).toHaveBeenCalledWith('id');
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateWelcomeMailTemplate() should call updateWelcomeMailTemplate from repo and return value', async () => {
    const res = await service.updateWelcomeMailTemplate(
      profile,
      profile.hospitalId,
      facilityPreferenceMock,
    );
    expect(
      facilityPreferenceRepositoryMock.updateWelcomeMailTemplate,
    ).toHaveBeenCalledWith(profile, profile.hospitalId, facilityPreferenceMock);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updatePatientAccessType() should call updatePatientLookupMode from repo and return value', async () => {
    const res = await service.updatePatientAccessType(
      profile.hospitalId,
      DefaultPatientAccessType.AllPatients,
    );
    expect(
      facilityPreferenceRepositoryMock.updatePatientAccessType,
    ).toHaveBeenCalledWith(
      profile.hospitalId,
      DefaultPatientAccessType.AllPatients,
    );
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateFormsMandatoryFields() should call updateFormsMandatoryFields from repo and return value', async () => {
    const res = await service.updateFormsMandatoryFields(
      profile,
      profile.hospitalId,
      facilityPreferenceMock,
    );
    expect(
      facilityPreferenceRepositoryMock.updateFormsMandatoryFields,
    ).toHaveBeenCalledWith(profile, profile.hospitalId, facilityPreferenceMock);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('saveFindingsTemplate() should call saveFindingsTemplate from repo and return value', async () => {
    const res = await service.saveFindingsTemplate(
      profile,
      facilityPreferenceMock,
    );
    expect(
      facilityPreferenceRepositoryMock.saveFindingsTemplate,
    ).toHaveBeenCalledWith(profile, facilityPreferenceMock);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateFindingsTemplate() should call updateFindingsTemplate from repo and return value', async () => {
    const res = await service.updateFindingsTemplate(
      profile,
      facilityPreferenceMock,
    );
    expect(
      facilityPreferenceRepositoryMock.updateFindingsTemplate,
    ).toHaveBeenCalledWith(profile, facilityPreferenceMock);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('deleteFindingsTemplate() should call deleteFindingsTemplate from repo and return value', async () => {
    const res = await service.deleteFindingsTemplate('id');
    expect(
      facilityPreferenceRepositoryMock.deleteFindingsTemplate,
    ).toHaveBeenCalledWith('id');
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('findByFacilityPreferenceIdForFindingsTemplate() should call findByFacilityPreferenceIdForFindingsTemplate from repo and return value', async () => {
    const res = await service.findByFacilityPreferenceIdForFindingsTemplate(
      'id',
    );
    expect(
      facilityPreferenceRepositoryMock.findByFacilityPreferenceIdForFindingsTemplate,
    ).toHaveBeenCalledWith('id');
    expect(res).toEqual([facilityPreferenceMock]);
  });

  it('findByHospitalIdForFindingsTemplate() should call findByHospitalIdForFindingsTemplate from repo and return value', async () => {
    const res = await service.findByHospitalIdForFindingsTemplate('id');
    expect(
      facilityPreferenceRepositoryMock.findByHospitalIdForFindingsTemplate,
    ).toHaveBeenCalledWith('id');
    expect(res).toEqual([facilityPreferenceMock]);
  });

  it('saveLabCommentsTemplate() should call saveLabCommentsTemplate from repo and return value', async () => {
    const res = await service.saveLabCommentsTemplate(
      profile,
      facilityPreferenceMock,
    );
    expect(
      facilityPreferenceRepositoryMock.saveLabCommentsTemplate,
    ).toHaveBeenCalledWith(profile, facilityPreferenceMock);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateLabCommentsTemplate() should call updateLabCommentsTemplate from repo and return value', async () => {
    const res = await service.updateLabCommentsTemplate(
      profile,
      facilityPreferenceMock,
    );
    expect(
      facilityPreferenceRepositoryMock.updateLabCommentsTemplate,
    ).toHaveBeenCalledWith(profile, facilityPreferenceMock);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('deleteLabCommentsTemplate() should call deleteLabCommentsTemplate from repo and return value', async () => {
    const res = await service.deleteLabCommentsTemplate('id');
    expect(
      facilityPreferenceRepositoryMock.deleteLabCommentsTemplate,
    ).toHaveBeenCalledWith('id');
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('findByFacilityPreferenceIdForLabCommentsTemplate() should call findByFacilityPreferenceIdForLabCommentsTemplate from repo and return value', async () => {
    const res = await service.findByFacilityPreferenceIdForLabCommentsTemplate(
      'id',
    );
    expect(
      facilityPreferenceRepositoryMock.findByFacilityPreferenceIdForLabCommentsTemplate,
    ).toHaveBeenCalledWith('id');
    expect(res).toEqual([facilityPreferenceMock]);
  });

  it('findByFacilityPreferenceIdForMedicalReportTemplate', async () => {
    const res =
      await service.findByFacilityPreferenceIdForMedicalReportTemplate('id');
    expect(
      facilityPreferenceRepositoryMock.findByFacilityPreferenceIdForMedicalReportTemplate,
    ).toHaveBeenCalledWith('id');
    expect(res).toEqual([facilityPreferenceMock]);
  });

  it('findByHospitalIdForMedicalReportTemplate', async () => {
    const res = await service.findByHospitalIdForMedicalReportTemplate('id');
    expect(
      facilityPreferenceRepositoryMock.findByHospitalIdForMedicalReportTemplate,
    ).toHaveBeenCalledWith('id');
    expect(res).toEqual([facilityPreferenceMock]);
  });

  it('saveMedicalReportTemplate', async () => {
    const input = {
      name: 'Name',
      facilityPreferenceId: 'id',
      template: 'template',
    };
    await service.saveMedicalReportTemplate(profile, input);
    expect(managerMock.save).toHaveBeenCalledWith(
      MedicalReportTemplateModel,
      new MedicalReportTemplateModel({
        ...input,
        createdBy: profile,
        creatorName: profile.fullName,
      }),
    );
  });

  it('updateMedicalReportTemplate', async () => {
    managerMock.findOneOrFail.mockImplementationOnce(() => Promise.resolve({}));
    managerMock.findOneOrFail.mockImplementationOnce(() => Promise.reject());
    const input = {
      id: 'id',
      facilityPreferenceId: 'id',
      template: 'template',
    };
    await service.updateMedicalReportTemplate(profile, input);
    expect(managerMock.save).toHaveBeenCalledWith(
      MedicalReportTemplateModel,
      new MedicalReportTemplateModel({
        template: input.template,
        updatedBy: profile,
        lastModifierName: profile.fullName,
      }),
    );

    await expect(
      service.updateMedicalReportTemplate(profile, input),
    ).rejects.toThrow('Template Not Found');
  });

  it('deleteMedicalReportTemplate', async () => {
    await service.deleteMedicalReportTemplate('id');
    expect(managerMock.delete).toHaveBeenCalledWith(
      MedicalReportTemplateModel,
      { id: 'id' },
    );
  });

  it('updateRadiologyContrastMode(): should call updateRadiologyContrastMode repository method', async () => {
    const res = await service.updateRadiologyContrastMode('hospital-id', true);
    expect(
      facilityPreferenceRepositoryMock.updateRadiologyContrastMode,
    ).toHaveBeenCalledWith('hospital-id', true);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateDashboardColourMode(): should call updateDashboardColourMode repository method', async () => {
    const res = await service.updateDashboardColourMode('hospital-id', false);
    expect(
      facilityPreferenceRepositoryMock.updateDashboardColourMode,
    ).toHaveBeenCalledWith('hospital-id', false);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateShowServiceDetails(): should call updateShowServiceDetails repository method', async () => {
    const res = await service.updateShowServiceDetails('hospital-id', true);
    expect(
      facilityPreferenceRepositoryMock.updateShowServiceDetails,
    ).toHaveBeenCalledWith('hospital-id', true);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateRolesServiceDetailsIsHidden(): should call updateRolesServiceDetailsIsHidden repository method', async () => {
    const res = await service.updateRolesServiceDetailsIsHidden('hospital-id', [
      UserType.Doctor,
    ]);
    expect(
      facilityPreferenceRepositoryMock.updateRolesServiceDetailsIsHidden,
    ).toHaveBeenCalledWith('hospital-id', [UserType.Doctor]);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateDefaultCommissionPayer', async () => {
    const res = await service.updateDefaultCommissionPayer(
      'hospital-id',
      'id',
      CommissionPayer.Patient,
    );
    expect(facilityPreferenceRepositoryMock.update).toHaveBeenCalledWith('id', {
      commissionPayer: CommissionPayer.Patient,
    });
    expect(res).toEqual(
      new CommissionPayerResponse({
        commissionPayer: CommissionPayer.Patient,
        hospitalId: 'hospital-id',
        facilityPreferenceId: 'id',
      }),
    );
  });

  it('updateFileNumberGenerate(): should disable file number generation', async () => {
    const localHospital = hospitalFactory.build({
      id: 'hospital-id',
      name: 'Clinify Health',
    });

    const facilityPreference = facilityPreferenceFactory.build({
      hospitalShortName: '',
    });

    facilityPreferenceRepositoryMock.findOneOrFail = jest.fn(() =>
      Promise.resolve(facilityPreference),
    );

    await service.updateFileNumberGenerate(localHospital, false);

    expect(
      facilityPreferenceRepositoryMock.findOneOrFail,
    ).toHaveBeenCalledTimes(1);
    expect(facilityPreferenceRepositoryMock.find).not.toHaveBeenCalled();
    expect(facilityPreferenceRepositoryMock.save).toHaveBeenLastCalledWith({
      ...facilityPreference,
      hospitalId: 'hospital-id',
      generateFileNumber: false,
      hospitalShortName: '',
    });
  });

  it('updateFileNumberGenerate(): should trow exception when hospital id or name is not present', async () => {
    const localHospital = hospitalFactory.build({
      id: 'hospital-id',
      name: 'Clinify Health',
    });

    facilityPreferenceRepositoryMock.findOneOrFail = jest.fn(() =>
      Promise.reject(''),
    );

    await expect(
      service.updateFileNumberGenerate(localHospital, false),
    ).rejects.toThrow('Facility Not Found');
  });

  it('updateFileNumberGenerate(): should update file number and generate hospital ref', async () => {
    const localHospital = hospitalFactory.build({
      id: 'hospital-id',
      name: 'Clinify Health',
    });

    const facilityPreference = facilityPreferenceFactory.build({
      hospitalShortName: '',
    });

    facilityPreferenceRepositoryMock.findOneOrFail = jest.fn(() =>
      Promise.resolve(facilityPreference),
    );

    facilityPreferenceRepositoryMock.find = jest.fn(() => Promise.resolve([]));

    await service.updateFileNumberGenerate(localHospital, true);

    expect(
      facilityPreferenceRepositoryMock.findOneOrFail,
    ).toHaveBeenCalledTimes(1);
    expect(facilityPreferenceRepositoryMock.find).toHaveBeenCalledTimes(1);
    expect(facilityPreferenceRepositoryMock.save).toHaveBeenLastCalledWith({
      ...facilityPreference,
      hospitalId: 'hospital-id',
      generateFileNumber: true,
      hospitalShortName: 'CLI',
    });
  });

  it('updateFileNumberGenerate(): should update file number and generate hospital ref', async () => {
    const localHospital = hospitalFactory.build({
      id: 'hospital-id',
      name: 'Clinify Health',
    });

    const facilityPreference = facilityPreferenceFactory.build({
      hospitalShortName: '',
    });

    facilityPreferenceRepositoryMock.findOneOrFail = jest.fn(() =>
      Promise.resolve(facilityPreference),
    );

    facilityPreferenceRepositoryMock.find = jest.fn(() =>
      Promise.resolve([
        { hospitalShortName: 'CLI' },
        { hospitalShortName: 'DIA' },
        { hospitalShortName: 'PAE' },
        { hospitalShortName: 'DIA1' },
        { hospitalShortName: 'CLI1' },
      ]),
    );

    await service.updateFileNumberGenerate(localHospital, true);

    expect(
      facilityPreferenceRepositoryMock.findOneOrFail,
    ).toHaveBeenCalledTimes(1);
    expect(facilityPreferenceRepositoryMock.find).toHaveBeenCalledTimes(1);
    expect(facilityPreferenceRepositoryMock.save).toHaveBeenLastCalledWith({
      ...facilityPreference,
      hospitalId: 'hospital-id',
      generateFileNumber: true,
      hospitalShortName: 'CLI2',
    });
  });

  it('updateFileNumberGenerate(): should only update file number', async () => {
    const localHospital = hospitalFactory.build({
      id: 'hospital-id',
      name: 'Clinify Health',
    });

    const facilityPreference = facilityPreferenceFactory.build({
      hospitalShortName: 'CLI3',
    });

    facilityPreferenceRepositoryMock.findOneOrFail = jest.fn(() =>
      Promise.resolve(facilityPreference),
    );

    await service.updateFileNumberGenerate(localHospital, true);

    expect(
      facilityPreferenceRepositoryMock.findOneOrFail,
    ).toHaveBeenCalledTimes(1);
    expect(facilityPreferenceRepositoryMock.find).not.toHaveBeenCalled();
    expect(facilityPreferenceRepositoryMock.save).toHaveBeenLastCalledWith({
      ...facilityPreference,
      hospitalId: 'hospital-id',
      generateFileNumber: true,
      hospitalShortName: 'CLI3',
    });
  });

  it('updateFileNumberGenerate(): should throw exception when hospital id or name is not present', async () => {
    await expect(
      service.updateFileNumberGenerate({} as any, true),
    ).rejects.toThrow('Facility Information Not Provided');
  });

  it('saveConsultationsTemplate(): should save consultations template', async () => {
    const input = {} as any;
    await service.saveConsultationsTemplate(profile, input);
    expect(
      facilityPreferenceRepositoryMock.saveConsultationsTemplate,
    ).toHaveBeenCalledWith(profile, input);
  });

  it('updateConsultationsTemplate(): should update consultations template', async () => {
    const input = {} as any;
    await service.updateConsultationsTemplate(profile, input);
    expect(
      facilityPreferenceRepositoryMock.updateConsultationsTemplate,
    ).toHaveBeenCalledWith(profile, input);
  });

  it('deleteConsultationsTemplate(): should delete consultations template', async () => {
    await service.deleteConsultationsTemplate(profile, 'id');
    expect(
      facilityPreferenceRepositoryMock.deleteConsultationsTemplate,
    ).toHaveBeenCalledWith(profile, 'id');
  });

  it('findByHospitalIdConsultationsTemplate(): should find consultations template', async () => {
    await service.findByHospitalIdConsultationsTemplate(profile, 'id');
    expect(
      facilityPreferenceRepositoryMock.findByHospitalIdConsultationsTemplate,
    ).toHaveBeenCalledWith(profile, 'id');
  });

  it('findByFacilityPreferenceIdConsultationsTemplate(): should find consultations template', async () => {
    await service.findByFacilityPreferenceIdConsultationsTemplate(
      profile,
      'id',
    );
    expect(
      facilityPreferenceRepositoryMock.findByFacilityPreferenceIdConsultationsTemplate,
    ).toHaveBeenCalledWith(profile, 'id');
  });

  it('updateReceiptSize() should update receipt size and return response', async () => {
    const profileMock = { ...profile, hospitalId: 'hospital1' };
    const facilityPreferenceId = 'facility1';
    const receiptSize = 'A4';

    facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce({});
    facilityPreferenceRepositoryMock.update.mockResolvedValueOnce({});

    const result = await service.updateReceiptSize(
      profileMock,
      facilityPreferenceId,
      receiptSize,
    );

    expect(facilityPreferenceRepositoryMock.findOneOrFail).toHaveBeenCalledWith(
      expect.objectContaining({
        where: expect.objectContaining({
          hospitalId: profileMock.hospitalId,
          id: facilityPreferenceId,
        }),
      }),
    );
    expect(facilityPreferenceRepositoryMock.update).toHaveBeenCalledWith(
      facilityPreferenceId,
      { receiptSize },
    );
    expect(result).toEqual({
      id: facilityPreferenceId,
      receiptSize,
    });
  });

  it('updateReceiptSize() should throw NotFoundException when facility preference not found', async () => {
    const profileMock = { ...profile, hospitalId: 'hospital1' };
    const facilityPreferenceId = 'facility1';
    const receiptSize = 'A4';

    facilityPreferenceRepositoryMock.findOneOrFail.mockRejectedValueOnce(
      new Error(),
    );

    await expect(
      service.updateReceiptSize(profileMock, facilityPreferenceId, receiptSize),
    ).rejects.toThrow('Facility Preference Not Found');

    expect(facilityPreferenceRepositoryMock.findOneOrFail).toHaveBeenCalledWith(
      expect.objectContaining({
        where: { hospitalId: profileMock.hospitalId, id: facilityPreferenceId },
      }),
    );
    expect(facilityPreferenceRepositoryMock.update).not.toHaveBeenCalled();
  });

  it('saveDischargeSummaryTemplate() should save discharge template', async () => {
    const input = {} as any;
    await service.saveDischargeSummaryTemplate(profile, input);
    expect(
      facilityPreferenceRepositoryMock.saveDischargeSummaryTemplate,
    ).toHaveBeenCalledWith(profile, input);
  });

  it('updateDischargeSummaryTemplate() should update discharge template', async () => {
    const input = {} as any;
    await service.updateDischargeSummaryTemplate(profile, input);
    expect(
      facilityPreferenceRepositoryMock.updateDischargeSummaryTemplate,
    ).toHaveBeenCalledWith(profile, input);
  });

  it('deleteDischargeSummaryTemplate() should delete discharge template', async () => {
    await service.deleteDischargeSummaryTemplate('id');
    expect(
      facilityPreferenceRepositoryMock.deleteDischargeSummaryTemplate,
    ).toHaveBeenCalledWith('id');
  });

  it('findByHospitalIdDischargeSummaryTemplates() should find discharge template', async () => {
    await service.findByHospitalIdDischargeSummaryTemplates('id');
    expect(
      facilityPreferenceRepositoryMock.findByHospitalIdDischargeSummaryTemplates,
    ).toHaveBeenCalledWith('id');
  });

  it('findByFacilityPreferenceIdDischargeSummaryTemplates() should find discharge template', async () => {
    await service.findByFacilityPreferenceIdDischargeSummaryTemplates('id');
    expect(
      facilityPreferenceRepositoryMock.findByFacilityPreferenceIdDischargeSummaryTemplates,
    ).toHaveBeenCalledWith('id');
  });

  it('saveChemoDiagnosisTemplate() should save chemo diagnosis template', async () => {
    const input = {} as any;
    await service.saveChemoDiagnosisTemplate(profile, input);
    expect(
      facilityPreferenceRepositoryMock.saveChemoDiagnosisTemplate,
    ).toHaveBeenCalledWith(profile, input);
  });

  it('updateChemoDiagnosisTemplate() should update chemo diagnosis template', async () => {
    const input = {} as any;
    await service.updateChemoDiagnosisTemplate(profile, 'id', input);
    expect(
      facilityPreferenceRepositoryMock.updateChemoDiagnosisTemplate,
    ).toHaveBeenCalledWith(profile, 'id', input);
  });

  it('deleteChemoDiagnosisTemplate() should delete chemo diagnosis template', async () => {
    await service.deleteChemoDiagnosisTemplate('id');
    expect(
      facilityPreferenceRepositoryMock.deleteChemoDiagnosisTemplate,
    ).toHaveBeenCalledWith('id');
  });

  it('findByHospitalIdChemoDiagnosisTemplates() should find chemo diagnosis template', async () => {
    await service.findByHospitalIdChemoDiagnosisTemplates(
      'id',
      null,
      'diagnosis',
    );
    expect(
      facilityPreferenceRepositoryMock.findByHospitalIdChemoDiagnosisTemplates,
    ).toHaveBeenCalledWith('id', null, 'diagnosis');
  });

  it('findByFacilityPreferenceIdChemoDiagnosisTemplates() should find chemo diagnosis template', async () => {
    await service.findByFacilityPreferenceIdChemoDiagnosisTemplates(
      'id',
      null,
      'diagnosis',
    );
    expect(
      facilityPreferenceRepositoryMock.findByFacilityPreferenceIdChemoDiagnosisTemplates,
    ).toHaveBeenCalledWith('id', null, 'diagnosis');
  });

  it('getOneChemoDiagnosisTemplate() should find single chemo diagnosis template', async () => {
    await service.getOneChemoDiagnosisTemplate('id');
    expect(
      facilityPreferenceRepositoryMock.getOneChemoDiagnosisTemplate,
    ).toHaveBeenCalledWith('id');
  });

  it('updateTariffsToUse', async () => {
    facilityPreferenceRepositoryMock.find.mockImplementationOnce(() =>
      Promise.resolve([]),
    );
    const res = await service.updateTariffsToUse('id', true, mockProfile);
    expect(facilityPreferenceRepositoryMock.update).toHaveBeenCalledWith(
      {
        id: In(['id']),
      },
      {
        useHQFacilityTariffs: true,
      },
    );
    expect(res).toEqual(
      expect.objectContaining({ id: 'id', useHQFacilityTariffs: true }),
    );
  });

  it('updateTariffsToUse(): should update an existing facility preference', async () => {
    const facilityPreference = facilityPreferenceFactory.build({
      useHQFacilityTariffs: false,
    });

    facilityPreferenceRepositoryMock.find.mockResolvedValueOnce([
      facilityPreference,
    ]);
    facilityPreferenceRepositoryMock.update.mockResolvedValueOnce(
      facilityPreference,
    );

    const result = await service.updateTariffsToUse('id', true, mockProfile);

    expect(facilityPreferenceRepositoryMock.find).toHaveBeenCalledWith({
      where: {
        hospital: {
          hqFacilityId: mockProfile.hospitalId,
        },
      },
      select: ['id'],
    });
    expect(facilityPreferenceRepositoryMock.update).toHaveBeenCalledWith(
      { id: In(['id', facilityPreference.id]) },
      { useHQFacilityTariffs: true },
    );

    expect(result).toEqual({
      id: 'id',
      useHQFacilityTariffs: true,
    });
  });

  it('updateInventoryToUse', async () => {
    facilityPreferenceRepositoryMock.find.mockImplementationOnce(() =>
      Promise.resolve([]),
    );
    const res = await service.updateInventoryToUse('id', true, mockProfile);
    expect(facilityPreferenceRepositoryMock.update).toHaveBeenCalledWith(
      {
        id: In(['id']),
      },
      {
        useHQFacilityInventory: true,
      },
    );
    expect(res).toEqual(
      expect.objectContaining({ id: 'id', useHQFacilityInventory: true }),
    );
  });

  it('updateSpecialistAccess', async () => {
    const res = await service.updateSpecialistAccess(profile, 'id', [
      'specialistId',
    ]);
    expect(
      facilityPreferenceRepositoryMock.updateSpecialistAccess,
    ).toHaveBeenCalledWith(profile, 'id', ['specialistId']);
    expect(res).toEqual(specialistAccessMock);
  });

  it('getSpecialistAccess', async () => {
    const res = await service.getSpecialistAccess(profile, 'id');
    expect(
      facilityPreferenceRepositoryMock.getSpecialistAccess,
    ).toHaveBeenCalledWith('id', profile.hospitalId);
    expect(res).toEqual(specialistAccessMock);
  });

  it('findByHospitalIdSpecialistAccess', async () => {
    const res = await service.findByHospitalIdSpecialistAccess('id');
    expect(
      facilityPreferenceRepositoryMock.findByHospitalIdSpecialistAccess,
    ).toHaveBeenCalledWith('id');
    expect(res).toEqual([specialistAccessMock]);
  });

  it('updatePatientSurveyLinks', async () => {
    await service.updatePatientSurveyLinks(profile, 'id', 'link 1', 'link 2');
    expect(
      facilityPreferenceRepositoryMock.updatePatientSurveyLinks,
    ).toHaveBeenCalledWith(profile, 'id', 'link 1', 'link 2');
  });

  it('saveOperationNoteTemplate', async () => {
    const input = {} as any;
    await service.saveOperationNoteTemplate(profile, input);
    expect(
      facilityPreferenceRepositoryMock.saveOperationNoteTemplate,
    ).toHaveBeenCalledWith(profile, input);
  });

  it('updateOperationNoteTemplate', async () => {
    const input = {} as any;
    await service.updateOperationNoteTemplate(profile, input);
    expect(
      facilityPreferenceRepositoryMock.updateOperationNoteTemplate,
    ).toHaveBeenCalledWith(profile, input);
  });

  it('deleteOperationNoteTemplate', async () => {
    await service.deleteOperationNoteTemplate('id');
    expect(
      facilityPreferenceRepositoryMock.deleteOperationNoteTemplate,
    ).toHaveBeenCalledWith('id');
  });

  it('findByFacilityPreferenceIdOperationNoteTemplates', async () => {
    await service.findByFacilityPreferenceIdOperationNoteTemplates('id');
    expect(
      facilityPreferenceRepositoryMock.findByFacilityPreferenceIdOperationNoteTemplates,
    ).toHaveBeenCalledWith('id');
  });

  it('findByHospitalIdOperationNoteTemplates', async () => {
    await service.findByHospitalIdOperationNoteTemplates('id');
    expect(
      facilityPreferenceRepositoryMock.findByHospitalIdOperationNoteTemplates,
    ).toHaveBeenCalledWith('id');
  });

  it('updateInventoryClass', async () => {
    await service.updateInventoryClass(profile, 'inventoryClass');
    expect(
      facilityPreferenceRepositoryMock.updateInventoryClass,
    ).toHaveBeenCalledWith(profile, 'inventoryClass');
  });

  it('updatePatientRegistrationFee(): should call updatePatientRegistrationFee repository method', async () => {
    const res = await service.updatePatientRegistrationFee('hospital-id', 1000);
    expect(
      facilityPreferenceRepositoryMock.updatePatientRegistrationFee,
    ).toHaveBeenCalledWith('hospital-id', 1000);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateEnrolleeCapitationAmount(): should call updateEnrolleeCapitationAmount repository method', async () => {
    const res = await service.updateEnrolleeCapitationAmount(
      'hospital-id',
      1000,
    );
    expect(
      facilityPreferenceRepositoryMock.updateEnrolleeCapitationAmount,
    ).toHaveBeenCalledWith('hospital-id', 1000);
    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateAutoProcessClaims(): should call updateAutoProcessClaims repository method and update staff profile preferences', async () => {
    const staffIds = [
      { id: 'staff-id-1' },
      { id: 'staff-id-2' },
      { id: 'staff-id-3' },
    ];
    managerMock.find.mockResolvedValueOnce(staffIds);

    const res = await service.updateAutoProcessClaims(
      'hospital-id',
      true,
      profile,
    );

    expect(
      facilityPreferenceRepositoryMock.updateAutoProcessClaims,
    ).toHaveBeenCalledWith('hospital-id', true);

    expect(managerMock.find).toHaveBeenCalledWith(expect.any(Function), {
      where: {
        hospital: { id: 'hospital-id' },
        type: In([
          UserType.ClaimOfficer,
          UserType.ClaimOfficerHOD,
          UserType.ClaimReviewer,
          UserType.ClaimReviewerHOD,
          UserType.ClaimAdmin,
          UserType.ClaimFinance,
        ]),
      },
      select: ['id'],
    });

    expect(
      MockProfilePreferenceService.updateProfilePreference,
    ).toHaveBeenCalledTimes(staffIds.length);
    staffIds.forEach(({ id }) => {
      expect(
        MockProfilePreferenceService.updateProfilePreference,
      ).toHaveBeenCalledWith(id, { autoProcessClaims: true }, profile);
    });

    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateAutoProcessClaims(): should handle case with no staff members with claim-related roles', async () => {
    managerMock.find.mockResolvedValueOnce([]);

    const res = await service.updateAutoProcessClaims(
      'hospital-id',
      false,
      profile,
    );

    expect(
      facilityPreferenceRepositoryMock.updateAutoProcessClaims,
    ).toHaveBeenCalledWith('hospital-id', false);

    expect(managerMock.find).toHaveBeenCalledWith(expect.any(Function), {
      where: {
        hospital: { id: 'hospital-id' },
        type: In([
          UserType.ClaimOfficer,
          UserType.ClaimOfficerHOD,
          UserType.ClaimReviewer,
          UserType.ClaimReviewerHOD,
          UserType.ClaimAdmin,
          UserType.ClaimFinance,
        ]),
      },
      select: ['id'],
    });

    expect(
      MockProfilePreferenceService.updateProfilePreference,
    ).not.toHaveBeenCalled();

    expect(res).toEqual(facilityPreferenceMock);
  });

  it('updateEnrollmentAgency(): should call updateEnrollmentAgency repository method', async () => {
    await service.updateEnrollmentAgency(profile, 'agentcy-id', ['agencyName']);
    expect(
      facilityPreferenceRepositoryMock.updateEnrollmentAgency,
    ).toHaveBeenCalledWith(profile, 'agentcy-id', ['agencyName']);
  });

  it('updateEnrollmentAgentAssignments(): should call updateEnrollmentAgentAssignments repository method', async () => {
    const mockAgent = {
      profileId: 'profile-id',
      administrationAgency: 'admin-agency',
      enrollmentAgency: 'enrollment-agency',
      accountNumber: '*********',
      accountName: 'Test Account',
      bankName: 'Test Bank',
      bvn: '452153',
      branchName: 'Branch Name',
      status: 'Active',
      enrollmentTpa: null,
    };
    await service.updateEnrollmentAgentAssignments(profile, mockAgent);
    expect(
      facilityPreferenceRepositoryMock.updateEnrollmentAgentAssignments,
    ).toHaveBeenCalledWith(profile, mockAgent);
  });

  describe('updateEnrolleeSponsors', () => {
    it('should update existing sponsors with new sponsors', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrolleeSponsors: ['old-sponsor'],
      };

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrolleeSponsors: ['new-sponsor-1', 'new-sponsor-2'],
      });

      const result = await service.updateEnrolleeSponsors(profile, [
        'new-sponsor-1',
        'new-sponsor-2',
      ]);

      expect(
        facilityPreferenceRepositoryMock.findOneOrFail,
      ).toHaveBeenCalledWith({
        where: { hospitalId: profile.hospitalId },
      });
      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrolleeSponsors: ['new-sponsor-1', 'new-sponsor-2'],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrolleeSponsors).toEqual([
        'new-sponsor-1',
        'new-sponsor-2',
      ]);
    });
  });

  describe('updateFieldOfficer', () => {
    it('should update existing field officer when profileId matches', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        fieldOfficers: [
          {
            profileId: 'profile-1',
            administrationAgency: 'admin-agency',
            enrollmentAgency: 'enrollment-agency',
            accountNumber: 'old-account',
            accountName: 'Old Name',
            bankName: 'Old Bank',
          },
        ],
      };

      const updatedFieldOfficer = {
        profileId: 'profile-1',
        administrationAgency: 'admin-agency',
        enrollmentAgency: 'enrollment-agency',
        accountNumber: 'new-account',
        accountName: 'New Name',
        bankName: 'New Bank',
        bvn: '452153',
        branchName: 'Branch Name',
        status: 'Active',
      };

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        fieldOfficers: [updatedFieldOfficer],
      });

      const result = await service.updateFieldOfficer(
        profile,
        updatedFieldOfficer,
      );

      expect(
        facilityPreferenceRepositoryMock.findOneOrFail,
      ).toHaveBeenCalledWith({
        where: { hospitalId: profile.hospitalId },
      });
      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        fieldOfficers: [updatedFieldOfficer],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.fieldOfficers[0]).toEqual(updatedFieldOfficer);
    });

    it('should add new field officer when profileId does not exist', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        fieldOfficers: [],
      };

      const newFieldOfficer = {
        profileId: 'profile-2',
        administrationAgency: 'admin-agency',
        enrollmentAgency: 'enrollment-agency',
        accountNumber: 'account',
        accountName: 'Name',
        bankName: 'Bank',
        bvn: '452153',
        branchName: 'Branch Name',
        status: 'Inactive',
      };

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        fieldOfficers: [newFieldOfficer],
      });

      const result = await service.updateFieldOfficer(profile, newFieldOfficer);

      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        fieldOfficers: [newFieldOfficer],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.fieldOfficers).toHaveLength(1);
      expect(result.fieldOfficers[0]).toEqual(newFieldOfficer);
    });
  });

  describe('updateEnrolleeReferral', () => {
    it('should update existing enrollee referral when referrerCode matches', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrolleeReferrals: [
          {
            name: 'Old Name',
            referrerCode: 'REF001',
            accountNumber: 'old-account',
            accountName: 'Old Account',
            bankName: 'Old Bank',
          },
        ],
      };

      const updatedReferral = {
        name: 'New Name',
        referrerCode: 'REF001',
        accountNumber: 'new-account',
        accountName: 'New Account',
        bankName: 'New Bank',
        bvn: '452153',
        branchName: 'Branch Name',
        phoneNumber: null,
        email: '<EMAIL>',
        status: 'Inactive',
      };

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrolleeReferrals: [updatedReferral],
      });

      const result = await service.updateEnrolleeReferral(
        profile,
        updatedReferral,
      );

      expect(
        facilityPreferenceRepositoryMock.findOneOrFail,
      ).toHaveBeenCalledWith({
        where: { hospitalId: profile.hospitalId },
      });
      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrolleeReferrals: [updatedReferral],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrolleeReferrals[0]).toEqual(updatedReferral);
    });

    it('should add new enrollee referral when referrerCode does not exist', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrolleeReferrals: [],
      };

      const newReferral = {
        name: 'New Referral',
        referrerCode: 'REF002',
        accountNumber: 'account',
        accountName: 'Account Name',
        bankName: 'Bank Name',
        bvn: '452153',
        branchName: 'Branch Name',
        phoneNumber: null,
        email: '<EMAIL>',
        status: 'Active',
      };

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrolleeReferrals: [newReferral],
      });

      const result = await service.updateEnrolleeReferral(profile, newReferral);

      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrolleeReferrals: [newReferral],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrolleeReferrals).toHaveLength(1);
      expect(result.enrolleeReferrals[0]).toEqual(newReferral);
    });

    it('should handle null enrolleeReferrals array', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrolleeReferrals: null,
      };

      const newReferral = {
        name: 'New Referral',
        referrerCode: 'REF003',
        accountNumber: 'account',
        accountName: 'Account Name',
        bankName: 'Bank Name',
        bvn: '452153',
        branchName: 'Branch Name',
        phoneNumber: null,
        email: '<EMAIL>',
        status: 'Inactive',
      };

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrolleeReferrals: [newReferral],
      });

      const result = await service.updateEnrolleeReferral(profile, newReferral);

      expect(result.enrolleeReferrals).toHaveLength(1);
      expect(result.enrolleeReferrals[0]).toEqual(newReferral);
    });
  });

  describe('updateSponsorAssignment', () => {
    it('should update existing sponsor details when sponsor ref matches', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrolleeSponsorAssigments: [
          {
            ref: 'sponsor-ref',
            sponsorName: 'sponsor-name',
            sponsorType: 'sponsor-type',
            sponsorLives: '750',
            agencyLives: '250',
            amountDue: '3000',
            paymentFrequency: 'annual',
            nextRenewalDate: null,
            renewalCount: '3',
            paymentStatus: 'Paid',
            paymentDateTime: null,
          },
        ],
      };

      const updatedSponsorDetails = {
        ref: 'sponsor-ref',
        sponsorName: 'sponsor-name',
        sponsorType: 'sponsor-type',
        sponsorLives: '750',
        agencyLives: '250',
        amountDue: '1000',
        paymentFrequency: 'annual',
        nextRenewalDate: null,
        renewalCount: '4',
        paymentStatus: 'Paid',
        paymentDateTime: null,
        status: 'Active',
      };

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrolleeSponsorAssigments: [updatedSponsorDetails],
      });

      const result = await service.updateSponsorAssignment(
        profile,
        updatedSponsorDetails,
      );

      expect(
        facilityPreferenceRepositoryMock.findOneOrFail,
      ).toHaveBeenCalledWith({
        where: { hospitalId: profile.hospitalId },
      });
      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrolleeSponsorAssigments: [updatedSponsorDetails],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrolleeSponsorAssigments[0]).toEqual(
        updatedSponsorDetails,
      );
    });

    it('should add new sponsor details when sponsor ref does not exist', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrolleeSponsorAssigments: [],
      };

      const newSponsorDetails = {
        ref: 'sponsor-ref',
        sponsorName: 'sponsor-name',
        sponsorType: 'sponsor-type',
        sponsorLives: '750',
        agencyLives: '250',
        amountDue: '1000',
        paymentFrequency: 'annual',
        nextRenewalDate: null,
        renewalCount: '4',
        paymentStatus: 'Paid',
        paymentDateTime: null,
        status: 'Active',
      };

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrolleeSponsorAssigments: [newSponsorDetails],
      });

      const result = await service.updateSponsorAssignment(
        profile,
        newSponsorDetails,
      );

      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrolleeSponsorAssigments: [newSponsorDetails],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrolleeSponsorAssigments).toHaveLength(1);
      expect(result.enrolleeSponsorAssigments[0]).toEqual(newSponsorDetails);
    });
  });

  describe('updateEnrolleeCapitationAmountByPlanType', () => {
    it('should update existing capitation amount when planTypeId matches', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrolleeCapitionAmountByPlanType: [
          {
            planTypeId: 'plan-type-1',
            planName: 'Plan Type 1',
            amount: 5000,
          },
          {
            planTypeId: 'plan-type-2',
            planName: 'Plan Type 2',
            amount: 3000,
          },
        ],
      };

      const updatedCapitationAmount = 7500;
      const planTypeId = 'plan-type-1';

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrolleeCapitionAmountByPlanType: [
          {
            planTypeId: 'plan-type-1',
            planName: 'Plan Type 1',
            amount: updatedCapitationAmount,
          },
          {
            planTypeId: 'plan-type-2',
            planName: 'Plan Type 2',
            amount: 3000,
          },
        ],
      });

      const result = await service.updateEnrolleeCapitationAmountByPlanType(
        profile,
        planTypeId,
        'Plan Type 1',
        updatedCapitationAmount,
      );

      expect(
        facilityPreferenceRepositoryMock.findOneOrFail,
      ).toHaveBeenCalledWith({
        where: { hospitalId: profile.hospitalId },
      });
      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrolleeCapitionAmountByPlanType: [
          {
            planTypeId: 'plan-type-1',
            planName: 'Plan Type 1',
            planTypeName: 'Plan Type 1',
            amount: updatedCapitationAmount,
          },
          {
            planTypeId: 'plan-type-2',
            planName: 'Plan Type 2',
            amount: 3000,
          },
        ],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrolleeCapitionAmountByPlanType).toHaveLength(2);
      expect(
        result.enrolleeCapitionAmountByPlanType.find(
          (item) => item.planTypeId === planTypeId,
        )?.amount,
      ).toBe(updatedCapitationAmount);
    });

    it('should add new capitation amount when planTypeId does not exist', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrolleeCapitionAmountByPlanType: [
          {
            planTypeId: 'plan-type-1',
            planName: 'Plan Type 1',
            amount: 5000,
          },
        ],
      };

      const newCapitationAmount = 4000;
      const newPlanTypeId = 'plan-type-2';

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrolleeCapitionAmountByPlanType: [
          {
            planTypeId: 'plan-type-1',
            planName: 'Plan Type 1',
            amount: 5000,
          },
          {
            planTypeId: newPlanTypeId,
            planName: 'New Plan Type',
            amount: newCapitationAmount,
          },
        ],
      });

      const result = await service.updateEnrolleeCapitationAmountByPlanType(
        profile,
        newPlanTypeId,
        'New Plan Type',
        newCapitationAmount,
      );

      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrolleeCapitionAmountByPlanType: [
          {
            planTypeId: 'plan-type-1',
            planName: 'Plan Type 1',
            amount: 5000,
          },
          {
            planTypeId: newPlanTypeId,
            planTypeName: 'New Plan Type',
            amount: newCapitationAmount,
          },
        ],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrolleeCapitionAmountByPlanType).toHaveLength(2);
      expect(
        result.enrolleeCapitionAmountByPlanType.find(
          (item) => item.planTypeId === newPlanTypeId,
        )?.amount,
      ).toBe(newCapitationAmount);
    });

    it('should handle empty enrolleeCapitionAmountByPlanType array', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrolleeCapitionAmountByPlanType: [],
      };

      const capitationAmount = 6000;
      const planTypeId = 'plan-type-1';
      const planName = 'Plan Name';

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrolleeCapitionAmountByPlanType: [
          {
            planTypeId,
            planName,
            amount: capitationAmount,
          },
        ],
      });

      const result = await service.updateEnrolleeCapitationAmountByPlanType(
        profile,
        planTypeId,
        planName,
        capitationAmount,
      );

      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrolleeCapitionAmountByPlanType: [
          {
            planTypeId,
            planTypeName: planName,
            amount: capitationAmount,
          },
        ],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrolleeCapitionAmountByPlanType).toHaveLength(1);
      expect(result.enrolleeCapitionAmountByPlanType[0]).toEqual({
        planTypeId,
        planName,
        amount: capitationAmount,
      });
    });

    it('should handle null enrolleeCapitionAmountByPlanType', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrolleeCapitionAmountByPlanType: null,
      };

      const capitationAmount = 6000;
      const planTypeId = 'plan-type-1';
      const planName = 'Plan Name';

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrolleeCapitionAmountByPlanType: [
          {
            planTypeId,
            planName,
            amount: capitationAmount,
          },
        ],
      });

      const result = await service.updateEnrolleeCapitationAmountByPlanType(
        profile,
        planTypeId,
        planName,
        capitationAmount,
      );

      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrolleeCapitionAmountByPlanType: [
          {
            planTypeId,
            planTypeName: planName,
            amount: capitationAmount,
          },
        ],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrolleeCapitionAmountByPlanType).toHaveLength(1);
      expect(result.enrolleeCapitionAmountByPlanType[0]).toEqual({
        planTypeId,
        planName,
        amount: capitationAmount,
      });
    });

    it('should throw NotFoundException when facility preference is not found', async () => {
      facilityPreferenceRepositoryMock.findOneOrFail.mockRejectedValueOnce(
        new Error('Not found'),
      );

      await expect(
        service.updateEnrolleeCapitationAmountByPlanType(
          profile,
          'plan-type-1',
          'plan-name',
          5000,
        ),
      ).rejects.toThrow('Not found');

      expect(
        facilityPreferenceRepositoryMock.findOneOrFail,
      ).toHaveBeenCalledWith({
        where: { hospitalId: profile.hospitalId },
      });
      expect(facilityPreferenceRepositoryMock.save).not.toHaveBeenCalled();
    });
  });

  describe('updateEnrolleeCapitationAmountPerPlan', () => {
    it('should update enrolleeCapitationAmountPerPlan to true', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrolleeCapitationAmountPerPlan: false,
      };

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrolleeCapitationAmountPerPlan: true,
      });

      const result = await service.updateEnrolleeCapitationAmountPerPlan(
        profile,
        true,
      );

      expect(
        facilityPreferenceRepositoryMock.findOneOrFail,
      ).toHaveBeenCalledWith({
        where: { hospitalId: profile.hospitalId },
      });
      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrolleeCapitationAmount: 0,
        enrolleeCapitationAmountPerPlan: true,
        enrolleeCapitionAmountByPlanType: [],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrolleeCapitationAmountPerPlan).toBe(true);
    });

    it('should update enrolleeCapitationAmountPerPlan to false', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrolleeCapitationAmountPerPlan: true,
      };

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrolleeCapitationAmountPerPlan: false,
      });

      const result = await service.updateEnrolleeCapitationAmountPerPlan(
        profile,
        false,
      );

      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrolleeCapitationAmount: 0,
        enrolleeCapitationAmountPerPlan: false,
        enrolleeCapitionAmountByPlanType: [],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrolleeCapitationAmountPerPlan).toBe(false);
    });

    it('should throw NotFoundException when facility preference is not found', async () => {
      facilityPreferenceRepositoryMock.findOneOrFail.mockRejectedValueOnce(
        new Error('Not found'),
      );

      await expect(
        service.updateEnrolleeCapitationAmountPerPlan(profile, true),
      ).rejects.toThrow('Not found');

      expect(
        facilityPreferenceRepositoryMock.findOneOrFail,
      ).toHaveBeenCalledWith({
        where: { hospitalId: profile.hospitalId },
      });
      expect(facilityPreferenceRepositoryMock.save).not.toHaveBeenCalled();
    });
  });

  describe('updateEnrolleeTpaAssignment', () => {
    it('should update existing tpa details when tpa ref matches', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrollmentTpaAssigments: [
          {
            ref: 'tpa-ref',
            name: 'TPA NAME',
            address: null,
            isTpa: true,
            country: 'Nigeria',
            state: 'Lagos State',
            localGovernmentArea: 'Ikeja',
            primaryEmailAddress: null,
            secondaryEmailAddress: null,
            startDate: null,
            endDate: null,
            renewalDate: null,
            tpaNumber: '12',
            tpaCode: 'TPC',
            accountName: null,
            accountNumber: null,
            bankName: null,
            bvn: null,
            branchName: null,
            status: 'Active',
          },
        ],
      };

      const updatedTpaDetails = {
        ref: 'tpa-ref',
        name: 'TPA NAME',
        address: null,
        isTpa: true,
        country: 'Nigeria',
        state: 'Lagos State',
        localGovernmentArea: 'Ikeja',
        primaryEmailAddress: null,
        secondaryEmailAddress: null,
        startDate: null,
        endDate: null,
        renewalDate: null,
        tpaNumber: '12',
        tpaCode: 'TPC',
        accountName: null,
        accountNumber: null,
        bankName: null,
        bvn: null,
        branchName: null,
        status: 'Inactive',
      };

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrollmentTpaAssigments: [updatedTpaDetails],
      });

      const result = await service.updateEnrolleeTpaAssignment(
        profile,
        updatedTpaDetails,
      );

      expect(
        facilityPreferenceRepositoryMock.findOneOrFail,
      ).toHaveBeenCalledWith({
        where: { hospitalId: profile.hospitalId },
      });
      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrollmentTpaAssigments: [updatedTpaDetails],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrollmentTpaAssigments[0]).toEqual(updatedTpaDetails);
    });

    it('should add new tpa details when tpa ref does not exist', async () => {
      const mockFacilityPreference = {
        id: 'facility-id',
        hospitalId: 'hospital-id',
        enrollmentTpaAssigments: [],
      };

      const newTpaDetails = {
        ref: 'tpa-ref',
        name: 'TPA NAME',
        address: null,
        isTpa: true,
        country: 'Nigeria',
        state: 'Lagos State',
        localGovernmentArea: 'Ikeja',
        primaryEmailAddress: null,
        secondaryEmailAddress: null,
        startDate: null,
        endDate: null,
        renewalDate: null,
        tpaNumber: '12',
        tpaCode: 'TPC',
        accountName: null,
        accountNumber: null,
        bankName: null,
        bvn: null,
        branchName: null,
        status: 'Active',
      };

      facilityPreferenceRepositoryMock.findOneOrFail.mockResolvedValueOnce(
        mockFacilityPreference,
      );
      facilityPreferenceRepositoryMock.save.mockResolvedValueOnce({
        ...mockFacilityPreference,
        enrollmentTpaAssigments: [newTpaDetails],
      });

      const result = await service.updateEnrolleeTpaAssignment(
        profile,
        newTpaDetails,
      );

      expect(facilityPreferenceRepositoryMock.save).toHaveBeenCalledWith({
        ...mockFacilityPreference,
        enrollmentTpaAssigments: [newTpaDetails],
        updatedBy: profile,
        lastModifierName: profile.fullName,
      });
      expect(result.enrollmentTpaAssigments).toHaveLength(1);
      expect(result.enrollmentTpaAssigments[0]).toEqual(newTpaDetails);
    });
  });
});
