/* eslint-disable max-lines */
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, ILike, In } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { FindingsTemplateModel } from '../models/findings-template.model';
import {
  CommissionPayer,
  PayoutCommissionPayer,
} from '@clinify/facility-preferences/enums/commission-payer';
import { DefaultPatientAccessType } from '@clinify/facility-preferences/enums/patient-lookup';
import {
  ChemoDiagnosisTemplate,
  NewChemoDiagnosisTemplate,
} from '@clinify/facility-preferences/interface/chemo-diagnosis.interface';
import {
  FieldOfficerResponseDto,
  EnrolleeReferralResponseDto,
  EnrollmentAgentDto,
  SponsorResponseDto,
  TpaResponseDto,
} from '@clinify/facility-preferences/interface/enrollment-agent.dto';
import {
  ConsultationsTemplateInput,
  DischargeSummaryTemplateInput,
  FindingsTemplateInput,
  LabCommentsTemplateInput,
  MandatoryFields,
  MedicalReportTemplateInput,
  NewConsultationsTemplateInput,
  NewDischargeSummaryTemplateInput,
  NewFindingsTemplateInput,
  NewLabCommentsTemplateInput,
  NewMedicalReportTemplateInput,
  NewOperationNoteTemplateInput,
  OperationNoteTemplateInput,
} from '@clinify/facility-preferences/interface/mandatory-fields';
import { ChemoDiagnosisTemplateModel } from '@clinify/facility-preferences/models/chemo-diagnosis-template.model';
import { ConsultationsTemplateModel } from '@clinify/facility-preferences/models/consultations-template.model';
import { DischargeSummaryTemplateModel } from '@clinify/facility-preferences/models/discharge-summary-template.model';
import { FacilityPreferenceModel } from '@clinify/facility-preferences/models/facility-preference.model';
import { LabCommentsTemplateModel } from '@clinify/facility-preferences/models/lab-comments-template.model';
import { MedicalReportTemplateModel } from '@clinify/facility-preferences/models/medical-report-template.model';
import { IFacilityPreferenceRepository } from '@clinify/facility-preferences/repositories/facility-preference.repository';
import { CommissionPayerResponse } from '@clinify/facility-preferences/responses/commission-payer.response';
import { MailTemplate } from '@clinify/facility-preferences/validators/mail-template.input';
import { validateFacilityPreferenceMutation } from '@clinify/facility-preferences/validators/validateFacilityPreferenceMutation';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { ProfilePreferenceService } from '@clinify/profile-preferences/services/profile-preference.service';
import { UserType } from '@clinify/shared/enums/users';
import { ProfileModel } from '@clinify/users/models/profile.model';

@Injectable()
export class FacilityPreferenceService {
  constructor(
    @InjectRepository(FacilityPreferenceModel)
    private readonly repository: IFacilityPreferenceRepository,
    private readonly manager: EntityManager,
    private readonly profilePreferenceService: ProfilePreferenceService,
  ) {}

  getFacilityPreference(hospitalId: string): Promise<FacilityPreferenceModel> {
    return this.repository.getFacilityPreference(hospitalId);
  }

  updateWelcomeMailTemplate(
    mutator: ProfileModel,
    hospitalId: string,
    input: MailTemplate,
  ): Promise<FacilityPreferenceModel> {
    validateFacilityPreferenceMutation(mutator, hospitalId);
    return this.repository.updateWelcomeMailTemplate(
      mutator,
      hospitalId,
      input,
    );
  }

  updateFormsMandatoryFields(
    mutator: ProfileModel,
    hospitalId: string,
    input: MandatoryFields,
  ): Promise<FacilityPreferenceModel> {
    validateFacilityPreferenceMutation(mutator, hospitalId);
    return this.repository.updateFormsMandatoryFields(
      mutator,
      hospitalId,
      input,
    );
  }

  updatePatientAccessType(
    hospitalId: string,
    mode: DefaultPatientAccessType,
  ): Promise<FacilityPreferenceModel> {
    return this.repository.updatePatientAccessType(hospitalId, mode);
  }

  saveFindingsTemplate(
    mutator: ProfileModel,
    input: NewFindingsTemplateInput,
  ): Promise<FindingsTemplateModel> {
    return this.repository.saveFindingsTemplate(mutator, input);
  }

  updateFindingsTemplate(
    mutator: ProfileModel,
    input: FindingsTemplateInput,
  ): Promise<FindingsTemplateModel> {
    return this.repository.updateFindingsTemplate(mutator, input);
  }

  deleteFindingsTemplate(id: string) {
    return this.repository.deleteFindingsTemplate(id);
  }
  findByFacilityPreferenceIdForFindingsTemplate(
    facilityPreferenceId: string,
  ): Promise<FindingsTemplateModel[]> {
    return this.repository.findByFacilityPreferenceIdForFindingsTemplate(
      facilityPreferenceId,
    );
  }
  findByHospitalIdForFindingsTemplate(
    hospitalId: string,
  ): Promise<FindingsTemplateModel[]> {
    return this.repository.findByHospitalIdForFindingsTemplate(hospitalId);
  }

  saveLabCommentsTemplate(
    mutator: ProfileModel,
    input: NewLabCommentsTemplateInput,
  ): Promise<LabCommentsTemplateModel> {
    return this.repository.saveLabCommentsTemplate(mutator, input);
  }

  updateLabCommentsTemplate(
    mutator: ProfileModel,
    input: LabCommentsTemplateInput,
  ): Promise<LabCommentsTemplateModel> {
    return this.repository.updateLabCommentsTemplate(mutator, input);
  }

  deleteLabCommentsTemplate(id: string) {
    return this.repository.deleteLabCommentsTemplate(id);
  }

  findByFacilityPreferenceIdForLabCommentsTemplate(
    facilityPreferenceId: string,
  ): Promise<LabCommentsTemplateModel[]> {
    return this.repository.findByFacilityPreferenceIdForLabCommentsTemplate(
      facilityPreferenceId,
    );
  }

  findByHospitalIdForLabCommentsTemplate(
    hospitalId: string,
  ): Promise<LabCommentsTemplateModel[]> {
    return this.repository.findByFacilityPreferenceIdForLabCommentsTemplate(
      hospitalId,
    );
  }

  findByFacilityPreferenceIdForMedicalReportTemplate(
    facilityPreferenceId: string,
  ): Promise<MedicalReportTemplateModel[]> {
    return this.repository.findByFacilityPreferenceIdForMedicalReportTemplate(
      facilityPreferenceId,
    );
  }

  findByHospitalIdForMedicalReportTemplate(
    hospitalId: string,
  ): Promise<MedicalReportTemplateModel[]> {
    return this.repository.findByHospitalIdForMedicalReportTemplate(hospitalId);
  }

  saveMedicalReportTemplate(
    mutator: ProfileModel,
    input: NewMedicalReportTemplateInput,
  ): Promise<MedicalReportTemplateModel> {
    return this.manager.save(
      MedicalReportTemplateModel,
      new MedicalReportTemplateModel({
        ...input,
        createdBy: mutator,
        creatorName: mutator.fullName,
      }),
    );
  }

  async updateMedicalReportTemplate(
    mutator: ProfileModel,
    input: MedicalReportTemplateInput,
  ): Promise<MedicalReportTemplateModel> {
    const { id, template } = input;
    const medicalReportTemplate = await this.manager
      .findOneOrFail(MedicalReportTemplateModel, {
        where: { id },
      })
      .catch(() => {
        throw new NotFoundException('Template Not Found');
      });

    return this.manager.save(MedicalReportTemplateModel, {
      ...medicalReportTemplate,
      template,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  }

  async deleteMedicalReportTemplate(
    id: string,
  ): Promise<MedicalReportTemplateModel> {
    await this.manager.delete(MedicalReportTemplateModel, {
      id,
    });

    return new MedicalReportTemplateModel({ id });
  }

  updateRadiologyContrastMode(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.repository.updateRadiologyContrastMode(hospitalId, mode);
  }

  updateDashboardColourMode(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.repository.updateDashboardColourMode(hospitalId, mode);
  }

  updateShowServiceDetails(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.repository.updateShowServiceDetails(hospitalId, mode);
  }

  updateInventoryClass(
    mutator: ProfileModel,
    inventoryClass: string,
  ): Promise<FacilityPreferenceModel> {
    return this.repository.updateInventoryClass(mutator, inventoryClass);
  }

  updateRolesServiceDetailsIsHidden(
    hospitalId: string,
    roles: string[],
  ): Promise<FacilityPreferenceModel> {
    return this.repository.updateRolesServiceDetailsIsHidden(hospitalId, roles);
  }

  async updateDefaultCommissionPayer(
    hospitalId: string,
    facilityPreferenceId: string,
    commissionPayer: CommissionPayer,
  ): Promise<CommissionPayerResponse> {
    return this.repository
      .update(facilityPreferenceId, { commissionPayer })
      .then(() => {
        return new CommissionPayerResponse({
          commissionPayer,
          hospitalId,
          facilityPreferenceId,
        });
      });
  }

  async updatePayoutCommissionPayer(
    mutator: ProfileModel,
    payoutCommissionPayer: PayoutCommissionPayer,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.repository.findOneOrFail({
      where: { hospitalId: mutator.hospitalId },
    });

    if (!facilityPreference) {
      throw new NotFoundException('Facility Preference Not Found');
    }

    return this.repository.save({
      ...facilityPreference,
      payoutCommissionPayer,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  }

  async updateReceiptSize(
    mutator: ProfileModel,
    facilityPreferenceId: string,
    receiptSize: string,
  ): Promise<FacilityPreferenceModel> {
    await this.repository
      .findOneOrFail({
        select: {
          id: true,
        },
        where: { hospitalId: mutator.hospitalId, id: facilityPreferenceId },
      })
      .catch(() => {
        throw new NotFoundException('Facility Preference Not Found');
      });

    return this.repository
      .update(facilityPreferenceId, { receiptSize })
      .then(() => {
        return new FacilityPreferenceModel({
          id: facilityPreferenceId,
          receiptSize,
        });
      });
  }

  async updateFileNumberGenerate(
    hospital: HospitalModel,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    if (!hospital?.id || !hospital?.name) {
      throw new BadRequestException('Facility Information Not Provided');
    }

    const facilityPreference = await this.repository
      .findOneOrFail({
        where: { hospitalId: hospital.id },
      })
      .catch(() => {
        throw new NotFoundException('Facility Not Found');
      });

    const { hospitalShortName: _hospitalShortName } = facilityPreference;

    let hospitalShortName = _hospitalShortName;
    if (mode && !hospitalShortName) {
      const hospitalFirstThreeLeters = hospital.name.substring(0, 3);

      const shortNames = await this.repository.find({
        select: ['hospitalShortName'],
        where: {
          hospitalShortName: ILike(
            `${hospitalFirstThreeLeters.toLocaleLowerCase()}%`,
          ),
        },
      });
      const shortNameNumbers = shortNames.map(({ hospitalShortName }) => {
        if (hospitalShortName.length === 3) {
          return 0;
        }
        return Number(hospitalShortName[3]);
      });

      const maxNumer = Math.max(...shortNameNumbers) + 1;

      if (!shortNames.length) {
        hospitalShortName = hospitalFirstThreeLeters.toLocaleUpperCase();
      } else {
        hospitalShortName = `${hospitalFirstThreeLeters.toLocaleUpperCase()}${maxNumer}`;
      }
    }

    return this.repository.save({
      ...facilityPreference,
      hospitalId: hospital.id,
      generateFileNumber: mode,
      hospitalShortName,
    });
  }
  updatePatientSurveyLinks(
    profile: ProfileModel,
    facilityPreferenceId: string,
    outPatientLink: string,
    inPatientLink: string,
  ) {
    return this.repository.updatePatientSurveyLinks(
      profile,
      facilityPreferenceId,
      outPatientLink,
      inPatientLink,
    );
  }

  async saveConsultationsTemplate(
    profile: ProfileModel,
    input: NewConsultationsTemplateInput,
  ): Promise<ConsultationsTemplateModel> {
    const consultationsTemplate =
      await this.repository.saveConsultationsTemplate(profile, input);
    return consultationsTemplate;
  }

  async updateConsultationsTemplate(
    profile: ProfileModel,
    input: ConsultationsTemplateInput,
  ): Promise<ConsultationsTemplateModel> {
    const consultationsTemplate =
      await this.repository.updateConsultationsTemplate(profile, input);
    return consultationsTemplate;
  }

  async deleteConsultationsTemplate(
    mutator: ProfileModel,
    id: string,
  ): Promise<ConsultationsTemplateModel> {
    const consultationsTemplate =
      await this.repository.deleteConsultationsTemplate(mutator, id);
    return consultationsTemplate;
  }

  async findByHospitalIdConsultationsTemplate(
    mutator: ProfileModel,
    id: string,
  ) {
    const consultationsTemplate =
      await this.repository.findByHospitalIdConsultationsTemplate(mutator, id);
    return consultationsTemplate;
  }

  async findByFacilityPreferenceIdConsultationsTemplate(
    mutator: ProfileModel,
    id: string,
  ) {
    const consultationsTemplate =
      await this.repository.findByFacilityPreferenceIdConsultationsTemplate(
        mutator,
        id,
      );
    return consultationsTemplate;
  }

  saveChemoDiagnosisTemplate(
    mutator: ProfileModel,
    input: NewChemoDiagnosisTemplate,
  ): Promise<ChemoDiagnosisTemplateModel> {
    return this.repository.saveChemoDiagnosisTemplate(mutator, input);
  }

  updateChemoDiagnosisTemplate(
    mutator: ProfileModel,
    id: string,
    input: ChemoDiagnosisTemplate,
  ): Promise<ChemoDiagnosisTemplateModel> {
    return this.repository.updateChemoDiagnosisTemplate(mutator, id, input);
  }

  deleteChemoDiagnosisTemplate(id: string) {
    return this.repository.deleteChemoDiagnosisTemplate(id);
  }
  findByHospitalIdChemoDiagnosisTemplates(
    hospitalId: string,
    section: string,
    chemoDiagnosis?: string,
  ): Promise<ChemoDiagnosisTemplateModel[]> {
    return this.repository.findByHospitalIdChemoDiagnosisTemplates(
      hospitalId,
      section,
      chemoDiagnosis,
    );
  }

  findByFacilityPreferenceIdChemoDiagnosisTemplates(
    facilityPreferenceId: string,
    section: string,
    chemoDiagnosis?: string,
  ): Promise<ChemoDiagnosisTemplateModel[]> {
    return this.repository.findByFacilityPreferenceIdChemoDiagnosisTemplates(
      facilityPreferenceId,
      section,
      chemoDiagnosis,
    );
  }

  getOneChemoDiagnosisTemplate(
    id: string,
  ): Promise<ChemoDiagnosisTemplateModel> {
    return this.repository.getOneChemoDiagnosisTemplate(id);
  }

  saveDischargeSummaryTemplate(
    mutator: ProfileModel,
    input: NewDischargeSummaryTemplateInput,
  ): Promise<DischargeSummaryTemplateModel> {
    return this.repository.saveDischargeSummaryTemplate(mutator, input);
  }

  updateDischargeSummaryTemplate(
    mutator: ProfileModel,
    input: DischargeSummaryTemplateInput,
  ): Promise<DischargeSummaryTemplateModel> {
    return this.repository.updateDischargeSummaryTemplate(mutator, input);
  }

  deleteDischargeSummaryTemplate(id: string) {
    return this.repository.deleteDischargeSummaryTemplate(id);
  }

  findByFacilityPreferenceIdDischargeSummaryTemplates(
    facilityPreferenceId: string,
  ): Promise<DischargeSummaryTemplateModel[]> {
    return this.repository.findByFacilityPreferenceIdDischargeSummaryTemplates(
      facilityPreferenceId,
    );
  }

  findByHospitalIdDischargeSummaryTemplates(
    hospitalId: string,
  ): Promise<DischargeSummaryTemplateModel[]> {
    return this.repository.findByHospitalIdDischargeSummaryTemplates(
      hospitalId,
    );
  }

  async updateTariffsToUse(
    facilityPreferenceId: string,
    useHQFacilityTariffs: boolean,
    mutator: ProfileModel,
  ): Promise<FacilityPreferenceModel> {
    const _update: QueryDeepPartialEntity<FacilityPreferenceModel> = {
      useHQFacilityTariffs,
    };
    const branchesPreference = await this.repository.find({
      where: { hospital: { hqFacilityId: mutator.hospitalId } },
      select: ['id'],
    });
    await this.repository.update(
      {
        id: In([
          facilityPreferenceId,
          ...branchesPreference?.map?.(({ id }) => id),
        ]),
      },
      _update,
    );
    return new FacilityPreferenceModel({
      id: facilityPreferenceId,
      useHQFacilityTariffs,
    });
  }

  async updateInventoryToUse(
    facilityPreferenceId: string,
    useHQFacilityInventory: boolean,
    mutator: ProfileModel,
  ): Promise<FacilityPreferenceModel> {
    const _update: QueryDeepPartialEntity<FacilityPreferenceModel> = {
      useHQFacilityInventory,
    };
    const branchesPreference = await this.repository.find({
      where: { hospital: { hqFacilityId: mutator.hospitalId } },
      select: ['id'],
    });
    await this.repository.update(
      {
        id: In([
          facilityPreferenceId,
          ...branchesPreference?.map?.(({ id }) => id),
        ]),
      },
      _update,
    );
    return new FacilityPreferenceModel({
      id: facilityPreferenceId,
      useHQFacilityInventory,
    });
  }

  updateSpecialistAccess(
    mutator: ProfileModel,
    profileId: string,
    specialistIds: string[],
  ) {
    return this.repository.updateSpecialistAccess(
      mutator,
      profileId,
      specialistIds,
    );
  }

  getSpecialistAccess(mutator: ProfileModel, profileId: string) {
    return this.repository.getSpecialistAccess(profileId, mutator.hospitalId);
  }

  findByHospitalIdSpecialistAccess(hospitalId: string) {
    return this.repository.findByHospitalIdSpecialistAccess(hospitalId);
  }

  saveOperationNoteTemplate(
    mutator: ProfileModel,
    input: NewOperationNoteTemplateInput,
  ) {
    return this.repository.saveOperationNoteTemplate(mutator, input);
  }

  updateOperationNoteTemplate(
    mutator: ProfileModel,
    input: OperationNoteTemplateInput,
  ) {
    return this.repository.updateOperationNoteTemplate(mutator, input);
  }

  deleteOperationNoteTemplate(id: string) {
    return this.repository.deleteOperationNoteTemplate(id);
  }

  findByFacilityPreferenceIdOperationNoteTemplates(
    facilityPreferenceId: string,
  ) {
    return this.repository.findByFacilityPreferenceIdOperationNoteTemplates(
      facilityPreferenceId,
    );
  }

  findByHospitalIdOperationNoteTemplates(hospitalId: string) {
    return this.repository.findByHospitalIdOperationNoteTemplates(hospitalId);
  }

  updateHmoSingleVisitPACode(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.repository.updateHmoSingleVisitPACode(hospitalId, mode);
  }

  updateCustomPaFormatType(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.repository.updateCustomPaFormatType(hospitalId, mode);
  }

  updateEnableBusinessRulePreventSubmit(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    return this.repository.updateEnableBusinessRulePreventSubmit(
      hospitalId,
      mode,
    );
  }

  updatePatientRegistrationFee(
    hospitalId: string,
    registrationFee: number,
  ): Promise<FacilityPreferenceModel> {
    return this.repository.updatePatientRegistrationFee(
      hospitalId,
      registrationFee,
    );
  }

  updateEnrolleeCapitationAmount(
    hospitalId: string,
    enrolleeCapitationAmount: number,
  ): Promise<FacilityPreferenceModel> {
    return this.repository.updateEnrolleeCapitationAmount(
      hospitalId,
      enrolleeCapitationAmount,
    );
  }

  async updateAutoProcessClaims(
    hospitalId: string,
    mode: boolean,
    mutator: ProfileModel,
  ): Promise<FacilityPreferenceModel> {
    const updatedPreference = await this.repository.updateAutoProcessClaims(
      hospitalId,
      mode,
    );
    const staffIds = await this.manager.find(ProfileModel, {
      where: {
        hospital: { id: hospitalId },
        type: In([
          UserType.ClaimOfficer,
          UserType.ClaimOfficerHOD,
          UserType.ClaimReviewer,
          UserType.ClaimReviewerHOD,
          UserType.ClaimAdmin,
          UserType.ClaimFinance,
        ]),
      },
      select: ['id'],
    });
    await Promise.all(
      staffIds.map(({ id }) => {
        return this.profilePreferenceService.updateProfilePreference(
          id,
          { autoProcessClaims: mode },
          mutator,
        );
      }),
    );

    return updatedPreference;
  }

  async updateAutoProcessPreauthorizations(
    hospitalId: string,
    mode: boolean,
    mutator: ProfileModel,
  ): Promise<FacilityPreferenceModel> {
    const updatedPreference =
      await this.repository.updateAutoProcessPreauthorizations(
        hospitalId,
        mode,
      );
    const staffIds = await this.manager.find(ProfileModel, {
      where: {
        hospital: { id: hospitalId },
        type: In([
          UserType.ClaimOfficer,
          UserType.ClaimOfficerHOD,
          UserType.ClaimReviewer,
          UserType.ClaimReviewerHOD,
          UserType.ClaimAdmin,
          UserType.ClaimFinance,
        ]),
      },
      select: ['id'],
    });
    await Promise.all(
      staffIds.map(({ id }) => {
        return this.profilePreferenceService.updateProfilePreference(
          id,
          { autoProcessPreauthorizations: mode },
          mutator,
        );
      }),
    );

    return updatedPreference;
  }

  async updateEnrollmentAgentAssignments(
    mutator: ProfileModel,
    agent: EnrollmentAgentDto,
  ) {
    return this.repository.updateEnrollmentAgentAssignments(mutator, agent);
  }

  async updateEnrollmentAgency(
    mutator: ProfileModel,
    agent: string,
    enrollmentAgency: string[],
  ): Promise<FacilityPreferenceModel> {
    return this.repository.updateEnrollmentAgency(
      mutator,
      agent,
      enrollmentAgency,
    );
  }

  async updateEnrolleeSponsors(
    mutator: ProfileModel,
    sponsors: string[],
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.repository.findOneOrFail({
      where: { hospitalId: mutator.hospitalId },
    });

    return this.repository.save({
      ...facilityPreference,
      enrolleeSponsors: sponsors,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  }

  async updateFieldOfficer(
    mutator: ProfileModel,
    fieldOfficer: FieldOfficerResponseDto,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.repository.findOneOrFail({
      where: { hospitalId: mutator.hospitalId },
    });

    const existingFieldOfficers = facilityPreference.fieldOfficers || [];
    const updatedFieldOfficers = [...existingFieldOfficers];

    const existingIndex = updatedFieldOfficers.findIndex(
      (existing) => existing.profileId === fieldOfficer.profileId,
    );

    if (existingIndex !== -1) {
      updatedFieldOfficers[existingIndex] = {
        ...updatedFieldOfficers[existingIndex],
        ...fieldOfficer,
      };
    } else {
      updatedFieldOfficers.push(fieldOfficer);
    }

    return this.repository.save({
      ...facilityPreference,
      fieldOfficers: updatedFieldOfficers,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  }

  async updateEnrolleeReferral(
    mutator: ProfileModel,
    enrolleeReferral: EnrolleeReferralResponseDto,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.repository.findOneOrFail({
      where: { hospitalId: mutator.hospitalId },
    });

    const existingEnrolleeReferrals =
      facilityPreference.enrolleeReferrals || [];
    const updatedEnrolleeReferrals = [...existingEnrolleeReferrals];

    const existingIndex = updatedEnrolleeReferrals.findIndex(
      (existing) => existing.referrerCode === enrolleeReferral.referrerCode,
    );

    if (existingIndex !== -1) {
      updatedEnrolleeReferrals[existingIndex] = {
        ...updatedEnrolleeReferrals[existingIndex],
        ...enrolleeReferral,
      };
    } else {
      updatedEnrolleeReferrals.push(enrolleeReferral);
    }

    return this.repository.save({
      ...facilityPreference,
      enrolleeReferrals: updatedEnrolleeReferrals,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  }

  async updateSponsorAssignment(
    mutator: ProfileModel,
    sponsor: SponsorResponseDto,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.repository.findOneOrFail({
      where: { hospitalId: mutator.hospitalId },
    });
    if (!facilityPreference) {
      throw new NotFoundException('Facility Preference Not Found');
    }

    const existingSponsors = facilityPreference.enrolleeSponsorAssigments || [];
    const updatedSponsors = [...existingSponsors];

    const existingIndex = updatedSponsors.findIndex(
      (existing) => existing.ref === sponsor.ref,
    );

    if (existingIndex !== -1) {
      updatedSponsors[existingIndex] = {
        ...updatedSponsors[existingIndex],
        ...sponsor,
      };
    } else {
      updatedSponsors.push(sponsor);
    }

    return this.repository.save({
      ...facilityPreference,
      enrolleeSponsorAssigments: updatedSponsors,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  }

  async updateEnrolleeCapitationAmountByPlanType(
    mutator: ProfileModel,
    planTypeId: string,
    planTypeName: string,
    capitationAmount: number,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.repository.findOneOrFail({
      where: { hospitalId: mutator.hospitalId },
    });

    if (!facilityPreference) {
      throw new NotFoundException('Facility Preference Not Found');
    }

    const existingCapitationAmounts =
      facilityPreference.enrolleeCapitionAmountByPlanType || [];
    const updatedCapitationAmounts = [...existingCapitationAmounts];

    const existingIndex = updatedCapitationAmounts.findIndex(
      (existing) => existing.planTypeId === planTypeId,
    );

    if (existingIndex !== -1) {
      updatedCapitationAmounts[existingIndex] = {
        ...updatedCapitationAmounts[existingIndex],
        planTypeId,
        planTypeName,
        amount: capitationAmount,
      };
    } else {
      updatedCapitationAmounts.push({
        planTypeId,
        amount: capitationAmount,
        planTypeName,
      });
    }

    return this.repository.save({
      ...facilityPreference,
      enrolleeCapitionAmountByPlanType: updatedCapitationAmounts,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  }

  async updateEnrolleeCapitationAmountPerPlan(
    mutator: ProfileModel,
    enabled: boolean,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.repository.findOneOrFail({
      where: { hospitalId: mutator.hospitalId },
    });

    if (!facilityPreference) {
      throw new NotFoundException('Facility Preference Not Found');
    }

    return this.repository.save({
      ...facilityPreference,
      enrolleeCapitationAmountPerPlan: enabled,
      enrolleeCapitionAmountByPlanType: [],
      enrolleeCapitationAmount: 0,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  }

  async updateEnrolleeTpaAssignment(
    mutator: ProfileModel,
    tpaInput: TpaResponseDto,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.repository.findOneOrFail({
      where: { hospitalId: mutator.hospitalId },
    });
    if (!facilityPreference) {
      throw new NotFoundException('Facility Preference Not Found');
    }

    const existingTpaInput = facilityPreference.enrollmentTpaAssigments || [];
    const updatedTpaInput = [...existingTpaInput];

    const existingIndex = updatedTpaInput.findIndex(
      (existing) => existing.ref === tpaInput.ref,
    );

    if (existingIndex !== -1) {
      updatedTpaInput[existingIndex] = {
        ...updatedTpaInput[existingIndex],
        ...tpaInput,
      };
    } else {
      updatedTpaInput.push(tpaInput);
    }

    return this.repository.save({
      ...facilityPreference,
      enrollmentTpaAssigments: updatedTpaInput,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  }
}
