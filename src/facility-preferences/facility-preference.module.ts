import { Lo<PERSON>, Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FacilityPreferenceModel } from './models/facility-preference.model';
import { CustomFacilityPreferenceRepoMethods } from './repositories/facility-preference.repository';
import { AuthorizationModule } from '@clinify/authorization/authorization.module';
import { extendModel } from '@clinify/database/extendModel';
import { FacilityPreferenceResolver } from '@clinify/facility-preferences/resolvers/facility-preference.resolver';
import { FindingsTemplateResolver } from '@clinify/facility-preferences/resolvers/findings-template.resolver';
import { FacilityPreferenceService } from '@clinify/facility-preferences/services/facility-preference.service';
import { ProfilePreferencesModule } from '@clinify/profile-preferences/profile-preferences.module';
import PubSub, { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';

@Module({
  imports: [
    TypeOrmModule.forFeature([FacilityPreferenceModel]),
    AuthorizationModule,
    ProfilePreferencesModule,
  ],
  providers: [
    extendModel(FacilityPreferenceModel, CustomFacilityPreferenceRepoMethods),
    FacilityPreferenceService,
    FacilityPreferenceResolver,
    FindingsTemplateResolver,
    Logger,
    {
      provide: PUB_SUB,
      useFactory: () => PubSub,
    },
  ],
  exports: [FacilityPreferenceService],
})
export class FacilityPreferenceModule {}
