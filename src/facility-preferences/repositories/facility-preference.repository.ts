/* eslint-disable max-lines */
import { NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { MailTemplate } from '../validators/mail-template.input';
import { DefaultPatientAccessType } from '@clinify/facility-preferences/enums/patient-lookup';
import {
  ChemoDiagnosisTemplate,
  NewChemoDiagnosisTemplate,
} from '@clinify/facility-preferences/interface/chemo-diagnosis.interface';
import {
  EnrollmentAgencyDto,
  EnrollmentAgentDto,
} from '@clinify/facility-preferences/interface/enrollment-agent.dto';
import {
  ConsultationsTemplateInput,
  DischargeSummaryTemplateInput,
  FindingsTemplateInput,
  LabCommentsTemplateInput,
  MandatoryFields,
  NewConsultationsTemplateInput,
  NewDischargeSummaryTemplateInput,
  NewFindingsTemplateInput,
  NewLabCommentsTemplateInput,
  NewOperationNoteTemplateInput,
  OperationNoteTemplateInput,
} from '@clinify/facility-preferences/interface/mandatory-fields';
import { ChemoDiagnosisCycleModel } from '@clinify/facility-preferences/models/chemo-diagnosis-cycle.model';
import { ChemoDiagnosisTemplateModel } from '@clinify/facility-preferences/models/chemo-diagnosis-template.model';
import { ConsultationsTemplateModel } from '@clinify/facility-preferences/models/consultations-template.model';
import { DischargeSummaryTemplateModel } from '@clinify/facility-preferences/models/discharge-summary-template.model';
import { FacilityPreferenceModel } from '@clinify/facility-preferences/models/facility-preference.model';
import { FindingsTemplateModel } from '@clinify/facility-preferences/models/findings-template.model';
import { LabCommentsTemplateModel } from '@clinify/facility-preferences/models/lab-comments-template.model';
import { MedicalReportTemplateModel } from '@clinify/facility-preferences/models/medical-report-template.model';
import { OperationNoteTemplateModel } from '@clinify/facility-preferences/models/operation-note-template.model';
import { SpecialistAccessModel } from '@clinify/facility-preferences/models/specialist-access.model';
import { ProfileModel } from '@clinify/users/models/profile.model';

export interface IFacilityPreferenceRepository
  extends Repository<FacilityPreferenceModel> {
  this: Repository<FacilityPreferenceModel>;

  getFacilityPreference(hospitalId: string): Promise<FacilityPreferenceModel>;

  updateWelcomeMailTemplate(
    profile: ProfileModel,
    hospitalId: string,
    input: MailTemplate,
  ): Promise<FacilityPreferenceModel>;

  updateFormsMandatoryFields(
    mutator: ProfileModel,
    hospitalId: string,
    input: MandatoryFields,
  ): Promise<FacilityPreferenceModel>;

  updatePatientAccessType(
    hospitalId: string,
    mode: DefaultPatientAccessType,
  ): Promise<FacilityPreferenceModel>;

  saveFindingsTemplate(
    mutator: ProfileModel,
    input: NewFindingsTemplateInput,
  ): Promise<FindingsTemplateModel>;

  updateFindingsTemplate(
    mutator: ProfileModel,
    input: FindingsTemplateInput,
  ): Promise<FindingsTemplateModel>;

  deleteFindingsTemplate(id: string): Promise<FindingsTemplateModel>;

  findByFacilityPreferenceIdForFindingsTemplate(
    facilityPreferenceId: string,
  ): Promise<FindingsTemplateModel[]>;

  findByHospitalIdForFindingsTemplate(
    hospitalId: string,
  ): Promise<FindingsTemplateModel[]>;

  saveLabCommentsTemplate(
    mutator: ProfileModel,
    input: NewLabCommentsTemplateInput,
  ): Promise<LabCommentsTemplateModel>;

  updateLabCommentsTemplate(
    mutator: ProfileModel,
    input: LabCommentsTemplateInput,
  ): Promise<LabCommentsTemplateModel>;

  deleteLabCommentsTemplate(id: string): Promise<LabCommentsTemplateModel>;

  findByFacilityPreferenceIdForLabCommentsTemplate(
    facilityPreferenceId: string,
  ): Promise<LabCommentsTemplateModel[]>;

  findByHospitalIdForLabCommentsTemplate(
    hospitalId: string,
  ): Promise<LabCommentsTemplateModel[]>;
  findByFacilityPreferenceIdForMedicalReportTemplate(
    facilityPreferenceId: string,
  ): Promise<MedicalReportTemplateModel[]>;

  findByHospitalIdForMedicalReportTemplate(
    hospitalId: string,
  ): Promise<MedicalReportTemplateModel[]>;

  updateRadiologyContrastMode(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel>;

  updateShowServiceDetails(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel>;

  updateInventoryClass(
    mutator: ProfileModel,
    inventoryClass: string,
  ): Promise<FacilityPreferenceModel>;

  updateRolesServiceDetailsIsHidden(
    hospitalId: string,
    roles: string[],
  ): Promise<FacilityPreferenceModel>;
  updateDashboardColourMode(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel>;

  updatePatientSurveyLinks(
    profile: ProfileModel,
    facilityPreferenceId: string,
    outPatientLink: string,
    inPatientLink: string,
  ): Promise<FacilityPreferenceModel>;

  saveConsultationsTemplate(
    mutator: ProfileModel,
    input: NewConsultationsTemplateInput,
  ): Promise<ConsultationsTemplateModel>;

  updateConsultationsTemplate(
    mutator: ProfileModel,
    input: ConsultationsTemplateInput,
  ): Promise<ConsultationsTemplateModel>;

  deleteConsultationsTemplate(
    mutator: ProfileModel,
    id: string,
  ): Promise<ConsultationsTemplateModel>;

  findByHospitalIdConsultationsTemplate(
    mutator: ProfileModel,
    id: string,
  ): Promise<[ConsultationsTemplateModel]>;

  findByFacilityPreferenceIdConsultationsTemplate(
    mutator: ProfileModel,
    id: string,
  ): Promise<[ConsultationsTemplateModel]>;

  saveChemoDiagnosisTemplate(
    mutator: ProfileModel,
    input: NewChemoDiagnosisTemplate,
  ): Promise<ChemoDiagnosisTemplateModel>;

  updateChemoDiagnosisTemplate(
    mutator: ProfileModel,
    id: string,
    input: ChemoDiagnosisTemplate,
  ): Promise<ChemoDiagnosisTemplateModel>;

  deleteChemoDiagnosisTemplate(
    id: string,
  ): Promise<ChemoDiagnosisTemplateModel>;

  findByHospitalIdChemoDiagnosisTemplates(
    hospitalId: string,
    section: string,
    chemoDiagnosis?: string,
  ): Promise<ChemoDiagnosisTemplateModel[]>;

  findByFacilityPreferenceIdChemoDiagnosisTemplates(
    id: string,
    section: string,
    chemoDiagnosis?: string,
  ): Promise<ChemoDiagnosisTemplateModel[]>;

  getOneChemoDiagnosisTemplate(
    id: string,
  ): Promise<ChemoDiagnosisTemplateModel>;

  saveDischargeSummaryTemplate(
    mutator: ProfileModel,
    input: NewDischargeSummaryTemplateInput,
  ): Promise<DischargeSummaryTemplateModel>;

  updateDischargeSummaryTemplate(
    mutator: ProfileModel,
    input: DischargeSummaryTemplateInput,
  ): Promise<DischargeSummaryTemplateModel>;

  deleteDischargeSummaryTemplate(
    id: string,
  ): Promise<DischargeSummaryTemplateModel>;

  findByHospitalIdDischargeSummaryTemplates(
    id: string,
  ): Promise<[DischargeSummaryTemplateModel]>;

  findByFacilityPreferenceIdDischargeSummaryTemplates(
    id: string,
  ): Promise<[DischargeSummaryTemplateModel]>;

  saveOperationNoteTemplate(
    mutator: ProfileModel,
    input: NewOperationNoteTemplateInput,
  ): Promise<OperationNoteTemplateModel>;

  updateOperationNoteTemplate(
    mutator: ProfileModel,
    input: OperationNoteTemplateInput,
  ): Promise<OperationNoteTemplateModel>;

  deleteOperationNoteTemplate(id: string): Promise<OperationNoteTemplateModel>;

  findByHospitalIdOperationNoteTemplates(
    id: string,
  ): Promise<[OperationNoteTemplateModel]>;

  findByFacilityPreferenceIdOperationNoteTemplates(
    id: string,
  ): Promise<[OperationNoteTemplateModel]>;

  updateSpecialistAccess(
    mutator: ProfileModel,
    patientId: string,
    specialistIds: string[],
  ): Promise<SpecialistAccessModel>;

  getSpecialistAccess(
    profileId: string,
    hospitalId: string,
  ): Promise<SpecialistAccessModel>;

  findByHospitalIdSpecialistAccess(
    hospitalId: string,
  ): Promise<SpecialistAccessModel[]>;

  updateHmoSingleVisitPACode(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel>;

  updateCustomPaFormatType(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel>;

  updateEnableBusinessRulePreventSubmit(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel>;

  updatePatientRegistrationFee(
    hospitalId: string,
    registrationFee: number,
  ): Promise<FacilityPreferenceModel>;

  updateEnrolleeCapitationAmount(
    hospitalId: string,
    enrolleeCapitationAmount: number,
  ): Promise<FacilityPreferenceModel>;

  updateAutoProcessClaims(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel>;

  updateAutoProcessPreauthorizations(
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel>;

  updateEnrollmentAgency(
    mutator: ProfileModel,
    administrationAgency: string,
    agencies: string[],
  ): Promise<FacilityPreferenceModel>;

  updateEnrollmentAgentAssignments(
    mutator: ProfileModel,
    agent: EnrollmentAgentDto,
  ): Promise<FacilityPreferenceModel>;
}

export const CustomFacilityPreferenceRepoMethods = {
  getFacilityPreference(hospitalId: string): Promise<FacilityPreferenceModel> {
    return this.findOne({ where: { hospitalId } });
  },

  async updateWelcomeMailTemplate(
    this: IFacilityPreferenceRepository,
    profile: ProfileModel,
    hospitalId: string,
    input: MailTemplate,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    return this.save({
      ...facilityPreference,
      welcomeMailTemplate: input,
      updatedBy: profile,
      hospitalId,
      lastModifierName: profile.fullName,
    });
  },

  async updateFormsMandatoryFields(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    hospitalId: string,
    input: MandatoryFields,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    return this.save({
      ...facilityPreference,
      mandatoryFields: input,
      updatedBy: mutator,
      hospitalId,
      lastModifierName: mutator.fullName,
    });
  },
  async updatePatientAccessType(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    mode: DefaultPatientAccessType,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    return this.save({
      ...facilityPreference,
      hospitalId,
      patientAccessType: mode,
    });
  },
  async saveFindingsTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    input: NewFindingsTemplateInput,
  ) {
    const newFindingsTemplate = new FindingsTemplateModel({
      ...input,
      createdBy: mutator,
      creatorName: mutator.fullName,
    });
    return this.manager.save(FindingsTemplateModel, newFindingsTemplate);
  },
  async updateFindingsTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    input: FindingsTemplateInput,
  ) {
    const { id, findings, impression } = input;
    const findingsTemplate = await this.manager.findOne(FindingsTemplateModel, {
      where: { id },
    });
    findingsTemplate.findings = findings;
    findingsTemplate.impression = impression;
    findingsTemplate.updatedBy = mutator;
    findingsTemplate.lastModifierName = mutator.fullName;
    return this.manager.save(FindingsTemplateModel, findingsTemplate);
  },
  async deleteFindingsTemplate(
    this: IFacilityPreferenceRepository,
    id: string,
  ) {
    const findingsTemplate = await this.manager.findOne(FindingsTemplateModel, {
      where: { id },
    });
    if (!findingsTemplate) {
      throw new Error('Template Not Found');
    }
    this.manager.delete(FindingsTemplateModel, {
      id,
    });
    return findingsTemplate;
  },
  async findByFacilityPreferenceIdForFindingsTemplate(
    this: IFacilityPreferenceRepository,
    facilityPreferenceId: string,
  ) {
    return this.manager.find(FindingsTemplateModel, {
      where: { facilityPreferenceId },
    });
  },
  async findByHospitalIdForFindingsTemplate(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
  ) {
    const facilityPreference = await this.findOne({
      where: { hospitalId },
      relations: ['findingsTemplates'],
    });
    return facilityPreference.findingsTemplates;
  },
  async saveLabCommentsTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    input: NewLabCommentsTemplateInput,
  ) {
    const newLabCommentsTemplate = new LabCommentsTemplateModel({
      ...input,
      createdBy: mutator,
      creatorName: mutator.fullName,
    });
    return this.manager.save(LabCommentsTemplateModel, newLabCommentsTemplate);
  },
  async updateLabCommentsTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    input: LabCommentsTemplateInput,
  ) {
    const { id, comment } = input;
    const labCommentsTemplate = await this.manager.findOne(
      LabCommentsTemplateModel,
      {
        where: { id },
      },
    );
    labCommentsTemplate.comment = comment;
    labCommentsTemplate.updatedBy = mutator;
    labCommentsTemplate.lastModifierName = mutator.fullName;
    return this.manager.save(LabCommentsTemplateModel, labCommentsTemplate);
  },
  async deleteLabCommentsTemplate(
    this: IFacilityPreferenceRepository,
    id: string,
  ) {
    const labCommentsTemplate = await this.manager.findOne(
      LabCommentsTemplateModel,
      {
        where: { id },
      },
    );
    if (!labCommentsTemplate) {
      throw new Error('Template Not Found');
    }
    this.manager.delete(LabCommentsTemplateModel, {
      id,
    });
    return labCommentsTemplate;
  },
  async findByFacilityPreferenceIdForLabCommentsTemplate(
    this: IFacilityPreferenceRepository,
    facilityPreferenceId: string,
  ) {
    return this.manager.find(LabCommentsTemplateModel, {
      where: { facilityPreferenceId },
    });
  },
  async findByHospitalIdForLabCommentsTemplate(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
  ) {
    const facilityPreference = await this.findOne({
      where: { hospitalId },
      relations: ['labCommentsTemplates'],
    });
    return facilityPreference.labCommentsTemplates;
  },
  async findByFacilityPreferenceIdForMedicalReportTemplate(
    this: IFacilityPreferenceRepository,
    facilityPreferenceId: string,
  ) {
    return this.manager.find(MedicalReportTemplateModel, {
      where: { facilityPreferenceId },
    });
  },
  async findByHospitalIdForMedicalReportTemplate(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
  ) {
    const facilityPreference = await this.findOne({
      where: { hospitalId },
      relations: ['medicalReportTemplates'],
    });
    return facilityPreference.medicalReportTemplates;
  },
  async updateRadiologyContrastMode(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    return this.save({
      ...facilityPreference,
      hospitalId,
      radiologyContrastConfirmation: mode,
    });
  },
  async updateDashboardColourMode(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    return this.save({
      ...facilityPreference,
      hospitalId,
      dashboardColourMode: mode,
    });
  },
  async updateShowServiceDetails(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    return this.save({
      ...facilityPreference,
      hospitalId,
      showServiceDetails: mode,
      ...(mode && { rolesServiceDetailsIsHidden: [] }),
    });
  },
  async updateInventoryClass(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    inventoryClass: string,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({
      where: { hospitalId: mutator.hospitalId },
    });
    return this.save({
      ...facilityPreference,
      hospitalId: mutator.hospitalId,
      inventoryClass,
      updatedBy: mutator,
    });
  },
  async updateRolesServiceDetailsIsHidden(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    roles: string[],
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    return this.save({
      ...facilityPreference,
      hospitalId,
      rolesServiceDetailsIsHidden: roles,
    });
  },
  async updatePatientSurveyLinks(
    this: IFacilityPreferenceRepository,
    profile: ProfileModel,
    facilityPreferenceId: string,
    outPatientLink: string,
    inPatientLink: string,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({
      where: { id: facilityPreferenceId },
    });
    return this.save({
      ...facilityPreference,
      outPatientLink,
      inPatientLink,
      updatedBy: profile,
      lastModifierName: profile.fullName,
    });
  },
  async saveConsultationsTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    input: NewConsultationsTemplateInput,
  ): Promise<ConsultationsTemplateModel> {
    const {
      name,
      facilityPreferenceId,
      complaints,
      historyComplaints,
      reviewSystems,
      physicalExamination,
      audiometry,
      healthEducation,
      treatmentPlan,
    } = input;
    const facilityPreference = await this.findOne({
      where: { id: facilityPreferenceId },
    });
    const newConsultationsTemplate = new ConsultationsTemplateModel({
      name,
      facilityPreference,
      facilityPreferenceId,
      complaints,
      historyComplaints,
      reviewSystems,
      physicalExamination,
      audiometry,
      healthEducation,
      treatmentPlan,
    });
    return this.manager.save(ConsultationsTemplateModel, {
      ...newConsultationsTemplate,
      createdBy: mutator,
      creatorName: mutator.fullName,
    });
  },
  async updateConsultationsTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    input: ConsultationsTemplateInput,
  ): Promise<ConsultationsTemplateModel> {
    const { id } = input;
    const newConsultationsTemplate = await this.manager
      .findOneOrFail(ConsultationsTemplateModel, {
        where: { id },
        relations: ['facilityPreference'],
      })
      .catch(() => {
        throw new NotFoundException('Template Not Found');
      });
    return this.manager.save(ConsultationsTemplateModel, {
      ...newConsultationsTemplate,
      ...input,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  },
  async deleteConsultationsTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    id: string,
  ): Promise<ConsultationsTemplateModel> {
    const consultationsTemplate = await this.manager
      .findOneOrFail(ConsultationsTemplateModel, {
        where: { id },
        relations: ['facilityPreference'],
      })
      .catch(() => {
        throw new NotFoundException('Template Not Found');
      });

    await this.manager.delete(ConsultationsTemplateModel, {
      id,
    });
    return consultationsTemplate;
  },
  async findByHospitalIdConsultationsTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    hospitalId: string,
  ) {
    const facilityPreference = await this.findOne({
      where: { hospitalId },
      relations: ['consultationsTemplates'],
    });
    return facilityPreference.consultationsTemplates;
  },
  async findByFacilityPreferenceIdConsultationsTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    id: string,
  ) {
    return this.manager.find(ConsultationsTemplateModel, {
      where: { facilityPreferenceId: id },
    });
  },
  async saveDischargeSummaryTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    input: NewDischargeSummaryTemplateInput,
  ): Promise<DischargeSummaryTemplateModel> {
    const { name, facilityPreferenceId, summary } = input;
    const facilityPreference = await this.findOne({
      where: { id: facilityPreferenceId },
    });
    const newDischargeSummaryTemplate = new DischargeSummaryTemplateModel({
      name,
      facilityPreference,
      facilityPreferenceId,
      summary,
    });
    return this.manager.save(DischargeSummaryTemplateModel, {
      ...newDischargeSummaryTemplate,
      createdBy: mutator,
      creatorName: mutator.fullName,
    });
  },
  async updateDischargeSummaryTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    input: DischargeSummaryTemplateInput,
  ): Promise<DischargeSummaryTemplateModel> {
    const { id } = input;
    const newDischargeSummaryTemplate = await this.manager
      .findOneOrFail(DischargeSummaryTemplateModel, {
        where: { id },
        relations: ['facilityPreference'],
      })
      .catch(() => {
        throw new NotFoundException('Template Not Found');
      });
    return this.manager.save(DischargeSummaryTemplateModel, {
      ...newDischargeSummaryTemplate,
      ...input,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  },
  async deleteDischargeSummaryTemplate(
    this: IFacilityPreferenceRepository,
    id: string,
  ): Promise<DischargeSummaryTemplateModel> {
    const dischargeSummaryTemplate = await this.manager
      .findOneOrFail(DischargeSummaryTemplateModel, {
        where: { id },
        relations: ['facilityPreference'],
      })
      .catch(() => {
        throw new NotFoundException('Template Not Found');
      });

    await this.manager.delete(DischargeSummaryTemplateModel, {
      id,
    });
    return dischargeSummaryTemplate;
  },
  async findByHospitalIdDischargeSummaryTemplates(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
  ) {
    const fp = await this.findOne({
      where: {
        hospitalId,
      },
      relations: ['dischargeSummaryTemplates'],
    });
    return fp?.dischargeSummaryTemplates;
  },
  async findByFacilityPreferenceIdDischargeSummaryTemplates(
    this: IFacilityPreferenceRepository,
    facilityPreferenceId: string,
  ) {
    return this.manager.find(DischargeSummaryTemplateModel, {
      where: { facilityPreferenceId },
    });
  },

  async saveChemoDiagnosisTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    input: NewChemoDiagnosisTemplate,
  ): Promise<ChemoDiagnosisTemplateModel> {
    const { type, combinationName, cycles, facilityPreferenceId } = input;
    const facilityPreference = await this.findOneOrFail({
      where: { id: facilityPreferenceId },
    }).catch(() => {
      throw new NotFoundException('Facility Preference Not Found');
    });
    const newChemoDiagnosisTemplate = new ChemoDiagnosisTemplateModel({
      type,
      combinationName,
      createdBy: mutator,
      creatorName: mutator.fullName,
      facilityPreference,
      facilityPreferenceId,
      section: input.section,
    });
    const savedRecord = await this.manager.save(
      ChemoDiagnosisTemplateModel,
      newChemoDiagnosisTemplate,
    );
    const cycleRecords = cycles.map((cycle) => {
      const newChemoDiagnosisTemplate = new ChemoDiagnosisCycleModel({
        cycleNumber: cycle.cycleNumber,
        drugs: cycle.drugs,
        createdBy: mutator,
        creatorName: mutator.fullName,
        chemoDiagnosisTemplateId: savedRecord.id,
        investigationDetails: cycle.investigationDetails,
      });
      return newChemoDiagnosisTemplate;
    });
    const newCycles = await this.manager.save(
      ChemoDiagnosisCycleModel,
      cycleRecords,
    );
    savedRecord.cycles = newCycles;
    return savedRecord;
  },
  async updateChemoDiagnosisTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    id: string,
    input: ChemoDiagnosisTemplate,
  ): Promise<ChemoDiagnosisTemplateModel> {
    const { combinationName, type, cycles } = input;
    const chemoDiagnosisTemplate = await this.manager
      .findOneOrFail(ChemoDiagnosisTemplateModel, {
        where: { id },
        relations: ['cycles'],
      })
      .catch(() => {
        throw new NotFoundException('Template Not Found');
      });
    const toDelete = chemoDiagnosisTemplate.cycles.filter(
      (cycle) => !cycles.some((c) => c.id === cycle.id),
    );
    const cycleRecords = cycles.map((cycle) => {
      const existingCycle = chemoDiagnosisTemplate.cycles.find(
        (c) => c.id === cycle.id,
      );
      const newChemoDiagnosisTemplate = new ChemoDiagnosisCycleModel({
        ...existingCycle,
        id: cycle.id,
        cycleNumber: cycle.cycleNumber,
        drugs: cycle.drugs,
        createdBy: mutator,
        creatorName: mutator.fullName,
        chemoDiagnosisTemplateId: id,
        investigationDetails: cycle.investigationDetails,
      });
      return newChemoDiagnosisTemplate;
    });
    const updatedCycles = await this.manager.save(
      ChemoDiagnosisCycleModel,
      cycleRecords,
    );
    if (toDelete?.length)
      await this.manager.delete(ChemoDiagnosisCycleModel, toDelete);

    await this.manager.update(ChemoDiagnosisTemplateModel, id, {
      id,
      type,
      combinationName,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });

    return {
      ...chemoDiagnosisTemplate,
      type,
      combinationName,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
      cycles: updatedCycles,
    };
  },

  async deleteChemoDiagnosisTemplate(
    this: IFacilityPreferenceRepository,
    id: string,
  ): Promise<ChemoDiagnosisTemplateModel> {
    const chemoDiagnosisTemplate = await this.manager
      .findOneOrFail(ChemoDiagnosisTemplateModel, {
        where: { id },
        relations: ['cycles'],
      })
      .catch(() => {
        throw new NotFoundException('Template Not Found');
      });

    await this.manager.delete(ChemoDiagnosisTemplateModel, {
      id,
    });
    return chemoDiagnosisTemplate;
  },

  async findByHospitalIdChemoDiagnosisTemplates(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    section: string,
    chemoDiagnosis?: string,
  ) {
    const chemoDiagnosisTemplates = await this.manager.find(
      ChemoDiagnosisTemplateModel,
      {
        where: {
          facilityPreference: {
            hospitalId,
          },
          ...(section ? { section } : {}),
          ...(chemoDiagnosis ? { type: chemoDiagnosis } : {}),
        },
        relations: ['cycles'],
      },
    );
    return chemoDiagnosisTemplates;
  },

  async findByFacilityPreferenceIdChemoDiagnosisTemplates(
    this: IFacilityPreferenceRepository,
    id: string,
    section,
    chemoDiagnosis?: string,
  ) {
    const chemoDiagnosisTemplates = await this.manager.find(
      ChemoDiagnosisTemplateModel,
      {
        where: {
          facilityPreference: {
            id,
          },
          ...(section ? { section } : {}),
          ...(chemoDiagnosis ? { type: chemoDiagnosis } : {}),
        },
        select: ['id', 'combinationName', 'type', 'section'],
      },
    );
    return chemoDiagnosisTemplates;
  },

  async getOneChemoDiagnosisTemplate(
    this: IFacilityPreferenceRepository,
    id: string,
  ) {
    return this.manager.findOneOrFail(ChemoDiagnosisTemplateModel, {
      where: { id },
      relations: ['cycles'],
    });
  },

  async updateSpecialistAccess(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    patientId: string,
    specialistIds: string[],
  ): Promise<SpecialistAccessModel> {
    const specialistAccess = await this.manager.findOne(SpecialistAccessModel, {
      where: { profileId: patientId, hospitalId: mutator.hospitalId },
      relations: ['profile'],
    });

    if (specialistAccess) {
      if (specialistIds.length === 0) {
        await this.manager.remove(SpecialistAccessModel, {
          ...specialistAccess,
          deletedBy: {
            id: mutator.id,
            fullName: mutator.fullName,
            entityId: specialistAccess.id,
          },
        } as any);
        specialistAccess.specialistIds = [];
        return specialistAccess;
      }
      specialistAccess.specialistIds = specialistIds;
      specialistAccess.updatedBy = mutator;
      specialistAccess.lastModifierName = mutator.fullName;

      await this.manager.save(SpecialistAccessModel, specialistAccess);
      return specialistAccess;
    }
    const newSpecialistAccess = new SpecialistAccessModel({
      profileId: patientId,
      specialistIds,
      hospitalId: mutator.hospitalId,
      createdBy: mutator,
      creatorName: mutator.fullName,
    });
    return this.manager.save(SpecialistAccessModel, newSpecialistAccess);
  },
  async getSpecialistAccess(
    this: IFacilityPreferenceRepository,
    profileId: string,
    hospitalId: string,
  ): Promise<SpecialistAccessModel> {
    return this.manager.findOne(SpecialistAccessModel, {
      where: { profileId, hospitalId },
      relations: ['profile'],
    });
  },
  async findByHospitalIdSpecialistAccess(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
  ): Promise<SpecialistAccessModel[]> {
    return this.manager.find(SpecialistAccessModel, {
      where: { hospitalId },
      relations: ['profile'],
    });
  },
  async saveOperationNoteTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    input: NewOperationNoteTemplateInput,
  ): Promise<OperationNoteTemplateModel> {
    const { name, note, facilityPreferenceId, postNote } = input;
    const facilityPreference = await this.findOne({
      where: { id: facilityPreferenceId },
    });
    const newOperationNoteTemplate = new OperationNoteTemplateModel({
      name,
      note,
      postNote,
      createdBy: mutator,
      creatorName: mutator.fullName,
      facilityPreference,
      facilityPreferenceId,
    });
    return this.manager.save(
      OperationNoteTemplateModel,
      newOperationNoteTemplate,
    );
  },
  async updateOperationNoteTemplate(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    input: OperationNoteTemplateInput,
  ): Promise<OperationNoteTemplateModel> {
    const { id, name, note, postNote } = input;
    const operationNoteTemplate = await this.manager.findOne(
      OperationNoteTemplateModel,
      {
        where: { id },
      },
    );
    operationNoteTemplate.name = name;
    operationNoteTemplate.note = note;
    operationNoteTemplate.postNote = postNote;
    operationNoteTemplate.updatedBy = mutator;
    operationNoteTemplate.lastModifierName = mutator.fullName;
    return this.manager.save(OperationNoteTemplateModel, operationNoteTemplate);
  },

  async deleteOperationNoteTemplate(
    this: IFacilityPreferenceRepository,
    id: string,
  ): Promise<OperationNoteTemplateModel> {
    const operationNoteTemplate = await this.manager.findOne(
      OperationNoteTemplateModel,
      {
        where: { id },
      },
    );
    if (!operationNoteTemplate) {
      throw new Error('Template not Found');
    }
    this.manager.delete(OperationNoteTemplateModel, {
      id,
    });
    return operationNoteTemplate;
  },

  async findByHospitalIdOperationNoteTemplates(
    this: IFacilityPreferenceRepository,
    id: string,
  ): Promise<OperationNoteTemplateModel[]> {
    const facilityPreference = await this.findOne({
      where: { hospitalId: id },
      relations: ['operationNoteTemplates'],
    });
    return facilityPreference.operationNoteTemplates;
  },

  async findByFacilityPreferenceIdOperationNoteTemplates(
    this: IFacilityPreferenceRepository,
    id: string,
  ): Promise<OperationNoteTemplateModel[]> {
    return this.manager.find(OperationNoteTemplateModel, {
      where: { facilityPreferenceId: id },
    });
  },

  async updateHmoSingleVisitPACode(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    facilityPreference.hmoSingleVisitPACode = mode;
    return this.manager.save(FacilityPreferenceModel, facilityPreference);
  },

  async updateCustomPaFormatType(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    facilityPreference.customPaFormatType = mode;
    return this.manager.save(FacilityPreferenceModel, facilityPreference);
  },

  async updateEnableBusinessRulePreventSubmit(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    facilityPreference.enableBusinessRulePreventSubmit = mode;
    return this.manager.save(FacilityPreferenceModel, facilityPreference);
  },

  async updatePatientRegistrationFee(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    registrationFee: number,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    facilityPreference.registrationFee = registrationFee;
    return this.manager.save(FacilityPreferenceModel, facilityPreference);
  },

  async updateEnrolleeCapitationAmount(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    enrolleeCapitationAmount: number,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    facilityPreference.enrolleeCapitationAmount = enrolleeCapitationAmount;
    return this.manager.save(FacilityPreferenceModel, facilityPreference);
  },

  async updateAutoProcessClaims(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    facilityPreference.autoProcessClaims = mode;
    return this.manager.save(FacilityPreferenceModel, facilityPreference);
  },
  async updateAutoProcessPreauthorizations(
    this: IFacilityPreferenceRepository,
    hospitalId: string,
    mode: boolean,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({ where: { hospitalId } });
    facilityPreference.autoProcessPreauthorizations = mode;
    return this.manager.save(FacilityPreferenceModel, facilityPreference);
  },
  async updateEnrollmentAgency(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    administrationAgency: string,
    agencies: string[],
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({
      where: { hospitalId: mutator.hospitalId },
    });
    if (!facilityPreference) {
      throw new NotFoundException('Facility Preference Not Found');
    }
    const enrollmentAgency = new EnrollmentAgencyDto();
    enrollmentAgency.administrationAgency = administrationAgency;
    enrollmentAgency.agencies = agencies;
    let existing = facilityPreference.enrollmentAgency || [];
    existing = existing.filter(
      (e) => e.administrationAgency !== administrationAgency,
    );
    existing.push(enrollmentAgency);

    return this.save({
      ...facilityPreference,
      enrollmentAgency: existing,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  },
  async updateEnrollmentAgentAssignments(
    this: IFacilityPreferenceRepository,
    mutator: ProfileModel,
    agent: EnrollmentAgentDto,
  ): Promise<FacilityPreferenceModel> {
    const facilityPreference = await this.findOne({
      where: { hospitalId: mutator.hospitalId },
    });
    if (!facilityPreference) {
      throw new NotFoundException('Facility Preference Not Found');
    }

    let existing = facilityPreference.enrollmentAgentAssigments || [];
    existing = existing.filter((e) => e.profileId !== agent.profileId);
    existing.push(agent);

    return this.save({
      ...facilityPreference,
      enrollmentAgentAssigments: existing,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  },
} as IFacilityPreferenceRepository;
