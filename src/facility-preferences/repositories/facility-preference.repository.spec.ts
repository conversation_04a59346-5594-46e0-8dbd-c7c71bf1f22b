/* eslint-disable max-len */
/* eslint-disable max-lines */
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendDSRepo, extendModel } from '@clinify/database/extendModel';
import { DefaultPatientAccessType } from '@clinify/facility-preferences/enums/patient-lookup';
import { NewChemoDiagnosisTemplate } from '@clinify/facility-preferences/interface/chemo-diagnosis.interface';
import { FacilityPreferenceModel } from '@clinify/facility-preferences/models/facility-preference.model';
import { MedicalReportTemplateModel } from '@clinify/facility-preferences/models/medical-report-template.model';
import {
  CustomFacilityPreferenceRepoMethods,
  IFacilityPreferenceRepository,
} from '@clinify/facility-preferences/repositories/facility-preference.repository';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { UserType } from '@clinify/shared/enums/users';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { CustomProfileRepoMethods } from '@clinify/users/repositories/profile.repository';
import { createFacilityPreference } from '@fixtures/facility-preference.fixture';
import { createHospitals } from '@fixtures/hospital.fixtures';
import { createUsers } from '@fixtures/user.fixtures';
const richtext =
  '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"1","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}';
const MockonsultationTemplateInput = {
  complaints: richtext,
  historyComplaints: richtext,
  healthEducation: richtext,
  reviewSystems: richtext,
  physicalExamination: richtext,
  audiometry: richtext,
  treatmentPlan: richtext,
  name: 'Template 2',
};

const inputData: NewChemoDiagnosisTemplate = {
  facilityPreferenceId: '',
  type: 'PreChemotherapy',
  combinationName: 'Test',
  section: 'chemo',
  cycles: [
    {
      cycleNumber: 1,
      drugs: [
        {
          day: '1',
          drugName: 'Test',
          drugId: '1',
          dosage: '50ml',
          dosagePercentage: 'full',
          route: 'Infusion',
          infusionUsed: 'No',
          ref: '',
          note: 'some note',
        },
      ],
    },
  ],
};
describe('FacilityPreferenceRepository', () => {
  let hospital: HospitalModel;
  let facilityPreference: FacilityPreferenceModel;
  let profile: ProfileModel;
  let ttm: any;
  let manager: EntityManager;
  let repo: IFacilityPreferenceRepository;
  let dataSource: DataSource;

  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeOrmModule.forFeature([ProfileModel, ProfileModel]),
      ],
      providers: [
        extendModel(
          FacilityPreferenceModel,
          CustomFacilityPreferenceRepoMethods,
        ),
        extendModel(ProfileModel, CustomProfileRepoMethods),
      ],
    }).compile();

    dataSource = module.get<DataSource>(DataSource);
    manager = dataSource.manager;
    repo = extendDSRepo<IFacilityPreferenceRepository>(
      dataSource,
      FacilityPreferenceModel,
      CustomFacilityPreferenceRepoMethods,
    );
  });

  beforeEach(async () => {
    [hospital] = await createHospitals(manager, 1);
    facilityPreference = await createFacilityPreference(manager, hospital);
    [{ defaultProfile: profile }] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.Admin,
    );
  });

  afterAll(async () => {
    await ttm.afterAll();
    await dataSource.destroy();
    await module.close();
  });

  it('repo is defined', () => {
    expect(repo).toBeDefined();
    expect(repo).toBeTruthy();
  });

  it('getFacilityPreference() should get facility preference', async () => {
    const res = await repo.getFacilityPreference(hospital.id);
    expect(res).toEqual(expect.objectContaining({ id: facilityPreference.id }));
  });

  it('updateWelcomeMailTemplate should update facility preference', async () => {
    const preference = await repo.getFacilityPreference(hospital.id);
    const updatedPreference = {
      ...preference,
      welcomeMailTemplate: { subject: 'Updated Subject', body: 'Updated Body' },
    };
    const res = await repo.updateWelcomeMailTemplate(
      profile,
      hospital.id,
      updatedPreference.welcomeMailTemplate,
    );

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        welcomeMailTemplate: {
          subject: 'Updated Subject',
          body: 'Updated Body',
        },
      }),
    );
  });

  it('updateFormsMandatoryFields() should update mandatory fields', async () => {
    const res = await repo.updateFormsMandatoryFields(profile, hospital.id, {});
    expect(res).toEqual(expect.objectContaining({ id: facilityPreference.id }));
  });

  it('updatePatientAccessType should update facility preference', async () => {
    const res = await repo.updatePatientAccessType(
      hospital.id,
      DefaultPatientAccessType.RegisteredPatients,
    );

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        patientAccessType: DefaultPatientAccessType.RegisteredPatients,
      }),
    );
  });
  it('saveFindingsTemplate() should save findings template', async () => {
    const input = {
      name: 'test',
      findings: 'My findings',
      impression: 'My impression',
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveFindingsTemplate(profile, input);
    expect(res).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        name: 'test',
        findings: 'My findings',
        impression: 'My impression',
        facilityPreferenceId: facilityPreference.id,
      }),
    );
  });
  it('updateFindingsTemplate() should update findings template', async () => {
    const input = {
      name: 'test',
      findings: 'My findings 1',
      impression: 'My impression',
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveFindingsTemplate(profile, input);
    const updatedInput = {
      id: res.id,
      name: 'test',
      findings: 'My findings 2',
      impression: 'My impression',
      facilityPreferenceId: facilityPreference.id,
    };
    const updatedRes = await repo.updateFindingsTemplate(profile, updatedInput);
    expect(updatedRes).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        name: 'test',
        findings: 'My findings 2',
        impression: 'My impression',
        facilityPreferenceId: facilityPreference.id,
      }),
    );
  });
  it('deleteFindingsTemplate() should delete findings template', async () => {
    const input = {
      name: 'test',
      findings: 'My findings 1',
      impression: 'My impression',
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveFindingsTemplate(profile, input);
    const deletedRes = await repo.deleteFindingsTemplate(res.id);
    expect(deletedRes).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        name: 'test',
        findings: 'My findings 1',
        impression: 'My impression',
        facilityPreferenceId: facilityPreference.id,
      }),
    );
  });

  it('deleteFindingsTemplate() should throw error if FindingsTemplate is not found', async () => {
    await expect(repo.deleteFindingsTemplate('invalid-id')).rejects.toThrow();
  });
  it('findByFacilityPreferenceIdForFindingsTemplate() should find findings template', async () => {
    const input = {
      name: 'test',
      findings: 'My findings 1',
      impression: 'My impression',
      facilityPreferenceId: facilityPreference.id,
    };
    await repo.saveFindingsTemplate(profile, input);
    const res = await repo.findByFacilityPreferenceIdForFindingsTemplate(
      facilityPreference.id,
    );
    expect(res).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(String),
          name: 'test',
          findings: 'My findings 1',
          impression: 'My impression',
          facilityPreferenceId: facilityPreference.id,
        }),
      ]),
    );
  });
  it('findByHospitalIdForFindingsTemplate() should find findings template', async () => {
    const input = {
      name: 'test',
      findings: 'My findings 1',
      impression: 'My impression',
      facilityPreferenceId: facilityPreference.id,
    };
    await repo.saveFindingsTemplate(profile, input);
    const res = await repo.findByHospitalIdForFindingsTemplate(hospital.id);
    expect(res).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(String),
          name: 'test',
          findings: 'My findings 1',
          impression: 'My impression',
          facilityPreferenceId: facilityPreference.id,
        }),
      ]),
    );
  });

  it('saveLabCommentsTemplate() should save lab comments template', async () => {
    const input = {
      name: 'test',
      comment: 'My comment',
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveLabCommentsTemplate(profile, input);
    expect(res).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        name: 'test',
        comment: 'My comment',
        facilityPreferenceId: facilityPreference.id,
      }),
    );
  });
  it('updateLabCommentsTemplate() should update lab comments template', async () => {
    const input = {
      name: 'test',
      comment: 'My comment 1',
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveLabCommentsTemplate(profile, input);
    const updatedInput = {
      id: res.id,
      name: 'test',
      comment: 'My comment 2',
      facilityPreferenceId: facilityPreference.id,
    };
    const updatedRes = await repo.updateLabCommentsTemplate(
      profile,
      updatedInput,
    );
    expect(updatedRes).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        name: 'test',
        comment: 'My comment 2',
        facilityPreferenceId: facilityPreference.id,
      }),
    );
  });

  it('deleteLabCommentsTemplate() should delete lab comments template', async () => {
    const input = {
      name: 'test',
      comment: 'My comment 1',
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveLabCommentsTemplate(profile, input);
    const deletedRes = await repo.deleteLabCommentsTemplate(res.id);
    expect(deletedRes).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        name: 'test',
        comment: 'My comment 1',
        facilityPreferenceId: facilityPreference.id,
      }),
    );
  });

  it('deleteLabCommentsTemplate() should throw error if LabCommentsTemplate is not found', async () => {
    await expect(
      repo.deleteLabCommentsTemplate('invalid-id'),
    ).rejects.toThrow();
  });

  it('findByFacilityPreferenceIdForLabCommentsTemplate() should find lab comments template', async () => {
    const input = {
      name: 'test',
      comment: 'My comment 1',
      facilityPreferenceId: facilityPreference.id,
    };
    await repo.saveLabCommentsTemplate(profile, input);
    const res = await repo.findByFacilityPreferenceIdForLabCommentsTemplate(
      facilityPreference.id,
    );
    expect(res).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(String),
          name: 'test',
          comment: 'My comment 1',
          facilityPreferenceId: facilityPreference.id,
        }),
      ]),
    );
  });

  it('findByFacilityPreferenceIdForLabCommentsTemplate() should throw error if LabCommentsTemplate is not found', async () => {
    await expect(
      repo.findByFacilityPreferenceIdForLabCommentsTemplate('invalid-id'),
    ).rejects.toThrow();
  });

  it('findByFacilityPreferenceIdForMedicalReportTemplate', async () => {
    const input = {
      name: 'Demo',
      template: 'Demo Template',
      facilityPreferenceId: facilityPreference.id,
    };
    await manager.save(MedicalReportTemplateModel, {
      ...input,
      createdBy: profile,
      facilityPreferenceId: facilityPreference.id,
      facilityPreference,
      creatorName: profile.fullName,
    });
    const res = await repo.findByFacilityPreferenceIdForMedicalReportTemplate(
      facilityPreference.id,
    );
    expect(res).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          name: 'Demo',
          template: 'Demo Template',
          facilityPreferenceId: facilityPreference.id,
        }),
      ]),
    );
  });

  it('findByHospitalIdForMedicalReportTemplate', async () => {
    const input = {
      name: 'Demo 2',
      template: 'Demo Template 2',
      facilityPreferenceId: facilityPreference.id,
    };
    await manager.save(MedicalReportTemplateModel, {
      ...input,
      createdBy: profile,
      facilityPreferenceId: facilityPreference.id,
      creatorName: profile.fullName,
    });
    const res = await repo.findByHospitalIdForMedicalReportTemplate(
      facilityPreference.hospitalId,
    );
    expect(res).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          name: 'Demo 2',
          template: 'Demo Template 2',
          facilityPreferenceId: facilityPreference.id,
        }),
      ]),
    );
  });

  it('updateRadiologyContrastMode should update facility preference', async () => {
    const res = await repo.updateRadiologyContrastMode(hospital.id, true);

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        radiologyContrastConfirmation: true,
      }),
    );

    const res1 = await repo.updateRadiologyContrastMode(hospital.id, false);

    expect(res1).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        radiologyContrastConfirmation: false,
      }),
    );
  });

  it('updateDashboardColourMode should update facility preference', async () => {
    const res = await repo.updateDashboardColourMode(hospital.id, false);

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        dashboardColourMode: false,
      }),
    );

    const res1 = await repo.updateDashboardColourMode(hospital.id, true);

    expect(res1).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        dashboardColourMode: true,
      }),
    );
  });

  it('updateShowServiceDetails should update facility preference', async () => {
    const res = await repo.updateShowServiceDetails(hospital.id, false);

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        showServiceDetails: false,
      }),
    );

    const res1 = await repo.updateShowServiceDetails(hospital.id, true);

    expect(res1).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        showServiceDetails: true,
      }),
    );
  });

  it('updateRolesServiceDetailsIsHidden should update facility preference', async () => {
    const res = await repo.updateRolesServiceDetailsIsHidden(hospital.id, [
      UserType.Doctor,
      UserType.OrganizationNurse,
    ]);

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        rolesServiceDetailsIsHidden: [
          UserType.Doctor,
          UserType.OrganizationNurse,
        ],
      }),
    );

    const res1 = await repo.updateRolesServiceDetailsIsHidden(hospital.id, []);

    expect(res1).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        rolesServiceDetailsIsHidden: [],
      }),
    );
  });

  it('saveConsultationsTemplate() should save consultations template', async () => {
    const input = {
      ...MockonsultationTemplateInput,
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveConsultationsTemplate(profile, input);
    expect(res).toEqual(
      expect.objectContaining({
        name: 'Template 2',
      }),
    );
  });

  it('updateConsultationsTemplate() should update consultations template', async () => {
    const input = {
      ...MockonsultationTemplateInput,
      facilityPreferenceId: facilityPreference.id,
      name: 'Template 1',
    };

    const record = await repo.saveConsultationsTemplate(profile, input);

    const res = await repo.updateConsultationsTemplate(profile, {
      ...input,
      id: record.id,
      name: 'Template 2',
    });
    expect(res).toEqual(
      expect.objectContaining({
        name: 'Template 2',
      }),
    );
  });

  it('updateConsultationsTemplate() should throw error if ConsultationsTemplate is not found', async () => {
    const input = {
      ...MockonsultationTemplateInput,
      facilityPreferenceId: facilityPreference.id,
      name: 'Template 1',
    };

    await repo.saveConsultationsTemplate(profile, input);

    await expect(
      repo.updateConsultationsTemplate(profile, {
        ...input,
        id: 'fake-id',
        name: 'Template 2',
      }),
    ).rejects.toThrow();
  });

  it('deleteConsultationsTemplate() should delete consultations template', async () => {
    const input = {
      ...MockonsultationTemplateInput,
      facilityPreferenceId: facilityPreference.id,
    };
    const record = await repo.saveConsultationsTemplate(profile, input);
    const res = await repo.deleteConsultationsTemplate(profile, record.id);
    expect(res).toEqual(
      expect.objectContaining({
        name: 'Template 2',
      }),
    );
  });

  it('deleteConsultationsTemplate() should throw error if ConsultationsTemplate is not found', async () => {
    await expect(
      repo.deleteConsultationsTemplate(profile, 'fake-id'),
    ).rejects.toThrow();
  });

  it('findByHospitalIdConsultationsTemplate() should find consultations template', async () => {
    const input = {
      ...MockonsultationTemplateInput,
      facilityPreferenceId: facilityPreference.id,
    };
    await repo.saveConsultationsTemplate(profile, input);
    const res = await repo.findByHospitalIdConsultationsTemplate(
      profile,
      facilityPreference.hospitalId,
    );
    expect(res[0].facilityPreferenceId).toEqual(facilityPreference.id);
  });

  it('findByFacilityPreferenceIdConsultationsTemplate() should find consultations template', async () => {
    const input = {
      ...MockonsultationTemplateInput,
      facilityPreferenceId: facilityPreference.id,
    };
    await repo.saveConsultationsTemplate(profile, input);
    const res = await repo.findByFacilityPreferenceIdConsultationsTemplate(
      profile,
      facilityPreference.id,
    );
    expect(res[0].facilityPreferenceId).toEqual(facilityPreference.id);
  });

  it('saveDischargeSummaryTemplate() should save discharge summary template', async () => {
    const input = {
      name: 'test',
      summary: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveDischargeSummaryTemplate(profile, input);
    expect(res).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        name: 'test',
        summary: richtext,
        facilityPreferenceId: facilityPreference.id,
      }),
    );
  });

  it('updateDischargeSummaryTemplate() should update discharge summary template', async () => {
    const input = {
      name: 'test',
      summary: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveDischargeSummaryTemplate(profile, input);
    const updatedInput = {
      id: res.id,
      name: 'test 2',
      summary: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    const updatedRes = await repo.updateDischargeSummaryTemplate(
      profile,
      updatedInput,
    );
    expect(updatedRes).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        name: 'test 2',
        summary: richtext,
        facilityPreferenceId: facilityPreference.id,
      }),
    );
  });

  it('updateDischargeSummaryTemplate() should throw error if DischargeSummaryTemplate is not found', async () => {
    const updatedInput = {
      id: 'fake-id',
      name: 'test 2',
      summary: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    await expect(
      repo.updateDischargeSummaryTemplate(profile, updatedInput),
    ).rejects.toThrow();
  });

  it('deleteDischargeSummaryTemplate() should delete discharge summary template', async () => {
    const input = {
      name: 'test',
      summary: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveDischargeSummaryTemplate(profile, input);
    const deletedRes = await repo.deleteDischargeSummaryTemplate(res.id);
    expect(deletedRes).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        name: 'test',
        summary: richtext,
        facilityPreferenceId: facilityPreference.id,
      }),
    );
  });

  it('deleteDischargeSummaryTemplate() should throw error if DischargeSummaryTemplate is not found', async () => {
    await expect(
      repo.deleteDischargeSummaryTemplate('invalid-id'),
    ).rejects.toThrow();
  });

  it('findByFacilityPreferenceIdDischargeSummaryTemplates() should find discharge summary template', async () => {
    const input = {
      name: 'test',
      summary: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    await repo.saveDischargeSummaryTemplate(profile, input);
    const res = await repo.findByFacilityPreferenceIdDischargeSummaryTemplates(
      facilityPreference.id,
    );
    expect(res).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(String),
          name: 'test',
          summary: richtext,
          facilityPreferenceId: facilityPreference.id,
        }),
      ]),
    );
  });

  it('findByHospitalIdDischargeSummaryTemplates() should find discharge summary template', async () => {
    const input = {
      name: 'test',
      summary: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    await repo.saveDischargeSummaryTemplate(profile, input);
    const res = await repo.findByHospitalIdDischargeSummaryTemplates(
      facilityPreference.hospitalId,
    );
    expect(res).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(String),
          name: 'test',
          summary: richtext,
          facilityPreferenceId: facilityPreference.id,
        }),
      ]),
    );
  });

  it('saveChemoDiagnosisTemplate() should save chemo diagnosis template', async () => {
    inputData.facilityPreferenceId = facilityPreference.id;
    const res = await repo.saveChemoDiagnosisTemplate(profile, inputData);
    expect(res).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        type: 'PreChemotherapy',
        combinationName: 'Test',
      }),
    );
  });

  it('saveChemoDiagnosisTemplate() should throw error if facilityPreference is not found', async () => {
    inputData.facilityPreferenceId = 'invalid-id';
    await expect(
      repo.saveChemoDiagnosisTemplate(profile, inputData),
    ).rejects.toThrow();
  });

  it('updateChemoDiagnosisTemplate() should update chemo diagnosis template', async () => {
    inputData.facilityPreferenceId = facilityPreference.id;
    const record = await repo.saveChemoDiagnosisTemplate(profile, inputData);
    const updatedInput = {
      facilityPreferenceId: facilityPreference.id,
      type: 'PreChemotherapy',
      combinationName: 'Test 2',
      cycles: [
        {
          cycleNumber: 1,
          drugs: [
            {
              day: '1',
              drugName: 'Paracetamol',
              drugId: '1',
              dosage: '50ml',
              dosagePercentage: 'full',
              route: 'Infusion',
              infusionUsed: 'No',
            },
          ],
        },
      ],
    };
    const updatedRes = await repo.updateChemoDiagnosisTemplate(
      profile,
      record.id,
      updatedInput as any,
    );
    expect(updatedRes.combinationName).toEqual('Test 2');
    expect(updatedRes.cycles[0].drugs[0].drugName).toEqual('Paracetamol');
  });

  it('updateChemoDiagnosisTemplate() should throw error if ChemoDiagnosisTemplate is not found', async () => {
    const updatedInput = {
      facilityPreferenceId: facilityPreference.id,
      type: 'PreChemotherapy',
      combinationName: 'Test 2',
      cycles: [
        {
          cycleNumber: 1,
          drugs: [
            {
              day: '1',
              drugName: 'Paracetamol',
              drugId: '1',
              dosage: '50ml',
              dosagePercentage: 'full',
              route: 'Infusion',
              infusionUsed: 'No',
            },
          ],
        },
      ],
    };
    await expect(
      repo.updateChemoDiagnosisTemplate(
        profile,
        'fake-id',
        updatedInput as any,
      ),
    ).rejects.toThrow();
  });

  it('deleteChemoDiagnosisTemplate() should delete chemo diagnosis template', async () => {
    inputData.facilityPreferenceId = facilityPreference.id;
    const record = await repo.saveChemoDiagnosisTemplate(profile, inputData);
    const res = await repo.deleteChemoDiagnosisTemplate(record.id);
    expect(res).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        type: 'PreChemotherapy',
        combinationName: 'Test',
      }),
    );
  });

  it('deleteChemoDiagnosisTemplate() should throw error if ChemoDiagnosisTemplate is not found', async () => {
    await expect(
      repo.deleteChemoDiagnosisTemplate('invalid-id'),
    ).rejects.toThrow();
  });

  it('findByFacilityPreferenceIdChemoDiagnosisTemplates() should find chemo diagnosis template', async () => {
    inputData.facilityPreferenceId = facilityPreference.id;
    await repo.saveChemoDiagnosisTemplate(profile, inputData);
    const res = await repo.findByFacilityPreferenceIdChemoDiagnosisTemplates(
      facilityPreference.id,
      null,
    );
    expect(res.length).toBeGreaterThan(0);
  });

  it('findByFacilityPreferenceIdChemoDiagnosisTemplates() should find chemo diagnosis template with section provided', async () => {
    inputData.facilityPreferenceId = facilityPreference.id;
    await repo.saveChemoDiagnosisTemplate(profile, inputData);
    const res = await repo.findByFacilityPreferenceIdChemoDiagnosisTemplates(
      facilityPreference.id,
      'chemo',
    );
    expect(res.length).toBeGreaterThan(0);
    expect(res[0].section).toBe('chemo');
  });

  it('findByFacilityPreferenceIdChemoDiagnosisTemplates() should find chemo diagnosis template with chemo diagnosis provided', async () => {
    inputData.facilityPreferenceId = facilityPreference.id;
    await repo.saveChemoDiagnosisTemplate(profile, inputData);
    const res = await repo.findByFacilityPreferenceIdChemoDiagnosisTemplates(
      facilityPreference.id,
      'chemo',
      'PreChemotherapy',
    );
    expect(res.length).toBeGreaterThan(0);
    expect(res[0].section).toBe('chemo');
  });

  it('findByHospitalIdChemoDiagnosisTemplates() should find chemo diagnosis template', async () => {
    inputData.facilityPreferenceId = facilityPreference.id;
    await repo.saveChemoDiagnosisTemplate(profile, inputData);
    const res = await repo.findByHospitalIdChemoDiagnosisTemplates(
      facilityPreference.hospitalId,
      null,
    );
    expect(res.length).toBeGreaterThan(0);
  });

  it('findByHospitalIdChemoDiagnosisTemplates() should find chemo diagnosis template with section provided', async () => {
    inputData.facilityPreferenceId = facilityPreference.id;
    await repo.saveChemoDiagnosisTemplate(profile, inputData);
    const res = await repo.findByHospitalIdChemoDiagnosisTemplates(
      facilityPreference.hospitalId,
      'chemo',
    );
    expect(res.length).toBeGreaterThan(0);
    expect(res[0].section).toBe('chemo');
  });

  it('findByHospitalIdChemoDiagnosisTemplates() should find chemo diagnosis template with chemo diagnosis provided', async () => {
    inputData.facilityPreferenceId = facilityPreference.id;
    await repo.saveChemoDiagnosisTemplate(profile, inputData);
    const res = await repo.findByHospitalIdChemoDiagnosisTemplates(
      facilityPreference.hospitalId,
      'chemo',
      'PreChemotherapy',
    );
    expect(res.length).toBeGreaterThan(0);
    expect(res[0].section).toBe('chemo');
  });

  it('getOneChemoDiagnosisTemplate() should find single chemo diagnosis template', async () => {
    inputData.facilityPreferenceId = facilityPreference.id;
    const res = await repo.saveChemoDiagnosisTemplate(profile, inputData);
    const result = await repo.getOneChemoDiagnosisTemplate(res.id);
    expect(result.id).toBe(res.id);
    expect(result.facilityPreferenceId).toBe(facilityPreference.id);
  });

  it('updateSpecialistAccess() should update specialist access', async () => {
    profile.hospitalId = hospital.id;
    const staffsId = [profile.id];
    const res = await repo.updateSpecialistAccess(
      profile,
      profile.id,
      staffsId,
    );

    expect(res.specialistIds).toEqual([profile.id]);
    const res1 = await repo.updateSpecialistAccess(profile, profile.id, [
      hospital.id,
    ]);

    expect(res1.specialistIds).toEqual([hospital.id]);

    const res2 = await repo.updateSpecialistAccess(profile, profile.id, []);

    expect(res2.specialistIds).toEqual([]);
  });

  it('getSpecialistAccess() should get specialist access', async () => {
    profile.hospitalId = hospital.id;
    const staffsId = [profile.id];
    await repo.updateSpecialistAccess(profile, profile.id, staffsId);
    const res = await repo.getSpecialistAccess(profile.id, profile.hospitalId);
    expect(res.specialistIds).toEqual([profile.id]);
  });

  it('findByHospitalIdSpecialistAccess() should find specialist access', async () => {
    profile.hospitalId = hospital.id;
    const staffsId = [profile.id];
    await repo.updateSpecialistAccess(profile, profile.id, staffsId);
    const res = await repo.findByHospitalIdSpecialistAccess(hospital.id);
    expect(res.length).toBeGreaterThan(0);
  });

  it('updatePatientSurveyLinks() should update patient survey links', async () => {
    const res = await repo.updatePatientSurveyLinks(
      profile,
      facilityPreference.id,
      'https://www.google.com',
      'https://www.google.com',
    );

    expect(res).toEqual(
      expect.objectContaining({
        outPatientLink: 'https://www.google.com',
        inPatientLink: 'https://www.google.com',
      }),
    );
  });

  it('saveOperationNoteTemplate() should save operation note template', async () => {
    const input = {
      name: 'test',
      note: richtext,
      postNote: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveOperationNoteTemplate(profile, input);
    expect(res).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        name: 'test',
        note: richtext,
        postNote: richtext,
        facilityPreferenceId: facilityPreference.id,
      }),
    );
  });

  it('updateOperationNoteTemplate() should update operation note template', async () => {
    const input = {
      name: 'test',
      note: richtext,
      postNote: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveOperationNoteTemplate(profile, input);
    const updatedInput = {
      id: res.id,
      name: 'test 2',
      note: richtext,
      postNote: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    const updatedRes = await repo.updateOperationNoteTemplate(
      profile,
      updatedInput,
    );
    expect(updatedRes).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        name: 'test 2',
        note: richtext,
        postNote: richtext,
        facilityPreferenceId: facilityPreference.id,
      }),
    );
  });

  it('updateOperationNoteTemplate() should throw error if OperationNoteTemplate is not found', async () => {
    const updatedInput = {
      id: 'fake-id',
      name: 'test 2',
      note: richtext,
      postNote: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    await expect(
      repo.updateOperationNoteTemplate(profile, updatedInput),
    ).rejects.toThrow();
  });

  it('deleteOperationNoteTemplate() should delete operation note template', async () => {
    const input = {
      name: 'test',
      note: richtext,
      postNote: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    const res = await repo.saveOperationNoteTemplate(profile, input);
    const deletedRes = await repo.deleteOperationNoteTemplate(res.id);
    expect(deletedRes).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        name: 'test',
        note: richtext,
        postNote: richtext,
        facilityPreferenceId: facilityPreference.id,
      }),
    );
  });

  it('deleteOperationNoteTemplate() should throw error if OperationNoteTemplate is not found', async () => {
    await expect(
      repo.deleteOperationNoteTemplate('invalid-id'),
    ).rejects.toThrow();
  });

  it('findByFacilityPreferenceIdOperationNoteTemplates() should find operation note template', async () => {
    const input = {
      name: 'test',
      note: richtext,
      postNote: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    await repo.saveOperationNoteTemplate(profile, input);
    const res = await repo.findByFacilityPreferenceIdOperationNoteTemplates(
      facilityPreference.id,
    );
    expect(res).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(String),
          name: 'test',
          note: richtext,
          postNote: richtext,
          facilityPreferenceId: facilityPreference.id,
        }),
      ]),
    );
  });

  it('findByHospitalIdOperationNoteTemplates() should find operation note template', async () => {
    const input = {
      name: 'test',
      note: richtext,
      postNote: richtext,
      facilityPreferenceId: facilityPreference.id,
    };
    await repo.saveOperationNoteTemplate(profile, input);
    const res = await repo.findByHospitalIdOperationNoteTemplates(
      facilityPreference.hospitalId,
    );

    expect(res).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(String),
          name: 'test',
          note: richtext,
          postNote: richtext,
          facilityPreferenceId: facilityPreference.id,
        }),
      ]),
    );
  });

  it('updateInventoryClass() should update inventory class', async () => {
    const res = await repo.updateInventoryClass(profile, 'test');
    expect(res).toEqual(
      expect.objectContaining({
        inventoryClass: 'test',
      }),
    );
  });

  it('updatePatientRegistrationFee should update facility preference', async () => {
    const res = await repo.updatePatientRegistrationFee(hospital.id, 1000);

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        registrationFee: 1000,
      }),
    );

    const res1 = await repo.updatePatientRegistrationFee(hospital.id, 2000);

    expect(res1).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        registrationFee: 2000,
      }),
    );
  });

  it('updateEnrolleeCapitationAmount should update enrollee capitation amount', async () => {
    const res = await repo.updateEnrolleeCapitationAmount(hospital.id, 1000);

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        enrolleeCapitationAmount: 1000,
      }),
    );

    const res1 = await repo.updateEnrolleeCapitationAmount(hospital.id, 2000);

    expect(res1).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        enrolleeCapitationAmount: 2000,
      }),
    );
  });

  it('updateAutoProcessClaims should update auto process claims', async () => {
    const res = await repo.updateAutoProcessClaims(hospital.id, true);

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        autoProcessClaims: true,
      }),
    );

    const res1 = await repo.updateAutoProcessClaims(hospital.id, false);

    expect(res1).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        autoProcessClaims: false,
      }),
    );
  });

  it('updateEnrollmentAgency should add new enrollment agency', async () => {
    const administrationAgency = 'TestAgent';
    const agencies = ['Agency1', 'Agency2'];

    const res = await repo.updateEnrollmentAgency(
      profile,
      administrationAgency,
      agencies,
    );

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        enrollmentAgency: expect.arrayContaining([
          expect.objectContaining({
            administrationAgency,
            agencies,
          }),
        ]),
        updatedBy: expect.objectContaining({ id: profile.id }),
        lastModifierName: profile.fullName,
      }),
    );
  });

  it('updateEnrollmentAgency should update existing enrollment agency with same agent', async () => {
    const administrationAgency = 'TestAgent';
    const initialAgencies = ['Agency1'];
    const updatedAgencies = ['Agency1', 'Agency2', 'Agency3'];

    // First, add an enrollment agency
    await repo.updateEnrollmentAgency(
      profile,
      administrationAgency,
      initialAgencies,
    );

    // Then update it with the same administration agency
    const res = await repo.updateEnrollmentAgency(
      profile,
      administrationAgency,
      updatedAgencies,
    );

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        enrollmentAgency: expect.arrayContaining([
          expect.objectContaining({
            administrationAgency,
            agencies: updatedAgencies,
          }),
        ]),
      }),
    );

    // Ensure there's only one entry for this administration agency
    expect(
      res.enrollmentAgency.filter(
        (e) => e.administrationAgency === administrationAgency,
      ),
    ).toHaveLength(1);
  });

  it('updateEnrollmentAgency should throw NotFoundException when facility preference not found', async () => {
    // Create a new profile with invalid hospitalId for this test
    const [invalidHospital] = await createHospitals(manager, 1);
    const [{ defaultProfile: invalidProfile }] = await createUsers(
      manager,
      1,
      invalidHospital,
      undefined,
      undefined,
      UserType.Admin,
    );
    // Remove the facility preference for this hospital to simulate not found
    await manager.delete(FacilityPreferenceModel, {
      hospitalId: invalidHospital.id,
    });

    const administrationAgency = 'TestAgent';
    const agencies = ['Agency1'];

    await expect(
      repo.updateEnrollmentAgency(
        invalidProfile,
        administrationAgency,
        agencies,
      ),
    ).rejects.toThrow('Facility Preference Not Found');
  });

  it('updateEnrollmentAgentAssignments should add new enrollment agent assignment', async () => {
    const agent = {
      profileId: 'test-profile-id',
      administrationAgency: 'TestAgent',
      enrollmentAgency: 'TestAgency',
      accountNumber: '*********',
      accountName: 'Test Account',
      bankName: 'Test Bank',
      bvn: '452153',
      branchName: 'Branch Name',
      status: 'Inactive',
      enrollmentTpa: null,
    };

    const res = await repo.updateEnrollmentAgentAssignments(profile, agent);

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        enrollmentAgentAssigments: expect.arrayContaining([
          expect.objectContaining({
            profileId: agent.profileId,
            administrationAgency: agent.administrationAgency,
            enrollmentAgency: agent.enrollmentAgency,
          }),
        ]),
        updatedBy: expect.objectContaining({ id: profile.id }),
        lastModifierName: profile.fullName,
      }),
    );
  });

  it('updateEnrollmentAgentAssignments should update existing assignment with same profileId and agent', async () => {
    const agent = {
      profileId: 'test-profile-id',
      administrationAgency: 'TestAgent',
      enrollmentAgency: 'InitialAgency',
      accountNumber: '*********',
      accountName: 'Test Account',
      bankName: 'Test Bank',
      bvn: '452153',
      branchName: 'Branch Name',
      status: 'Inactive',
      enrollmentTpa: null,
    };

    // First, add an enrollment agent assignment
    await repo.updateEnrollmentAgentAssignments(profile, agent);

    // Then update it with the same profileId and agent
    const updatedAgent = {
      ...agent,
      enrollmentAgency: 'UpdatedAgency',
    };
    const res = await repo.updateEnrollmentAgentAssignments(
      profile,
      updatedAgent,
    );

    expect(res).toEqual(
      expect.objectContaining({
        id: facilityPreference.id,
        enrollmentAgentAssigments: expect.arrayContaining([
          expect.objectContaining({
            profileId: agent.profileId,
            administrationAgency: agent.administrationAgency,
            enrollmentAgency: 'UpdatedAgency',
          }),
        ]),
      }),
    );

    // Ensure there's only one entry for this profileId and administration agency combination
    expect(
      res.enrollmentAgentAssigments.filter(
        (e) =>
          e.profileId === agent.profileId &&
          e.administrationAgency === agent.administrationAgency,
      ),
    ).toHaveLength(1);
  });

  it('updateEnrollmentAgentAssignments should allow multiple assignments for different profileId', async () => {
    const agent1 = {
      profileId: 'test-profile-id-1',
      administrationAgency: 'TestAgent1',
      enrollmentAgency: 'TestAgency',
      accountNumber: '*********',
      accountName: 'Test Account 1',
      bankName: 'Test Bank',
      bvn: '452153',
      branchName: 'Branch Name',
      status: 'Active',
      enrollmentTpa: null,
    };

    const agent2 = {
      profileId: 'test-profile-id-2',
      administrationAgency: 'TestAgent1',
      enrollmentAgency: 'TestAgency',
      accountNumber: '*********',
      accountName: 'Test Account 2',
      bankName: 'Test Bank',
      bvn: '452153',
      branchName: 'Branch Name',
      status: 'Active',
      enrollmentTpa: null,
    };

    const agent1Updated = {
      ...agent1,
      administrationAgency: 'TestAgent2',
    };

    // Add first assignment
    let res = await repo.updateEnrollmentAgentAssignments(profile, agent1);

    expect(res.enrollmentAgentAssigments).toHaveLength(1);
    expect(res.enrollmentAgentAssigments).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          profileId: agent1.profileId,
          administrationAgency: agent1.administrationAgency,
        }),
      ]),
    );

    // Add second assignment with different profileId
    res = await repo.updateEnrollmentAgentAssignments(profile, agent2);

    expect(res.enrollmentAgentAssigments).toHaveLength(2);
    expect(res.enrollmentAgentAssigments).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          profileId: agent1.profileId,
          administrationAgency: agent1.administrationAgency,
        }),
        expect.objectContaining({
          profileId: agent2.profileId,
          administrationAgency: agent2.administrationAgency,
        }),
      ]),
    );

    // Update first assignment with different agent
    res = await repo.updateEnrollmentAgentAssignments(profile, agent1Updated);

    expect(res.enrollmentAgentAssigments).toHaveLength(2);
    expect(res.enrollmentAgentAssigments).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          profileId: agent2.profileId,
          administrationAgency: agent2.administrationAgency,
        }),
        expect.objectContaining({
          profileId: agent1.profileId,
          administrationAgency: agent1Updated.administrationAgency,
        }),
      ]),
    );
  });

  it('updateEnrollmentAgentAssignments should throw NotFoundException when facility preference not found', async () => {
    // Create a new profile with invalid hospitalId for this test
    const [invalidHospital] = await createHospitals(manager, 1);
    const [{ defaultProfile: invalidProfile }] = await createUsers(
      manager,
      1,
      invalidHospital,
      undefined,
      undefined,
      UserType.Admin,
    );
    // Remove the facility preference for this hospital to simulate not found
    await manager.delete(FacilityPreferenceModel, {
      hospitalId: invalidHospital.id,
    });

    const agent = {
      profileId: 'test-profile-id',
      administrationAgency: 'TestAgent',
      enrollmentAgency: 'TestAgency',
      accountNumber: '*********',
      accountName: 'Test Account',
      bankName: 'Test Bank',
      bvn: '452153',
      branchName: 'Branch Name',
      status: 'Active',
      enrollmentTpa: null,
    };

    await expect(
      repo.updateEnrollmentAgentAssignments(invalidProfile, agent),
    ).rejects.toThrow('Facility Preference Not Found');
  });
});
