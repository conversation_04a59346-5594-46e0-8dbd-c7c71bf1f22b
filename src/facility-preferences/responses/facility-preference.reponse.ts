import { Field, ID, ObjectType } from '@nestjs/graphql';
import { MailTemplate } from '@clinify/facility-preferences/validators/mail-template.input';

@ObjectType()
export class FacilityPreferencePublic {
  @Field(() => MailTemplate, { nullable: true })
  welcomeMailTemplate?: MailTemplate;

  @Field(() => ID, { nullable: true })
  id?: string;

  @Field({ nullable: true })
  useHQFacilityTariffs?: boolean;

  @Field({ nullable: true })
  useHQFacilityInventory?: boolean;
}

@ObjectType()
export class CapitionAmountByPlanType {
  @Field(() => ID, { nullable: true })
  planTypeId: string;

  @Field(() => ID, { nullable: true })
  planTypeName: string;

  @Field({ nullable: true })
  amount: number;
}
