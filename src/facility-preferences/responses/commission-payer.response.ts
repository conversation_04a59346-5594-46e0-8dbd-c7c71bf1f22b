import { Field, ObjectType } from '@nestjs/graphql';
import { CommissionPayer } from '@clinify/facility-preferences/enums/commission-payer';

@ObjectType()
export class CommissionPayerResponse {
  @Field(() => String)
  facilityPreferenceId: string;

  @Field(() => String)
  hospitalId: string;

  @Field(() => CommissionPayer, { nullable: true })
  commissionPayer?: CommissionPayer;

  constructor(response: Partial<CommissionPayerResponse>) {
    Object.assign(this, response);
  }
}

@ObjectType()
export class UpdateReceiptSizeResponse {
  @Field(() => String)
  facilityPreferenceId: string;

  @Field({ nullable: true })
  receiptSize?: string;

  constructor(response: UpdateReceiptSizeResponse) {
    Object.assign(this, response);
  }
}
