import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsEmpty, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { FacilityPreferenceModel } from '@clinify/facility-preferences/models/facility-preference.model';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
@Entity({ name: 'medical_report_template' })
export class MedicalReportTemplateModel {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsEmpty()
  @IsUUID('4')
  id: string;

  @Field()
  @Column({ name: 'name' })
  name: string;

  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Column({ name: 'updated_by', nullable: true })
  updatedById?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'created_by' })
  createdBy?: ProfileModel;

  @Column({ name: 'created_by', nullable: false })
  createdById: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'creator_name' })
  creatorName?: string;

  @Field(() => String)
  @Column({ name: 'template', type: 'json' })
  template: string;

  @Field(() => FacilityPreferenceModel, { nullable: true })
  @ManyToOne(
    () => FacilityPreferenceModel,
    (preference) => preference.medicalReportTemplates,
  )
  @JoinColumn({ name: 'facility_preference_id' })
  facilityPreference?: FacilityPreferenceModel;

  @Column({ name: 'facility_preference_id' })
  facilityPreferenceId: string;

  constructor(partial: Partial<MedicalReportTemplateModel>) {
    Object.assign(this, partial);
  }
}
