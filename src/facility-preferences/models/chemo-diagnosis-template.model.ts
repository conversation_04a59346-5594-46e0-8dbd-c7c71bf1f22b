import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsEmpty, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { FacilityPreferenceModel } from '../../facility-preferences/models/facility-preference.model';
import { ProfileModel } from '../../users/models/profile.model';
import { ChemoDiagnosisCycleModel } from '@clinify/facility-preferences/models/chemo-diagnosis-cycle.model';

@ObjectType()
export class BaseAudits {
  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Column({ name: 'updated_by', nullable: true })
  updatedById?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'created_by' })
  createdBy?: ProfileModel;

  @Column({ name: 'created_by', nullable: false })
  createdById: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'creator_name' })
  creatorName?: string;
}
@ObjectType()
@Entity({
  name: 'chemo_diagnosis_template',
})
export class ChemoDiagnosisTemplateModel extends BaseAudits {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsEmpty()
  @IsUUID('4')
  id: string;

  @Field({ nullable: false })
  @Column({
    name: 'type',
    nullable: false,
  })
  type: string;

  @Field({ nullable: false })
  @Column({
    name: 'name',
    nullable: false,
  })
  combinationName: string;

  @Field(() => [ChemoDiagnosisCycleModel], { nullable: false })
  @OneToMany(
    () => ChemoDiagnosisCycleModel,
    (cycle) => cycle.chemoDiagnosisTemplate,
  )
  cycles: ChemoDiagnosisCycleModel[];

  @Field(() => FacilityPreferenceModel, { nullable: true })
  @ManyToOne(
    () => FacilityPreferenceModel,
    (preference) => preference.chemoDiagnosisTemplates,
  )
  @JoinColumn({ name: 'facility_preference_id' })
  facilityPreference?: FacilityPreferenceModel;

  @Column({ name: 'facility_preference_id', nullable: false })
  facilityPreferenceId: string;

  @Field()
  @Column({ name: 'section', default: 'chemo' })
  section: string;

  constructor(partial: Partial<ChemoDiagnosisTemplateModel>) {
    super();
    Object.assign(this, partial);
  }
}
