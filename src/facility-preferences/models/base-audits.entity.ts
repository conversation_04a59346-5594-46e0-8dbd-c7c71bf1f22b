import { Field, ObjectType } from '@nestjs/graphql';
import { IsDate } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  UpdateDateColumn,
} from 'typeorm';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
export abstract class BaseAudits {
  @IsDate()
  @Field()
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field()
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Column({ name: 'updated_by' })
  lastModifierId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;
}

@ObjectType()
export abstract class AuditEntitiesWithHospital extends BaseAudits {
  @Field(() => HospitalModel)
  @OneToOne(() => HospitalModel, (hospital) => hospital, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'hospital_id' })
  hospital?: HospitalModel;

  @Index()
  @Column({ name: 'hospital_id' })
  hospitalId?: string;
}
