import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsUUID } from 'class-validator';
import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import {
  CommissionPayer,
  PayoutCommissionPayer,
} from '@clinify/facility-preferences/enums/commission-payer';
import { DefaultPatientAccessType } from '@clinify/facility-preferences/enums/patient-lookup';
import { AuditEntitiesWithHospital } from '@clinify/facility-preferences/interface/base-audits.entity';
import {
  EnrollmentAgencyDto,
  EnrollmentAgentDto,
  FieldOfficerResponseDto,
  EnrolleeReferralResponseDto,
  SponsorResponseDto,
  TpaResponseDto,
} from '@clinify/facility-preferences/interface/enrollment-agent.dto';
import { MandatoryFields } from '@clinify/facility-preferences/interface/mandatory-fields';
import { ChemoDiagnosisTemplateModel } from '@clinify/facility-preferences/models/chemo-diagnosis-template.model';
import { ConsultationsTemplateModel } from '@clinify/facility-preferences/models/consultations-template.model';
import { DischargeSummaryTemplateModel } from '@clinify/facility-preferences/models/discharge-summary-template.model';
import { FindingsTemplateModel } from '@clinify/facility-preferences/models/findings-template.model';
import { LabCommentsTemplateModel } from '@clinify/facility-preferences/models/lab-comments-template.model';
import { MedicalReportTemplateModel } from '@clinify/facility-preferences/models/medical-report-template.model';
import { OperationNoteTemplateModel } from '@clinify/facility-preferences/models/operation-note-template.model';
import { CapitionAmountByPlanType } from '@clinify/facility-preferences/responses/facility-preference.reponse';
import { MailTemplate } from '@clinify/facility-preferences/validators/mail-template.input';
import { TestReferenceRangeModel } from '@clinify/investigation/models/test-reference-range.model';

@ObjectType()
@Entity({ name: 'facility_preferences' })
export class FacilityPreferenceModel extends AuditEntitiesWithHospital {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsUUID('4')
  id: string;

  @Field(() => MailTemplate, { nullable: true })
  @Column({ type: 'jsonb', nullable: true, name: 'welcome_mail_template' })
  welcomeMailTemplate: MailTemplate;

  @Field(() => MandatoryFields, { nullable: true })
  @Column({ type: 'jsonb', nullable: true, name: 'mandatory_fields' })
  mandatoryFields?: MandatoryFields;

  @Field(() => DefaultPatientAccessType, { nullable: true })
  @Column({ name: 'patient_access_type', nullable: true, type: 'text' })
  patientAccessType?: DefaultPatientAccessType;

  @Field(() => String, { nullable: true })
  @Column({ name: 'radiology_exam_source', nullable: true, type: 'text' })
  radiologyExamSource?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'procedure_type_source',
    nullable: true,
    type: 'text',
    default: 'Clinify',
  })
  procedureTypeSource?: string;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'retainership_source',
    nullable: true,
    type: 'text',
    default: 'Clinify',
  })
  retainershipSource?: string;

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'show_service_details',
    nullable: true,
    type: 'boolean',
    default: true,
  })
  showServiceDetails?: boolean;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'inventory_class',
    nullable: true,
    type: 'text',
    default: 'External',
  })
  inventoryClass?: string;

  @Field(() => [String], { nullable: true })
  @Column({
    name: 'roles_service_details_is_hidden',
    nullable: true,
    type: 'text',
    array: true,
  })
  rolesServiceDetailsIsHidden?: string[];

  @Field(() => String, { nullable: true })
  @Field(() => Boolean, { nullable: true, defaultValue: false })
  @Column({
    name: 'radiology_contrast_confirmation',
    nullable: true,
    type: 'boolean',
    default: false,
  })
  radiologyContrastConfirmation?: boolean;

  @Field(() => Boolean, { nullable: true, defaultValue: false })
  @Column({
    name: 'dashboard_colour_mode',
    nullable: true,
    type: 'boolean',
    default: false,
  })
  dashboardColourMode?: boolean;

  @Field(() => Boolean, { nullable: true, defaultValue: false })
  @Column({
    name: 'generate_file_number',
    nullable: true,
    type: 'boolean',
    default: false,
  })
  generateFileNumber?: boolean;

  @Field(() => String, { nullable: true })
  @Column({ name: 'hospital_short_name', nullable: true, type: 'text' })
  hospitalShortName?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'laboratory_test_source', nullable: true, type: 'text' })
  laboratoryTestSource?: string;

  @OneToMany(
    () => TestReferenceRangeModel,
    (refReg) => refReg.facilityPreference,
    {
      onDelete: 'CASCADE',
      cascade: true,
    },
  )
  testReferenceRanges?: TestReferenceRangeModel[];

  @OneToMany(
    () => ConsultationsTemplateModel,
    (consultationsTemplate) => consultationsTemplate.facilityPreference,
    {
      onDelete: 'CASCADE',
      cascade: true,
    },
  )
  consultationsTemplates?: ConsultationsTemplateModel[];

  @OneToMany(
    () => FindingsTemplateModel,
    (template) => template.facilityPreference,
    {
      onDelete: 'CASCADE',
      cascade: true,
    },
  )
  findingsTemplates?: FindingsTemplateModel[];

  @OneToMany(
    () => LabCommentsTemplateModel,
    (template) => template.facilityPreference,
    {
      onDelete: 'CASCADE',
      cascade: true,
    },
  )
  labCommentsTemplates?: LabCommentsTemplateModel[];

  @OneToMany(
    () => MedicalReportTemplateModel,
    (template) => template.facilityPreference,
    { onDelete: 'CASCADE', cascade: true },
  )
  medicalReportTemplates?: MedicalReportTemplateModel[];

  @OneToMany(
    () => ChemoDiagnosisTemplateModel,
    (template) => template.facilityPreference,
    { onDelete: 'CASCADE', cascade: true },
  )
  chemoDiagnosisTemplates?: ChemoDiagnosisTemplateModel[];

  @OneToMany(
    () => DischargeSummaryTemplateModel,
    (template) => template.facilityPreference,
    { onDelete: 'CASCADE', cascade: true },
  )
  dischargeSummaryTemplates?: DischargeSummaryTemplateModel[];

  @OneToMany(
    () => OperationNoteTemplateModel,
    (template) => template.facilityPreference,
    { onDelete: 'CASCADE', cascade: true },
  )
  operationNoteTemplates?: OperationNoteTemplateModel[];

  @Field(() => CommissionPayer, { nullable: true })
  @Column({
    name: 'commission_payer',
    type: 'enum',
    enum: CommissionPayer,
    nullable: true,
    default: CommissionPayer.Facility,
  })
  commissionPayer?: CommissionPayer;

  @Field(() => Number, { nullable: true })
  @Column({ name: 'commission_amount', type: 'float', nullable: true })
  commissionAmount?: number;

  @Field(() => Number, { nullable: true })
  @Column({ name: 'commission_percent', type: 'float', nullable: true })
  commissionPercent?: number;

  @Field(() => String, { nullable: true })
  @Column({ name: 'out_patient_link', nullable: true, type: 'text' })
  outPatientLink?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'in_patient_link', nullable: true, type: 'text' })
  inPatientLink?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'receipt_size', nullable: true })
  receiptSize?: string;

  @Field({ nullable: true })
  @Column({ name: 'include_hq_facility_tariffs', nullable: true })
  useHQFacilityTariffs?: boolean;

  @Field({ nullable: true })
  @Column({ name: 'include_hq_facility_inventory', nullable: true })
  useHQFacilityInventory?: boolean;

  @Field({ nullable: true })
  @Column({
    name: 'enrollee_capitation_amount',
    nullable: true,
    type: 'float',
    default: 0,
  })
  enrolleeCapitationAmount?: number;

  @Field(() => [CapitionAmountByPlanType], { nullable: true })
  @Column({
    type: 'jsonb',
    nullable: true,
    name: 'enrollee_capition_amount_by_plan_type',
  })
  enrolleeCapitionAmountByPlanType?: CapitionAmountByPlanType[];

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'enrollee_capitation_amount_per_plan',
    nullable: true,
    type: 'boolean',
    default: false,
  })
  enrolleeCapitationAmountPerPlan?: boolean;

  @Field(() => PayoutCommissionPayer, { nullable: true })
  @Column({
    name: 'payout_commission_payer',
    type: 'enum',
    enum: PayoutCommissionPayer,
    nullable: true,
    default: PayoutCommissionPayer.Agency,
  })
  payoutCommissionPayer?: PayoutCommissionPayer;

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'hmo_single_visit_pa_code',
    nullable: true,
    type: 'boolean',
    default: false,
  })
  hmoSingleVisitPACode?: boolean;

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'custom_pa_format_type',
    nullable: true,
    type: 'boolean',
    default: false,
  })
  customPaFormatType?: boolean;

  @Field({ nullable: true })
  @Column({ name: 'registration_fee', nullable: true, type: 'float' })
  registrationFee?: number;

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'auto_process_claims',
    nullable: true,
    type: 'boolean',
  })
  autoProcessClaims?: boolean;

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'auto_process_preauthorizations',
    nullable: true,
    type: 'boolean',
  })
  autoProcessPreauthorizations?: boolean;

  @Field(() => [EnrollmentAgencyDto], { nullable: true })
  @Column({ type: 'jsonb', nullable: true, name: 'enrollment_agency' })
  enrollmentAgency?: EnrollmentAgencyDto[];

  @Field(() => [EnrollmentAgentDto], { nullable: true })
  @Column({
    type: 'jsonb',
    nullable: true,
    name: 'enrollment_agent_assigments',
  })
  enrollmentAgentAssigments?: EnrollmentAgentDto[];

  @Field(() => [String], { nullable: true })
  @Column({
    name: 'enrollee_sponsors',
    type: 'text',
    array: true,
    nullable: true,
  })
  enrolleeSponsors: string[];

  @Field(() => [SponsorResponseDto], { nullable: true })
  @Column({
    type: 'jsonb',
    nullable: true,
    name: 'enrollee_sponsors_assigments',
  })
  enrolleeSponsorAssigments?: SponsorResponseDto[];

  @Field(() => [FieldOfficerResponseDto], { nullable: true })
  @Column({
    type: 'jsonb',
    nullable: true,
    name: 'field_officers',
  })
  fieldOfficers?: FieldOfficerResponseDto[];

  @Field(() => [EnrolleeReferralResponseDto], { nullable: true })
  @Column({
    type: 'jsonb',
    nullable: true,
    name: 'enrollee_referrals',
  })
  enrolleeReferrals?: EnrolleeReferralResponseDto[];

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'enrollee_business_rule_prevent_submit',
    nullable: true,
    type: 'boolean',
    default: false,
  })
  enableBusinessRulePreventSubmit?: boolean;

  @Field(() => [TpaResponseDto], { nullable: true })
  @Column({
    type: 'jsonb',
    nullable: true,
    name: 'enrollment_tpa_assigments',
  })
  enrollmentTpaAssigments?: TpaResponseDto[];

  constructor(preference: Partial<FacilityPreferenceModel>) {
    super();
    Object.assign(this, preference);
  }
}
