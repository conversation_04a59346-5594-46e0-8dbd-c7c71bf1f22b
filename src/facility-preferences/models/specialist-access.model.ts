import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsEmpty, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ProfileModel } from '../../users/models/profile.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';

@ObjectType()
export class BaseAudits {
  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Column({ name: 'updated_by', nullable: true })
  updatedById?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'created_by' })
  createdBy?: ProfileModel;

  @Column({ name: 'created_by', nullable: false })
  createdById: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'creator_name' })
  creatorName?: string;
}

@ObjectType()
@Entity({
  name: 'specialist_access',
})
export class SpecialistAccessModel extends BaseAudits {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsEmpty()
  @IsUUID('4')
  id: string;

  @Field(() => [String], { nullable: true })
  @Column({ name: 'specialist_ids', type: 'simple-array', nullable: true })
  specialistIds?: string[];

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile.specialistAccess, {
    onDelete: 'CASCADE',
    nullable: false,
  })
  @JoinColumn({ name: 'profile_id' })
  profile: ProfileModel;

  @Index()
  @Column({ name: 'profile_id' })
  @Field({ nullable: true })
  profileId: string;

  @Field(() => HospitalModel, { nullable: true })
  @ManyToOne(() => HospitalModel, (hospital) => hospital.specialistAccess)
  @JoinColumn({ name: 'hospital_id' })
  hospital: HospitalModel;

  @Field(() => String, { nullable: true })
  @Index()
  @Column({ name: 'hospital_id', nullable: true })
  hospitalId: string;

  constructor(partial: Partial<SpecialistAccessModel>) {
    super();
    Object.assign(this, partial);
  }
}
