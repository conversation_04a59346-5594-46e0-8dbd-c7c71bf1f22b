import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsEmpty, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ProfileModel } from '../../users/models/profile.model';
import { ChemoDiagnosisDrugTemplate } from '@clinify/facility-preferences/interface/chemo-diagnosis.interface';
import { ChemoDiagnosisTemplateModel } from '@clinify/facility-preferences/models/chemo-diagnosis-template.model';
import { ChemoInvestigationDetails } from '@clinify/oncology-consultation-history/validators/oncology-consultation-history.input';

@ObjectType()
export class BaseAudits {
  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Column({ name: 'updated_by', nullable: true })
  updatedById?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'created_by' })
  createdBy?: ProfileModel;

  @Column({ name: 'created_by', nullable: false })
  createdById: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'creator_name' })
  creatorName?: string;
}
@ObjectType()
@Entity({
  name: 'chemo_diagnosis_cycle',
})
export class ChemoDiagnosisCycleModel extends BaseAudits {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsEmpty()
  @IsUUID('4')
  id: string;

  @Field({ nullable: false })
  @Column({
    name: 'cycleNumber',
    nullable: false,
  })
  cycleNumber: number;

  @Field(() => [ChemoDiagnosisDrugTemplate], { nullable: true })
  @Column({ type: 'jsonb', nullable: true, name: 'drugs' })
  drugs?: ChemoDiagnosisDrugTemplate[];

  @Field(() => [ChemoInvestigationDetails], { nullable: true })
  @Column({ type: 'jsonb', nullable: true, name: 'investigation_details' })
  investigationDetails?: ChemoInvestigationDetails[];

  @Field(() => ChemoDiagnosisTemplateModel, { nullable: true })
  @ManyToOne(
    () => ChemoDiagnosisTemplateModel,
    (chemoDiagnosisTemplate) => chemoDiagnosisTemplate.cycles,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'chemo_diagnosis_template_id' })
  chemoDiagnosisTemplate?: ChemoDiagnosisTemplateModel;

  @Column({ name: 'chemo_diagnosis_template_id', nullable: false })
  chemoDiagnosisTemplateId: string;

  constructor(partial: Partial<ChemoDiagnosisCycleModel>) {
    super();
    Object.assign(this, partial);
  }
}
