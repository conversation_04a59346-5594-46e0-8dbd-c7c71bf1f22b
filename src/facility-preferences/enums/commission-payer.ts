import { registerEnumType } from '@nestjs/graphql';

export enum CommissionPayer {
  Patient = 'Patient',
  Facility = 'Facility',
}

registerEnumType(CommissionPayer, {
  name: 'CommissionPayer',
  description: 'Default commission payer for invoice',
});

export enum PayoutCommissionPayer {
  Provider = 'Provider',
  Agency = 'Agency',
}

registerEnumType(PayoutCommissionPayer, {
  name: 'PayoutCommissionPayer',
  description: 'Default commission payer for payout',
});
