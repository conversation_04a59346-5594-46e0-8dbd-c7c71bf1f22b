import { Test, TestingModule } from '@nestjs/testing';
import { WithdrawalRequestResolver } from './withdrawal-request.resolver';
import { WithdrawalRequestService } from '../services/withdrawal-request.service';
import { userFactory } from '@mocks/factories/user.factory';
import { withdrawalRequestFactory } from '@mocks/factories/withdrawal-request.factory';

const WithdrawalRequests = withdrawalRequestFactory.buildList(3);
const WithdrawalRequest = WithdrawalRequests[0];
const user = userFactory.build();

const mockWithdrawalRequestervice = {
  getUserWithdrawalRequests: jest.fn(() => WithdrawalRequests),
  getWithdrawalRequest: jest.fn(() => WithdrawalRequest),
  getAllWithdrawalRequests: jest.fn(() => WithdrawalRequests),
  confirmWithdrawal: jest.fn(() => ({
    status: 'success',
    message: 'withdrawal request confirmed',
    data: WithdrawalRequest,
  })),
  approveWithdrawalInTransaction: jest.fn(() => ({
    status: 'success',
    message: 'withdrawal request approved',
  })),
  authenticatePaystackTransfer: jest.fn(() => ({
    status: 'success',
    message: 'transfer successful',
  })),
  createWithdrawalRequest: jest.fn(() => WithdrawalRequest),
  resendFailedTransfer: jest.fn(() => WithdrawalRequest),
};

describe('WithdrawalRequestResolver', () => {
  let resolver: WithdrawalRequestResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WithdrawalRequestResolver,
        WithdrawalRequestService,
        {
          provide: WithdrawalRequestService,
          useValue: mockWithdrawalRequestervice,
        },
      ],
    }).compile();

    resolver = module.get<WithdrawalRequestResolver>(WithdrawalRequestResolver);
    jest.clearAllMocks();
  });

  it('getUserWithdrawalRequests() should retrieve user specific requests', async () => {
    const expectedOutput = WithdrawalRequests;
    const actualResponse = await resolver.getUserWithdrawalRequests(user, {});
    expect(actualResponse).toEqual(expectedOutput);
  });

  it('getWithdrawalRequest() should return a withdrawal request instance', async () => {
    const expectedOutput = WithdrawalRequest;
    const actualResponse = await resolver.getWithdrawalRequestById(
      WithdrawalRequest.id,
    );
    expect(actualResponse).toEqual(expectedOutput);
  });

  it('getAllWithdrawalRequests() should return a withdrawal request instance', async () => {
    const expectedOutput = WithdrawalRequests;
    const actualResponse = await resolver.getAllWithdrawalRequests({});
    expect(actualResponse).toEqual(expectedOutput);
  });

  it('approveWithdrawalRequest() should return an approved withdrawal request instance', async () => {
    const actualResponse = await resolver.approveWithdrawalRequest(
      WithdrawalRequest.Id,
      user,
    );
    expect(actualResponse.message).toEqual('withdrawal request approved');
  });

  it('resendFailedTransfer() should resend a failed transfer', async () => {
    const expectedOutput = WithdrawalRequest;
    const actualResponse = await resolver.resendFailedTransfer(
      WithdrawalRequest.Id,
      user,
    );
    expect(actualResponse).toEqual(expectedOutput);
  });

  it('authenticatePaystackTransfer() should validate paystack otp', async () => {
    const actualResponse = await resolver.authenticatePaystackTransfer({
      otp: '12345',
      transferCode: '1x2y3z',
    });
    expect(actualResponse.message).toEqual('transfer successful');
  });

  it('confirmWithdrawalRequest() should confirm a withdrawal request initiated', async () => {
    const actualResponse = await resolver.confirmWithdrawalRequest(
      WithdrawalRequest.id,
      '1234',
      user,
    );
    expect(actualResponse.message).toEqual('withdrawal request confirmed');
  });
});
