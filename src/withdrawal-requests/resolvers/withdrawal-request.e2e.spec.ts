import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { INestApplication, ValidationPipe } from '@nestjs/common';

import { EventEmitterModule } from '@nestjs/event-emitter';
import { GraphQLModule } from '@nestjs/graphql';

import { getModelToken } from '@nestjs/mongoose';
import { Test } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import request from 'supertest';
import { WithdrawalRequestService } from '../services/withdrawal-request.service';
import { WithdrawalRequestModule } from '../withdrawal-request.module';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { TestDataSourceOptions } from '@clinify/data-source';
import {
  DefaultInvImage,
  InventoryUpload,
} from '@clinify/integrations/inventory-blk/entities/FileUpload.entity';
import { PriceUpload } from '@clinify/integrations/price-blk/entities/PriceFileUpload.entity';
import { UserType } from '@clinify/shared/enums/users';
import { withdrawalRequestFactory } from '@mocks/factories/withdrawal-request.factory';
import gqlAuthGuardMock from '@mocks/gqlAuthGuard.mock';

jest.mock('@aws-sdk/client-bedrock-runtime', () => {
  const mockInvokeModelCommand = jest.fn();
  return {
    BedrockRuntimeClient: jest.fn(() => ({
      send: jest.fn(),
    })),
    InvokeModelCommand: mockInvokeModelCommand,
  };
});

const withdrawalRequests = withdrawalRequestFactory.buildList(3);
const withdrawalRequest = withdrawalRequests[0];

const mockWithdrawalRequestService = {
  getUserWithdrawalRequests: jest.fn(() => ({
    list: withdrawalRequests,
    totalCount: 1,
  })),
  getWithdrawalRequest: jest.fn(() => withdrawalRequest),
  getAllWithdrawalRequests: jest.fn(() => ({
    list: withdrawalRequests,
    totalCount: 1,
  })),
  createWithdrawalRequest: jest.fn(() => withdrawalRequest),
  approveWithdrawalInTransaction: jest.fn(() => ({
    status: true,
    message: 'withdrawal request approved and fund transfer initiated',
    data: withdrawalRequest,
  })),
  authenticatePaystackTransfer: jest.fn(() => ({
    status: true,
    message: 'otp verification successful',
  })),
  confirmWithdrawal: jest.fn(() => ({
    message: 'withdrawal request confirmed',
    data: withdrawalRequest,
  })),
  resendFailedTransfer: jest.fn(() => ({
    message: 'transfer successful',
    data: withdrawalRequest,
  })),
};

describe('WithdrawalRequestResolver', () => {
  let testHttpServer: request.SuperTest<request.Test>;
  let app: INestApplication;

  beforeAll(async () => {
    const builder = await Test.createTestingModule({
      imports: [
        WithdrawalRequestModule,
        TypeOrmModule.forRoot(TestDataSourceOptions),
        EventEmitterModule.forRoot(),
        GraphQLModule.forRoot<ApolloDriverConfig>({
          debug: false,
          playground: false,
          driver: ApolloDriver,
          autoSchemaFile: true,
          include: [WithdrawalRequestModule],
          context: ({ req }) => ({ req }),
        }),
      ],
    })
      .overrideProvider(WithdrawalRequestService)
      .useValue(mockWithdrawalRequestService)
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .overrideGuard(GqlAuthGuard)
      .useValue(gqlAuthGuardMock(UserType.Doctor))
      .compile();

    app = builder.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    testHttpServer = request(app.getHttpServer());
  });

  afterAll(async () => await app.close());

  it('getUserWithdrawalRequests should return user withdrawal requests', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query{
            getUserWithdrawalRequests(
                filter: {
                  skip: ${0},
                  take: ${10}
              }){
               list {
                amount
                id
              }
               totalCount
             }
           }`,
      })
      .expect(({ body }) => {
        const { getUserWithdrawalRequests } = body.data;
        expect(
          mockWithdrawalRequestService.getUserWithdrawalRequests,
        ).toBeCalled();
        expect(getUserWithdrawalRequests.list.length).toEqual(
          withdrawalRequests.length,
        );
        expect(getUserWithdrawalRequests.list[0].id).toEqual(
          withdrawalRequests[0].id,
        );
      })
      .expect(200)
      .end(done);
  });

  it('getWithdrawalRequestById should return a specific user request', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query{
            getWithdrawalRequestById(id: "${withdrawalRequest.id}"){
                    id
                }
            }`,
      })
      .expect(({ body }) => {
        const { getWithdrawalRequestById } = body.data;
        expect(mockWithdrawalRequestService.getWithdrawalRequest).toBeCalled();
        expect(getWithdrawalRequestById.id).toEqual(withdrawalRequest.id);
      })
      .expect(200)
      .end(done);
  });
});

describe('WithdrawalRequestResolver Admin', () => {
  let testHttpServer: request.SuperTest<request.Test>;
  let app: INestApplication;

  beforeAll(async () => {
    const builder = await Test.createTestingModule({
      imports: [
        WithdrawalRequestModule,
        TypeOrmModule.forRoot(TestDataSourceOptions),
        EventEmitterModule.forRoot(),
        GraphQLModule.forRoot<ApolloDriverConfig>({
          debug: false,
          driver: ApolloDriver,
          playground: false,
          autoSchemaFile: true,
          include: [WithdrawalRequestModule],
          context: ({ req }) => ({ req }),
        }),
      ],
    })
      .overrideProvider(WithdrawalRequestService)
      .useValue(mockWithdrawalRequestService)
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .overrideGuard(GqlAuthGuard)
      .useValue(gqlAuthGuardMock(UserType.Admin))
      .compile();

    app = builder.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    testHttpServer = request(app.getHttpServer());
  });

  afterAll(async () => await app.close());

  it('getAllWithdrawalRequests should return All db withdrawal requests', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `query{
            getAllWithdrawalRequests(
                  filter: {
                      skip: ${0},
                      take: ${10}
                  }){
                 list {
                  amount
                  id
                }
                 totalCount
               }
             }`,
      })
      .expect(({ body }) => {
        const { getAllWithdrawalRequests } = body.data;
        expect(
          mockWithdrawalRequestService.getAllWithdrawalRequests,
        ).toBeCalled();
        expect(getAllWithdrawalRequests.list.length).toEqual(
          withdrawalRequests.length,
        );
        expect(getAllWithdrawalRequests.list[0].id).toEqual(
          withdrawalRequests[0].id,
        );
      })
      .expect(200)
      .end(done);
  });

  it('approveWithdrawalRequest should approve a withdrawal request', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation{
          approveWithdrawalRequest(
                    id: "${withdrawalRequest.id}"
                    ){
                    status
                    message
               }
             }`,
      })
      .expect(({ body }) => {
        const {
          approveWithdrawalRequest: { status, message },
        } = body.data;
        expect(message).toEqual(
          'withdrawal request approved and fund transfer initiated',
        );
        expect(status).toEqual(true);
      })
      .expect(200)
      .end(done);
  });

  it('confirmWithdrawalRequest should confirm a withdrawal request', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation{
          confirmWithdrawalRequest(
            otp: "qwerty",
            id: "${withdrawalRequest.id}"
                  ){
                    message
                    data {
                      id
                    }
               }
             }`,
      })
      .expect(({ body }) => {
        const {
          confirmWithdrawalRequest: { message, data },
        } = body.data;
        expect(message).toEqual('withdrawal request confirmed');
        expect(data.id).toEqual(withdrawalRequest.id);
      })
      .expect(200)
      .end(done);
  });

  it('authenticatePaystackTransfer should confirm paystack token', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation{
          authenticatePaystackTransfer(
            auth: {
                    otp: "123456"
                    transferCode: "1x2y3z"
                  }){
                    status
                    message
               }
             }`,
      })
      .expect(({ body }) => {
        const {
          authenticatePaystackTransfer: { message, status },
        } = body.data;
        expect(message).toEqual('otp verification successful');
        expect(status).toEqual(true);
      })
      .expect(200)
      .end(done);
  });

  it('resendFailedTransfer should resend a failed transfer', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `mutation{
          resendFailedTransfer(
            id: "${withdrawalRequest.id}"
            ){
                    message
                    data {
                      id
                    }
               }
             }`,
      })
      .expect(({ body }) => {
        const {
          resendFailedTransfer: { message },
        } = body.data;
        expect(message).toEqual('transfer successful');
      })
      .expect(200)
      .end(done);
  });
});
