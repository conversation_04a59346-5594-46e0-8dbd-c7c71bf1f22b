import { UseGuards } from '@nestjs/common';
import { Args, Query, Mutation, Resolver } from '@nestjs/graphql';
import { AuthenticateTransferInput } from '../inputs/authenticate-withdrawal.input';
import { WithdrawalRequestFilter } from '../inputs/withdrawal-record-filter.input';
import { WithdrawalRequestModel } from '../models/withdrawal-request.model';
import { WithdrawalRequestResponse } from '../responses/withdrawal-request.response';
import { WithdrawalRequestService } from '../services/withdrawal-request.service';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { RolesAuthGuard } from '@clinify/authentication/guards/roles.guard';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { AppServices } from '@clinify/shared/enums/services';
import { UserType } from '@clinify/shared/enums/users';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { WithdrawalResponse } from '@clinify/wallets/responses/withdrawal.response';

@UseGuards(
  GqlAuthGuard,
  RolesAuthGuard(
    UserType.Admin,
    UserType.Doctor,
    UserType.OrganizationCashier,
    UserType.Pharmacist,
  ),
)
@LogService(AppServices.Bill)
@Resolver(() => WithdrawalRequestModel)
export class WithdrawalRequestResolver {
  constructor(
    private readonly withdrawalRequestService: WithdrawalRequestService,
  ) {}

  @Query(() => WithdrawalRequestResponse)
  @UseGuards(
    RolesAuthGuard(
      UserType.Doctor,
      UserType.Pharmacist,
      UserType.OrganizationCashier,
    ),
  )
  async getUserWithdrawalRequests(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'filter',
      type: () => WithdrawalRequestFilter,
      nullable: true,
    })
    options?: Partial<WithdrawalRequestFilter>,
  ): Promise<WithdrawalRequestResponse> {
    return this.withdrawalRequestService.getUserWithdrawalRequests(
      profile,
      options,
    );
  }

  @Query(() => WithdrawalRequestModel)
  @UseGuards(
    RolesAuthGuard(
      UserType.Doctor,
      UserType.Pharmacist,
      UserType.OrganizationCashier,
    ),
  )
  async getWithdrawalRequestById(
    @Args('id') requestId: string,
  ): Promise<WithdrawalRequestModel> {
    return this.withdrawalRequestService.getWithdrawalRequest(requestId);
  }

  @Query(() => WithdrawalRequestResponse)
  @UseGuards(RolesAuthGuard(UserType.Admin)) // this should only be accessible to the admin
  async getAllWithdrawalRequests(
    @Args({
      name: 'filter',
      type: () => WithdrawalRequestFilter,
      nullable: true,
    })
    options?: WithdrawalRequestFilter,
  ): Promise<WithdrawalRequestResponse> {
    return this.withdrawalRequestService.getAllWithdrawalRequests(options);
  }

  @Mutation(() => WithdrawalResponse)
  @UseGuards(RolesAuthGuard(UserType.Admin)) // this should only be accessible to the admin
  async approveWithdrawalRequest(
    @Args('id')
    withdrawalRequestId: string,
    @CurrentProfile() profile: ProfileModel,
  ): Promise<WithdrawalResponse> {
    return this.withdrawalRequestService.approveWithdrawalInTransaction({
      updatedBy: profile,
      withdrawalRequestId,
    });
  }

  @Mutation(() => WithdrawalResponse)
  @UseGuards(RolesAuthGuard(UserType.Admin)) // this should only be accessible to the admin
  async authenticatePaystackTransfer(
    @Args({
      name: 'auth',
      type: () => AuthenticateTransferInput,
      nullable: false,
    })
    { otp, transferCode }: AuthenticateTransferInput,
  ): Promise<WithdrawalResponse> {
    return this.withdrawalRequestService.authenticatePaystackTransfer({
      otp,
      transferCode,
    });
  }

  @Mutation(() => WithdrawalResponse)
  @UseGuards(
    RolesAuthGuard(
      UserType.Admin,
      UserType.Doctor,
      UserType.Pharmacist,
      UserType.OrganizationCashier,
    ),
  )
  async confirmWithdrawalRequest(
    @Args('id')
    withdrawalRequestId: string,
    @Args('otp')
    otpCode: string,
    @CurrentProfile() profile: ProfileModel,
  ): Promise<WithdrawalResponse> {
    return this.withdrawalRequestService.confirmWithdrawal({
      profile,
      withdrawalRequestId,
      otpCode,
    });
  }

  @Mutation(() => WithdrawalResponse)
  @UseGuards(RolesAuthGuard(UserType.Admin))
  async resendFailedTransfer(
    @Args('id')
    withdrawalRequestId: string,
    @CurrentProfile() profile: ProfileModel,
  ): Promise<WithdrawalResponse> {
    return this.withdrawalRequestService.resendFailedTransfer(
      profile,
      withdrawalRequestId,
    );
  }
}
