import { forwardRef, Logger, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WithdrawalRequestModel } from './models/withdrawal-request.model';
import { CustomWithdrawalRequestRepoMethods } from './repositories/withdrawal-request.repository';
import { WithdrawalRequestResolver } from './resolvers/withdrawal-request.resolver';
import { WithdrawalRequestService } from './services/withdrawal-request.service';
import { AuthenticationModule } from '@clinify/authentication/authentication.module';
import { BankDetailModule } from '@clinify/bank-details/bank-detail.module';
import { BankDetailModel } from '@clinify/bank-details/models/bank-detail.model';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { extendModel } from '@clinify/database/extendModel';
import { MailerModule } from '@clinify/shared/mailer/mailer.module';
import { SharedModule } from '@clinify/shared/module';
import { WalletTransactionModule } from '@clinify/wallet-transactions/wallet-transaction.module';
import { WalletRepository } from '@clinify/wallets/repositories/wallet.repository';
import { WalletModule } from '@clinify/wallets/wallet.module';

@Module({
  providers: [
    extendModel(WithdrawalRequestModel, CustomWithdrawalRequestRepoMethods),
    WithdrawalRequestService,
    WithdrawalRequestResolver,
    Logger,
  ],
  imports: [
    MailerModule,
    forwardRef(() => SharedModule),
    forwardRef(() => BankDetailModule),
    TypeOrmModule.forFeature([WithdrawalRequestModel, BankDetailModel]),
    TypeormExtendedModule.forCustomRepository([WalletRepository]),
    forwardRef(() => WalletModule),
    forwardRef(() => AuthenticationModule),
    forwardRef(() => WalletTransactionModule),
  ],
  exports: [WithdrawalRequestService],
})
export class WithdrawalRequestModule {}
