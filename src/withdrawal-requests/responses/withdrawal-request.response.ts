import { Field, ObjectType, Int } from '@nestjs/graphql';
import { WithdrawalRequestModel } from '../models/withdrawal-request.model';

@ObjectType()
export class WithdrawalRequestResponse {
  constructor(
    withdrawalRequests: WithdrawalRequestModel[],
    totalCount: number,
  ) {
    this.list = withdrawalRequests;
    this.totalCount = totalCount;
  }

  @Field(() => Int)
  totalCount: number;

  @Field(() => [WithdrawalRequestModel])
  list: WithdrawalRequestModel[];
}
