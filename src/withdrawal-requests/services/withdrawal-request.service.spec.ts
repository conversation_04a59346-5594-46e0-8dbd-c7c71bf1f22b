/* eslint-disable max-lines */
import { Test } from '@nestjs/testing';
import { getRepositoryToken, TypeOrmModule } from '@nestjs/typeorm';
import * as bcrypt from 'bcryptjs';
import { Chance } from 'chance';
import { EntityManager } from 'typeorm';
import { WithdrawalRequestService } from './withdrawal-request.service';
import { TestDataSourceOptions } from '../../data-source';
import { WithdrawalRequestModel } from '../models/withdrawal-request.model';
import { loggerMock } from '@clinify/__mocks__/logger';
import { AuthenticationService } from '@clinify/authentication/services/authentication.service';
import { BankDetailModel } from '@clinify/bank-details/models/bank-detail.model';
import {
  ApprovalStatus,
  TransactionStatus,
  TransactionType,
  WithdrawalConfirmation,
} from '@clinify/shared/enums/transaction';
import { MAILER } from '@clinify/shared/mailer/constants';
import { PaystackService } from '@clinify/shared/services/paystack.service';
import { TransactionProfileService } from '@clinify/shared/services/transacting-profile.service';
import { TransactionReferenceService } from '@clinify/shared/services/transaction-reference.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';
import { WalletTransactionService } from '@clinify/wallet-transactions/services/wallet-transaction.service';
import { WalletRepository } from '@clinify/wallets/repositories/wallet.repository';
import { WalletService } from '@clinify/wallets/services/wallet.service';
import { bankDetailFactory } from '@mocks/factories/bank-detail.factory';
import { walletTransactionFactory } from '@mocks/factories/wallet-transaction.factory';
import { walletFactory } from '@mocks/factories/wallet.factory';
import { withdrawalRequestFactory } from '@mocks/factories/withdrawal-request.factory';

const chance = new Chance();
const withdrawalRequests = withdrawalRequestFactory.buildList(3);
const withdrawalRequest = withdrawalRequests[0];
const bankDetails = bankDetailFactory.buildList(3);
const bankDetail = bankDetailFactory.build();
const walletTransaction = walletTransactionFactory.build();
const wallet = walletFactory.build();

const patientProfile = new ProfileModel();
patientProfile.type = 'Patient';
patientProfile.isDefault = true;
patientProfile.id = chance.guid({ version: 4 });

const doctorProfile = new ProfileModel();
doctorProfile.type = 'Doctor';
doctorProfile.isDefault = true;
doctorProfile.id = chance.guid({ version: 4 });
doctorProfile.clinifyId = chance.guid({ version: 4 });

const getUser = (profile = doctorProfile) => {
  const newUser = new UserModel();
  newUser.email = chance.email();
  newUser.id = chance.guid({ version: 4 });
  newUser.country = chance.country();
  newUser.phoneNumber = chance.phone({ formatted: false });
  newUser.passCode = bcrypt.hashSync('passCode', 10);
  newUser.profiles = [profile];
  return newUser;
};

const mockWithdrawalRequestRepository = {
  getUserWithdrawalRequests: jest.fn(() => withdrawalRequests),
  getAllWithdrawalRequests: jest.fn(() => withdrawalRequests),
  getWithdrawalRequest: jest.fn(() => withdrawalRequest),
  updateWithdrawalRequest: jest.fn(() => withdrawalRequest),
  save: jest.fn(),
  findOne: jest.fn(() => withdrawalRequest),
  byClinifyId: jest.fn(),
};

const mockBankDetailRepository = {
  getSavedBankDetails: jest.fn(() => bankDetails),
  getAllBankDetails: jest.fn(() => bankDetail),
  bankDetailExist: jest.fn(() => true),
  saveBankDetail: jest.fn(() => bankDetail),
  getBankDetail: jest.fn(() => bankDetail),
  save: jest.fn(() => bankDetail),
};

const mockWalletRepository = {
  save: jest.fn((v) => v),
};

const mockTransactionReferenceService = {
  createTransactionReference: jest.fn(() => ({ id: 'transactionReference' })),
  decodeTransactionReference: jest.fn(() => ({
    id: '123',
    transactionType: TransactionType.TOPUP,
  })),
};

const mockWalletService = {
  getOrCreateWallet: jest.fn(() => wallet),
  validatePasscode: jest.fn(() => true),
  createPaymentTransaction: jest.fn(() => walletTransaction),
  withdrawInTransaction: jest.fn(() => ({
    status: 'success',
    message: 'Withdrawal Successful',
    data: walletTransaction,
  })),
  initiateWithdrawal: jest.fn(() => ({
    status: 'success',
  })),
};

const mockEntityManager = {
  queryRunner: { isTransactionActive: true },
  withRepository: jest.fn((v) => v),
};

const mockWalletTransactionService = {
  createWalletTransaction: jest.fn(() => walletTransaction),
  saveWalletTransactionInTransaction: jest.fn(() => walletTransaction),
  updateWalletTransaction: jest.fn(() => walletTransaction),
  getWalletTransactionByReference: jest.fn(() => walletTransaction),
};

const mockPaystackService = {
  transfer: jest.fn(() => ({
    gateway_response: 'Approved',
    status: 'success',
  })),
  finalizeTransfer: jest.fn(() => ({
    message: 'Approved',
    status: true,
  })),
};

const mockMailerService = {
  sendMail: jest.fn(),
};

const mockAuthenticationService = {
  verifyOtp: jest.fn(() => ({
    status: 'success',
  })),
};

const mockTransactionProfileService = {
  getTransactingProfile: jest.fn(() => ({
    profileType: 'profile',
    profile: { id: 'testId', clinifyId: 'testClinifyId' },
  })),
};

describe('WithdrawalRequestService', () => {
  let service: WithdrawalRequestService;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        WithdrawalRequestService,
        {
          provide: WalletService,
          useValue: mockWalletService,
        },
        {
          provide: PaystackService,
          useValue: mockPaystackService,
        },
        {
          provide: TransactionProfileService,
          useValue: mockTransactionProfileService,
        },
        {
          provide: TransactionReferenceService,
          useValue: mockTransactionReferenceService,
        },
        {
          provide: WalletTransactionService,
          useValue: mockWalletTransactionService,
        },
        {
          provide: AuthenticationService,
          useValue: mockAuthenticationService,
        },
        {
          provide: MAILER,
          useValue: mockMailerService,
        },
        {
          provide: getRepositoryToken(BankDetailModel),
          useValue: mockBankDetailRepository,
        },
        {
          provide: getRepositoryToken(WithdrawalRequestModel),
          useValue: mockWithdrawalRequestRepository,
        },
        {
          provide: WalletRepository,
          useValue: mockWalletRepository,
        },
        {
          provide: EntityManager,
          useValue: mockEntityManager,
        },
        { ...loggerMock },
      ],
    }).compile();
    service = module.get(WithdrawalRequestService);
  });

  it('getUserWithdrawalRequests() should return all user specific withdrawal request', async () => {
    const expectedValue = [...withdrawalRequests];
    const actualValue = await service.getUserWithdrawalRequests(
      patientProfile,
      {},
    );
    expect(actualValue).toStrictEqual(expectedValue);
    expect(
      mockWithdrawalRequestRepository.getUserWithdrawalRequests,
    ).toHaveBeenCalledTimes(1);
  });
  it('getWithdrawalRequest() should return a single WithdrawalRequest record', async () => {
    const expectedValue = withdrawalRequest;
    const actualValue = await service.getWithdrawalRequest(
      withdrawalRequest.id,
    );
    expect(actualValue).toStrictEqual(expectedValue);
    expect(
      mockWithdrawalRequestRepository.getWithdrawalRequest,
    ).toHaveBeenCalledTimes(1);
  });
  it('getWithdrawalRequest() should throw an error when request doent exist', async () => {
    const mockWithdrawalRequestspy = jest.spyOn(
      mockWithdrawalRequestRepository,
      'getWithdrawalRequest',
    );
    mockWithdrawalRequestspy.mockImplementationOnce(() => {
      throw new Error('Request not found');
    });
    try {
      await service.getWithdrawalRequest(withdrawalRequest.id);
    } catch ({ message }) {
      expect(
        mockWithdrawalRequestRepository.getWithdrawalRequest,
      ).toHaveBeenCalledTimes(1);
      expect(message).toEqual('Request not found');
    }
  });
  it('getAllWithdrawalRequests() should return all WithdrawalRequest record', async () => {
    const expectedValue = withdrawalRequests;
    const actualValue = await service.getAllWithdrawalRequests({});
    expect(actualValue).toStrictEqual(expectedValue);
    expect(
      mockWithdrawalRequestRepository.getAllWithdrawalRequests,
    ).toHaveBeenCalledTimes(1);
  });
  it('createWithdrawalRequest() should create a WithdrawalRequest record', async () => {
    const expectedValue = withdrawalRequest;
    mockWithdrawalRequestRepository.save.mockImplementationOnce(
      () => withdrawalRequest,
    );
    const actualValue = await service.createWithdrawalRequest(
      withdrawalRequest,
    );
    expect(actualValue).toStrictEqual(expectedValue);
    expect(mockWithdrawalRequestRepository.save).toHaveBeenCalledTimes(1);
  });
  it('confirmWithdrawal() should throw error if user doesnt own the withdrawal request', async () => {
    bankDetail.profile.clinifyId = '123456';
    const profile = getUser().defaultProfile;
    profile.user = getUser();
    try {
      await service.confirmWithdrawal({
        profile,
        withdrawalRequestId: withdrawalRequest.id,
        otpCode: '1234',
      });
    } catch (error) {
      expect(error.message).toStrictEqual(
        'Not Authorized To Confirmed Withdrawal Request',
      );
    }
  });
  it('confirmWithdrawal() should only confirm request if autoWithdrawal is false', async () => {
    service.autoApproveWithdrawals = false;
    const user = getUser();
    const profile = getUser().defaultProfile;
    profile.id = bankDetail.profile.id;
    profile.clinifyId = 'testClinifyId';
    profile.user = user;
    withdrawalRequest.profile = profile;
    const response = await service.confirmWithdrawal({
      profile,
      withdrawalRequestId: withdrawalRequest.id,
      otpCode: '1234',
    });
    expect(response.message).toStrictEqual('Withdrawal Request Confirmed');
  });
  it('approveWithdrawalInTransaction() should throw error if not confirmed', async () => {
    const user = getUser();
    user.defaultProfile.id = bankDetail.profile.id;
    user.defaultProfile.clinifyId = bankDetail.profile.clinifyId;
    withdrawalRequest.approvalStatus = ApprovalStatus.APPROVED;
    try {
      await service.approveWithdrawalInTransaction({
        updatedBy: user.defaultProfile,
        withdrawalRequestId: withdrawalRequest.id,
      });
    } catch ({ message }) {
      expect(message).toStrictEqual('Withdrawal Request Already Approved');
    }
  });
  it('approveWithdrawalInTransaction() should throw error request already approved', async () => {
    const user = getUser();
    user.defaultProfile.id = bankDetail.profile.id;
    user.defaultProfile.clinifyId = bankDetail.profile.clinifyId;
    withdrawalRequest.approvalStatus = ApprovalStatus.PENDING_APPROVAL;
    withdrawalRequest.withdrawalConfirmation =
      WithdrawalConfirmation.NOT_CONFIRMED;
    try {
      await service.approveWithdrawalInTransaction({
        updatedBy: user.defaultProfile,
        withdrawalRequestId: withdrawalRequest.id,
      });
    } catch ({ message }) {
      expect(message).toStrictEqual('Withdrawal Request Not Confirmed');
    }
  });
  it('approveWithdrawalInTransaction() should approve withdrawal and initiate transfer', async () => {
    const user = getUser();
    user.defaultProfile.id = bankDetail.profile.id;
    user.defaultProfile.clinifyId = bankDetail.profile.clinifyId;
    withdrawalRequest.withdrawalConfirmation = WithdrawalConfirmation.CONFIRMED;
    const response = await service.approveWithdrawalInTransaction({
      updatedBy: user.defaultProfile,
      withdrawalRequestId: withdrawalRequest.id,
    });
    expect(Object.keys(response).length).toStrictEqual(3);
    expect(response.message).toStrictEqual('Withdrawal Successful');
    expect(response.data).toStrictEqual(walletTransaction);
  });
  it('approveWithdrawalInTransaction() should throw error if approval fails', async () => {
    const user = getUser();
    const expectedError = 'Error Approving Withdrawal Request';
    user.defaultProfile.id = bankDetail.profile.id;
    user.defaultProfile.clinifyId = bankDetail.profile.clinifyId;
    withdrawalRequest.withdrawalConfirmation = WithdrawalConfirmation.CONFIRMED;
    const repoSpy = jest.spyOn(
      mockWithdrawalRequestRepository,
      'getWithdrawalRequest',
    );
    repoSpy.mockImplementationOnce(() => {
      throw new Error(expectedError);
    });
    try {
      await service.approveWithdrawalInTransaction({
        updatedBy: user.defaultProfile,
        withdrawalRequestId: withdrawalRequest.id,
      });
    } catch ({ message }) {
      expect(message).toStrictEqual(expectedError);
    }
  });
  it('confirmWithdrawal() should initiate withdrawal if autoWithdrawal is true', async () => {
    service.autoApproveWithdrawals = true;
    const user = getUser();
    const profile = getUser().defaultProfile;
    profile.id = bankDetail.profile.id;
    profile.clinifyId = 'testClinifyId';
    profile.user = user;
    withdrawalRequest.profile = profile;

    mockWithdrawalRequestRepository.getWithdrawalRequest = jest
      .fn()
      .mockImplementationOnce(() => ({
        ...withdrawalRequest,
        withdrawalConfirmation: WithdrawalConfirmation.NOT_CONFIRMED,
      }))
      .mockImplementationOnce(() => ({
        ...withdrawalRequest,
        withdrawalConfirmation: WithdrawalConfirmation.CONFIRMED,
      }));
    mockWithdrawalRequestRepository.updateWithdrawalRequest = jest
      .fn()
      .mockImplementationOnce(() => withdrawalRequest);

    const response = await service.confirmWithdrawal({
      profile,
      withdrawalRequestId: withdrawalRequest.id,
      otpCode: '1234',
    });
    expect(Object.keys(response).length).toStrictEqual(3);
    expect(response.message).toStrictEqual('Withdrawal Successful');
    expect(response.data).toStrictEqual(walletTransaction);
  });
  it('createWithdrawalRequest() should throw Error creating withdrawal request', () => {
    mockWithdrawalRequestRepository.save = jest.fn(() => {
      throw new Error();
    });
    expect(
      service.createWithdrawalRequest(withdrawalRequest),
    ).rejects.toThrowError('Error Creating Withdrawal Request');
    expect(mockWithdrawalRequestRepository.save).toHaveBeenCalledTimes(1);
  });
  it('confirmWithdrawal() should throw an error if request already confirmed', async () => {
    mockWithdrawalRequestRepository.save = jest.fn(() => {
      throw new Error('Error updating withdrawal request');
    });
    const requestSpy = jest.spyOn(
      mockWithdrawalRequestRepository,
      'getWithdrawalRequest',
    );
    requestSpy.mockImplementationOnce(() => {
      withdrawalRequest.withdrawalConfirmation =
        WithdrawalConfirmation.CONFIRMED;
      return withdrawalRequest;
    });
    const user = getUser();
    const profile = getUser().defaultProfile;
    profile.id = bankDetail.profile.id;
    profile.user = user;
    profile.clinifyId = bankDetail.profile.clinifyId;
    try {
      await service.confirmWithdrawal({
        profile,
        withdrawalRequestId: withdrawalRequest.id,
        otpCode: '1234',
      });
    } catch (error) {
      expect(error.message).toStrictEqual(
        'Withdrawal Request Already Confirmed',
      );
    }
  });
  it('createWithdrawalRequest() should throw an error on create failure', async () => {
    try {
      await service.createWithdrawalRequest(withdrawalRequest);
    } catch (error) {
      expect(error.message).toStrictEqual('Error Creating Withdrawal Request');
      expect(mockWithdrawalRequestRepository.save).toHaveBeenCalledTimes(1);
    }
  });
  it('updateWithdrawalRequest() should throw an error on update failure', async () => {
    const mockWithdrawalRequestspy = jest.spyOn(
      mockWithdrawalRequestRepository,
      'updateWithdrawalRequest',
    );
    mockWithdrawalRequestspy.mockImplementationOnce(() => {
      throw new Error('request not found');
    });
    try {
      await service.updateWithdrawalRequest(withdrawalRequest.id, {
        approvalStatus: ApprovalStatus.APPROVED,
      });
    } catch (error) {
      expect(error.message).toStrictEqual('Error Updating Withdrawal Request');
      expect(
        mockWithdrawalRequestRepository.updateWithdrawalRequest,
      ).toHaveBeenCalledTimes(1);
    }
  });
  it('findPendingWithdrawalRequest() should return an empty array when user has no pending withdrawal request', async () => {
    mockWithdrawalRequestRepository.findOne = jest.fn(() => null);
    const data = await service.findPendingWithdrawalRequest(
      withdrawalRequest.clinifyId,
    );
    expect(data).toStrictEqual(null);
    expect(mockWithdrawalRequestRepository.findOne).toHaveBeenCalledTimes(1);
  });

  it('authenticatePaystackTransfer() should successfullly verify OTP', async () => {
    const { status, message } = await service.authenticatePaystackTransfer({
      otp: '1234',
      transferCode: '1w2x3y4z',
    });
    expect(status).toBeTruthy();
    expect(message).toEqual('Approved');
  });
  it('authenticatePaystackTransfer() should throw error when verification fails', async () => {
    mockPaystackService.finalizeTransfer = jest.fn(() => ({
      message: 'Rejected',
      status: false,
    }));
    try {
      await service.authenticatePaystackTransfer({
        otp: '1234',
        transferCode: '1w2x3y4z',
      });
    } catch ({ message }) {
      expect(message).toEqual('Rejected');
    }
  });
  it('resendFailedTransfer() should resend a failed transfer', async () => {
    mockWithdrawalRequestRepository.findOne = jest
      .fn()
      .mockImplementationOnce(() => withdrawalRequest);
    mockWithdrawalRequestRepository.getWithdrawalRequest = jest
      .fn()
      .mockImplementationOnce(() => withdrawalRequest);
    mockWithdrawalRequestRepository.updateWithdrawalRequest = jest
      .fn()
      .mockImplementationOnce(() => withdrawalRequest);
    mockWithdrawalRequestRepository.save = jest.fn();
    const user = getUser();
    const data = await service.resendFailedTransfer(
      user.defaultProfile,
      withdrawalRequest.id,
    );
    expect(data.status).toEqual('success');
  });
  it('resendFailedTransfer() should not resend an already successful withdrawal request', async () => {
    const user = getUser();
    const requestSpy = jest.spyOn(
      mockWithdrawalRequestRepository,
      'getWithdrawalRequest',
    );
    requestSpy.mockImplementationOnce(() => ({
      ...withdrawalRequest,
      fundsTransferStatus: TransactionStatus.SUCCESS,
    }));
    try {
      await service.resendFailedTransfer(
        user.defaultProfile,
        withdrawalRequest.id,
      );
    } catch ({ message }) {
      expect(message).toEqual('Transfer Not Failed Or Reversed');
    }
  });
  it('resendFailedTransferInTransaction() should abort and return error when there is one', async () => {
    const user = getUser();
    mockWithdrawalRequestRepository.getWithdrawalRequest = jest
      .fn()
      .mockImplementationOnce(() => withdrawalRequest);
    withdrawalRequest.fundstTansferStatus = TransactionStatus.SUCCESS;
    mockPaystackService.transfer = jest.fn(() => {
      throw new Error('failed');
    });
    try {
      await service.resendFailedTransfer(
        user.defaultProfile,
        withdrawalRequest.id,
      );
    } catch ({ message }) {
      expect(message).toEqual('Error Making Transfer');
    }
  });
  it('confirmWithdrawal() should throw error if invalid otp is supplied', async () => {
    const user = getUser();
    const profile = getUser().defaultProfile;
    bankDetail.profile.clinifyId = 'testClinifyId';
    profile.clinifyId = bankDetail.profile.clinifyId;
    profile.user = user;
    withdrawalRequest.profile = profile;

    mockAuthenticationService.verifyOtp = jest
      .fn()
      .mockImplementationOnce(() => ({
        status: 'success',
      }));
    mockWithdrawalRequestRepository.getWithdrawalRequest = jest
      .fn()
      .mockImplementationOnce(() => ({
        ...withdrawalRequest,
        withdrawalConfirmation: WithdrawalConfirmation.NOT_CONFIRMED,
      }));
    mockWithdrawalRequestRepository.updateWithdrawalRequest = jest
      .fn()
      .mockImplementationOnce(() => {
        throw Error();
      });

    try {
      await service.confirmWithdrawal({
        profile,
        withdrawalRequestId: withdrawalRequest.id,
        otpCode: '1234',
      });
    } catch (error) {
      expect(error.message).toStrictEqual(
        'Error Confirming Withdrawal Request',
      );
    }
  });
  it('confirmWithdrawal() throw error if confirmation fails', async () => {
    service.autoApproveWithdrawals = true;
    const user = getUser();
    const profile = getUser().defaultProfile;
    user.defaultProfile.id = bankDetail.profile.id;
    user.defaultProfile.clinifyId = 'testClinifyId';
    profile.user = user;
    withdrawalRequest.profile = profile;

    mockWithdrawalRequestRepository.getWithdrawalRequest = jest
      .fn()
      .mockImplementationOnce(() => ({
        ...withdrawalRequest,
        withdrawalConfirmation: WithdrawalConfirmation.NOT_CONFIRMED,
      }))
      .mockImplementationOnce(() => ({
        ...withdrawalRequest,
        withdrawalConfirmation: WithdrawalConfirmation.CONFIRMED,
      }));
    mockWithdrawalRequestRepository.updateWithdrawalRequest = jest
      .fn()
      .mockImplementationOnce(() => withdrawalRequest)
      .mockImplementationOnce(() => withdrawalRequest);
    mockWithdrawalRequestRepository.save = jest
      .fn()
      .mockImplementationOnce(() => withdrawalRequest)
      .mockImplementationOnce(() => {
        throw Error();
      });

    try {
      await service.confirmWithdrawal({
        profile: user.defaultProfile,
        withdrawalRequestId: withdrawalRequest.id,
        otpCode: '1234',
      });
    } catch ({ message }) {
      expect(message).toStrictEqual('Error Confirming Withdrawal Request');
    }
  });
});
