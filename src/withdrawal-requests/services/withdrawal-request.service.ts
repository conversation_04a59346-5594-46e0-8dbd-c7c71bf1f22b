import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { AuthenticateTransferInput } from '../inputs/authenticate-withdrawal.input';
import { WithdrawalRequestFilter } from '../inputs/withdrawal-record-filter.input';
import {
  IApproveWithdrawal,
  IConfirmWithdrawal,
  IGetPendingRequests,
  IWithdrawalRequest,
} from '../interfaces/withdrawal-request.interface';
import { WithdrawalRequestModel } from '../models/withdrawal-request.model';
import { IWithdrawalRequestRepository } from '../repositories/withdrawal-request.repository';
import { WithdrawalRequestResponse } from '../responses/withdrawal-request.response';
import { AuthenticationService } from '@clinify/authentication/services/authentication.service';
import { ITransferInput } from '@clinify/bank-details/interfaces/bank-detail.interface';
import { config } from '@clinify/config';
import { customDSSerializeInTransaction } from '@clinify/database';
import { CurrencyUnitMap } from '@clinify/shared/enums/currency';
import {
  ApprovalStatus,
  TransactionStatus,
  WithdrawalConfirmation,
} from '@clinify/shared/enums/transaction';
import { RequireOneProfileType } from '@clinify/shared/interfaces/transacting-profile.interface';
import { MAILER } from '@clinify/shared/mailer/constants';
import { IMessage } from '@clinify/shared/mailer/mailer.interface';
import { MailerService } from '@clinify/shared/mailer/mailer.service';
import { PaystackService } from '@clinify/shared/services/paystack.service';
import { TransactionProfileService } from '@clinify/shared/services/transacting-profile.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { WalletTransactionModel } from '@clinify/wallet-transactions/models/wallet-transaction.model';
import { WalletTransactionService } from '@clinify/wallet-transactions/services/wallet-transaction.service';
import { WalletRepository } from '@clinify/wallets/repositories/wallet.repository';
import { WithdrawalResponse } from '@clinify/wallets/responses/withdrawal.response';
import { WalletService } from '@clinify/wallets/services/wallet.service';

// @todo: do a check for type mismatch

@Injectable()
export class WithdrawalRequestService {
  public constructor(
    @Inject(forwardRef(() => WalletService))
    private readonly walletService: WalletService,
    @Inject(forwardRef(() => AuthenticationService))
    private authenticationService: AuthenticationService,
    @InjectRepository(WithdrawalRequestModel)
    private withdrawalRequestRepo: IWithdrawalRequestRepository,
    private readonly walletRepo: WalletRepository,
    private walletTransactionService: WalletTransactionService,
    private readonly transactionProfileService: TransactionProfileService,
    private paystackService: PaystackService,
    @Inject(EntityManager) readonly entityManager: EntityManager,
    @Inject(MAILER) private mailerService: MailerService,
    private dataSource: DataSource,
  ) {}

  autoApproveWithdrawals = config.wallet.autoApproveWithdrawals;

  async getUserWithdrawalRequests(
    profile: ProfileModel,
    options?: Partial<WithdrawalRequestFilter>,
  ): Promise<WithdrawalRequestResponse> {
    const { profileType, ...profileDataByType } =
      this.transactionProfileService.getTransactingProfile(profile);
    const withdrawalRequest =
      await this.withdrawalRequestRepo.getUserWithdrawalRequests({
        ...profileDataByType,
        profileType,
        options,
      });
    return withdrawalRequest;
  }

  async getWithdrawalRequest(
    requestId: string,
  ): Promise<WithdrawalRequestModel> {
    const withdrawalRequest =
      await this.withdrawalRequestRepo.getWithdrawalRequest(requestId);
    return withdrawalRequest;
  }

  async getAllWithdrawalRequests(
    options?: WithdrawalRequestFilter,
  ): Promise<WithdrawalRequestResponse> {
    const withdrawalRequest =
      await this.withdrawalRequestRepo.getAllWithdrawalRequests(options);
    return withdrawalRequest;
  }

  async updateWithdrawalRequest(
    id: string,
    dataUpdate: Partial<IWithdrawalRequest>,
  ): Promise<WithdrawalRequestModel> {
    try {
      const withdrawalRequest =
        await this.withdrawalRequestRepo.updateWithdrawalRequest(
          id,
          dataUpdate,
        );
      return withdrawalRequest;
    } catch (error) {
      throw new BadRequestException('Error Updating Withdrawal Request', error);
    }
  }

  async createWithdrawalRequest({
    amount,
    profile,
    currency,
    walletBalance,
    bankDetail,
  }: Partial<IWithdrawalRequest>): Promise<WithdrawalRequestModel> {
    const { profileType, ...profileDataByType } =
      this.transactionProfileService.getTransactingProfile(profile);
    try {
      const newWithdrawalRequest = new WithdrawalRequestModel({
        amount,
        ...profileDataByType,
        currency,
        walletBalance,
        bankDetail,
      });
      const withdrawalRequest = await this.withdrawalRequestRepo.save(
        newWithdrawalRequest,
      );
      return withdrawalRequest;
    } catch (error) {
      throw new BadRequestException('Error Creating Withdrawal Request', error);
    }
  }

  async confirmWithdrawal({
    profile,
    withdrawalRequestId,
    otpCode,
  }: IConfirmWithdrawal): Promise<WithdrawalResponse> {
    const { profileType, ...profileDataByType } =
      this.transactionProfileService.getTransactingProfile(profile);

    await this.authenticationService.verifyOtp({
      phoneNumber: profile?.user.phoneNumber,
      otpCode,
    });

    const updatedBy = profile;
    const requestHolder = profileDataByType[profileType];
    const {
      amount,
      currency,
      withdrawalConfirmation,
      [profileType]: { clinifyId },
    }: Partial<WithdrawalRequestModel> = await this.getWithdrawalRequest(
      withdrawalRequestId,
    );

    if (withdrawalConfirmation === WithdrawalConfirmation.CONFIRMED) {
      throw new BadRequestException('Withdrawal Request Already Confirmed');
    }
    if (requestHolder.clinifyId !== clinifyId) {
      throw new UnauthorizedException(
        'Not Authorized To Confirmed Withdrawal Request',
      );
    }

    const requestStatus = await customDSSerializeInTransaction(
      this.dataSource,
      async (manager) => {
        try {
          const withdrawalRequestRepo = manager.withRepository(
            this.withdrawalRequestRepo,
          );
          const updatedRequest =
            await withdrawalRequestRepo.updateWithdrawalRequest(
              withdrawalRequestId,
              {
                updatedBy,
                withdrawalConfirmation: WithdrawalConfirmation.CONFIRMED,
              },
            );
          if (this.autoApproveWithdrawals) {
            const withdrawalRequest = await this.approveWithdrawalInTransaction(
              {
                updatedBy,
                withdrawalRequestId,
              },
              manager,
            );
            return withdrawalRequest;
          }
          return {
            message: 'Withdrawal Request Confirmed',
            data: updatedRequest,
          };
        } catch (err) {
          throw new InternalServerErrorException(
            'Error Confirming Withdrawal Request',
            err,
          );
        }
      },
    );
    const msg: IMessage = {
      to: `${requestHolder.email}`,
      subject: 'Funds Withdrawal Notificatiom',
      html: `
      <div style="
      width:  100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-self: left;
      background-color: #F3F6F7;
      padding: 20px;
      ">
      <p style="
      font-weight: bold;
      font-size: 20px;">Clinify Funds Withdrawal Confirmation</p>
       <div style="
       width:  50%;
       height: max-content;
       display: flex;
       flex-direction: column;
       padding: 5px;
       border-radius: 5px;
       background-color: #FFFFFF;
       font-size: 16px;
       ">

      <p>Dear ${requestHolder.fullName || requestHolder.name}</p>
      <p> This is to inform you that a withdrawal of ${amount / 100} ${
        CurrencyUnitMap[currency]
      } has been initiated from your Clinify wallet</p>

      <p>If this withdrawal attempt was not made by you, it means someone gained
      unauthorized access to your profile. It may be an indication that you have
      been the target of a phishing attempt and might want to consider escalating
      to the clinify team and changing your login details.</p></div></div>
      `,
    };

    await this.mailerService.sendMail(msg);
    return requestStatus;
  }

  async approveWithdrawalInTransaction(
    { updatedBy, withdrawalRequestId }: IApproveWithdrawal,
    entityManager: EntityManager = this.entityManager,
  ): Promise<WithdrawalResponse> {
    const withdrawalRequestRepo = entityManager.withRepository(
      this.withdrawalRequestRepo,
    );
    const withdrawalRequest = await withdrawalRequestRepo.getWithdrawalRequest(
      withdrawalRequestId,
    );
    const { withdrawalConfirmation, approvalStatus } = withdrawalRequest;
    if (approvalStatus === ApprovalStatus.APPROVED) {
      throw new BadRequestException('Withdrawal Request Already Approved');
    }
    if (withdrawalConfirmation !== WithdrawalConfirmation.CONFIRMED) {
      throw new BadRequestException('Withdrawal Request Not Confirmed');
    }
    return await customDSSerializeInTransaction(
      this.dataSource,
      async (manager) => {
        const withdrawalRequestRepo = manager.withRepository(
          this.withdrawalRequestRepo,
        );
        try {
          await withdrawalRequestRepo.save({
            ...withdrawalRequest,
            updatedBy,
            approvalStatus: ApprovalStatus.APPROVED,
            fundsTransferStatus: TransactionStatus.PENDING,
          });
        } catch (error) {
          throw new InternalServerErrorException(
            'Error Approving Withdrawal Request',
            error,
          );
        }
        return await this.walletService.withdrawInTransaction(
          withdrawalRequestId,
          manager,
        );
      },
    );
  }

  async authenticatePaystackTransfer({
    otp,
    transferCode,
  }: AuthenticateTransferInput): Promise<WithdrawalResponse> {
    const { status, message } = await this.paystackService.finalizeTransfer({
      transfer_code: transferCode,
      otp,
    });
    if (!status) {
      throw new BadRequestException(message);
    }
    return { status, message };
  }

  async resendFailedTransfer(
    updatedBy: ProfileModel,
    withdrawalRequestId: string,
  ): Promise<WithdrawalResponse> {
    const request = await this.getWithdrawalRequest(withdrawalRequestId);
    const { fundsTransferStatus, amount, bankDetail } = request;
    const paystackTransferPayload = {
      source: 'balance',
      amount,
      recipient: bankDetail.transferRecipientCode,
      reference: withdrawalRequestId,
    };
    if (
      ![TransactionStatus.FAILED, TransactionStatus.REVERSED].includes(
        fundsTransferStatus,
      )
    ) {
      throw new BadRequestException('Transfer Not Failed Or Reversed');
    }

    const walletTransaction =
      await this.walletTransactionService.getWalletTransactionByReference(
        withdrawalRequestId,
      );
    return await customDSSerializeInTransaction(this.dataSource, (manager) =>
      this.resendFailedTransferInTransaction(
        amount,
        updatedBy,
        walletTransaction,
        paystackTransferPayload,
        withdrawalRequestId,
        manager,
      ),
    );
  }

  async resendFailedTransferInTransaction(
    amount: number,
    updatedBy: ProfileModel,
    { senderWallet }: WalletTransactionModel,
    paystackTransferPayload: ITransferInput,
    withdrawalRequestId: string,
    manager: EntityManager,
  ): Promise<WithdrawalResponse> {
    const { profileType, ...profileDataByType } =
      this.transactionProfileService.getTransactingProfile(
        senderWallet.profile,
      );
    try {
      const walletRepository = manager.withRepository(this.walletRepo);
      const withdrawalRequestRepo = manager.withRepository(
        this.withdrawalRequestRepo,
      );
      const totalSent = senderWallet.totalSent + amount;
      const withdrawalRequest = await withdrawalRequestRepo.findOne({
        where: { ...(profileDataByType as any), id: withdrawalRequestId },
      });
      await withdrawalRequestRepo.save({
        ...withdrawalRequest,
        updatedBy,
        fundsTransferStatus: TransactionStatus.PENDING,
      });
      await walletRepository.save({
        ...senderWallet,
        totalSent,
      });
      const transfer = await this.paystackService.transfer(
        paystackTransferPayload,
      );
      return transfer;
    } catch (error) {
      throw new BadRequestException('Error Making Transfer', error);
    }
  }

  async findPendingWithdrawalRequest(
    userByProfile: RequireOneProfileType<
      IGetPendingRequests,
      'profile' | 'hospital'
    >,
  ): Promise<WithdrawalRequestModel> {
    const withdrawalRequest = await this.withdrawalRequestRepo.findOne({
      where: {
        ...(userByProfile as any),
        approvalStatus: ApprovalStatus.PENDING_APPROVAL,
      },
      relations: ['profile', 'hospital', 'updatedBy'],
    });
    return withdrawalRequest;
  }
}
