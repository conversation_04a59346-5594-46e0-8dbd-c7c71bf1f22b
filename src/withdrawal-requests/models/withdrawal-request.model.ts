import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsEmpty, IsInt, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BankDetailModel } from '@clinify/bank-details/models/bank-detail.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { PayoutModel } from '@clinify/payouts/models/payout.model';
import { AccountHolder } from '@clinify/shared/enums/account-holder';
import { Currency } from '@clinify/shared/enums/currency';
import {
  ApprovalStatus,
  TransactionStatus,
  WithdrawalConfirmation,
} from '@clinify/shared/enums/transaction';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
@Entity('withdrawal_requests')
export class WithdrawalRequestModel {
  @IsEmpty()
  @IsUUID('4')
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile.withdrawalRequests, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn([{ referencedColumnName: 'id', name: 'profile' }])
  profile: ProfileModel;

  @Field(() => HospitalModel, { nullable: true })
  @ManyToOne(() => HospitalModel, (hospital) => hospital.withdrawalRequests, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn([{ referencedColumnName: 'id', name: 'hospital' }])
  hospital: HospitalModel;

  @Field(() => AccountHolder, { nullable: false })
  @Column({
    type: 'enum',
    nullable: false,
    name: 'holder_type',
    enum: AccountHolder,
    default: AccountHolder.User,
  })
  holderType: AccountHolder;

  @IsInt()
  @Field(() => Number, { nullable: false })
  @Column({
    type: 'integer',
    nullable: false,
  })
  amount: number; // In Kobo

  @Field(() => BankDetailModel, { nullable: false })
  @ManyToOne(
    () => BankDetailModel,
    (bankDetail) => bankDetail.withdrawalRequests,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ referencedColumnName: 'id', name: 'bank_detail' })
  bankDetail: BankDetailModel;

  @IsInt()
  @Field(() => Number, { nullable: false })
  @Column({
    name: 'wallet_balance',
    type: 'integer',
    nullable: false,
  })
  walletBalance: number; // In Kobo

  @Field(() => Currency, { nullable: false })
  @Column({
    type: 'enum',
    enum: Currency,
    nullable: false,
  })
  currency: Currency;

  @Field(() => WithdrawalConfirmation, { nullable: false })
  @Column({
    name: 'withdrawal_confirmation',
    type: 'enum',
    enum: WithdrawalConfirmation,
    nullable: false,
    default: WithdrawalConfirmation.NOT_CONFIRMED,
  })
  withdrawalConfirmation: WithdrawalConfirmation;

  @Field(() => ApprovalStatus, { nullable: false })
  @Column({
    name: 'approval_status',
    type: 'enum',
    enum: ApprovalStatus,
    nullable: false,
    default: ApprovalStatus.PENDING_APPROVAL,
  })
  approvalStatus: ApprovalStatus;

  @Field(() => TransactionStatus, { nullable: false })
  @Column({
    name: 'funds_transfer_status',
    type: 'enum',
    enum: TransactionStatus,
    nullable: false,
    default: TransactionStatus.NOT_YET_PROCESSED,
  })
  fundsTransferStatus: TransactionStatus;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'transfer_code',
    type: 'text',
    nullable: true,
  })
  transferCode: string;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, { onDelete: 'CASCADE' })
  @JoinColumn({ referencedColumnName: 'id', name: 'updated_by' })
  updatedBy: ProfileModel;

  @OneToMany(() => PayoutModel, (payout) => payout.withdrawalRequest)
  payouts?: PayoutModel[];

  constructor(withdrawalRequest: Partial<WithdrawalRequestModel>) {
    Object.assign(this, withdrawalRequest);
  }
}
