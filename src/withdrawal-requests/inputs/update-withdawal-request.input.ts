import { Field, InputType } from '@nestjs/graphql';
import {
  ApprovalStatus,
  WithdrawalConfirmation,
} from '@clinify/shared/enums/transaction';

@InputType()
export class UpdateWithdrawalRequestInput {
  @Field(() => String, { nullable: false })
  id?: string;

  @Field(() => ApprovalStatus, { nullable: true })
  approvalStatus?: ApprovalStatus;

  @Field(() => WithdrawalConfirmation, { nullable: true })
  withdrawalConfirmation?: WithdrawalConfirmation;
}
