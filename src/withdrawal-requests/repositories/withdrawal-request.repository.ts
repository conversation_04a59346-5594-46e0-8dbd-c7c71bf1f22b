import { NotFoundException } from '@nestjs/common';
import moment from 'moment';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { WithdrawalRequestFilter } from '../inputs/withdrawal-record-filter.input';
import {
  IGetWithdrawalRequest,
  IWithdrawalRequest,
} from '../interfaces/withdrawal-request.interface';
import { WithdrawalRequestModel } from '../models/withdrawal-request.model';
import { WithdrawalRequestResponse } from '../responses/withdrawal-request.response';
import { AccountHolder } from '@clinify/shared/enums/account-holder';
import { RequireOneProfileType } from '@clinify/shared/interfaces/transacting-profile.interface';
import { takePaginatedResponses } from '@clinify/utils/pagination';

const withinRange = (
  query: SelectQueryBuilder<WithdrawalRequestModel>,
  { dateRange, skip = 0, take = 50 }: Partial<WithdrawalRequestFilter> = {},
) => {
  if (dateRange?.from) {
    const from = moment(dateRange.from).startOf('day').toDate();
    query = query.andWhere('(withdrawal_requests.createdDate > :from)', {
      from,
    });
  }

  if (dateRange?.to) {
    const to = moment(dateRange.to).endOf('day').toDate();
    query = query.andWhere('(withdrawal_requests.createdDate < :to)', { to });
  }
  query
    .orderBy('withdrawal_requests.createdDate', 'DESC')
    .skip(skip)
    .take(take);
};

export interface IWithdrawalRequestRepository
  extends Repository<WithdrawalRequestModel> {
  this: Repository<WithdrawalRequestModel>;
  getUserWithdrawalRequests({
    profileType,
    options,
    ...profileDataByType
  }: RequireOneProfileType<
    IGetWithdrawalRequest,
    'profile' | 'hospital'
  >): Promise<WithdrawalRequestResponse>;
  getAllWithdrawalRequests(
    options?: WithdrawalRequestFilter,
  ): Promise<WithdrawalRequestResponse>;
  getWithdrawalRequest(requestId: string): Promise<WithdrawalRequestModel>;
  updateWithdrawalRequest(
    requestId: string,
    dataUpdate: Partial<IWithdrawalRequest>,
  ): Promise<WithdrawalRequestModel>;
}

export const CustomWithdrawalRequestRepoMethods: Pick<
  IWithdrawalRequestRepository,
  | 'getUserWithdrawalRequests'
  | 'getAllWithdrawalRequests'
  | 'getWithdrawalRequest'
  | 'updateWithdrawalRequest'
> = {
  async getUserWithdrawalRequests(
    this: IWithdrawalRequestRepository,
    {
      profileType,
      options,
      ...profileDataByType
    }: RequireOneProfileType<IGetWithdrawalRequest, 'profile' | 'hospital'>,
  ): Promise<WithdrawalRequestResponse> {
    const { clinifyId } = profileDataByType[profileType];
    const query = this.createQueryBuilder('withdrawal_requests')
      .leftJoinAndSelect('withdrawal_requests.updatedBy', 'updatedBy')
      .leftJoinAndSelect('withdrawal_requests.bankDetail', 'bankDetail');

    if (profileType === AccountHolder.User) {
      query
        .leftJoinAndSelect('withdrawal_requests.profile', 'profile')
        .andWhere('(profile.clinifyId = :clinifyId )', { clinifyId });
    }

    if (profileType === AccountHolder.Hospital) {
      query
        .leftJoinAndSelect('withdrawal_requests.hospital', 'hospital')
        .andWhere('(hospital.clinifyId = :clinifyId)', { clinifyId });
    }

    withinRange(query, options);
    const response = await query.getManyAndCount();
    return new WithdrawalRequestResponse(
      ...takePaginatedResponses(response, 50),
    );
  },

  async getAllWithdrawalRequests(
    options?: WithdrawalRequestFilter,
  ): Promise<WithdrawalRequestResponse> {
    const query = this.createQueryBuilder('withdrawal_requests')
      .leftJoinAndSelect('withdrawal_requests.profile', 'profile')
      .leftJoinAndSelect('withdrawal_requests.hospital', 'hospital')
      .leftJoinAndSelect('withdrawal_requests.updatedBy', 'updatedBy')
      .leftJoinAndSelect('withdrawal_requests.bankDetail', 'bankDetail')
      .addOrderBy('withdrawal_requests.updatedDate', 'DESC');

    withinRange(query, options);
    const response = await query.getManyAndCount();
    return new WithdrawalRequestResponse(
      ...takePaginatedResponses(response, 50),
    );
  },

  async getWithdrawalRequest(
    this: IWithdrawalRequestRepository,
    requestId: string,
  ): Promise<WithdrawalRequestModel> {
    return this.findOneOrFail({
      relations: ['profile', 'hospital', 'bankDetail', 'updatedBy'],
      where: { id: requestId },
    }).catch(() => {
      throw new NotFoundException('Withdrawal Request Not Found');
    });
  },

  async updateWithdrawalRequest(
    this: IWithdrawalRequestRepository,
    requestId: string,
    dataUpdate: Partial<IWithdrawalRequest>,
  ): Promise<WithdrawalRequestModel> {
    const request = await this.getWithdrawalRequest(requestId);
    return this.save({
      ...request,
      ...dataUpdate,
    });
  },
};
