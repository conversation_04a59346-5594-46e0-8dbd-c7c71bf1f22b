import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import {
  CustomWithdrawalRequestRepoMethods,
  IWithdrawalRequestRepository,
} from './withdrawal-request.repository';
import { WithdrawalRequestModel } from '../models/withdrawal-request.model';
import { hospitalFactory } from '@clinify/__mocks__/factories/hospital.factory';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendDSRepo, extendModel } from '@clinify/database/extendModel';
import { AccountHolder } from '@clinify/shared/enums/account-holder';
import {
  ApprovalStatus,
  WithdrawalConfirmation,
} from '@clinify/shared/enums/transaction';
import { MailerModule } from '@clinify/shared/mailer/mailer.module';
import { createWithdrawalRequests } from '@clinify/utils/tests/withdrawal-request.fixtures';

describe('WithdrawalRequestRepository', () => {
  let withdrawalRequests: WithdrawalRequestModel[];
  let dataSource: DataSource;
  let manager: EntityManager;
  let repository: IWithdrawalRequestRepository;

  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeOrmModule.forFeature([WithdrawalRequestModel]),
        MailerModule,
      ],
      providers: [
        extendModel(WithdrawalRequestModel, CustomWithdrawalRequestRepoMethods),
      ],
    }).compile();

    dataSource = module.get<DataSource>(DataSource);
    manager = dataSource.manager;
    repository = extendDSRepo<IWithdrawalRequestRepository>(
      dataSource,
      WithdrawalRequestModel,
      CustomWithdrawalRequestRepoMethods,
    );
  });

  beforeEach(async () => {
    withdrawalRequests = await createWithdrawalRequests(manager, 3);
  });
  afterAll(async () => {
    await repository.clear();
    await dataSource.destroy();
    await module.close();
  });
  it('getAllWithdrawalRequests() should return all withdrawal db withdrawal requests', async () => {
    const returnedRequests = await repository.getAllWithdrawalRequests();
    expect(returnedRequests.list.length).toBeGreaterThan(2);
  });

  it('getUserWithdrawalRequests() should return withdrawal requests made by user', async () => {
    const { createdDate } = withdrawalRequests[0];
    const expectedResult = withdrawalRequests[0];
    const returnedRequests = await repository.getUserWithdrawalRequests({
      profile: expectedResult.profile,
      profileType: AccountHolder.User,
      options: {
        skip: 0,
        take: 10,
        dateRange: {
          from: createdDate,
          to: createdDate,
        },
      },
    });
    expect(returnedRequests.list.length).toEqual(withdrawalRequests.length);
  });

  it('getUserWithdrawalRequests() should return withdrawal requests made by user', async () => {
    const expectedResult = withdrawalRequests[0];
    const returnedRequests = await repository.getUserWithdrawalRequests({
      profile: expectedResult.profile,
      profileType: AccountHolder.User,
    });
    expect(returnedRequests.list.length).toEqual(withdrawalRequests.length);
  });

  it('getUserWithdrawalRequests() should return withdrawal requests made by hospital', async () => {
    const returnedRequests = await repository.getUserWithdrawalRequests({
      hospital: hospitalFactory.build(),
      profileType: AccountHolder.Hospital,
    });
    expect(returnedRequests.list).toEqual([]);
  });

  it('getWithdrawalRequest() should return a single withdrawal request by Id', async () => {
    const expectedResult = withdrawalRequests[0];
    const returnedRequest = await repository.getWithdrawalRequest(
      expectedResult.id,
    );
    expect(returnedRequest.id).toEqual(expectedResult.id);
  });

  it('getWithdrawalRequest() should throw error if request not found', async () => {
    const invalidRequestId = '37cbb4e8-195e-11eb-802e-875171779e66';
    try {
      await repository.getWithdrawalRequest(invalidRequestId);
    } catch ({ message }) {
      expect(message).toEqual('Withdrawal Request Not Found');
    }
  });

  it('updateWithdrawalRequest() should return a single withdrawal request by Id', async () => {
    const expectedResult = withdrawalRequests[0];
    const returnedRequest = await repository.updateWithdrawalRequest(
      expectedResult.id,
      {
        withdrawalConfirmation: WithdrawalConfirmation.CONFIRMED,
        approvalStatus: ApprovalStatus.APPROVED,
      },
    );
    expect(returnedRequest.withdrawalConfirmation).toEqual(
      WithdrawalConfirmation.CONFIRMED,
    );
    expect(returnedRequest.approvalStatus).toEqual(ApprovalStatus.APPROVED);
  });
});
