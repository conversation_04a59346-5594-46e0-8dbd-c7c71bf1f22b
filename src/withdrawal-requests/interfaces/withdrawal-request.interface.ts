import { WithdrawalRequestFilter } from '../inputs/withdrawal-record-filter.input';
import { BankDetailModel } from '@clinify/bank-details/models/bank-detail.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { AccountHolder } from '@clinify/shared/enums/account-holder';
import { Currency } from '@clinify/shared/enums/currency';
import {
  ApprovalStatus,
  TransactionStatus,
  WithdrawalConfirmation,
} from '@clinify/shared/enums/transaction';
import { ProfileModel } from '@clinify/users/models/profile.model';

export interface IWithdrawalRequest {
  id: string;
  createdDate?: Date;
  updatedDate?: Date;
  profile: ProfileModel;
  amount: number;
  walletBalance: number;
  bankDetail: BankDetailModel;
  currency: Currency;
  withdrawalConfirmation: WithdrawalConfirmation;
  approvalStatus: ApprovalStatus;
  updatedBy?: ProfileModel;
  fundsTransferStatus: TransactionStatus;
}

export interface IConfirmWithdrawal {
  profile: ProfileModel;
  withdrawalRequestId: string;
  otpCode: string;
}

export interface IApproveWithdrawal {
  updatedBy: ProfileModel;
  withdrawalRequestId: string;
}

export interface IAuthenticateTransfer {
  otp: string;
  transfer_code: string;
  updatedBy: ProfileModel;
}

export interface IGetWithdrawalRequest {
  profile: ProfileModel;
  hospital: HospitalModel;
  profileType: AccountHolder;
  options?: Partial<WithdrawalRequestFilter>;
}

export interface IGetPendingRequests {
  profile: ProfileModel;
  hospital: HospitalModel;
}
