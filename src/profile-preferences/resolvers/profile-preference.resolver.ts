import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { UpdateProfilePreferenceInput } from '@clinify/profile-preferences/inputs/update-profile-preference.input';
import { ProfilePreferenceModel } from '@clinify/profile-preferences/models/profile-preference.model';
import { ProfilePreferenceService } from '@clinify/profile-preferences/services/profile-preference.service';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { ProfileModel } from '@clinify/users/models/profile.model';

@Resolver(() => ProfilePreferenceModel)
export class ProfilePreferenceResolver {
  constructor(
    private readonly profilePreferenceService: ProfilePreferenceService,
  ) {}

  @Query(() => ProfilePreferenceModel)
  @UseGuards(GqlAuthGuard)
  async getProfilePreference(
    @Args('profileId') profileId: string,
    @CurrentProfile() mutator: ProfileModel,
  ): Promise<ProfilePreferenceModel> {
    return this.profilePreferenceService.getProfilePreference(
      profileId,
      mutator,
    );
  }

  @Query(() => ProfilePreferenceModel)
  @UseGuards(GqlAuthGuard)
  async getMyProfilePreference(
    @CurrentProfile() mutator: ProfileModel,
  ): Promise<ProfilePreferenceModel> {
    return this.profilePreferenceService.getProfilePreference(
      mutator.id,
      mutator,
    );
  }

  @Mutation(() => ProfilePreferenceModel)
  @UseGuards(GqlAuthGuard)
  async updateProfilePreference(
    @Args('profileId') profileId: string,
    @Args('input') input: UpdateProfilePreferenceInput,
    @CurrentProfile() mutator: ProfileModel,
  ): Promise<ProfilePreferenceModel> {
    return this.profilePreferenceService.updateProfilePreference(
      profileId,
      input,
      mutator,
    );
  }

  @Mutation(() => ProfilePreferenceModel)
  @UseGuards(GqlAuthGuard)
  async updateMyProfilePreference(
    @Args('input') input: UpdateProfilePreferenceInput,
    @CurrentProfile() mutator: ProfileModel,
  ): Promise<ProfilePreferenceModel> {
    return this.profilePreferenceService.updateProfilePreference(
      mutator.id,
      input,
      mutator,
    );
  }
}
