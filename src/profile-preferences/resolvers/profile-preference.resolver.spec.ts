import { Test, TestingModule } from '@nestjs/testing';
import { ProfilePreferenceResolver } from './profile-preference.resolver';
import { UpdateProfilePreferenceInput } from '@clinify/profile-preferences/inputs/update-profile-preference.input';
import { ProfilePreferenceModel } from '@clinify/profile-preferences/models/profile-preference.model';
import { ProfilePreferenceService } from '@clinify/profile-preferences/services/profile-preference.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';

describe('ProfilePreferenceResolver', () => {
  let resolver: ProfilePreferenceResolver;
  const mockProfilePreferenceService = {
    getProfilePreference: jest.fn(),
    updateProfilePreference: jest.fn(),
  };

  const mockUser: UserModel = {
    id: 'user-1',
    defaultProfile: {
      id: 'profile-1',
      fullName: 'Test User',
    } as ProfileModel,
  } as UserModel;

  const mockProfile: ProfileModel = mockUser.defaultProfile;

  const mockProfilePreference: ProfilePreferenceModel = {
    id: 'preference-1',
    profileId: 'profile-1',
    autoProcessClaims: false,
    profile: mockProfile,
  } as ProfilePreferenceModel;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProfilePreferenceResolver,
        {
          provide: ProfilePreferenceService,
          useValue: mockProfilePreferenceService,
        },
      ],
    }).compile();

    resolver = module.get<ProfilePreferenceResolver>(ProfilePreferenceResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('getProfilePreference', () => {
    it('should return a profile preference', async () => {
      mockProfilePreferenceService.getProfilePreference.mockResolvedValue(
        mockProfilePreference,
      );

      const result = await resolver.getProfilePreference(
        'profile-1',
        mockProfile,
      );

      expect(result).toEqual(mockProfilePreference);
      expect(
        mockProfilePreferenceService.getProfilePreference,
      ).toHaveBeenCalledWith('profile-1', mockProfile);
    });
  });

  describe('getMyProfilePreference', () => {
    it('should return the current user profile preference', async () => {
      mockProfilePreferenceService.getProfilePreference.mockResolvedValue(
        mockProfilePreference,
      );

      const result = await resolver.getMyProfilePreference(mockProfile);

      expect(result).toEqual(mockProfilePreference);
      expect(
        mockProfilePreferenceService.getProfilePreference,
      ).toHaveBeenCalledWith(mockProfile.id, mockProfile);
    });
  });

  describe('updateProfilePreference', () => {
    it('should update a profile preference', async () => {
      const updateInput: UpdateProfilePreferenceInput = {
        autoProcessClaims: true,
      };

      const updatedPreference = {
        ...mockProfilePreference,
        autoProcessClaims: true,
      };

      mockProfilePreferenceService.updateProfilePreference.mockResolvedValue(
        updatedPreference,
      );

      const result = await resolver.updateProfilePreference(
        'profile-1',
        updateInput,
        mockProfile,
      );

      expect(result).toEqual(updatedPreference);
      expect(
        mockProfilePreferenceService.updateProfilePreference,
      ).toHaveBeenCalledWith('profile-1', updateInput, mockProfile);
    });
  });

  describe('updateMyProfilePreference', () => {
    it('should update the current user profile preference', async () => {
      const updateInput: UpdateProfilePreferenceInput = {
        autoProcessClaims: true,
      };

      const updatedPreference = {
        ...mockProfilePreference,
        autoProcessClaims: true,
      };

      mockProfilePreferenceService.updateProfilePreference.mockResolvedValue(
        updatedPreference,
      );

      const result = await resolver.updateMyProfilePreference(
        updateInput,
        mockProfile,
      );

      expect(result).toEqual(updatedPreference);
      expect(
        mockProfilePreferenceService.updateProfilePreference,
      ).toHaveBeenCalledWith(mockProfile.id, updateInput, mockProfile);
    });
  });
});
