import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { ProfilePreferenceModel } from '@clinify/profile-preferences/models/profile-preference.model';
import { ProfilePreferenceRepository } from '@clinify/profile-preferences/repositories/profile-preference.repository';
import { ProfilePreferenceResolver } from '@clinify/profile-preferences/resolvers/profile-preference.resolver';
import { ProfilePreferenceService } from '@clinify/profile-preferences/services/profile-preference.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProfilePreferenceModel]),
    TypeormExtendedModule.forCustomRepository([ProfilePreferenceRepository]),
  ],
  providers: [ProfilePreferenceService, ProfilePreferenceResolver],
  exports: [ProfilePreferenceService],
})
export class ProfilePreferencesModule {}
