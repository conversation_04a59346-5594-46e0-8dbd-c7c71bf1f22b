import { Injectable } from '@nestjs/common';
import { ProfilePreferenceModel } from '@clinify/profile-preferences/models/profile-preference.model';
import { ProfilePreferenceRepository } from '@clinify/profile-preferences/repositories/profile-preference.repository';
import { ProfileModel } from '@clinify/users/models/profile.model';

@Injectable()
export class ProfilePreferenceService {
  constructor(
    private readonly profilePreferenceRepository: ProfilePreferenceRepository,
  ) {}

  /**
   * Get profile preference by profile ID
   *
   * @param profileId - The ID of the profile
   * @param mutator - The profile making the request
   * @returns The profile preference
   */
  async getProfilePreference(
    profileId: string,
    mutator: ProfileModel,
  ): Promise<ProfilePreferenceModel> {
    return this.profilePreferenceRepository.getOrCreateProfilePreference(
      profileId,
      mutator,
    );
  }

  /**
   * Update profile preference
   *
   * @param profileId - The ID of the profile
   * @param data - The data to update
   * @param mutator - The profile making the update
   * @returns The updated profile preference
   */
  async updateProfilePreference(
    profileId: string,
    data: Partial<ProfilePreferenceModel>,
    mutator: ProfileModel,
  ): Promise<ProfilePreferenceModel> {
    return this.profilePreferenceRepository.updateProfilePreference(
      profileId,
      data,
      mutator,
    );
  }
}
