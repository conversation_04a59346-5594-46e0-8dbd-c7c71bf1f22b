import { Test, TestingModule } from '@nestjs/testing';
import { ProfilePreferenceService } from './profile-preference.service';
import { ProfilePreferenceModel } from '@clinify/profile-preferences/models/profile-preference.model';
import { ProfilePreferenceRepository } from '@clinify/profile-preferences/repositories/profile-preference.repository';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';

describe('ProfilePreferenceService', () => {
  let service: ProfilePreferenceService;
  const mockProfilePreferenceRepository = {
    getOrCreateProfilePreference: jest.fn(),
    updateProfilePreference: jest.fn(),
  };

  const mockUser: UserModel = {
    id: 'user-1',
    defaultProfile: {
      id: 'profile-1',
      fullName: 'Test User',
    } as ProfileModel,
  } as UserModel;

  const mockProfile: ProfileModel = mockUser.defaultProfile;

  const mockProfilePreference: ProfilePreferenceModel = {
    id: 'preference-1',
    profileId: 'profile-1',
    autoProcessClaims: false,
    profile: mockProfile,
  } as ProfilePreferenceModel;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProfilePreferenceService,
        {
          provide: ProfilePreferenceRepository,
          useValue: mockProfilePreferenceRepository,
        },
      ],
    }).compile();

    service = module.get<ProfilePreferenceService>(ProfilePreferenceService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getProfilePreference', () => {
    it('should get or create a profile preference', async () => {
      mockProfilePreferenceRepository.getOrCreateProfilePreference.mockResolvedValue(
        mockProfilePreference,
      );

      const result = await service.getProfilePreference(
        'profile-1',
        mockProfile,
      );

      expect(result).toEqual(mockProfilePreference);
      expect(
        mockProfilePreferenceRepository.getOrCreateProfilePreference,
      ).toHaveBeenCalledWith('profile-1', mockProfile);
    });
  });

  describe('updateProfilePreference', () => {
    it('should update a profile preference', async () => {
      const updateData = {
        autoProcessClaims: true,
      };

      const updatedPreference = {
        ...mockProfilePreference,
        autoProcessClaims: true,
      };

      mockProfilePreferenceRepository.updateProfilePreference.mockResolvedValue(
        updatedPreference,
      );

      const result = await service.updateProfilePreference(
        'profile-1',
        updateData,
        mockProfile,
      );

      expect(result).toEqual(updatedPreference);
      expect(
        mockProfilePreferenceRepository.updateProfilePreference,
      ).toHaveBeenCalledWith('profile-1', updateData, mockProfile);
    });
  });
});
