import { NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { CustomRepository } from '@clinify/custom-repository/decorators/custom-repo.decorator';
import { ProfilePreferenceModel } from '@clinify/profile-preferences/models/profile-preference.model';
import { ProfileModel } from '@clinify/users/models/profile.model';

@CustomRepository(ProfilePreferenceModel)
export class ProfilePreferenceRepository extends Repository<ProfilePreferenceModel> {
  private async getProfilePreferenceByProfileId(
    profileId: string,
  ): Promise<ProfilePreferenceModel> {
    return this.findOne({
      where: { profileId },
      relations: ['profile', 'createdBy', 'updatedBy'],
    }).then((preference) => {
      if (!preference) {
        throw new NotFoundException('Profile preference not found');
      }
      return preference;
    });
  }

  private async createProfilePreference(
    profileId: string,
    data: Partial<ProfilePreferenceModel>,
    mutator: ProfileModel,
  ): Promise<ProfilePreferenceModel> {
    const preference = new ProfilePreferenceModel({
      ...data,
      profileId,
      createdBy: mutator,
      creatorId: mutator.id,
      creatorName: mutator.fullName,
    });

    return this.save(preference);
  }

  async getOrCreateProfilePreference(
    profileId: string,
    mutator: ProfileModel,
  ): Promise<ProfilePreferenceModel> {
    try {
      return await this.getProfilePreferenceByProfileId(profileId);
    } catch (error) {
      if (error instanceof NotFoundException) {
        return this.createProfilePreference(profileId, {}, mutator);
      }
      throw error;
    }
  }

  async updateProfilePreference(
    profileId: string,
    data: Partial<ProfilePreferenceModel>,
    mutator: ProfileModel,
  ): Promise<ProfilePreferenceModel> {
    const preference = await this.getOrCreateProfilePreference(
      profileId,
      mutator,
    );

    Object.assign(preference, {
      ...data,
      updatedBy: mutator,
      lastModifierId: mutator.id,
      lastModifierName: mutator.fullName,
    });

    return this.save(preference);
  }
}
