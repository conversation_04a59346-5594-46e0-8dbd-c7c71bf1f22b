import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { TestDataSourceOptions } from '@clinify/data-source';
import { ProfilePreferenceRepository } from '@clinify/profile-preferences/repositories/profile-preference.repository';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { createUsers } from '@fixtures/user.fixtures';

describe('ProfilePreferenceRepository', () => {
  let ds: DataSource;
  let manager: EntityManager;
  let repo: ProfilePreferenceRepository;
  let profile: ProfileModel;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeormExtendedModule.forCustomRepository([
          ProfilePreferenceRepository,
        ]),
      ],
      providers: [],
    }).compile();

    ds = module.get<DataSource>(DataSource);
    manager = ds.manager;
    repo = module.get<ProfilePreferenceRepository>(ProfilePreferenceRepository);

    const [user] = await createUsers(manager, 1);
    profile = user.defaultProfile;
  });

  afterAll(async () => {
    await ds.destroy();
    await module.close();
  });

  it('should be defined', () => {
    expect(repo).toBeDefined();
  });

  describe('getOrCreateProfilePreference', () => {
    it('should create a new profile preference if one does not exist', async () => {
      try {
        await manager.query(
          'DELETE FROM profile_preferences WHERE profile = $1',
          [profile.id],
        );
      } catch (error) {
        // no-ops
      }

      const result = await repo.getOrCreateProfilePreference(
        profile.id,
        profile,
      );

      expect(result).toBeDefined();
      expect(result.profileId).toEqual(profile.id);
      expect(result.creatorId).toEqual(profile.id);
      expect(result.creatorName).toEqual(profile.fullName);
      expect(result.autoProcessClaims).toEqual(null);
    });

    it('should return existing profile preference if one exists', async () => {
      const createdPreference = await repo.getOrCreateProfilePreference(
        profile.id,
        profile,
      );

      const result = await repo.getOrCreateProfilePreference(
        profile.id,
        profile,
      );

      expect(result).toBeDefined();
      expect(result.id).toEqual(createdPreference.id);
      expect(result.profileId).toEqual(profile.id);
    });
  });

  describe('updateProfilePreference', () => {
    it('should update an existing profile preference', async () => {
      await repo.getOrCreateProfilePreference(profile.id, profile);

      const updateData = {
        autoProcessClaims: true,
      };

      const result = await repo.updateProfilePreference(
        profile.id,
        updateData,
        profile,
      );

      expect(result).toBeDefined();
      expect(result.profileId).toEqual(profile.id);
      expect(result.autoProcessClaims).toEqual(true);
      expect(result.lastModifierId).toEqual(profile.id);
      expect(result.lastModifierName).toEqual(profile.fullName);
    });

    it('should create and update a profile preference if one does not exist', async () => {
      try {
        await manager.query(
          'DELETE FROM profile_preferences WHERE profile = $1',
          [profile.id],
        );
      } catch (error) {
        // no-op
      }

      const updateData = {
        autoProcessClaims: true,
      };

      const result = await repo.updateProfilePreference(
        profile.id,
        updateData,
        profile,
      );

      expect(result).toBeDefined();
      expect(result.profileId).toEqual(profile.id);
      expect(result.autoProcessClaims).toEqual(true);
      expect(result.creatorId).toEqual(profile.id);
      expect(result.creatorName).toEqual(profile.fullName);
      expect(result.lastModifierId).toEqual(profile.id);
      expect(result.lastModifierName).toEqual(profile.fullName);
    });
  });

  // Test private methods indirectly through public methods
  describe('private methods', () => {
    it('getProfilePreferenceByProfileId should throw NotFoundException if preference does not exist', async () => {
      // First, ensure no preference exists
      try {
        await manager.query(
          'DELETE FROM profile_preferences WHERE profile = $1',
          [profile.id],
        );
      } catch (error) {
        // no-op
      }

      const spy = jest.spyOn(repo, 'getOrCreateProfilePreference');

      await repo.getOrCreateProfilePreference(profile.id, profile);

      expect(spy).toHaveBeenCalled();

      spy.mockRestore();
    });

    it('createProfilePreference should create a new preference with correct data', async () => {
      try {
        await manager.query(
          'DELETE FROM profile_preferences WHERE profile = $1',
          [profile.id],
        );
      } catch (error) {
        // no-op
      }

      const result = await repo.getOrCreateProfilePreference(
        profile.id,
        profile,
      );
      expect(result).toBeDefined();
      expect(result.profileId).toEqual(profile.id);
      expect(result.creatorId).toEqual(profile.id);
      expect(result.creatorName).toEqual(profile.fullName);
    });
  });
});
