import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OneToOne } from 'typeorm/decorator/relations/OneToOne';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
@Entity({ name: 'profile_preferences' })
export class ProfilePreferenceModel {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsUUID('4')
  id: string;

  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'updated_by', nullable: true })
  lastModifierId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'created_by' })
  createdBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'created_by', nullable: true })
  creatorId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'creator_name' })
  creatorName?: string;

  @Field(() => ProfileModel, { nullable: true })
  @OneToOne(() => ProfileModel, (profile) => profile.profilePreference, {
    onDelete: 'CASCADE',
    nullable: false,
  })
  @JoinColumn({ name: 'profile' })
  profile: ProfileModel;

  @Index()
  @Column({ name: 'profile' })
  profileId?: string;

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'auto_process_claims',
    nullable: true,
    type: 'boolean',
  })
  autoProcessClaims?: boolean;

  @Field(() => Boolean, { nullable: true })
  @Column({
    name: 'auto_process_preauthorizations',
    nullable: true,
    type: 'boolean',
  })
  autoProcessPreauthorizations?: boolean;

  constructor(preference?: Partial<ProfilePreferenceModel>) {
    Object.assign(this, preference);
  }
}
