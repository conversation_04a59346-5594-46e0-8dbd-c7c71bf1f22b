import { Field, InputType, ObjectType } from '@nestjs/graphql';

@InputType()
@ObjectType('ClaimsApprovalInputType')
export class ClaimsApprovalInput {
  @Field(() => String, { nullable: true })
  status: string;

  @Field(() => String, { nullable: true })
  comment?: string;

  @Field(() => [String], { nullable: true })
  rejectionReason?: string[];

  @Field(() => String, { nullable: true })
  specifyReasonForRejection?: string;

  @Field(() => String, { nullable: true })
  statusDescription?: string;

  @Field(() => Number, { nullable: true })
  serviceAmount?: number;

  @Field(() => String)
  vettingGroup: string;

  @Field(() => String)
  creatorId: string;

  @Field(() => String)
  creatorName: string;

  @Field(() => Date)
  createdDate?: Date;

  @Field(() => Date, { nullable: true })
  updatedDate?: Date;

  @Field({ nullable: true })
  isAutoProcessed?: boolean;
}
