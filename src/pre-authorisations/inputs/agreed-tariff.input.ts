import { Field, InputType } from '@nestjs/graphql';

@InputType()
export class AgreedTariffInput {
  @Field(() => String)
  providerId: string;

  @Field(() => String)
  id: string;

  @Field(() => String)
  enrolleeId: string;

  @Field(() => Date)
  treatmentDate: Date;

  @Field(() => Number, { defaultValue: 0 })
  position: number;

  @Field(() => String, { nullable: true })
  utilizationId?: string;

  @Field(() => String, { nullable: true })
  visitTypeId?: string;

  @Field(() => String, { nullable: true })
  facilityId?: string;

  @Field(() => String, { nullable: true })
  externalPlanId?: string;
}
