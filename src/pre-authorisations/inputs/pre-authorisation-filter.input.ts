import { Field, InputType, registerEnumType } from '@nestjs/graphql';
import { IsOptional } from 'class-validator';
import { FilterInput } from '@clinify/shared/validators/filter.input';

export enum PreauthDateFilterType {
  CreatedDate = 'CreatedDate',
  RequestDate = 'RequestDate',
}

export enum TimeSortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

registerEnumType(PreauthDateFilterType, { name: 'PreauthDateFilterType' });

@InputType()
export class PreauthorizationFilterInput extends FilterInput {
  @Field(() => String, { nullable: true })
  @IsOptional()
  status?: string;

  @Field(() => String, { nullable: true })
  hmo?: string;

  @Field(() => String, { nullable: true })
  hospitalId?: string;

  @Field({ defaultValue: false, nullable: true })
  providerInsight?: boolean;

  @Field(() => String, { nullable: true })
  providerType?: string;

  @Field(() => PreauthDateFilterType, {
    defaultValue: PreauthDateFilterType.RequestDate,
  })
  filterDateField?: PreauthDateFilterType;

  @Field({ nullable: true })
  showCompleted?: boolean;

  @Field({ nullable: true })
  showNotCompleted?: boolean;

  @Field({ nullable: true })
  showSubmitted?: boolean;

  @Field({ nullable: true })
  showNotSubmitted?: boolean;

  @Field(() => TimeSortOrder, { nullable: true })
  timeSortOrder?: TimeSortOrder;
}

registerEnumType(PreauthDateFilterType, { name: 'FilterDateType' });

registerEnumType(TimeSortOrder, { name: 'TimeSortOrder' });
