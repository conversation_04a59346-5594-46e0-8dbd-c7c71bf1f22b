import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class FlaggedRuleItemDto {
  @Field({ nullable: true })
  category?: string;

  @Field({ nullable: true })
  frequencyCategory?: string;

  @Field({ nullable: true })
  id?: string;

  @Field({ nullable: true })
  ruleId: string;

  @Field({ nullable: true })
  utilizationId?: string;

  @Field({ nullable: true })
  message?: string;
}
@ObjectType()
export class FlagDto {
  @Field({ nullable: true })
  flag: string;

  @Field({ nullable: true })
  flaggedById?: string;

  @Field({ nullable: true })
  flaggedByRole?: string;

  @Field({ nullable: true })
  flaggedByFullname?: string;

  @Field({ nullable: true })
  flagDateAndTime?: Date;

  @Field({ nullable: true })
  ruleId?: string;

  @Field(() => [FlaggedRuleItemDto], { nullable: true })
  flaggedItems?: FlaggedRuleItemDto[];

  @Field({ nullable: true })
  utilizationId?: string;
}
