import { Field, InputType } from '@nestjs/graphql';
import { IsOptional, IsUUID } from 'class-validator';
import { PreauthUtilisationInput } from './preauth-utilisation.input';
import { PhoneNumberInput } from '@clinify/shared/validators/phone-number.input';
import { DiagnosisInput } from '@clinify/shared/validators/service-detail.input';

@InputType()
export class PreauthorisationInput {
  @Field(() => String)
  clinifyId?: string;

  @Field(() => String)
  enrolleeId: string;

  @Field(() => Date)
  requestDateTime?: Date;

  @Field(() => Date, { nullable: true })
  treatmentStartDate?: Date;

  @Field(() => Date, { nullable: true })
  treatmentEndDate?: Date;

  @Field(() => String)
  requestedBy?: string;

  @Field(() => String, { nullable: true })
  facilityName?: string;

  @Field(() => String, { nullable: true })
  facilityAddress?: string;

  @Field(() => String, { nullable: true })
  rank?: string;

  @Field(() => String, { nullable: true })
  department?: string;

  @Field(() => String, { nullable: true })
  specialty?: string;

  @Field(() => String, { nullable: true })
  presentingComplain?: string;

  @Field(() => String, { nullable: true })
  referredBy?: string;

  @Field(() => String, { nullable: true })
  referralCode?: string;

  @Field(() => String, { nullable: true })
  referredFrom?: string;

  @Field(() => String, { nullable: true })
  referredTo?: string;

  @Field(() => String)
  providerId: string;

  @Field(() => String)
  serviceType: string;

  @Field(() => String)
  serviceTypeCode: string;

  @Field(() => String, { nullable: true })
  serviceName: string;

  @Field(() => String, { nullable: true })
  priority?: string;

  @Field(() => [DiagnosisInput])
  diagnosis?: DiagnosisInput[];

  @Field(() => [PreauthUtilisationInput])
  utilizations?: PreauthUtilisationInput[];

  @Field(() => String, { nullable: true })
  patientType?: string;

  @Field(() => String, { nullable: true })
  paymentType?: string;

  @Field(() => String, { nullable: true })
  additionalNote?: string;

  @Field(() => [String], { nullable: true })
  documentUrl?: string[];

  @Field(() => String, { nullable: true })
  visitId?: string;

  @Field(() => Date, { nullable: true })
  visitDate?: Date;

  @Field(() => String, { nullable: true })
  memberUniqueId?: string;

  @Field(() => String, { nullable: true })
  staffEmail?: string;

  @Field(() => String, { nullable: true })
  facilityId?: string;

  @Field(() => PhoneNumberInput, { nullable: true })
  enrolleePhoneNumber?: PhoneNumberInput;

  @Field(() => String, { nullable: true })
  enrolleeEmail?: string;

  @Field(() => Boolean, { nullable: true })
  isExternalPlanType?: boolean;

  @Field(() => String, { nullable: true })
  externalPlanTypeId?: string;
}

@InputType()
export class PreauthorisationUpdateInput extends PreauthorisationInput {
  @Field({ nullable: true })
  @IsUUID('4')
  @IsOptional()
  id?: string;
}
