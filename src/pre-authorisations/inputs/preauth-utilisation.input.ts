import { Field, InputType } from '@nestjs/graphql';
import { ClaimsApprovalInput } from './approval-detail.input';

@InputType()
export class PreauthUtilisationInput {
  @Field({ nullable: true })
  id?: string;

  @Field(() => String, { nullable: true })
  category: string; // i.e utilization category

  @Field(() => String, { nullable: true })
  type: string;

  @Field(() => String, { nullable: true })
  quantity: string;

  @Field(() => String, { nullable: true })
  price: string;

  @Field({ nullable: true })
  medicationCategory?: string;

  @Field({ nullable: true })
  dosage?: string;

  @Field({ nullable: true })
  dosageUnit?: string;

  @Field({ nullable: true })
  frequency?: string;

  @Field({ nullable: true })
  duration?: string;

  @Field({ nullable: true })
  birthCount?: string;

  @Field(() => Date, { nullable: true })
  deliveryDateTime?: Date;

  @Field({ nullable: true })
  specialty?: string;

  @Field(() => String, { nullable: true })
  utilizationId?: string;

  @Field(() => String, { nullable: true })
  utilizationCode: string;

  @Field(() => String, { nullable: true })
  paCode?: string;

  @Field(() => String, { nullable: true })
  status?: string;

  @Field(() => [String], { nullable: true })
  rejectionReason?: string[];

  @Field(() => String, { nullable: true })
  statusDescription?: string;

  @Field(() => String, { nullable: true })
  specifyReasonForRejection?: string;

  @Field(() => [ClaimsApprovalInput], { nullable: true })
  utilisationStatus?: ClaimsApprovalInput[];

  @Field({
    nullable: true,
    description: 'Service type from which this utilization is created',
  })
  serviceName?: string;

  @Field(() => String, { nullable: true })
  paymentModel?: string;

  @Field(() => String, { nullable: true })
  rhesusFactor?: string;
}
