import { UseGuards } from '@nestjs/common';
import { Parent, ResolveField, Resolver } from '@nestjs/graphql';
import { PreAuthUtilisationsModel } from '../models/utilisations.model';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { AuthorizationGuard } from '@clinify/authorization/guards/authorization.guard';
import { TransferFundModel } from '@clinify/banks/models/transfer-fund.model';
import { BankService } from '@clinify/banks/services/bank.service';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { AppServices } from '@clinify/shared/enums/services';

@Resolver(() => PreAuthUtilisationsModel)
export class UtilizationResolver {
  constructor(private bankService: BankService) {}

  @ResolveField('transferFund', () => TransferFundModel, { nullable: true })
  async transferFund(
    @Parent() utilization: PreAuthUtilisationsModel,
  ): Promise<TransferFundModel | null> {
    if (!utilization.transferFundId) {
      return null;
    }

    return this.bankService.getTransferFund(utilization.transferFundId);
  }
}
