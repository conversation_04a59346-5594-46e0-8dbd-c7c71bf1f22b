import { Test, TestingModule } from '@nestjs/testing';
import { UtilizationResolver } from './utilization.resolver';
import { PreAuthUtilisationsModel } from '../models/utilisations.model';
import { TransferFundModel } from '@clinify/banks/models/transfer-fund.model';
import { BankService } from '@clinify/banks/services/bank.service';
import { utilizationFactory } from '@mocks/factories/preauthorization.factory';

describe('UtilizationResolver', () => {
  let resolver: UtilizationResolver;
  let bankService: BankService;

  const mockBankService = {
    getTransferFund: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UtilizationResolver,
        {
          provide: BankService,
          useValue: mockBankService,
        },
      ],
    }).compile();

    resolver = module.get<UtilizationResolver>(UtilizationResolver);
    bankService = module.get<BankService>(BankService);
    jest.clearAllMocks();
  });

  describe('transferFund', () => {
    it('should return null when transferFundId is not provided', async () => {
      const utilization = new PreAuthUtilisationsModel({
        transferFundId: null,
      });

      const result = await resolver.transferFund(utilization);

      expect(result).toBeNull();
      expect(mockBankService.getTransferFund).not.toHaveBeenCalled();
    });

    it('should return null when transferFundId is undefined', async () => {
      const utilization = new PreAuthUtilisationsModel({
        transferFundId: undefined,
      });

      const result = await resolver.transferFund(utilization);

      expect(result).toBeNull();
      expect(mockBankService.getTransferFund).not.toHaveBeenCalled();
    });

    it('should call bankService.getTransferFund with the correct transferFundId', async () => {
      const transferFundId = 'test-transfer-fund-id';
      const utilization = new PreAuthUtilisationsModel({
        transferFundId,
      });
      const expectedTransferFund = new TransferFundModel({
        id: transferFundId,
      });
      mockBankService.getTransferFund.mockResolvedValue(expectedTransferFund);

      const result = await resolver.transferFund(utilization);

      expect(mockBankService.getTransferFund).toHaveBeenCalledWith(
        transferFundId,
      );
      expect(result).toEqual(expectedTransferFund);
    });

    it('should handle when bankService.getTransferFund returns null', async () => {
      const transferFundId = 'non-existent-id';
      const utilization = new PreAuthUtilisationsModel({
        transferFundId,
      });
      mockBankService.getTransferFund.mockResolvedValue(null);

      const result = await resolver.transferFund(utilization);

      expect(mockBankService.getTransferFund).toHaveBeenCalledWith(
        transferFundId,
      );
      expect(result).toBeNull();
    });

    it('should handle when bankService.getTransferFund throws an error', async () => {
      const transferFundId = 'error-id';
      const utilization = new PreAuthUtilisationsModel({
        transferFundId,
      });
      const error = new Error('Test error');
      mockBankService.getTransferFund.mockRejectedValue(error);

      await expect(resolver.transferFund(utilization)).rejects.toThrow(error);
      expect(mockBankService.getTransferFund).toHaveBeenCalledWith(
        transferFundId,
      );
    });

    it('should work with a utilization from the factory', async () => {
      const utilization = utilizationFactory.build({
        transferFundId: 'factory-transfer-fund-id',
      });
      const expectedTransferFund = new TransferFundModel({
        id: utilization.transferFundId,
      });
      mockBankService.getTransferFund.mockResolvedValue(expectedTransferFund);

      const result = await resolver.transferFund(utilization);

      expect(mockBankService.getTransferFund).toHaveBeenCalledWith(
        utilization.transferFundId,
      );
      expect(result).toEqual(expectedTransferFund);
    });
  });
});
