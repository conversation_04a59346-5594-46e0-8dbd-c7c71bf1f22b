import { EventEmitter2 } from '@nestjs/event-emitter';
import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { PreauthorizationResolver } from './preauthorization.resolvers';
import { AuthorizationModule } from '../../authorization/authorization.module';
import { TestDataSourceOptions } from '../../data-source';
import { extendModel } from '../../database/extendModel';
import { HmoProviderModule } from '../../hmo-providers/hmo-provider.module';
import { HmoProviderModel } from '../../hmo-providers/models/hmo-provider.model';
import { NotificationsService } from '../../notifications/services/notifications.service';
import { PreauthorisationModel } from '../models/preauthorisation.model';
import { PreAuthUtilisationsModel } from '../models/utilisations.model';
import { CustomPreauthorizationRepoMethods } from '../repositories/pre-authorization.repositories';
import { PreauthorisationService } from '../services/pre-authorisation.service';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { HmoClaimService } from '@clinify/hmo-claims/services/hmo-claim.service';
import { HmoProviderRepository } from '@clinify/hmo-providers/repositories/hmo-provider.repository';
import { HmoProviderService } from '@clinify/hmo-providers/services/hmo-provider.service';
import {
  DefaultInvImage,
  InventoryUpload,
} from '@clinify/integrations/inventory-blk/entities/FileUpload.entity';
import { PriceUpload } from '@clinify/integrations/price-blk/entities/PriceFileUpload.entity';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import {
  preauthorizationFactory,
  utilizationFactory,
} from '@mocks/factories/preauthorization.factory';
import { mockUser } from '@mocks/factories/user.factory';

jest.mock('@aws-sdk/client-bedrock-runtime', () => {
  const mockInvokeModelCommand = jest.fn();
  return {
    BedrockRuntimeClient: jest.fn(() => ({
      send: jest.fn(),
    })),
    InvokeModelCommand: mockInvokeModelCommand,
  };
});

const profile = profileFactory.build();
const user = mockUser;

const preauthorizationData = preauthorizationFactory.build();
const list = preauthorizationFactory.buildList(4);

const PreauthorizationServiceMock = {
  addPreauthorization: jest.fn(() => preauthorizationData),
  deletePreauthorizations: jest.fn(() => [preauthorizationData]),
  getPreauthorization: jest.fn(() => preauthorizationData),
  getPreAuthDetail: jest.fn(() => preauthorizationData),
  getPreAuthDetailBulk: jest.fn(() => [preauthorizationData]),
  getAgreedTariff: jest.fn(() => ({})),
  findByHospital: jest.fn(() => ({ itemCount: 3, list })),
  findByProfile: jest.fn(() => ({ itemCount: 3, list })),
  archivePreauthorizations: jest.fn(() => preauthorizationData),
  updatePreauthUtilizationQuantity: jest.fn(() => preauthorizationData),
  synchronizePreauthorizations: jest.fn(),
  movePreauthorizationToClaims: jest.fn(() => [[preauthorizationData]]),
  getUtilization: jest.fn(() => utilizationFactory.build()),
  flagPreauthorization: jest.fn(() => preauthorizationData),
  getProvidersWithRequestedPreauthorizations: jest.fn(),
  updatePreauthorization: jest.fn(() => preauthorizationData),
  flagUtilizations: jest.fn(() => [utilizationFactory.build()]),
  getPreauthorizationSummary: jest.fn(() => ({
    totalPreauthorizations: 10,
    totalApprovedApprovedPreauthorizations: 5,
    totalRejectedPreauthorizations: 2,
    totalPendingPreauthorizations: 3,
    totalPreauthorizationsAmount: 5000,
    totalApprovedApprovedPreauthorizationsAmount: 3000,
    totalRejectedPreauthorizationsAmount: 1000,
    totalPendingPreauthorizationsAmount: 1000,
  })),
};

const ManagerMock = {
  findOneOrFail: jest.fn(() => user.defaultProfile),
};

const MockHmoClaimService = {};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

const mockEventEmitter = {
  emit: jest.fn(),
};

const TEST_URL = 'http://example.com';

describe('PreathorizationResolver', () => {
  let resolver: PreauthorizationResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        AuthorizationModule,
        HmoProviderModule,
        TypeOrmModule.forFeature([
          PreAuthUtilisationsModel,
          PreauthorisationModel,
          HmoProviderModel,
        ]),
      ],
      providers: [
        extendModel(PreauthorisationModel, CustomPreauthorizationRepoMethods),
        HmoProviderRepository,
        PreauthorizationResolver,
        PreauthorisationService,
        NotificationsService,
        {
          provide: EntityManager,
          useValue: ManagerMock,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: HmoClaimService,
          useValue: MockHmoClaimService,
        },
      ],
    })
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .overrideProvider(HmoProviderService)
      .useValue({
        getHmoProviderModule: jest.fn().mockImplementation(() => ({
          requestPreAuthorization: jest.fn(),
        })),
      })
      .overrideProvider(PreauthorisationService)
      .useValue(PreauthorizationServiceMock)
      .overrideProvider(NotificationsService)
      .useValue({
        handleNoticationEvent: jest.fn(),
      })
      .overrideProvider(PUB_SUB)
      .useValue(pubSubMock)
      .compile();

    resolver = module.get<PreauthorizationResolver>(PreauthorizationResolver);
    jest.clearAllMocks();
  });

  it('addPreauthoriztion(): should save preauthorization', async () => {
    const preAuthInput = preauthorizationData;
    delete preAuthInput.profile;
    const response = await resolver.addPreauthorization(
      profile,
      preAuthInput,
      undefined,
      TEST_URL,
    );
    expect(
      PreauthorizationServiceMock.addPreauthorization,
    ).toHaveBeenCalledWith(profile, preAuthInput, TEST_URL);
    expect(response).toEqual(preauthorizationData);
  });

  it('preauthorization(): should get preauthorization', async () => {
    const response = await resolver.preauthorization(
      profile,
      'id',
      'clinify-id',
    );
    expect(
      PreauthorizationServiceMock.getPreauthorization,
    ).toHaveBeenCalledWith(profile, 'id');
    expect(response).toEqual(preauthorizationData);
  });

  it('fetchPreauthorizationsByHospitalId(): should get preauthorizations by hospitalId', async () => {
    const response = await resolver.fetchPreauthorizationsByHospitalId(
      'hospitalId',
      profile,
      {
        take: 1,
        skip: 0,
      },
    );
    expect(PreauthorizationServiceMock.findByHospital).toHaveBeenCalledWith(
      'hospitalId',
      {
        take: 1,
        skip: 0,
      },
      profile,
    );
    expect(response.list).toEqual(list);
  });

  it('synchronizePreauthorizations(): should call synchronizePreauthorization from service', async () => {
    await resolver.synchronizePreauthorizations(profile, ['id']);
    expect(
      PreauthorizationServiceMock.synchronizePreauthorizations,
    ).toHaveBeenCalledWith(profile, ['id']);
  });

  it('movePreauthorizationToClaims(): should call movePreauthorizationToClaims from service', async () => {
    await resolver.movePreauthorizationToClaims(profile, ['id'], 'origin');
    expect(
      PreauthorizationServiceMock.movePreauthorizationToClaims,
    ).toHaveBeenCalledWith(profile, ['id']);
  });

  it('agreedTariff(): should get agreedTariff', async () => {
    const input = {
      providerId: 'id',
      id: 'some-id',
      position: 50,
      enrolleeId: 'enrolleeId',
      treatmentDate: new Date(),
    };
    const response = await resolver.agreedTariff(profile, input);
    expect(PreauthorizationServiceMock.getAgreedTariff).toHaveBeenCalledWith(
      profile,
      input,
    );
    expect(response).toEqual({});
  });

  it('deletePreauthorizations(): should get deletePreauthorizations', async () => {
    const response = await resolver.deletePreauthorizations(profile, ['id']);
    expect(
      PreauthorizationServiceMock.deletePreauthorizations,
    ).toHaveBeenCalledWith(profile, ['id']);
    expect(response).toEqual([preauthorizationData]);
  });

  it('archivePreauthorizations(): should archivePreauthorizations', async () => {
    await resolver.archivePreauthorizations(profile, ['id'], true);
    expect(
      PreauthorizationServiceMock.archivePreauthorizations,
    ).toHaveBeenCalledWith(profile, ['id'], true);
  });

  it('updateUtilizationSubsHandler(): should trigger UtilizationUpdated subscription', () => {
    resolver.updateUtilizationSubsHandler(
      'profile-id',
      'hospital-id',
      'hmoProvider-id',
    );
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith('UtilizationUpdated');
  });

  it('updatePreauthUtilizationQuantity', async () => {
    await resolver.updatePreauthUtilizationQuantity(profile, 'id', 10);
    expect(
      PreauthorizationServiceMock.updatePreauthUtilizationQuantity,
    ).toHaveBeenCalledWith(profile, 'id', 10);
  });

  it('getUtilization(): should call getUtilization service method', async () => {
    await resolver.getUtilization(profile, 'pa-code', 'utilization-id');
    expect(PreauthorizationServiceMock.getUtilization).toHaveBeenLastCalledWith(
      profile,
      'pa-code',
      'utilization-id',
      undefined,
    );

    await resolver.getUtilization(
      profile,
      'pa-code',
      'utilization-id',
      'PREAUTH',
    );
    expect(PreauthorizationServiceMock.getUtilization).toHaveBeenLastCalledWith(
      profile,
      'pa-code',
      'utilization-id',
      'PREAUTH',
    );

    await resolver.getUtilization(
      profile,
      'pa-code',
      'utilization-id',
      'CLAIM',
    );
    expect(PreauthorizationServiceMock.getUtilization).toHaveBeenLastCalledWith(
      profile,
      'pa-code',
      'utilization-id',
      'CLAIM',
    );
  });

  it('flagPreauthorization(): should call flagPreauthorizations service method', async () => {
    await resolver.flagPreauthorization(profile, 'id', 'flag', false);
    expect(
      PreauthorizationServiceMock.flagPreauthorization,
    ).toHaveBeenCalledWith(profile, 'id', 'flag', false);
  });

  it('flagPreauthorizationSubsHandler', () => {
    resolver.flagPreauthorizationSubsHandler('id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'PreauthorizationFlagged',
    );
  });

  it('getProvidersWithRequestedPreauthorizations', async () => {
    await resolver.getProvidersWithRequestedPreauthorizations(profile, {
      archive: false,
    });
    expect(
      PreauthorizationServiceMock.getProvidersWithRequestedPreauthorizations,
    ).toHaveBeenCalledWith(profile, {
      archive: false,
    });
  });

  it('updatePreauthorization(): should call updatePreauthorization service method', async () => {
    const input = preauthorizationFactory.build();

    await resolver.updatePreauthorization(profile, 'pre-auth-id', input);
    expect(
      PreauthorizationServiceMock.updatePreauthorization,
    ).toHaveBeenCalledWith(profile, 'pre-auth-id', input);
    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'HMOPreauthorizationUpdated',
      {
        HMOPreauthorizationUpdated: preauthorizationData,
      },
    );
  });

  it('flagUtilization', async () => {
    await resolver.flagUtilizations(profile, ['id'], 'flag', false);
    expect(PreauthorizationServiceMock.flagUtilizations).toHaveBeenCalledWith(
      profile,
      ['id'],
      'flag',
      false,
    );
    expect(pubSubMock.publish).toHaveBeenCalledWith('UtilizationFlagged', {
      UtilizationFlagged: expect.anything(),
    });
  });

  it('flagUtilizationSubsHandler', () => {
    resolver.flagUtilizationSubsHandler('id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith('UtilizationFlagged');
  });

  it('getPreauthorizationSummary should call getPreauthorizationSummary from service and return value', async () => {
    const options = {
      skip: 0,
      take: 10,
      status: 'Approved',
      dateRange: { from: new Date(), to: new Date() },
    };
    const profileId = 'profile-id';
    const providerId = 'provider-id';

    const response = await resolver.getPreauthorizationSummary(
      profile,
      options,
      profileId,
      providerId,
    );

    expect(
      PreauthorizationServiceMock.getPreauthorizationSummary,
    ).toHaveBeenCalledWith(profile, options, profileId, providerId);

    expect(response).toEqual(
      expect.objectContaining({
        totalPreauthorizations: 10,
        totalApprovedApprovedPreauthorizations: 5,
        totalRejectedPreauthorizations: 2,
        totalPendingPreauthorizations: 3,
        totalPreauthorizationsAmount: 5000,
        totalApprovedApprovedPreauthorizationsAmount: 3000,
        totalRejectedPreauthorizationsAmount: 1000,
        totalPendingPreauthorizationsAmount: 1000,
      }),
    );
  });
});
