import { Inject, UseGuards } from '@nestjs/common';
import {
  Args,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
  Subscription,
} from '@nestjs/graphql';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { RolesAuthGuard } from '../../authentication/guards/roles.guard';
import { HmoProviderModel } from '../../hmo-providers/models/hmo-provider.model';
import { NotificationsService } from '../../notifications/services/notifications.service';
import { UserType } from '../../shared/enums/users';
import { AgreedTariffInput } from '../inputs/agreed-tariff.input';
import { PreauthorizationFilterInput } from '../inputs/pre-authorisation-filter.input';
import {
  PreauthorisationInput,
  PreauthorisationUpdateInput,
} from '../inputs/preauthorisation.input';
import { PreauthorisationModel } from '../models/preauthorisation.model';
import { PreAuthUtilisationsModel } from '../models/utilisations.model';
import { AgreedTariffResponse } from '../responses/agreed-tariff.response';
import {
  PreauthorisationResponse,
  PreauthorizationSummary,
} from '../responses/pre-authorisation.response';
import { PreauthorisationService } from '../services/pre-authorisation.service';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { PinAuthGuard } from '@clinify/authentication/guards/pin.guard';
import { ProfileDataAccessGuard } from '@clinify/authentication/guards/profile-data-acess.guard';
import { AuthorizationGuard } from '@clinify/authorization/guards/authorization.guard';
import { HmoClaimService } from '@clinify/hmo-claims/services/hmo-claim.service';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import {
  DashboardIcon,
  NotificationTag,
} from '@clinify/shared/enums/notifications';
import { AppServices } from '@clinify/shared/enums/services';
import { ProfileModel } from '@clinify/users/models/profile.model';
import {
  filterHMOPreauthorizationAdded,
  filterHMOPreauthorizationUpdated,
  filterPreauthorizationFlagged,
  filterUtilizationFlagged,
  filterUtilizationUpdated,
} from '@clinify/utils/subscriptions/filters';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const {
  UtilizationUpdated,
  HMOPreauthorizationAdded,
  HMOPreauthorizationUpdated,
  HMOClaimUpdated,
  PreauthorizationFlagged,
  UtilizationFlagged,
} = SubscriptionTypes;
@LogService(AppServices.Note)
@UseGuards(GqlAuthGuard, AuthorizationGuard)
@Resolver(() => PreauthorisationModel)
export class PreauthorizationResolver {
  constructor(
    private preAuthService: PreauthorisationService,
    private notificationService: NotificationsService,
    private readonly hmoClaimService: HmoClaimService,
    @Inject(PUB_SUB) private pubSub: RedisPubSub,
  ) {}

  @UseGuards(ProfileDataAccessGuard('clinifyId'))
  @Query(() => PreauthorisationModel)
  async preauthorization(
    @CurrentProfile() profile: ProfileModel,
    @Args('id') id: string,
    @Args('clinifyId') _clinifyId: string,
  ): Promise<PreauthorisationModel> {
    return this.preAuthService.getPreauthorization(profile, id);
  }

  @Query(() => AgreedTariffResponse)
  async agreedTariff(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: AgreedTariffInput,
  ): Promise<AgreedTariffResponse> {
    return this.preAuthService.getAgreedTariff(profile, input);
  }

  @UseGuards(GqlAuthGuard, RolesAuthGuard(UserType.SuperAdmin))
  @Query(() => PreauthorisationResponse)
  async fetchPreauthorizationsByHospitalId(
    @Args('hospitalId') hospitalId: string,
    @CurrentProfile() mutator: ProfileModel,
    @Args({
      name: 'filterOptions',
      nullable: true,
      type: () => PreauthorizationFilterInput,
    })
    filterOptions?: PreauthorizationFilterInput,
  ): Promise<PreauthorisationResponse> {
    return this.preAuthService.findByHospital(
      hospitalId,
      filterOptions,
      mutator,
    );
  }

  @Mutation(() => [PreauthorisationModel])
  synchronizePreauthorizations(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) ids: string[],
  ): Promise<PreauthorisationModel[]> {
    return this.preAuthService.synchronizePreauthorizations(profile, ids);
  }

  @UseGuards(PinAuthGuard, ProfileDataAccessGuard('input.clinifyId'))
  @Mutation(() => PreauthorisationModel)
  async addPreauthorization(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: PreauthorisationInput,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
    @Args({ name: 'origin', type: () => String, nullable: true })
    origin?: string,
  ): Promise<PreauthorisationModel> {
    const item = await this.preAuthService.addPreauthorization(
      profile,
      input,
      origin,
    );
    const details = {
      modelName: DashboardIcon.PreAuthorization,
      action: NotificationTag.Submitted,
      item,
    };
    await this.pubSub.publish(HMOPreauthorizationAdded, {
      [HMOPreauthorizationAdded]: item,
    });
    this.notificationService.handleNoticationEvent({ profile, details });

    return item;
  }

  @UseGuards(PinAuthGuard, ProfileDataAccessGuard('input.clinifyId'))
  @Mutation(() => PreauthorisationModel)
  async updatePreauthorization(
    @CurrentProfile() profile: ProfileModel,
    @Args('id') preAuthId: string,
    @Args('input') input: PreauthorisationUpdateInput,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<PreauthorisationModel> {
    const item = await this.preAuthService.updatePreauthorization(
      profile,
      preAuthId,
      input,
    );
    const details = {
      modelName: DashboardIcon.PreAuthorization,
      action: NotificationTag.Updated,
      item: { ...item, provider: item.providerId },
    };
    await this.pubSub.publish(HMOPreauthorizationUpdated, {
      [HMOPreauthorizationUpdated]: item,
    });
    this.notificationService.handleNoticationEvent({ profile, details });

    return item;
  }

  @Subscription(() => PreauthorisationModel, {
    name: HMOPreauthorizationAdded,
    filter: filterHMOPreauthorizationAdded,
  })
  updateHMOPreAuthSubsHandler(
    @Args('profileId') _profileId: string,
    @Args('hospitalId') _hospitalId: string,
    @Args('hmoProviderId', { nullable: true }) _hmoProviderId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(HMOPreauthorizationAdded);
  }

  @Subscription(() => PreauthorisationModel, {
    name: HMOPreauthorizationUpdated,
    filter: filterHMOPreauthorizationUpdated,
  })
  addHMOPreAuthSubsHandler(
    @Args('profileId') _profileId: string,
    @Args('hospitalId') _hospitalId: string,
    @Args('hmoProviderId', { nullable: true }) _hmoProviderId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(HMOPreauthorizationUpdated);
  }

  @Mutation(() => [PreauthorisationModel])
  async deletePreauthorizations(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) ids: string[],
  ): Promise<PreauthorisationModel[]> {
    const items = await this.preAuthService.deletePreauthorizations(
      profile,
      ids,
    );

    const details = {
      modelName: DashboardIcon.PreAuthorization,
      action: NotificationTag.Deleted,
      item: items,
    };
    this.notificationService.handleNoticationEvent({ profile, details });

    return items;
  }

  @Mutation(() => [PreauthorisationModel])
  async archivePreauthorizations(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) ids: string[],
    @Args('archive') archive: boolean,
  ): Promise<PreauthorisationModel[]> {
    return this.preAuthService.archivePreauthorizations(profile, ids, archive);
  }

  @Mutation(() => [PreauthorisationModel])
  async movePreauthorizationToClaims(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) ids: string[],
    @Args({ name: 'origin' }) origin: string,
  ): Promise<PreauthorisationModel[]> {
    const [items, claims] =
      await this.preAuthService.movePreauthorizationToClaims(profile, ids);
    items?.forEach((item) => {
      const details = {
        modelName: DashboardIcon.PreAuthorization,
        action: NotificationTag.Moved,
        item,
      };
      this.notificationService.handleNoticationEvent({ profile, details });
      this.pubSub.publish(HMOPreauthorizationUpdated, {
        [HMOPreauthorizationUpdated]: item,
      });
    });
    claims?.forEach(async (claim) => {
      const availableVettingGroups =
        await this.preAuthService.getHmoProviderRoles(
          claim.providerId || claim.provider.id || profile.hmoId,
        );
      const details = {
        modelName: DashboardIcon.Claim,
        action: NotificationTag.Submitted,
        item: claim,
      };
      this.notificationService.handleNoticationEvent({ profile, details });
      this.pubSub.publish(HMOClaimUpdated, {
        [HMOClaimUpdated]: {
          ...claim,
          __availableVettingGroups: availableVettingGroups,
        },
      });
    });

    if (claims?.length) {
      this.hmoClaimService.sendEnrolleeHmoClaimEmail(
        claims.map((c) => c.id),
        origin,
      );
    }

    return items;
  }

  @Query(() => PreAuthUtilisationsModel, { nullable: true })
  async getUtilization(
    @CurrentProfile() profile: ProfileModel,
    @Args('paCode') paCode: string,
    @Args('utilizationId') utilizationId: string,
    @Args({ name: 'utilType', type: () => String, nullable: true })
    utilType?: string,
  ): Promise<PreAuthUtilisationsModel | null> {
    return this.preAuthService.getUtilization(
      profile,
      paCode,
      utilizationId,
      utilType,
    );
  }

  @Query(() => [HospitalModel])
  getProvidersWithRequestedPreauthorizations(
    @CurrentProfile() mutator: ProfileModel,
    @Args('filterOptions') filterOptions: PreauthorizationFilterInput,
  ): Promise<HospitalModel[]> {
    return this.preAuthService.getProvidersWithRequestedPreauthorizations(
      mutator,
      filterOptions,
    );
  }

  @Query(() => PreauthorizationSummary)
  getPreauthorizationSummary(
    @CurrentProfile() mutator: ProfileModel,
    @Args({ name: 'filterOptions', type: () => PreauthorizationFilterInput })
    filterInput: PreauthorizationFilterInput,
    @Args('profileId', { nullable: true }) profileId?: string,
    @Args('providerId', { nullable: true }) providerId?: string,
  ): Promise<PreauthorizationSummary> {
    return this.preAuthService.getPreauthorizationSummary(
      mutator,
      filterInput,
      profileId,
      providerId,
    );
  }

  @Mutation(() => PreAuthUtilisationsModel)
  updatePreauthUtilizationQuantity(
    @CurrentProfile() mutator: ProfileModel,
    @Args({ name: 'id' }) utilizationId: string,
    @Args({ name: 'quantity' }) quantity: number,
  ): Promise<PreAuthUtilisationsModel> {
    return this.preAuthService.updatePreauthUtilizationQuantity(
      mutator,
      utilizationId,
      quantity,
    );
  }

  @Mutation(() => PreauthorisationModel)
  async flagPreauthorization(
    @CurrentProfile() mutator: ProfileModel,
    @Args({ name: 'id' }) id: string,
    @Args({ name: 'flag' }) flag: string,
    @Args({ name: 'unset' }) unset: boolean,
  ): Promise<PreauthorisationModel> {
    const pa = await this.preAuthService.flagPreauthorization(
      mutator,
      id,
      flag,
      unset,
    );
    this.pubSub.publish(PreauthorizationFlagged, {
      [PreauthorizationFlagged]: {
        ...pa,
        hospitalId: mutator.hospitalId,
      },
    });

    return pa;
  }

  @Mutation(() => [PreAuthUtilisationsModel])
  async flagUtilizations(
    @CurrentProfile() mutator: ProfileModel,
    @Args({
      name: 'utilizationIds',
      nullable: 'itemsAndList',
      type: () => [String],
    })
    utilizationIds: string[],
    @Args({ name: 'flag' }) flag: string,
    @Args({ name: 'unset', type: () => Boolean }) unset: boolean,
  ): Promise<PreAuthUtilisationsModel[]> {
    const utils = await this.preAuthService.flagUtilizations(
      mutator,
      utilizationIds,
      flag,
      unset,
    );

    utils.forEach((util) => {
      this.pubSub.publish(UtilizationFlagged, {
        [UtilizationFlagged]: {
          ...util,
          hospitalId: mutator.hospitalId,
        },
      });
    });

    return utils;
  }

  @ResolveField(() => String, { name: 'grandTotal', defaultValue: '0' })
  getGrandTotal(@Parent() root: PreauthorisationModel): string {
    return root.utilizations
      .reduce(
        (acc: number, curr) => acc + Number(curr.price) * Number(curr.quantity),
        0,
      )
      .toString();
  }

  @ResolveField(() => String, { name: 'totalQuantity', defaultValue: '0' })
  getTotalQuantity(@Parent() root: PreauthorisationModel): string {
    return root.utilizations
      .reduce((acc, curr) => acc + Number(curr.quantity), 0)
      .toString();
  }

  @ResolveField(() => HmoProviderModel, { name: 'provider' })
  async getProvider(
    @Parent() root: PreauthorisationModel,
  ): Promise<HmoProviderModel> {
    if (root.provider) {
      return root.provider;
    }
    const provider = await this.preAuthService.getHmoProvider(root.providerId);
    return provider;
  }

  @Subscription(() => PreAuthUtilisationsModel, {
    name: UtilizationUpdated,
    filter: filterUtilizationUpdated,
  })
  updateUtilizationSubsHandler(
    @Args('profileId') _profileId: string,
    @Args('hospitalId') _hospitalId: string,
    @Args('hmoProviderId', { nullable: true }) _hmoProviderId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(UtilizationUpdated);
  }

  @Subscription(() => PreauthorisationModel, {
    name: PreauthorizationFlagged,
    filter: filterPreauthorizationFlagged,
  })
  flagPreauthorizationSubsHandler(
    @Args('hospitalId') _hospitalId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(PreauthorizationFlagged);
  }

  @Subscription(() => PreAuthUtilisationsModel, {
    name: UtilizationFlagged,
    filter: filterUtilizationFlagged,
  })
  flagUtilizationSubsHandler(
    @Args('hospitalId') _hospitalId: string,
  ): AsyncIterator<unknown> {
    return this.pubSub.asyncIterator(UtilizationFlagged);
  }
}
