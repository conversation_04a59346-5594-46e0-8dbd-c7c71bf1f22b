import { Test, TestingModule } from '@nestjs/testing';
import { PreauthorisationService } from './pre-authorisation.service';
import { PreauthorisationStatus } from '../interface/preauthorizations.interface';

jest.mock('@aws-sdk/client-bedrock-runtime', () => {
  const mockInvokeModelCommand = jest.fn();
  return {
    BedrockRuntimeClient: jest.fn(() => ({
      send: jest.fn(),
    })),
    InvokeModelCommand: mockInvokeModelCommand,
  };
});

describe('PreauthorisationService - handleMedicationPreauthorizationStatusUpdate', () => {
  let service: PreauthorisationService;
  let mockPubSub: any;
  let mockManager: any;
  let mockRepo: any;

  beforeAll(async () => {
    mockPubSub = {
      publish: jest.fn().mockResolvedValue(undefined),
    };

    mockRepo = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    mockManager = {
      withRepository: jest.fn().mockReturnValue(mockRepo),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: PreauthorisationService,
          useFactory: () => {
            // Create a service instance with properly mocked dependencies
            const serviceInstance = Object.create(
              PreauthorisationService.prototype,
            );
            serviceInstance.repo = mockRepo;
            serviceInstance.manager = mockManager;
            serviceInstance.pubSub = mockPubSub;

            // Bind the method to the service instance
            serviceInstance.handleMedicationPreauthorizationStatusUpdate =
              PreauthorisationService.prototype.handleMedicationPreauthorizationStatusUpdate.bind(
                serviceInstance,
              );

            return serviceInstance;
          },
        },
      ],
    }).compile();

    service = module.get<PreauthorisationService>(PreauthorisationService);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('handleMedicationPreauthorizationStatusUpdate', () => {
    it('should update utilization with matching medication name and set medicationClaimId', async () => {
      // Arrange
      const preAuthId = 'preauth-123';
      const medicationName = 'Paracetamol';
      const hmoClaimId = 'claim-456';

      const mockUtilizations = [
        {
          id: 'util-1',
          type: 'Paracetamol',
          medicationClaimId: null,
        },
        {
          id: 'util-2',
          type: 'Ibuprofen',
          medicationClaimId: null,
        },
      ];

      const mockPreauthorization = {
        id: preAuthId,
        status: PreauthorisationStatus.Pending,
        claimStatus: 'Pending',
        utilizations: mockUtilizations,
        createdBy: {},
        profile: {},
        provider: {},
        updatedBy: {},
      };

      mockRepo.findOne.mockResolvedValue(mockPreauthorization);
      mockRepo.save.mockResolvedValue(mockPreauthorization);

      // Act
      await service.handleMedicationPreauthorizationStatusUpdate(
        preAuthId,
        medicationName,
        hmoClaimId,
      );

      // Assert
      expect(mockRepo.findOne).toHaveBeenCalledWith({
        where: { id: preAuthId },
        relations: [
          'utilizations',
          'createdBy',
          'profile',
          'provider',
          'updatedBy',
        ],
      });

      expect(mockUtilizations[0].medicationClaimId).toBe(hmoClaimId);
      expect(mockUtilizations[1].medicationClaimId).toBeNull();
      expect(mockPreauthorization.claimStatus).toBe('Partially Submitted');
      expect(mockRepo.save).toHaveBeenCalledWith(mockPreauthorization);
      expect(mockPubSub.publish).toHaveBeenCalledWith(
        'HMOPreauthorizationUpdated',
        {
          HMOPreauthorizationUpdated: mockPreauthorization,
        },
      );
    });

    it('should set status to Submitted when all utilizations have medicationClaimId', async () => {
      // Arrange
      const preAuthId = 'preauth-123';
      const medicationName = 'Ibuprofen';
      const hmoClaimId = 'claim-456';

      const mockUtilizations = [
        {
          id: 'util-1',
          type: 'Paracetamol',
          medicationClaimId: 'existing-claim-1',
        },
        {
          id: 'util-2',
          type: 'Ibuprofen',
          medicationClaimId: null,
        },
      ];

      const mockPreauthorization = {
        id: preAuthId,
        status: PreauthorisationStatus.Pending,
        claimStatus: 'Pending',
        utilizations: mockUtilizations,
        createdBy: {},
        profile: {},
        provider: {},
        updatedBy: {},
      };

      mockRepo.findOne.mockResolvedValue(mockPreauthorization);
      mockRepo.save.mockResolvedValue(mockPreauthorization);

      // Act
      await service.handleMedicationPreauthorizationStatusUpdate(
        preAuthId,
        medicationName,
        hmoClaimId,
      );

      // Assert
      expect(mockUtilizations[1].medicationClaimId).toBe(hmoClaimId);
      expect(mockPreauthorization.status).toBe(
        PreauthorisationStatus.Submitted,
      );
      expect(mockPreauthorization.claimStatus).toBe('Submitted');
      expect(mockRepo.save).toHaveBeenCalledWith(mockPreauthorization);
      expect(mockPubSub.publish).toHaveBeenCalled();
    });

    it('should set claimStatus to Partially Submitted when some utilizations have medicationClaimId', async () => {
      // Arrange
      const preAuthId = 'preauth-123';
      const medicationName = 'Paracetamol';
      const hmoClaimId = 'claim-456';

      const mockUtilizations = [
        {
          id: 'util-1',
          type: 'Paracetamol',
          medicationClaimId: null,
        },
        {
          id: 'util-2',
          type: 'Ibuprofen',
          medicationClaimId: null,
        },
        {
          id: 'util-3',
          type: 'Consultation',
          medicationClaimId: null,
        },
      ];

      const mockPreauthorization = {
        id: preAuthId,
        status: PreauthorisationStatus.Pending,
        claimStatus: 'Pending',
        utilizations: mockUtilizations,
        createdBy: {},
        profile: {},
        provider: {},
        updatedBy: {},
      };

      mockRepo.findOne.mockResolvedValue(mockPreauthorization);
      mockRepo.save.mockResolvedValue(mockPreauthorization);

      // Act
      await service.handleMedicationPreauthorizationStatusUpdate(
        preAuthId,
        medicationName,
        hmoClaimId,
      );

      // Assert
      expect(mockUtilizations[0].medicationClaimId).toBe(hmoClaimId);
      expect(mockUtilizations[1].medicationClaimId).toBeNull();
      expect(mockUtilizations[2].medicationClaimId).toBeNull();
      expect(mockPreauthorization.status).toBe(PreauthorisationStatus.Pending);
      expect(mockPreauthorization.claimStatus).toBe('Partially Submitted');
      expect(mockRepo.save).toHaveBeenCalledWith(mockPreauthorization);
    });

    it('should not update utilizations that do not match medication name', async () => {
      // Arrange
      const preAuthId = 'preauth-123';
      const medicationName = 'NonExistentMedication';
      const hmoClaimId = 'claim-456';

      const mockUtilizations = [
        {
          id: 'util-1',
          type: 'Paracetamol',
          medicationClaimId: null,
        },
        {
          id: 'util-2',
          type: 'Ibuprofen',
          medicationClaimId: null,
        },
      ];

      const mockPreauthorization = {
        id: preAuthId,
        status: PreauthorisationStatus.Pending,
        claimStatus: 'Pending',
        utilizations: mockUtilizations,
        createdBy: {},
        profile: {},
        provider: {},
        updatedBy: {},
      };

      mockRepo.findOne.mockResolvedValue(mockPreauthorization);
      mockRepo.save.mockResolvedValue(mockPreauthorization);

      // Act
      await service.handleMedicationPreauthorizationStatusUpdate(
        preAuthId,
        medicationName,
        hmoClaimId,
      );

      // Assert
      expect(mockUtilizations[0].medicationClaimId).toBeNull();
      expect(mockUtilizations[1].medicationClaimId).toBeNull();
      expect(mockPreauthorization.status).toBe(PreauthorisationStatus.Pending);
      expect(mockPreauthorization.claimStatus).toBe('Pending');
      expect(mockRepo.save).toHaveBeenCalledWith(mockPreauthorization);
    });

    it('should work with custom EntityManager when provided', async () => {
      // Arrange
      const preAuthId = 'preauth-123';
      const medicationName = 'Paracetamol';
      const hmoClaimId = 'claim-456';
      const customManager = {
        withRepository: jest.fn(),
      };

      const mockUtilizations = [
        {
          id: 'util-1',
          type: 'Paracetamol',
          medicationClaimId: null,
        },
      ];

      const mockPreauthorization = {
        id: preAuthId,
        status: PreauthorisationStatus.Pending,
        claimStatus: 'Partially Submitted',
        utilizations: mockUtilizations,
        createdBy: {},
        profile: {},
        provider: {},
        updatedBy: {},
      };

      const customMockRepo = {
        findOne: jest.fn().mockResolvedValue(mockPreauthorization),
        save: jest.fn().mockResolvedValue(mockPreauthorization),
      };

      customManager.withRepository.mockReturnValue(customMockRepo);

      // Act
      await service.handleMedicationPreauthorizationStatusUpdate(
        preAuthId,
        medicationName,
        hmoClaimId,
        customManager as any,
      );

      // Assert
      expect(customManager.withRepository).toHaveBeenCalledWith(
        expect.anything(),
      );
      expect(customMockRepo.findOne).toHaveBeenCalled();
      expect(customMockRepo.save).toHaveBeenCalled();
      expect(mockUtilizations[0].medicationClaimId).toBe(hmoClaimId);
    });

    it('should handle multiple utilizations with same medication name', async () => {
      // Arrange
      const preAuthId = 'preauth-123';
      const medicationName = 'Paracetamol';
      const hmoClaimId = 'claim-456';

      const mockUtilizations = [
        {
          id: 'util-1',
          type: 'Paracetamol',
          medicationClaimId: null,
        },
        {
          id: 'util-2',
          type: 'Paracetamol',
          medicationClaimId: null,
        },
        {
          id: 'util-3',
          type: 'Ibuprofen',
          medicationClaimId: null,
        },
      ];

      const mockPreauthorization = {
        id: preAuthId,
        status: PreauthorisationStatus.Pending,
        claimStatus: 'Pending',
        utilizations: mockUtilizations,
        createdBy: {},
        profile: {},
        provider: {},
        updatedBy: {},
      };

      mockRepo.findOne.mockResolvedValue(mockPreauthorization);
      mockRepo.save.mockResolvedValue(mockPreauthorization);

      // Act
      await service.handleMedicationPreauthorizationStatusUpdate(
        preAuthId,
        medicationName,
        hmoClaimId,
      );

      // Assert
      expect(mockUtilizations[0].medicationClaimId).toBe(hmoClaimId);
      expect(mockUtilizations[1].medicationClaimId).toBe(hmoClaimId);
      expect(mockUtilizations[2].medicationClaimId).toBeNull();
      expect(mockPreauthorization.claimStatus).toBe('Partially Submitted');
    });

    it('should handle empty utilizations array', async () => {
      // Arrange
      const preAuthId = 'preauth-123';
      const medicationName = 'Paracetamol';
      const hmoClaimId = 'claim-456';

      const mockPreauthorization = {
        id: preAuthId,
        status: PreauthorisationStatus.Pending,
        claimStatus: 'Pending',
        utilizations: [],
        createdBy: {},
        profile: {},
        provider: {},
        updatedBy: {},
      };

      mockRepo.findOne.mockResolvedValue(mockPreauthorization);
      mockRepo.save.mockResolvedValue(mockPreauthorization);

      // Act
      await service.handleMedicationPreauthorizationStatusUpdate(
        preAuthId,
        medicationName,
        hmoClaimId,
      );

      // Assert
      expect(mockPreauthorization.status).toBe(
        PreauthorisationStatus.Submitted,
      );
      expect(mockPreauthorization.claimStatus).toBe('Submitted');
      expect(mockRepo.save).toHaveBeenCalledWith(mockPreauthorization);
    });

    it('should publish subscription event with correct payload', async () => {
      // Arrange
      const preAuthId = 'preauth-123';
      const medicationName = 'Paracetamol';
      const hmoClaimId = 'claim-456';

      const mockUtilizations = [
        {
          id: 'util-1',
          type: 'Paracetamol',
          medicationClaimId: null,
        },
      ];

      const mockPreauthorization = {
        id: preAuthId,
        status: PreauthorisationStatus.Pending,
        claimStatus: 'Pending',
        utilizations: mockUtilizations,
        createdBy: {},
        profile: {},
        provider: {},
        updatedBy: {},
      };

      mockRepo.findOne.mockResolvedValue(mockPreauthorization);
      mockRepo.save.mockResolvedValue(mockPreauthorization);

      // Act
      await service.handleMedicationPreauthorizationStatusUpdate(
        preAuthId,
        medicationName,
        hmoClaimId,
      );

      // Assert
      expect(mockPubSub.publish).toHaveBeenCalledWith(
        'HMOPreauthorizationUpdated',
        {
          HMOPreauthorizationUpdated: mockPreauthorization,
        },
      );
    });
  });
});
