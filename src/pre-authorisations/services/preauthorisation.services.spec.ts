import { HttpService } from '@nestjs/axios';
import { Provider } from '@nestjs/common';
import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken, TypeOrmModule } from '@nestjs/typeorm';
import { <PERSON><PERSON>ty<PERSON>anager, IsNull, Not } from 'typeorm';
import { PreauthorisationService } from './pre-authorisation.service';
import { AuthorizationModule } from '../../authorization/authorization.module';
import { TestDataSourceOptions } from '../../data-source';
import { extendModel } from '../../database/extendModel';
import { HmoProviderModule } from '../../hmo-providers/hmo-provider.module';
import { HmoProviderModel } from '../../hmo-providers/models/hmo-provider.model';
import { ProfileModel } from '../../users/models/profile.model';
import { PreauthorisationModel } from '../models/preauthorisation.model';
import { PreAuthUtilisationsModel } from '../models/utilisations.model';
import { IPreauthorizationRepository } from '../repositories/pre-authorization.repositories';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { loggerMock } from '@clinify/__mocks__/logger';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { HmoProviderService } from '@clinify/hmo-providers/services/hmo-provider.service';
import { AiProducer } from '@clinify/integrations/ai/producers/ai.producer';
import {
  DefaultInvImage,
  InventoryUpload,
} from '@clinify/integrations/inventory-blk/entities/FileUpload.entity';
import { PriceUpload } from '@clinify/integrations/price-blk/entities/PriceFileUpload.entity';
import { UserModel } from '@clinify/users/models/user.model';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { preauthorizationFactory } from '@mocks/factories/preauthorization.factory';
import { mockUser } from '@mocks/factories/user.factory';

jest.mock('@aws-sdk/client-bedrock-runtime', () => {
  const mockInvokeModelCommand = jest.fn();
  return {
    BedrockRuntimeClient: jest.fn(() => ({
      send: jest.fn(),
    })),
    InvokeModelCommand: mockInvokeModelCommand,
  };
});

const user: UserModel = mockUser;
const profile = user.defaultProfile;
const preAuthList = preauthorizationFactory.buildList(3);
const preAuthData = preAuthList[0];
const MockHmoProviderService = {
  getHmoProviderModule: jest.fn(() => {
    return {
      synchronizePreauthorizations: jest.fn(() => {
        return Promise.resolve(preAuthData.utilizations);
      }),
      getAgreedTariff: jest.fn(() => 200),
      requestPreAuthorization: jest.fn(() => {
        return Promise.resolve(preAuthData.utilizations);
      }),
      movePreauthorizationToClaims: jest.fn(() => {
        return Promise.resolve(preAuthData.utilizations);
      }),
    };
  }),
  movePreAuthUtilizationToClaim: jest.fn(() => preAuthList),
  getAgreedTariff: jest.fn(() => ({ cost: 200, label: 'label' })),
  requestPreAuthorization: jest.fn(() => preAuthList[0]),
  updatePreAuthorization: jest.fn(),
};
const MockPreauthorisationRepo = {
  deletePreauthorizations: jest.fn(() => [preAuthData]),
  getPreauthorization: jest.fn(() => preAuthData),
  getPreAuthDetail: jest.fn(() => preAuthData),
  getPreAuthDetailBulk: jest.fn(() => preAuthList),
  findByHospital: jest.fn(() => preAuthList),
  findByProfile: jest.fn(() => preAuthList),
  archivePreauthorizations: jest.fn(() => preAuthList),
  save: jest.fn((v: any) => v),
  find: jest.fn(() => [preAuthList[0]]),
  findOne: jest.fn(() => preAuthList[0]),
  getProvidersWithRequestedPreauthorizations: jest.fn(() => []),
  getPreauthorizationSummary: jest.fn(() => ({
    totalPreauthorizations: 10,
    totalApprovedApprovedPreauthorizations: 5,
    totalRejectedPreauthorizations: 2,
    totalPendingPreauthorizations: 3,
    totalPreauthorizationsAmount: 5000,
    totalApprovedApprovedPreauthorizationsAmount: 3000,
    totalRejectedPreauthorizationsAmount: 1000,
    totalPendingPreauthorizationsAmount: 1000,
  })),
};

const MockProfileRepository = {
  findOne: jest.fn(() => profile),
};

const commonManagerMethods = {
  save: jest.fn(() => preAuthData),
  delete: jest.fn(),
};
const mockAiProducer = {
  addClaimApprovalQueue: jest.fn(),
  addPreAuthApprovalQueue: jest.fn(),
};

const mockPubSub = {
  publish: jest.fn(),
  subscribe: jest.fn(),
  unsubscribe: jest.fn(),
  asyncIterator: jest.fn(),
};
const ManagerMock = {
  findOneOrFail: jest.fn(() => Promise.resolve(profile)) as jest.Mock<
    Promise<unknown>,
    []
  >,
  findOne: jest.fn(() => profile),
  getCustomRepository: jest.fn(() => ({
    save: jest.fn((v) => v),
  })),
  withRepository: jest.fn().mockReturnValue(commonManagerMethods),
  find: jest.fn(() => [preAuthData]),
  update: jest.fn(() => ({ affected: 1 })),
  save: jest.fn(() => preAuthData),
  transaction: jest.fn((callback) => callback(ManagerMock)),
};

describe('PreAuthorizationService', () => {
  let service: PreauthorisationService;
  let preAuthRepo: Provider<IPreauthorizationRepository>;

  beforeEach(async () => {
    preAuthRepo = extendModel(PreauthorisationModel, MockPreauthorisationRepo);
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        AuthorizationModule,
        HmoProviderModule,
        TypeOrmModule.forFeature([
          PreAuthUtilisationsModel,
          PreauthorisationModel,
          HmoProviderModel,
        ]),
      ],
      providers: [
        PreauthorisationService,
        preAuthRepo,
        HmoProviderService,
        extendModel(ProfileModel, MockProfileRepository),
        extendModel(HmoClaimModel, {}),
        {
          provide: HttpService,
          useValue: {},
        },
        {
          provide: AiProducer,
          useValue: mockAiProducer,
        },
        {
          provide: PUB_SUB,
          useValue: mockPubSub,
        },
        { ...loggerMock },
      ],
    })
      .overrideProvider(getModelToken(InventoryUpload.name))
      .useValue({})
      .overrideProvider(getModelToken(DefaultInvImage.name))
      .useValue({})
      .overrideProvider(getModelToken(PriceUpload.name))
      .useValue({})
      .overrideProvider(HmoProviderService)
      .useValue(MockHmoProviderService)
      .overrideProvider(getRepositoryToken(PreauthorisationModel))
      .useValue(MockPreauthorisationRepo)
      .overrideProvider(getRepositoryToken(HmoClaimModel))
      .useValue({})
      .overrideProvider(EntityManager)
      .useValue(ManagerMock)
      .compile();

    service = module.get<PreauthorisationService>(PreauthorisationService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(service).toBeTruthy();
  });

  it('addPreauthorization() should add preauthorization', async () => {
    const input = preAuthData;
    delete input.profile;

    const response = await service.addPreauthorization(profile, input);
    expect(response.creatorId).toEqual(profile.id);
  });

  it('addPreauthorization() should add preauthorization with Clinify', async () => {
    const input = preAuthData;
    delete input.profile;
    MockHmoProviderService.getHmoProviderModule.mockReturnValueOnce({
      isClinify: true,
    } as any);
    const response = await service.addPreauthorization(profile, input);
    expect(response.creatorId).toEqual(profile.id);
    expect(MockHmoProviderService.requestPreAuthorization).toBeCalled();
  });

  it('deletePreauthorizations() should delete preauthorization', async () => {
    const response = await service.deletePreauthorizations(profile, ['id']);
    expect(
      MockPreauthorisationRepo.deletePreauthorizations,
    ).toHaveBeenCalledWith(profile, ['id']);
    expect(response).toEqual([preAuthData]);
  });

  it('getPreauthorization() should get Preauthorization', async () => {
    const response = await service.getPreauthorization(profile, 'id');
    expect(MockPreauthorisationRepo.getPreauthorization).toHaveBeenCalledWith(
      profile,
      'id',
    );
    expect(response).toEqual(preAuthData);
  });

  it('getAgreedTariff() should get AgreedTariff', async () => {
    const input = {
      providerId: 'id',
      id: 'some-id',
      position: 50,
      enrolleeId: 'enrolleeId',
      treatmentDate: new Date(),
    };
    const response = await service.getAgreedTariff(user.defaultProfile, input);

    expect(response.amount).toEqual(200);
  });

  it('getAgreedTariff() should get AgreedTariff with Clinify', async () => {
    const input = {
      providerId: 'id',
      id: 'some-id',
      position: 50,
      enrolleeId: 'enrolleeId',
      treatmentDate: new Date(),
    };
    MockHmoProviderService.getHmoProviderModule.mockReturnValueOnce({
      isClinify: true,
    } as any);
    const response = await service.getAgreedTariff(user.defaultProfile, input);

    expect(response.amount).toEqual(200);
    expect(MockHmoProviderService.getAgreedTariff).toBeCalled();
  });

  it('getAgreedTariff() should get prognosis AgreedTariff', async () => {
    MockHmoProviderService.getHmoProviderModule.mockReturnValueOnce({
      isPrognosis: true,
      getAgreedTariff: jest.fn(() => {
        return {
          cost: 390,
          label: 'label',
        };
      }),
    } as any);
    const input = {
      providerId: 'id',
      id: 'some-id',
      position: 50,
      prognosis: true,
      enrolleeId: 'enrolleeId',
      treatmentDate: new Date(),
    };
    const response = await service.getAgreedTariff(user.defaultProfile, input);

    expect(response.amount).toEqual(390);
    expect(response.label).toEqual('label');
  });

  it('findByHospital() should findByHospital', async () => {
    const response = await service.findByHospital(
      'id',
      {},
      user.defaultProfile,
    );
    expect(MockPreauthorisationRepo.findByHospital).toHaveBeenCalledWith(
      'id',
      {},
      user.defaultProfile,
    );
    expect(response).toEqual(preAuthList);
  });

  it('findByProfile() should findByProfile', async () => {
    const response = await service.findByProfile('id', {}, profile);
    expect(MockPreauthorisationRepo.findByProfile).toHaveBeenCalledWith(
      'id',
      {},
      profile,
    );
    expect(response).toEqual(preAuthList);
  });

  it('archivePreauthorizations() should archivePreauthorizations', async () => {
    const profile = user.defaultProfile;
    const response = await service.archivePreauthorizations(
      profile,
      ['id'],
      true,
    );
    expect(
      MockPreauthorisationRepo.archivePreauthorizations,
    ).toHaveBeenCalledWith(profile, ['id'], true);
    expect(response).toEqual(preAuthList);
  });

  it('synchronizePreauthorizations() should call synchronizePreauthorizations with clinify', async () => {
    MockHmoProviderService.getHmoProviderModule.mockReturnValueOnce({
      isClinify: true,
    } as any);
    const response = await service.synchronizePreauthorizations(profile, [
      'id',
    ]);
    expect(response.length).toEqual(1);
    expect(ManagerMock.find).toBeCalled();
  });
  it('synchronizePreauthorizations() should call synchronizePreauthorizations from repo and return value', async () => {
    const response = await service.synchronizePreauthorizations(profile, [
      'id',
    ]);
    expect(response.length).toEqual(1);
  });

  it('movePreauthorizationToClaims() should call movePreauthorizationToClaims from repo and return value', async () => {
    const response = await service.movePreauthorizationToClaims(
      user.defaultProfile,
      ['id'],
    );
    expect(response.length).toEqual(2);
  });
  it('movePreauthorizationToClaims() should call movePreauthorizationToClaims  with clinify', async () => {
    MockHmoProviderService.getHmoProviderModule.mockReturnValueOnce({
      isClinify: true,
    } as any);
    const [response] = await service.movePreauthorizationToClaims(
      user.defaultProfile,
      ['id'],
    );

    expect(response.length).toEqual(1);
    expect(MockHmoProviderService.getHmoProviderModule).toBeCalled();
    expect(MockHmoProviderService.movePreAuthUtilizationToClaim).toBeCalled();
  });
  it('getHmoProvider should return hmoProvider entity by id', async () => {
    ManagerMock.findOneOrFail = jest.fn(() =>
      Promise.resolve({ id: 'hmo-provider-id' }),
    );
    const response = await service.getHmoProvider('id');
    expect(response).toEqual(
      expect.objectContaining({ id: 'hmo-provider-id' }),
    );
  });
  it('updatePreauthUtilizationQuantity', async () => {
    await service.updatePreauthUtilizationQuantity(profile, 'id', 10);
    expect(ManagerMock.update).toBeCalled();
  });

  it('getUtilization(): should call getUtilization service method', async () => {
    await service.getUtilization(profile, 'pa-code', 'utilization-id');
    expect(ManagerMock.findOne).toHaveBeenCalledWith(PreAuthUtilisationsModel, {
      where: { paCode: 'pa-code', utilizationId: 'utilization-id' },
    });
  });

  it('getUtilization(): should call getUtilization service method with pre auth filter', async () => {
    await service.getUtilization(
      profile,
      'pa-code',
      'utilization-id',
      'PREAUTH',
    );
    expect(ManagerMock.findOne).toHaveBeenCalledWith(PreAuthUtilisationsModel, {
      where: {
        paCode: 'pa-code',
        utilizationId: 'utilization-id',
        preAuthorizationId: Not(IsNull()),
      },
    });
  });

  it('getUtilization(): should call getUtilization service method with claims filter', async () => {
    await service.getUtilization(profile, 'pa-code', 'utilization-id', 'CLAIM');
    expect(ManagerMock.findOne).toHaveBeenCalledWith(PreAuthUtilisationsModel, {
      where: {
        paCode: 'pa-code',
        utilizationId: 'utilization-id',
        hmoClaimId: Not(IsNull()),
      },
    });
  });

  it('flagPreauthorization', async () => {
    await service.flagPreauthorization(profile, 'id', 'flag', false);
    expect(MockPreauthorisationRepo.save).toBeCalled();
  });

  it('getProvidersWithRequestedPreauthorizations', async () => {
    await service.getProvidersWithRequestedPreauthorizations(profile, {
      archive: false,
    });
    expect(
      MockPreauthorisationRepo.getProvidersWithRequestedPreauthorizations,
    ).toHaveBeenCalledWith(expect.anything(), profile, { archive: false });
  });

  it('updatePreauthorization(): should not call updatePreauthorization service', async () => {
    const mutator = profileFactory.build();
    const input = preauthorizationFactory.build();

    MockHmoProviderService.getHmoProviderModule.mockReturnValueOnce({
      isClinify: false,
    } as any);

    const res = await service.updatePreauthorization(
      mutator,
      'pre-auth-id',
      input,
    );
    expect(res).toBeFalsy();
    expect(
      MockHmoProviderService.updatePreAuthorization,
    ).not.toHaveBeenCalled();
  });

  it('updatePreauthorization(): should call updatePreauthorization service', async () => {
    const mutator = profileFactory.build();
    const input = preauthorizationFactory.build();

    MockHmoProviderService.getHmoProviderModule.mockReturnValueOnce({
      isClinify: true,
    } as any);
    MockHmoProviderService.updatePreAuthorization.mockReturnValueOnce({
      id: 'pre-auth-id',
    } as any);
    await service.updatePreauthorization(mutator, 'pre-auth-id', input);

    expect(
      MockHmoProviderService.updatePreAuthorization,
    ).toHaveBeenLastCalledWith(mutator, 'pre-auth-id', input, ManagerMock);
  });

  it('flagUtilization', async () => {
    ManagerMock.update.mockImplementationOnce(() => ({ affected: 1 }));
    await service.flagUtilizations(profile, ['id'], 'flag', false);
    expect(ManagerMock.save).toBeCalled();
  });

  it('getPreauthorizationSummary should call getPreauthorizationSummary from repo and return value', async () => {
    const options = {
      skip: 0,
      take: 10,
      status: 'Approved',
      dateRange: { from: new Date(), to: new Date() },
    };
    const profileId = 'profile-id';
    const providerId = 'provider-id';

    const response = await service.getPreauthorizationSummary(
      profile,
      options,
      profileId,
      providerId,
    );

    expect(
      MockPreauthorisationRepo.getPreauthorizationSummary,
    ).toHaveBeenCalledWith(
      expect.anything(),
      profile,
      options,
      profileId,
      providerId,
    );

    expect(response).toEqual(
      expect.objectContaining({
        totalPreauthorizations: 10,
        totalApprovedApprovedPreauthorizations: 5,
        totalRejectedPreauthorizations: 2,
        totalPendingPreauthorizations: 3,
        totalPreauthorizationsAmount: 5000,
        totalApprovedApprovedPreauthorizationsAmount: 3000,
        totalRejectedPreauthorizationsAmount: 1000,
        totalPendingPreauthorizationsAmount: 1000,
      }),
    );
  });
});
