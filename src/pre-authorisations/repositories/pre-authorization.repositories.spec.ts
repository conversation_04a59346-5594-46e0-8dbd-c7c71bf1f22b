/* eslint-disable max-lines */
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import Chance from 'chance';
import moment from 'moment/moment';
import { DataSource, EntityManager } from 'typeorm';
import {
  CustomPreauthorizationRepoMethods,
  IPreauthorizationRepository,
} from './pre-authorization.repositories';
import { TestDataSourceOptions } from '../../data-source';
import * as db from '../../database';
import { extendDSRepo, extendModel } from '../../database/extendModel';
import {
  PreauthDateFilterType,
  TimeSortOrder,
} from '../inputs/pre-authorisation-filter.input';
import { PreauthorisationModel } from '../models/preauthorisation.model';
import { utilizationFactory } from '@clinify/__mocks__/factories/hmo-claim.factory';
import { preauthorizationFactory } from '@clinify/__mocks__/factories/preauthorization.factory';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { CustomHmoClaimRepoMethods } from '@clinify/hmo-claims/repositories/hmo-claim.repository';
import { HmoProviderRepository } from '@clinify/hmo-providers/repositories/hmo-provider.repository';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { PreAuthUtilisationsModel } from '@clinify/pre-authorisations/models/utilisations.model';
import { UserType } from '@clinify/shared/enums/users';
import { RecordCreator } from '@clinify/shared/validators/filter.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { createHmoProfileFixtures } from '@clinify/utils/tests/hmo-profiles.fixtures';
import { createHospitals } from '@clinify/utils/tests/hospital.fixtures';
import { createPreauthorization } from '@clinify/utils/tests/preauthorization.fixtures';
import { createUsers } from '@clinify/utils/tests/user.fixtures';
import { createHmoProviderFixtures } from '@fixtures/hmo-provider.fixtures';
import { createPartner } from '@fixtures/partner.fixture';

const chance = new Chance();

describe('Pre-authorization repository', () => {
  let preAuthorizations: PreauthorisationModel[];
  let profile: ProfileModel;
  let hospital: HospitalModel;
  let preAuthorization: PreauthorisationModel;
  let manager: EntityManager;
  let repo: IPreauthorizationRepository;

  let module: TestingModule;
  let ds: DataSource;

  const spySlaveQuery = jest.spyOn(db, 'queryWithSlave');

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        extendModel(PreauthorisationModel, CustomPreauthorizationRepoMethods),
        HmoProviderRepository,
        extendModel(HmoClaimModel, CustomHmoClaimRepoMethods),
      ],
    }).compile();

    ds = module.get<DataSource>(DataSource);
    manager = ds.manager;
    repo = extendDSRepo<IPreauthorizationRepository>(
      ds,
      PreauthorisationModel,
      CustomPreauthorizationRepoMethods,
    );
    spySlaveQuery.mockImplementation((qr, pr) => manager.query(qr, pr));
  });

  beforeEach(async () => {
    [hospital] = await createHospitals(manager, 1);
    preAuthorizations = await createPreauthorization(
      manager,
      3,
      undefined,
      undefined,
      undefined,
      hospital,
    );
    preAuthorization = preAuthorizations[0];
    profile = preAuthorization.profile;
    profile.hospital = hospital;
  });

  afterAll(async () => {
    await ds.destroy();
    await module.close();
  });

  it('repo should be defined', () => {
    expect(repo).toBeDefined();
  });

  it('getPreauthorization() it should get preauthorization', async () => {
    const result = await repo.getPreauthorization(profile, preAuthorization.id);
    expect(result.id).toEqual(preAuthorization.id);
  });
  it('getPreauthorization() it should get preauthorization', async () => {
    const [hospital] = await createHospitals(manager, 1, null);
    const [hmoProfile] = await createHmoProfileFixtures(manager);
    const [doctor] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.ClaimReviewer,
    );
    const [enrollee] = await createUsers(
      manager,
      1,
      undefined,
      undefined,
      hmoProfile,
    );
    const utilizations = utilizationFactory.buildList(
      3,
    ) as PreAuthUtilisationsModel[];
    utilizations[0].utilisationStatus = [
      {
        status: 'Approved',
        statusDescription: 'Approved',
        createdDate: new Date(),
        vettingGroup: UserType.ClaimOfficer,
        creatorId: doctor.defaultProfile.id,
        creatorName: doctor.defaultProfile.fullName,
      },
    ];
    const preauthInput =
      preauthorizationFactory.build() as PreauthorisationModel;
    preauthInput.utilizations = utilizations;
    const [preauth] = await createPreauthorization(
      manager,
      1,
      doctor.profiles[0],
      undefined,
      enrollee.defaultProfile,
      hospital,
      undefined,
      undefined,
      [preauthInput],
    );
    const result = await repo.getPreauthorization(
      doctor.defaultProfile,
      preauth.id,
    );
    expect(result.id).toEqual(preauth.id);
    expect(result.utilizations.length).toBe(3);
  });
  it('findByProfile() should find preauth by profile', async () => {
    const res = await repo.findByProfile(
      preAuthorization.profileId,
      {},
      profile,
    );
    expect(res.list.length).toBeGreaterThan(0);
  });

  it('findByProfile() should accept filter', async () => {
    const filter = {
      skip: 0,
      take: 40,
      dateRange: {
        from: new Date(),
        to: new Date(),
      },
      keyword: 'some text',
      status: 'Submitted',
      providerType: 'pharmacy',
    };
    const res = await repo.findByProfile(
      preAuthorization.profileId,
      filter,
      profile,
    );
    expect(res).toHaveProperty('list');
  });

  it('findByProfile() - all filters', async () => {
    const filter = {
      skip: 0,
      take: 40,
      dateRange: {
        from: new Date(),
        to: new Date(),
      },
      keyword: 'some text',
      status: 'Submitted',
      creator: RecordCreator.OTHERS,
      hospitalId: chance.guid({ version: 4 }),
      providerId: chance.guid({ version: 4 }),
    };
    const res = await repo.findByProfile(preAuthorization.profileId, filter, {
      ...profile,
      hmoId: chance.guid({ version: 4 }),
    } as ProfileModel);
    expect(res).toHaveProperty('list');
  });

  it('findByHospital() should find by hospital', async () => {
    const res = await repo.findByHospital(hospital.id, {}, profile);
    expect(res).toHaveProperty('list');
  });

  it('findByHospital() should find by hospital against partnerId', async () => {
    const [partner] = await createPartner(manager);
    const [hospitalA, hospitalB] = await createHospitals(
      manager,
      2,
      null,
      partner,
    );
    await createPreauthorization(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalA,
    );
    await createPreauthorization(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalB,
    );
    const res = await repo.findByHospital(hospital.id, {}, {
      ...profile,
      isPartnerProfile: true,
      partnerId: partner.id,
    } as ProfileModel);
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(10);
  });

  it('findByHospital() should find by hospital against partnerId and providerInsight', async () => {
    const [partner] = await createPartner(manager);
    const [hospitalA, hospitalB] = await createHospitals(
      manager,
      2,
      null,
      partner,
    );
    await createPreauthorization(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalA,
    );
    await createPreauthorization(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalB,
    );
    const res = await repo.findByHospital(
      hospitalA.id,
      {
        providerInsight: true,
      },
      {
        ...profile,
        isPartnerProfile: true,
        partnerId: partner.id,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(5);
  });

  it('findByHospital() should find by hospital against hmoId', async () => {
    const [hospitalA] = await createHospitals(manager);
    const preauths = await createPreauthorization(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalA,
    );
    const res = await repo.findByHospital(hospital.id, {}, {
      ...profile,
      hmoId: preauths[0].providerId,
    } as ProfileModel);
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(5);
  });

  it('findByHospital() should find by hospital against hmoId and provider insight', async () => {
    const [hospitalA] = await createHospitals(manager);
    const preauths = await createPreauthorization(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalA,
    );
    const res = await repo.findByHospital(
      hospitalA.id,
      {
        providerInsight: true,
      },
      {
        ...profile,
        hmoId: preauths[0].providerId,
      } as ProfileModel,
    );
    expect(res).toHaveProperty('list');
    expect(res.totalCount).toBe(5);
  });

  it('deletePreauthorizations() should delete preauthorization', async () => {
    const [pAuth] = await createPreauthorization(manager, 1);
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    expect(
      repo.deletePreauthorizations(pAuth.profile, [pAuth.id]),
    ).rejects.toThrow('Record Cannot Be Deleted');
  });

  it('archivePreauthorizations() should archive preauth', async () => {
    const [res] = await repo.archivePreauthorizations(
      profile,
      [preAuthorization.id],
      true,
    );
    expect(res.archived).toEqual(true);
  });

  it('getProvidersWithRequestedPreauthorizations', async () => {
    const [hospitalA] = await createHospitals(manager);
    await createPreauthorization(
      manager,
      5,
      profile,
      undefined,
      profile,
      hospitalA,
    );

    const res = await repo.getProvidersWithRequestedPreauthorizations(
      ds,
      profile,
      {
        archive: false,
      },
    );
    expect(res.length).toBe(0);
  });

  describe('getPreauthorizationSummary', () => {
    it('should return a summary of preauthorizations', async () => {
      const [hospitalA] = await createHospitals(manager);

      const approvedPreauth =
        preauthorizationFactory.build() as PreauthorisationModel;
      approvedPreauth.utilizations = utilizationFactory.buildList(
        2,
      ) as PreAuthUtilisationsModel[];
      approvedPreauth.utilizations.forEach((u) => {
        u.status = 'Approved';
        u.price = '500';
        u.quantity = '1';
      });

      const rejectedPreauth =
        preauthorizationFactory.build() as PreauthorisationModel;
      rejectedPreauth.utilizations = utilizationFactory.buildList(
        1,
      ) as PreAuthUtilisationsModel[];
      rejectedPreauth.utilizations.forEach((u) => {
        u.status = 'Rejected';
        u.price = '300';
        u.quantity = '1';
      });

      const pendingPreauth =
        preauthorizationFactory.build() as PreauthorisationModel;
      pendingPreauth.utilizations = utilizationFactory.buildList(
        1,
      ) as PreAuthUtilisationsModel[];
      pendingPreauth.utilizations.forEach((u) => {
        u.status = 'Pending';
        u.price = '200';
        u.quantity = '1';
      });

      await createPreauthorization(
        manager,
        1,
        profile,
        undefined,
        profile,
        hospitalA,
        undefined,
        undefined,
        [approvedPreauth, rejectedPreauth, pendingPreauth],
      );

      const result = await repo.getPreauthorizationSummary(ds, profile, {
        archive: false,
      });

      expect(result).toBeDefined();
      expect(Number(result.totalPreauthorizations)).toBeGreaterThanOrEqual(3);
      expect(
        Number(result.totalApprovedApprovedPreauthorizations),
      ).toBeGreaterThanOrEqual(1);
      expect(
        Number(result.totalRejectedPreauthorizations),
      ).toBeGreaterThanOrEqual(1);
      expect(
        Number(result.totalPendingPreauthorizations),
      ).toBeGreaterThanOrEqual(1);

      expect(result.totalPreauthorizationsAmount).toBeDefined();
      expect(result.totalApprovedApprovedPreauthorizationsAmount).toBeDefined();
      expect(result.totalRejectedPreauthorizationsAmount).toBeDefined();
      expect(result.totalPendingPreauthorizationsAmount).toBeDefined();
    });

    it('should filter by status', async () => {
      const [hospitalA] = await createHospitals(manager);

      const approvedPreauth =
        preauthorizationFactory.build() as PreauthorisationModel;
      approvedPreauth.utilizations = utilizationFactory.buildList(
        2,
      ) as PreAuthUtilisationsModel[];
      approvedPreauth.utilizations.forEach((u) => {
        u.status = 'Approved';
        u.price = '500';
        u.quantity = '1';
      });

      await createPreauthorization(
        manager,
        1,
        profile,
        undefined,
        profile,
        hospitalA,
        undefined,
        undefined,
        [approvedPreauth],
      );

      const result = await repo.getPreauthorizationSummary(ds, profile, {
        archive: false,
        status: 'Approved',
      });

      expect(result).toBeDefined();
      expect(
        Number(result.totalApprovedApprovedPreauthorizations),
      ).toBeGreaterThanOrEqual(1);
      expect(result.totalApprovedApprovedPreauthorizationsAmount).toBeDefined();
    });

    it('should filter by date range', async () => {
      const [hospitalA] = await createHospitals(manager);

      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 10);

      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 10);

      const specificDate = new Date();

      const datePreauth =
        preauthorizationFactory.build() as PreauthorisationModel;
      datePreauth.utilizations = utilizationFactory.buildList(
        1,
      ) as PreAuthUtilisationsModel[];
      datePreauth.utilizations.forEach((u) => {
        u.status = 'Approved';
        u.price = '750';
        u.quantity = '1';
      });
      datePreauth.requestDateTime = specificDate;

      await createPreauthorization(
        manager,
        1,
        profile,
        undefined,
        profile,
        hospitalA,
        undefined,
        undefined,
        [datePreauth],
      );

      const result = await repo.getPreauthorizationSummary(ds, profile, {
        archive: false,
        dateRange: {
          from: pastDate,
          to: futureDate,
        },
      });

      expect(result).toBeDefined();
      expect(Number(result.totalPreauthorizations)).toBeGreaterThanOrEqual(1);
      expect(result.totalPreauthorizationsAmount).toBeDefined();
    });

    it('should filter by profileId', async () => {
      const [hospitalA] = await createHospitals(manager);
      const [user] = await createUsers(manager, 1);
      const specificProfile = user.defaultProfile;

      const profilePreauth =
        preauthorizationFactory.build() as PreauthorisationModel;
      profilePreauth.utilizations = utilizationFactory.buildList(
        1,
      ) as PreAuthUtilisationsModel[];
      profilePreauth.utilizations.forEach((u) => {
        u.status = 'Approved';
        u.price = '600';
        u.quantity = '1';
      });

      await createPreauthorization(
        manager,
        1,
        profile,
        undefined,
        specificProfile,
        hospitalA,
        undefined,
        undefined,
        [profilePreauth],
      );

      const result = await repo.getPreauthorizationSummary(
        ds,
        profile,
        {
          archive: false,
        },
        specificProfile.id,
      );

      expect(result).toBeDefined();
      expect(Number(result.totalPreauthorizations)).toBeGreaterThanOrEqual(1);
      expect(result.totalPreauthorizationsAmount).toBeDefined();
    });

    it('should filter by providerId', async () => {
      const [hospitalA] = await createHospitals(manager);
      const providerId = chance.guid({ version: 4 });

      const providerPreauth =
        preauthorizationFactory.build() as PreauthorisationModel;
      providerPreauth.utilizations = utilizationFactory.buildList(
        1,
      ) as PreAuthUtilisationsModel[];
      providerPreauth.utilizations.forEach((u) => {
        u.status = 'Approved';
        u.price = '800';
        u.quantity = '1';
      });
      providerPreauth.providerId = providerId;

      await createPreauthorization(
        manager,
        1,
        profile,
        undefined,
        profile,
        hospitalA,
        undefined,
        undefined,
        [providerPreauth],
      );

      const result = await repo.getPreauthorizationSummary(
        ds,
        profile,
        {
          archive: false,
        },
        undefined,
        providerId,
      );

      expect(result).toBeDefined();
    });
  });

  describe('Completion Status Filtering (New Feature)', () => {
    it('should filter completed preauthorizations when showCompleted is true', async () => {
      const [newHospital] = await createHospitals(manager, 1);
      const [hmoProfile] = await createHmoProfileFixtures(manager);
      const [claimOfficer] = await createUsers(
        manager,
        1,
        newHospital,
        undefined,
        undefined,
        UserType.ClaimOfficer,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        hmoProfile.providerId,
      );
      const [enrollee] = await createUsers(
        manager,
        1,
        undefined,
        undefined,
        hmoProfile,
      );

      const preauthInput =
        preauthorizationFactory.build() as PreauthorisationModel;
      preauthInput.providerId = hmoProfile.providerId;
      const [preauth] = await createPreauthorization(
        manager,
        1,
        claimOfficer.profiles[0],
        undefined,
        enrollee.defaultProfile,
        newHospital,
        undefined,
        undefined,
        [preauthInput],
      );

      const utilizations = utilizationFactory.buildList(
        1,
      ) as PreAuthUtilisationsModel[];
      utilizations[0].preAuthorizationId = preauth.id;
      utilizations[0].utilisationStatus = [
        {
          status: 'Approved',
          creatorId: claimOfficer.defaultProfile.id,
          createdDate: new Date(),
          vettingGroup: UserType.ClaimOfficer,
          creatorName: claimOfficer.defaultProfile.fullName,
          statusDescription: 'Approved',
        },
      ];
      await manager.save(PreAuthUtilisationsModel, utilizations);

      const res = await repo.findByProfile(
        enrollee.defaultProfile.id,
        { showCompleted: true },
        claimOfficer.defaultProfile,
      );
      expect(res.totalCount).toBe(0);
      expect(claimOfficer.defaultProfile.type).toBe(UserType.ClaimOfficer);
    });

    it('should filter not completed preauthorizations when showNotCompleted is true', async () => {
      const [newHospital] = await createHospitals(manager, 1);
      const [hmoProfile] = await createHmoProfileFixtures(manager);
      const [claimOfficer] = await createUsers(
        manager,
        1,
        newHospital,
        undefined,
        undefined,
        UserType.ClaimOfficer,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        hmoProfile.providerId,
      );
      const [enrollee] = await createUsers(
        manager,
        1,
        undefined,
        undefined,
        hmoProfile,
      );

      const preauthInput =
        preauthorizationFactory.build() as PreauthorisationModel;
      preauthInput.providerId = hmoProfile.providerId;
      const [preauth] = await createPreauthorization(
        manager,
        1,
        claimOfficer.profiles[0],
        undefined,
        enrollee.defaultProfile,
        newHospital,
        undefined,
        undefined,
        [preauthInput],
      );

      const utilizations = utilizationFactory.buildList(
        1,
      ) as PreAuthUtilisationsModel[];
      utilizations[0].preAuthorizationId = preauth.id;
      utilizations[0].utilisationStatus = [
        {
          status: 'Pending',
          creatorId: 'different-user-id',
          createdDate: new Date(),
          vettingGroup: UserType.ClaimOfficer,
          creatorName: 'Different User',
          statusDescription: 'Pending',
        },
      ];
      await manager.save(PreAuthUtilisationsModel, utilizations);

      const res = await repo.findByProfile(
        enrollee.defaultProfile.id,
        { showNotCompleted: true },
        claimOfficer.defaultProfile,
      );
      expect(res.totalCount).toBe(0);
    });

    it('should work with findByHospital method as well', async () => {
      const [newHospital] = await createHospitals(manager, 1);
      const [hmoProfile] = await createHmoProfileFixtures(manager);
      const [claimOfficer] = await createUsers(
        manager,
        1,
        newHospital,
        undefined,
        undefined,
        UserType.ClaimOfficer,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        hmoProfile.providerId,
      );

      const res = await repo.findByHospital(
        newHospital.id,
        { showCompleted: true },
        claimOfficer.defaultProfile,
      );
      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
    });
  });

  describe('timeSortOrder Filter Tests', () => {
    let timeSortProfile: ProfileModel;
    let timeSortPreauthorizations: PreauthorisationModel[];

    beforeEach(async () => {
      // Create test data with different timestamps for timeSortOrder tests
      const hospitals = await createHospitals(manager, 1);
      const hmoProviders = await createHmoProviderFixtures(manager, 1);
      const [user] = await createUsers(
        manager,
        1,
        hospitals[0],
        hmoProviders[0],
        null,
        UserType.Patient,
      );
      timeSortProfile = user.profiles[0];

      // Create preauthorizations with different updated dates and times
      const baseDate = moment('2024-01-15').toDate();
      const dates = [
        moment(baseDate).hour(9).minute(30).toDate(), // 09:30
        moment(baseDate).hour(14).minute(15).toDate(), // 14:15
        moment(baseDate).hour(11).minute(45).toDate(), // 11:45
        moment(baseDate).hour(16).minute(20).toDate(), // 16:20
        moment(baseDate).hour(8).minute(10).toDate(), // 08:10
      ];

      timeSortPreauthorizations = [];
      for (const date of dates) {
        const [preauth] = await createPreauthorization(
          manager,
          1,
          null,
          null,
          timeSortProfile,
          hospitals[0],
          { providerId: hmoProviders[0].id },
          date,
        );
        timeSortPreauthorizations.push(preauth);
      }
    });

    describe('findByProfile with timeSortOrder', () => {
      it('should sort by time in ASC order when timeSortOrder is ASC', async () => {
        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by time in ascending order
        // Expected order: 08:10, 09:30, 11:45, 14:15, 16:20
        const times = result.list.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );

        expect(times[0]).toBe('08:10');
        expect(times[1]).toBe('09:30');
        expect(times[2]).toBe('11:45');
        expect(times[3]).toBe('14:15');
        expect(times[4]).toBe('16:20');
      });

      it('should sort by time in DESC order when timeSortOrder is DESC', async () => {
        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.DESC,
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by time in descending order
        // Expected order: 16:20, 14:15, 11:45, 09:30, 08:10
        const times = result.list.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );

        expect(times[0]).toBe('16:20');
        expect(times[1]).toBe('14:15');
        expect(times[2]).toBe('11:45');
        expect(times[3]).toBe('09:30');
        expect(times[4]).toBe('08:10');
      });

      it('should use default sorting when timeSortOrder is not provided', async () => {
        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by updatedDate DESC (default behavior)
        // Should be sorted by full timestamp descending
        const timestamps = result.list.map((item) =>
          moment(item.updatedDate).valueOf(),
        );

        for (let i = 0; i < timestamps.length - 1; i++) {
          expect(timestamps[i]).toBeGreaterThanOrEqual(timestamps[i + 1]);
        }
      });

      it('should handle timeSortOrder with other filters', async () => {
        // Create a preauthorization with a specific keyword
        const [specificPreauth] = await createPreauthorization(
          manager,
          1,
          null,
          null,
          timeSortProfile,
          null,
          {
            code: 'SPECIAL-CODE-123',
            providerId: timeSortPreauthorizations[0].providerId,
          },
          moment('2024-01-15').hour(12).minute(0).toDate(),
        );

        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            keyword: 'SPECIAL-CODE',
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(1);
        expect(result.list[0].code).toBe('SPECIAL-CODE-123');
      });

      it('should handle pagination with timeSortOrder', async () => {
        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 2,
            take: 2,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(2);
        expect(result).toHaveProperty('totalCount', 5);

        // Verify that pagination works correctly with time sorting
        const times = result.list.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );

        // Should get the 3rd and 4th items in ASC order: 11:45, 14:15
        expect(times[0]).toBe('11:45');
        expect(times[1]).toBe('14:15');
      });
    });

    describe('findByHospital with timeSortOrder', () => {
      it('should sort by time in ASC order when timeSortOrder is ASC', async () => {
        const hospitalId = timeSortPreauthorizations[0].hospital.id;

        const result = await repo.findByHospital(
          hospitalId,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by time in ascending order
        const times = result.list.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );

        expect(times[0]).toBe('08:10');
        expect(times[1]).toBe('09:30');
        expect(times[2]).toBe('11:45');
        expect(times[3]).toBe('14:15');
        expect(times[4]).toBe('16:20');
      });

      it('should sort by time in DESC order when timeSortOrder is DESC', async () => {
        const hospitalId = timeSortPreauthorizations[0].hospital.id;

        const result = await repo.findByHospital(
          hospitalId,
          {
            timeSortOrder: TimeSortOrder.DESC,
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by time in descending order
        const times = result.list.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );

        expect(times[0]).toBe('16:20');
        expect(times[1]).toBe('14:15');
        expect(times[2]).toBe('11:45');
        expect(times[3]).toBe('09:30');
        expect(times[4]).toBe('08:10');
      });

      it('should use default sorting when timeSortOrder is not provided', async () => {
        const hospitalId = timeSortPreauthorizations[0].hospital.id;

        const result = await repo.findByHospital(
          hospitalId,
          {
            skip: 0,
            take: 10,
          },
          timeSortProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(5);

        // Verify that results are sorted by updatedDate DESC (default behavior)
        const timestamps = result.list.map((item) =>
          moment(item.updatedDate).valueOf(),
        );

        for (let i = 0; i < timestamps.length - 1; i++) {
          expect(timestamps[i]).toBeGreaterThanOrEqual(timestamps[i + 1]);
        }
      });
    });

    describe('Edge cases and integration tests', () => {
      it('should handle preauthorizations with same date but different times', async () => {
        // Create preauthorizations on the same date but different times
        const sameDate = moment('2024-02-01').toDate();
        const sameDatePreauths = [];

        const times = [
          moment(sameDate).hour(10).minute(0).toDate(),
          moment(sameDate).hour(10).minute(30).toDate(),
          moment(sameDate).hour(10).minute(15).toDate(),
        ];

        for (const time of times) {
          const [preauth] = await createPreauthorization(
            manager,
            1,
            null,
            null,
            timeSortProfile,
            null,
            { providerId: timeSortPreauthorizations[0].providerId },
            time,
          );
          sameDatePreauths.push(preauth);
        }

        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 0,
            take: 20,
          },
          timeSortProfile,
        );

        // Find the same-date preauths in the result
        const sameDateResults = result.list.filter(
          (item) =>
            moment(item.updatedDate).format('YYYY-MM-DD') === '2024-02-01',
        );

        expect(sameDateResults).toHaveLength(3);

        // Verify they are sorted by time
        const times_result = sameDateResults.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );

        expect(times_result[0]).toBe('10:00');
        expect(times_result[1]).toBe('10:15');
        expect(times_result[2]).toBe('10:30');
      });

      it('should handle preauthorizations across different dates with timeSortOrder', async () => {
        // Create preauthorizations on different dates
        const dates = [
          moment('2024-03-01').hour(15).minute(0).toDate(),
          moment('2024-03-02').hour(9).minute(0).toDate(),
          moment('2024-03-01').hour(10).minute(0).toDate(),
        ];

        for (const date of dates) {
          await createPreauthorization(
            manager,
            1,
            null,
            null,
            timeSortProfile,
            null,
            { providerId: timeSortPreauthorizations[0].providerId },
            date,
          );
        }

        const result = await repo.findByProfile(
          timeSortProfile.id,
          {
            timeSortOrder: TimeSortOrder.DESC,
            skip: 0,
            take: 20,
          },
          timeSortProfile,
        );

        // Should be sorted by date DESC first, then by time DESC
        // Expected order: 2024-03-02 09:00, 2024-03-01 15:00, 2024-03-01 10:00, ...
        const march2Results = result.list.filter(
          (item) =>
            moment(item.updatedDate).format('YYYY-MM-DD') === '2024-03-02',
        );
        const march1Results = result.list.filter(
          (item) =>
            moment(item.updatedDate).format('YYYY-MM-DD') === '2024-03-01',
        );

        expect(march2Results).toHaveLength(1);
        expect(march1Results).toHaveLength(2);

        // March 2 should come first
        const march2Index = result.list.findIndex(
          (item) =>
            moment(item.updatedDate).format('YYYY-MM-DD') === '2024-03-02',
        );
        const march1Index = result.list.findIndex(
          (item) =>
            moment(item.updatedDate).format('YYYY-MM-DD') === '2024-03-01',
        );

        expect(march2Index).toBeLessThan(march1Index);

        // Within March 1, times should be sorted DESC (15:00 before 10:00)
        const march1Times = march1Results.map((item) =>
          moment(item.updatedDate).format('HH:mm'),
        );
        expect(march1Times[0]).toBe('15:00');
        expect(march1Times[1]).toBe('10:00');
      });

      it('should handle empty results with timeSortOrder', async () => {
        // Create a new profile with no preauthorizations
        const hospitals = await createHospitals(manager, 1);
        const hmoProviders = await createHmoProviderFixtures(manager, 1);
        const [newUser] = await createUsers(
          manager,
          1,
          hospitals[0],
          hmoProviders[0],
          null,
          UserType.Patient,
        );
        const newProfile = newUser.profiles[0];

        const result = await repo.findByProfile(
          newProfile.id,
          {
            timeSortOrder: TimeSortOrder.ASC,
            skip: 0,
            take: 10,
          },
          newProfile,
        );

        expect(result).toHaveProperty('list');
        expect(result.list).toHaveLength(0);
        expect(result).toHaveProperty('totalCount', 0);
      });
    });
  });
});
