import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import {
  CustomPreauthorizationRepoMethods,
  IPreauthorizationRepository,
} from './pre-authorization.repositories';
import { TestDataSourceOptions } from '../../data-source';
import * as db from '../../database';
import { extendDSRepo, extendModel } from '../../database/extendModel';
import { PreauthorisationModel } from '../models/preauthorisation.model';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { CustomHmoClaimRepoMethods } from '@clinify/hmo-claims/repositories/hmo-claim.repository';
import { HmoProviderRepository } from '@clinify/hmo-providers/repositories/hmo-provider.repository';
import { UserType } from '@clinify/shared/enums/users';
import { createHmoProfileFixtures } from '@clinify/utils/tests/hmo-profiles.fixtures';
import { createHospitals } from '@clinify/utils/tests/hospital.fixtures';
import { createPreauthorization } from '@clinify/utils/tests/preauthorization.fixtures';
import { createUsers } from '@clinify/utils/tests/user.fixtures';

describe('Pre-authorization repository - Submission Status Filtering', () => {
  let manager: EntityManager;
  let repo: IPreauthorizationRepository;
  let module: TestingModule;
  let ds: DataSource;

  const spySlaveQuery = jest.spyOn(db, 'queryWithSlave');

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        extendModel(PreauthorisationModel, CustomPreauthorizationRepoMethods),
        HmoProviderRepository,
        extendModel(HmoClaimModel, CustomHmoClaimRepoMethods),
      ],
    }).compile();

    ds = module.get<DataSource>(DataSource);
    manager = ds.manager;
    repo = extendDSRepo<IPreauthorizationRepository>(
      ds,
      PreauthorisationModel,
      CustomPreauthorizationRepoMethods,
    );
    spySlaveQuery.mockImplementation((qr, pr) => manager.query(qr, pr));
  });

  afterAll(async () => {
    await ds.destroy();
    await module.close();
  });

  describe('Submission Status Filtering', () => {
    it('should filter submitted preauthorizations when showSubmitted is true', async () => {
      const [newHospital] = await createHospitals(manager, 1);
      const [hmoProfile] = await createHmoProfileFixtures(manager);
      const [claimOfficer] = await createUsers(
        manager,
        1,
        newHospital,
        undefined,
        undefined,
        UserType.ClaimOfficer,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        hmoProfile.providerId,
      );
      const [enrollee] = await createUsers(
        manager,
        1,
        undefined,
        undefined,
        hmoProfile,
      );

      // Create a submitted preauthorization
      await createPreauthorization(
        manager,
        1,
        claimOfficer.profiles[0],
        undefined,
        enrollee.defaultProfile,
        newHospital,
        { claimStatus: 'Submitted', providerId: hmoProfile.providerId },
      );

      // Create a non-submitted preauthorization
      await createPreauthorization(
        manager,
        1,
        claimOfficer.profiles[0],
        undefined,
        enrollee.defaultProfile,
        newHospital,
        { claimStatus: 'Draft', providerId: hmoProfile.providerId },
      );

      const res = await repo.findByProfile(
        enrollee.defaultProfile.id,
        { showSubmitted: true },
        claimOfficer.defaultProfile,
      );

      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
      // Should only return submitted preauthorizations
      const submittedResults = res.list.filter(
        (p) => p.claimStatus === 'Submitted',
      );
      expect(submittedResults.length).toBeGreaterThan(0);
      // Verify no non-submitted preauthorizations are returned
      const nonSubmittedResults = res.list.filter(
        (p) => p.claimStatus !== 'Submitted',
      );
      expect(nonSubmittedResults.length).toBe(0);
    });

    it('should filter non-submitted preauthorizations when showNotSubmitted is true', async () => {
      const [newHospital] = await createHospitals(manager, 1);
      const [hmoProfile] = await createHmoProfileFixtures(manager);
      const [claimOfficer] = await createUsers(
        manager,
        1,
        newHospital,
        undefined,
        undefined,
        UserType.ClaimOfficer,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        hmoProfile.providerId,
      );
      const [enrollee] = await createUsers(
        manager,
        1,
        undefined,
        undefined,
        hmoProfile,
      );

      // Create a submitted preauthorization
      await createPreauthorization(
        manager,
        1,
        claimOfficer.profiles[0],
        undefined,
        enrollee.defaultProfile,
        newHospital,
        { claimStatus: 'Submitted', providerId: hmoProfile.providerId },
      );

      // Create non-submitted preauthorizations with different statuses
      await createPreauthorization(
        manager,
        1,
        claimOfficer.profiles[0],
        undefined,
        enrollee.defaultProfile,
        newHospital,
        { claimStatus: 'Draft', providerId: hmoProfile.providerId },
      );

      await createPreauthorization(
        manager,
        1,
        claimOfficer.profiles[0],
        undefined,
        enrollee.defaultProfile,
        newHospital,
        { claimStatus: 'Pending', providerId: hmoProfile.providerId },
      );

      const res = await repo.findByProfile(
        enrollee.defaultProfile.id,
        { showNotSubmitted: true },
        claimOfficer.defaultProfile,
      );

      expect(res).toHaveProperty('list');
      expect(res).toHaveProperty('totalCount');
      // Should only return non-submitted preauthorizations
      const nonSubmittedResults = res.list.filter(
        (p) => p.claimStatus !== 'Submitted',
      );
      expect(nonSubmittedResults.length).toBeGreaterThan(0);
      // Verify no submitted preauthorizations are returned
      const submittedResults = res.list.filter(
        (p) => p.claimStatus === 'Submitted',
      );
      expect(submittedResults.length).toBe(0);
    });

    it('should work with findByHospital method for submission status filtering', async () => {
      const [newHospital] = await createHospitals(manager, 1);
      const [hmoProfile] = await createHmoProfileFixtures(manager);
      const [claimOfficer] = await createUsers(
        manager,
        1,
        newHospital,
        undefined,
        undefined,
        UserType.ClaimOfficer,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        hmoProfile.providerId,
      );

      // Test showSubmitted with findByHospital
      const resSubmitted = await repo.findByHospital(
        newHospital.id,
        { showSubmitted: true },
        claimOfficer.defaultProfile,
      );
      expect(resSubmitted).toHaveProperty('list');
      expect(resSubmitted).toHaveProperty('totalCount');

      // Test showNotSubmitted with findByHospital
      const resNotSubmitted = await repo.findByHospital(
        newHospital.id,
        { showNotSubmitted: true },
        claimOfficer.defaultProfile,
      );
      expect(resNotSubmitted).toHaveProperty('list');
      expect(resNotSubmitted).toHaveProperty('totalCount');
    });

    it('should handle combination of showSubmitted and showNotSubmitted filters', async () => {
      const [newHospital] = await createHospitals(manager, 1);
      const [hmoProfile] = await createHmoProfileFixtures(manager);
      const [claimOfficer] = await createUsers(
        manager,
        1,
        newHospital,
        undefined,
        undefined,
        UserType.ClaimOfficer,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        hmoProfile.providerId,
      );
      const [enrollee] = await createUsers(
        manager,
        1,
        undefined,
        undefined,
        hmoProfile,
      );

      const resBoth = await repo.findByProfile(
        enrollee.defaultProfile.id,
        { showSubmitted: true, showNotSubmitted: true },
        claimOfficer.defaultProfile,
      );

      expect(resBoth).toHaveProperty('list');
      expect(resBoth).toHaveProperty('totalCount');
    });
  });
});
