import { registerEnumType } from '@nestjs/graphql';

interface IPreAuthResult {
  visit_details_id: number;
  procedureCategory: string;
  procedureName: string;
  status: string;
  preAuthCode: string;
}

export interface ILeadwayPreauthorizationResponse {
  success: boolean;
  errorMsg: string;
  message: string;
  VisitID: number;
  visitdetails_id: number[];
  preautResult: IPreAuthResult[];
  message_display: string;
}

export interface ILeadwayPreauthorizationResponseHTTP {
  data: ILeadwayPreauthorizationResponse;
}

export interface ILeadwayAgreedTariffResponse {
  success: boolean;
  errorMsg: string;
  amount: number;
}

export interface ILeadwayAgreedTariffResponseHTTP {
  data: ILeadwayAgreedTariffResponse;
}

export enum PreauthorisationStatus {
  Pending = 'Pending',
  Submitted = 'Submitted',
  Success = 'Success',
  Error = 'Error',
  Denied = 'Denied',
}

export enum PreauthClaimStatus {
  NotSubmitted = 'Not Submitted',
  Submitted = 'Submitted',
}

registerEnumType(PreauthorisationStatus, { name: 'PreauthorizationStatus' });

registerEnumType(PreauthClaimStatus, { name: 'PreauthClaimStatus' });
