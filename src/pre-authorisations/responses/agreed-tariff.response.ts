import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class AgreedTariffResponse {
  @Field(() => Boolean)
  success: boolean;

  @Field(() => Number)
  amount: number;

  @Field(() => Number, { defaultValue: 0 })
  position: number;

  @Field(() => String, { nullable: true })
  label?: string;

  @Field(() => String, { nullable: true })
  utilisationCategory?: string;

  @Field(() => String, { nullable: true })
  utilisationCategoryId?: string;

  @Field(() => Boolean, { nullable: true })
  capitated?: boolean;
}
