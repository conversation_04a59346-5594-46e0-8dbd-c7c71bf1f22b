import { Field, Int, ObjectType } from '@nestjs/graphql';
import { PreauthorisationModel } from '../models/preauthorisation.model';

@ObjectType()
export class PreauthorisationResponse {
  constructor(preAuths: PreauthorisationModel[], totalCount: number) {
    this.list = preAuths;
    this.totalCount = totalCount;
  }

  @Field(() => Int)
  totalCount: number;

  @Field(() => [PreauthorisationModel])
  list: PreauthorisationModel[];
}

@ObjectType()
export class PreauthorizationSummary {
  @Field({ nullable: true })
  totalPreauthorizations: number;

  @Field({ nullable: true })
  totalApprovedApprovedPreauthorizations: number;

  @Field({ nullable: true })
  totalRejectedPreauthorizations: number;

  @Field({ nullable: true })
  totalPendingPreauthorizations: number;

  @Field({ nullable: true })
  totalPreauthorizationsAmount: number;

  @Field({ nullable: true })
  totalApprovedApprovedPreauthorizationsAmount: number;

  @Field({ nullable: true })
  totalRejectedPreauthorizationsAmount: number;

  @Field({ nullable: true })
  totalPendingPreauthorizationsAmount: number;
}
