import { HttpModule } from '@nestjs/axios';
import { forwardR<PERSON>, Lo<PERSON>, Module } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PreauthorisationModel } from './models/preauthorisation.model';
import { PreAuthUtilisationsModel } from './models/utilisations.model';
import { CustomPreauthorizationRepoMethods } from './repositories/pre-authorization.repositories';
import { PreauthorizationResolver } from './resolvers/preauthorization.resolvers';
import { UtilizationResolver } from './resolvers/utilization.resolver';
import { PreauthorisationService } from './services/pre-authorisation.service';
import { BankModule } from '../banks/bank.module';
import { HmoClaimModel } from '../hmo-claims/models/hmo-claim.model';
import { CustomHmoClaimRepoMethods } from '../hmo-claims/repositories/hmo-claim.repository';
import { HmoProviderModule } from '../hmo-providers/hmo-provider.module';
import { HmoProviderRepository } from '../hmo-providers/repositories/hmo-provider.repository';
import { NotificationsService } from '../notifications/services/notifications.service';
import { MailerModule } from '../shared/mailer/mailer.module';
import { AuthorizationModule } from '@clinify/authorization/authorization.module';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { extendModel } from '@clinify/database/extendModel';
import { HmoClaimModule } from '@clinify/hmo-claims/hmo-claims.module';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { AiModule } from '@clinify/integrations/ai/ai.module';
import { NotificationsModel } from '@clinify/notifications/models/notifications.model';
import PubSub, { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';

@Module({
  imports: [
    AuthorizationModule,
    forwardRef(() => HmoProviderModule),
    forwardRef(() => AiModule),
    TypeOrmModule.forFeature([
      PreAuthUtilisationsModel,
      PreauthorisationModel,
      HmoProviderModel,
      NotificationsModel,
      PreauthorisationModel,
    ]),
    HttpModule.register({}),
    MailerModule,
    TypeormExtendedModule.forCustomRepository([HmoProviderRepository]),
    forwardRef(() => HmoClaimModule),
    forwardRef(() => BankModule),
  ],
  providers: [
    extendModel(PreauthorisationModel, CustomPreauthorizationRepoMethods),
    extendModel(HmoClaimModel, CustomHmoClaimRepoMethods),
    PreauthorisationService,
    PreauthorizationResolver,
    UtilizationResolver,
    NotificationsService,
    Logger,
    { provide: PUB_SUB, useFactory: () => PubSub },
    EventEmitter2,
  ],
  exports: [PreauthorisationService],
})
export class PreauthorizationModule {}
