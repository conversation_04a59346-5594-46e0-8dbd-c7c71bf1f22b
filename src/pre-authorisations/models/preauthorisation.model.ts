import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PreAuthUtilisationsModel } from './utilisations.model';
import { FlagDto } from '../inputs/flag.dto';
import {
  PreauthClaimStatus,
  PreauthorisationStatus,
} from '../interface/preauthorizations.interface';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { MedicationModel } from '@clinify/medications/models/medication.model';
import { PhoneNumberInput } from '@clinify/shared/validators/phone-number.input';
import { DiagnosisInput } from '@clinify/shared/validators/service-detail.input';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
export abstract class BaseAudits {
  @IsDate()
  @Field({ nullable: false })
  @Index()
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'updated_by', nullable: true })
  lastModifierId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'created_by' })
  createdBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'created_by', nullable: true })
  creatorId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'creator_name', nullable: true })
  creatorName?: string;
}

@ObjectType()
export abstract class AuditEntitiesWithProfile extends BaseAudits {
  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn({ name: 'profile_id' })
  profile?: ProfileModel;

  @Index()
  @Column({ name: 'profile_id' })
  @Field({ nullable: true })
  profileId?: string;
}

@ObjectType()
@Entity('pre_authorizations')
export class PreauthorisationModel extends AuditEntitiesWithProfile {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsUUID('4')
  id: string;

  @Field(() => Date)
  @Column({ name: 'request_date_time' })
  requestDateTime?: Date;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'treatment_start_date', nullable: true })
  treatmentStartDate?: Date;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'treatment_end_date', nullable: true })
  treatmentEndDate?: Date;

  @Field(() => String, { nullable: true })
  @Column({ name: 'requested_by', type: 'text' })
  requestedBy?: string;

  @Field(() => String)
  @Column({ name: 'service_type', type: 'text' })
  serviceType: string;

  @Field(() => String)
  @Column({ name: 'service_type_code', type: 'text' })
  serviceTypeCode: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'service_name', type: 'text', nullable: true })
  serviceName: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'priority', type: 'text', nullable: true })
  priority?: string;

  @Field(() => [DiagnosisInput], { nullable: true })
  @Column({
    nullable: true,
    name: 'diagnosis',
    type: 'jsonb',
  })
  diagnosis?: DiagnosisInput[];

  @Field(() => [PreAuthUtilisationsModel], { nullable: true })
  @OneToMany(
    () => PreAuthUtilisationsModel,
    (utilisation) => utilisation.preAuthorization,
    { cascade: true },
  )
  utilizations?: PreAuthUtilisationsModel[];

  @Field(() => String, { nullable: true })
  @Column({ name: 'additional_note', type: 'text', nullable: true })
  additionalNote?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'presenting_complain', type: 'text', nullable: true })
  presentingComplain: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'referred_by', type: 'text', nullable: true })
  referredBy?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'referral_code', type: 'text', nullable: true })
  referralCode?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'referred_from', type: 'text', nullable: true })
  referredFrom?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'referred_to', type: 'text', nullable: true })
  referredTo?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'specialty' })
  specialty: string;

  @Field({ nullable: true })
  @Column({ name: 'rank', nullable: true })
  rank: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'department' })
  department: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'facility_name' })
  facilityName: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'facility_address' })
  facilityAddress: string;

  @Field({ nullable: true })
  @Column({ name: 'visit_id', nullable: true })
  visitId?: string;

  @Field(() => [String], { nullable: true })
  @Column({ name: 'visit_detail_ids', type: 'jsonb', nullable: true })
  visitDetailIds?: string[];

  @Field(() => [String], { nullable: true })
  @Column({ name: 'documents', type: 'text', array: true, nullable: true })
  documentUrl?: string[];

  @Field(() => PreauthorisationStatus)
  @Column({
    name: 'status',
    type: 'text',
    default: PreauthorisationStatus.Submitted,
  })
  status?: PreauthorisationStatus;

  @Field(() => String, { nullable: true })
  @Column({
    name: 'claim_status',
    type: 'text',
    default: PreauthClaimStatus.NotSubmitted,
    nullable: true,
  })
  claimStatus?: string;

  @Field({ nullable: true })
  @Column({ name: 'batch_number', nullable: true })
  batchNumber?: string;

  @Field({
    nullable: true,
    description: 'This is the claimiId from HMO Provider',
  })
  @Column({ name: 'claim_id', nullable: true })
  claimId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'code', type: 'text', nullable: true })
  code?: string;

  @Field(() => HospitalModel, { nullable: true })
  @ManyToOne(() => HospitalModel, (hospital) => hospital.preauthorisations)
  @JoinColumn({ name: 'hospital_id' })
  hospital?: HospitalModel;

  @Field()
  @Index()
  @Column({ name: 'hospital_id', nullable: false })
  hospitalId: string;

  @Field(() => HmoProviderModel, { nullable: true })
  @ManyToOne(() => HmoProviderModel, (provider) => provider.preauthorisations)
  @JoinColumn({ name: 'provider_id' })
  provider?: HmoProviderModel;

  @Column({ name: 'provider_id', nullable: false })
  providerId: string;

  @Field(() => Boolean, { defaultValue: false })
  @Column({ name: 'archived', type: 'boolean', default: false })
  archived?: boolean;

  @Field(() => Date, { nullable: true })
  @Column({
    name: 'response_date_time',
    nullable: true,
    type: 'timestamptz',
  })
  responseDateTime?: Date;

  @Field(() => [FlagDto], { nullable: true })
  @Column({ name: 'flags', nullable: true, type: 'jsonb' })
  flags?: FlagDto[];

  @Field({ nullable: true })
  @Column({ name: 'enrollee_number', nullable: true })
  enrolleeNumber?: string;

  @Field(() => PhoneNumberInput, { nullable: true })
  @Column({ name: 'enrollee_phone_number', type: 'text', nullable: true })
  enrolleePhoneNumber: PhoneNumberInput;

  @Field(() => String, { nullable: true })
  @Column({ name: 'enrollee_email', type: 'text', nullable: true })
  enrolleeEmail?: string;

  @Field(() => Boolean, { defaultValue: false })
  @Column({ name: 'single_visit_pa_code', type: 'boolean', default: false })
  singleVisitPACode?: boolean;

  @Field(() => Boolean, { nullable: true })
  @Column({ name: 'is_external_plan_type', type: 'boolean', default: false })
  isExternalPlanType?: boolean;

  @Field(() => String, { nullable: true })
  @Column({ name: 'external_plan_type_id', nullable: true })
  externalPlanTypeId?: string;

  @Field(() => MedicationModel, { nullable: true })
  @OneToOne(
    () => MedicationModel,
    (medication) => medication.preauthorisation,
    { nullable: true, onDelete: 'SET NULL' },
  )
  medication?: MedicationModel;

  constructor(preAuth: Partial<PreauthorisationModel>) {
    super();
    Object.assign(this, preAuth);
  }
}
