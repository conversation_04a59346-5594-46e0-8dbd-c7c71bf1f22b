import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsDate, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PreauthorisationModel } from './preauthorisation.model';
import { ClaimsApprovalInput } from '../inputs/approval-detail.input';
import { FlagDto } from '../inputs/flag.dto';
import { TransferFundModel } from '@clinify/banks/models/transfer-fund.model';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { HmoProfilePlanModel } from '@clinify/hmo-providers/models/hmo-profile-plan.model';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { AIReason } from '@clinify/pre-authorisations/inputs/ai-reason';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
export abstract class BaseAudits {
  @IsDate()
  @Field({ nullable: false })
  @Index()
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'SET NULL',
    nullable: true,
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'updated_by', nullable: true })
  lastModifierId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'SET NULL',
    nullable: true,
  })
  @JoinColumn({ name: 'created_by' })
  createdBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'created_by', nullable: true })
  creatorId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'creator_name', nullable: true })
  creatorName?: string;
}

@ObjectType()
@Entity('pre_auth_utilisations')
export class PreAuthUtilisationsModel extends BaseAudits {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsUUID('4')
  id: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'visit_details_id', nullable: true })
  visitDetailsId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'category', type: 'text' })
  category: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'utilizationId', nullable: true })
  utilizationId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'utilizationCode', type: 'text' })
  utilizationCode: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'type', type: 'text' })
  type: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'status', type: 'text', nullable: true })
  status?: string;

  @Field(() => [String], { nullable: true })
  @Column({
    name: 'rejection_reason',
    nullable: true,
    type: 'text',
    array: true,
  })
  rejectionReason?: string[];

  @Field(() => String, { nullable: true })
  @Column({
    name: 'specify_reason_for_rejection',
    type: 'text',
    nullable: true,
  })
  specifyReasonForRejection?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'status_description', type: 'text', nullable: true })
  statusDescription?: string;

  @Field(() => [ClaimsApprovalInput], { nullable: true })
  @Column({
    nullable: true,
    name: 'utilisation_status',
    type: 'jsonb',
  })
  utilisationStatus?: ClaimsApprovalInput[];

  @Field({ nullable: true })
  @Column({ name: 'pa_util_processed', nullable: true })
  paUtilProcessed?: boolean;

  @Field(() => String, { nullable: true })
  @Column({ name: 'quantity', type: 'text' })
  quantity: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'pa_code', type: 'text', nullable: true })
  paCode: string;

  @Field(() => String, { nullable: true, description: 'Value is in Naira' })
  @Column({ name: 'price', type: 'text' })
  price: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'medication_category' })
  medicationCategory?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'dosage' })
  dosage: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'dosage_unit' })
  dosageUnit: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'frequency' })
  frequency: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'duration' })
  duration: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'birth_count' })
  birthCount?: string;

  @Field(() => Date, { nullable: true })
  @Column({ name: 'delivery_date_time', nullable: true })
  deliveryDateTime?: Date;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true, name: 'specialty' })
  specialty?: string;

  @ManyToOne(() => PreauthorisationModel, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'pre_auth' })
  preAuthorization: PreauthorisationModel;

  @Field(() => String)
  @Column({ name: 'pre_auth', type: 'text' })
  preAuthorizationId: string;

  @ManyToOne(() => HmoClaimModel, (hmoClaim) => hmoClaim.utilizations, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'hmo_claim' })
  hmoClaim: HmoClaimModel;

  @Field(() => String)
  @Column({ name: 'hmo_claim', type: 'text' })
  hmoClaimId: string;

  @Field(() => String, { nullable: true })
  @Index()
  @Column({ name: 'visit_type_id', type: 'text', nullable: true })
  visitTypeId: string;

  @Field(() => HmoProviderModel, { nullable: true })
  @ManyToOne(() => HmoProviderModel, (hmoAdmin) => hmoAdmin, {
    onDelete: 'NO ACTION',
    nullable: true,
  })
  @JoinColumn({ name: 'hmo_provider_id' })
  hmoProvider: HmoProviderModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'hmo_provider_id', nullable: true })
  hmoProviderId?: string;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (hmoAdmin) => hmoAdmin, {
    onDelete: 'NO ACTION',
    nullable: true,
  })
  @JoinColumn({ name: 'profile_id' })
  profile: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'profile_id', nullable: true })
  profileId?: string;

  @Field(() => HmoProfilePlanModel, { nullable: true })
  @ManyToOne(() => HmoProfilePlanModel, (hmoAdmin) => hmoAdmin, {
    onDelete: 'NO ACTION',
    nullable: true,
  })
  @JoinColumn({ name: 'hmo_profile_plan_id' })
  hmoProfilePlan: HmoProfilePlanModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'hmo_profile_plan_id', nullable: true })
  hmoProfilePlanId?: string;

  @ManyToOne(
    () => TransferFundModel,
    (transferFund) => transferFund.utilisations,
    {
      onDelete: 'SET NULL',
      nullable: true,
    },
  )
  @JoinColumn({ name: 'transfer_fund_id' })
  transferFund: TransferFundModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'transfer_fund_id', nullable: true })
  transferFundId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Service type from which this utilization is created',
  })
  @Column({ name: 'service_name', nullable: true })
  serviceName?: string;

  @Field(() => Boolean, { nullable: true })
  @Column({ nullable: true, name: 'confirmation' })
  confirmation?: boolean;

  @Field(() => Number, { nullable: true })
  @Column({ name: 'percentage_covered', nullable: true, type: 'float' })
  percentageCovered?: number;

  @Field(() => Number, { nullable: true })
  @Column({ name: 'amount_covered', nullable: true, type: 'float' })
  amountCovered?: number;

  @Field(() => String, { nullable: true })
  @Column({ name: 'payment_model', type: 'text', nullable: true })
  paymentModel?: string;

  @Field(() => [FlagDto], { nullable: true })
  @Column({ name: 'flags', nullable: true, type: 'jsonb' })
  flags?: FlagDto[];

  @Field(() => AIReason, { nullable: true })
  @Column({ name: 'ai_reason', nullable: true, type: 'jsonb' })
  aiReason?: AIReason;

  @ManyToOne(() => HmoClaimModel, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'medication_claim_id' })
  medicationClaim?: HmoClaimModel;

  @Column({ name: 'medication_claim_id', nullable: true })
  medicationClaimId?: string;

  get totalAmountCovered(): number {
    if (this.amountCovered) return this.amountCovered;
    if (!this.percentageCovered) {
      return Number(this.quantity || 1) * Number(this.price);
    }

    return (
      (this.percentageCovered / 100) *
      Number(this.price || 0) *
      Number(this.quantity || 1)
    );
  }

  constructor(preAuth: Partial<PreAuthUtilisationsModel>) {
    super();
    Object.assign(this, preAuth);
  }
}
