import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { RequestPackageFilterInput } from '../inputs/request-packages-filter.input';
import {
  NewRequestPackageInput,
  RequestPackageInput,
} from '../inputs/request-packages.input';
import { RequestPackageModel } from '../models/request-packages.model';
import { IRequestPackageRepository } from '../repositories/request-packages.repository';
import { RequestPackageResponse } from '../responses/request-packages.response';
import { BillDetailsModel } from '@clinify/bills/models/bill-details.model';
import { BillModel } from '@clinify/bills/models/bill.model';
import { customDSSerializeInTransaction } from '@clinify/database';
import { validateCreation } from '@clinify/shared/validators/validate-record-mutation.validator';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { IProfileRepository } from '@clinify/users/repositories/profile.repository';
import { DEFAULT_VAT_PERCENTAGE } from '@clinify/utils/helpers/billing.util';

@Injectable()
export class RequestPackageService {
  constructor(
    @InjectRepository(RequestPackageModel)
    public repository: IRequestPackageRepository,
    @InjectRepository(ProfileModel)
    private profileRepository: IProfileRepository,
    private dataSource: DataSource,
  ) {}

  getAllPackages(
    mutator: ProfileModel,
    profileId: string,
    filter: Partial<RequestPackageFilterInput>,
  ): Promise<RequestPackageResponse> {
    return this.repository.findByProfile(mutator, profileId, filter);
  }

  getOneRequestPackage(
    mutator: ProfileModel,
    requestPackageId: string,
  ): Promise<RequestPackageModel> {
    return this.repository.getOneRequestPackage(mutator, requestPackageId);
  }

  async saveRequestPackage(
    mutator: ProfileModel,
    input: NewRequestPackageInput,
  ): Promise<RequestPackageModel> {
    const profile = await this.profileRepository.findOne({
      where: {
        clinifyId: input.clinifyId,
      },
    });
    validateCreation(profile, input);
    const { hospital, fullName } = mutator;

    const newRecord = new RequestPackageModel({
      ...input,
      profile,
      hospital: hospital || undefined,
      creatorName: fullName,
      createdBy: mutator,
    });

    const {
      requestDate,
      packageName,
      price,
      serviceDetails,
      facilityName,
      facilityAddress,
      patientType,
      paymentType,
    } = input;

    const totalAmount = parseInt(price, 10);
    const vatAmount = (totalAmount * DEFAULT_VAT_PERCENTAGE) / 100;
    const amountDue = totalAmount + vatAmount;

    return customDSSerializeInTransaction(this.dataSource, async (manager) => {
      const requestPackageTrxnRepo = manager.withRepository(this.repository);

      const newPackage = await requestPackageTrxnRepo.save(newRecord);

      const detail = new BillDetailsModel({
        billType: 'Package',
        billName: `Package - ${packageName}`,
        patientType,
        paymentType: paymentType || 'Private',
        serviceDetails,
        subServiceType: packageName,
        description: 'For Packages',
        quantity: 1,
        unitPrice: totalAmount,
        amount: totalAmount,
        vatAmount,
        amountDue,
        amountOutstanding: amountDue,
        amountOwing: amountDue,
        amountPaid: 0,
        createdBy: mutator,
        creatorName: fullName,
        reference: newPackage.id,
        senderHospital: hospital.id,
        serviceType: 'Packages',
      });

      const bill = await manager.save(
        BillModel,
        new BillModel({
          details: [detail],
          totalAmount,
          vatAmount,
          amountDue,
          amountOutstanding: amountDue,
          amountOwning: amountDue,
          unitPrice: totalAmount,
          amountOverpaid: 0,
          amountUnderpaid: 0,
          billingDateTime: requestDate,
          raisedBy: fullName,
          hospitalName: facilityName,
          hospitalAddress: facilityAddress,
          autoGenerated: true,
          senderHospital: hospital,
          receiverProfile: profile,
          createdBy: mutator,
          creatorName: fullName,
          isPackage: true,
        }),
      );

      await manager
        .createQueryBuilder()
        .update(RequestPackageModel)
        .set({ bill, updatedDate: () => 'updated_date' })
        .where('id = :id', { id: newPackage.id })
        .execute();

      return { ...newPackage, bill };
    });
  }

  updateRequestPackage(
    mutator: ProfileModel,
    input: RequestPackageInput,
    requestPackageId: string,
  ): Promise<RequestPackageModel> {
    return this.repository.updateRequestPackage(
      mutator,
      input,
      requestPackageId,
    );
  }

  async deleteRequestPackages(
    mutator: ProfileModel,
    requestPackageIds: string[],
  ): Promise<RequestPackageModel[]> {
    return customDSSerializeInTransaction(this.dataSource, async (manager) => {
      const requestPackageTrxnRepo = manager.withRepository(this.repository);

      const billItemsToDelete = await manager
        .createQueryBuilder()
        .select(['reference'])
        .from(BillDetailsModel, 'bill_details')
        .where('reference IN (:...references)', {
          references: requestPackageIds,
        })
        .andWhere('(amount_paid < 1)')
        .execute();

      const requestPackageIdsToDelete = billItemsToDelete?.map(
        ({ reference }) => reference,
      );

      const items = await requestPackageTrxnRepo.deleteRequestPackages(
        mutator,
        requestPackageIdsToDelete,
      );
      const billIdsToDelete = items.map(({ bill }) => bill.id);

      await manager.delete(BillModel, billIdsToDelete);

      return items;
    });
  }

  archiveRequestPackages(
    mutator: ProfileModel,
    requestPackageIds: string[],
    archive: boolean,
  ): Promise<RequestPackageModel[]> {
    return this.repository.archiveRequestPackages(
      mutator,
      requestPackageIds,
      archive,
    );
  }
}
