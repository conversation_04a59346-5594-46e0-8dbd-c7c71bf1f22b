import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { RequestPackageService } from './request-packages.service';
import { RequestPackageModel } from '../models/request-packages.model';
import { billFactory } from '@clinify/__mocks__/factories/bill.factory';
import { hospitalFactory } from '@clinify/__mocks__/factories/hospital.factory';
import { requestPackageFactory } from '@clinify/__mocks__/factories/package.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { BillModel } from '@clinify/bills/models/bill.model';
import { UserType } from '@clinify/shared/enums/users';
import { ProfileModel } from '@clinify/users/models/profile.model';

const mockRequestPackageRepository = {
  findByProfile: jest.fn(),
  getOneRequestPackage: jest.fn(),
  updateRequestPackage: jest.fn(),
  archiveRequestPackages: jest.fn(),
};

const mockProfileRepository = {
  findOne: jest.fn(),
};

const entityManagerMock = {
  save: jest.fn(),
  delete: jest.fn(),
  withRepository: jest.fn().mockReturnValue({
    save: jest.fn(),
    deleteRequestPackages: jest.fn(),
  }),
  createQueryBuilder: jest.fn(() => ({
    update: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    execute: jest.fn(),
  })),
  queryRunner: { isTransactionActive: true },
};

describe('RequestPackageService', () => {
  let service: RequestPackageService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [],
      providers: [
        RequestPackageService,
        {
          provide: getRepositoryToken(RequestPackageModel),
          useValue: mockRequestPackageRepository,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: mockProfileRepository,
        },
        {
          provide: DataSource,
          useValue: {
            manager: entityManagerMock,
            transaction: jest.fn((cb) => cb(entityManagerMock)),
          },
        },
      ],
    }).compile();

    service = module.get<RequestPackageService>(RequestPackageService);
  });

  it('getAllPackages(): should call findByProfile repository method', async () => {
    const mutator = profileFactory.build();
    await service.getAllPackages(mutator, 'profile-id', {});

    expect(mockRequestPackageRepository.findByProfile).toHaveBeenCalledWith(
      mutator,
      'profile-id',
      {},
    );
  });

  it('getOneRequestPackage(): should call getOneRequestPackage repository method', async () => {
    const mutator = profileFactory.build();

    await service.getOneRequestPackage(mutator, 'request-package-id');

    expect(
      mockRequestPackageRepository.getOneRequestPackage,
    ).toHaveBeenCalledWith(mutator, 'request-package-id');
  });

  it('saveRequestPackage(): should save requested package and create bill', async () => {
    const hospital = hospitalFactory.build();
    const mutator = {
      ...profileFactory.build(),
      type: UserType.OrganizationDoctor,
      hospital,
      fullName: 'John Doe',
    };
    const profile = { ...profileFactory.build(), type: UserType.Patient };
    const input = { ...requestPackageFactory.build(), clinifyId: 'clinify-id' };
    const bill = billFactory.build();

    mockProfileRepository.findOne = jest.fn(() => Promise.resolve(profile));
    entityManagerMock.withRepository().save = jest.fn(() =>
      Promise.resolve(input),
    );
    entityManagerMock.save = jest.fn(() => Promise.resolve(bill));

    await service.saveRequestPackage(mutator, input);

    expect(mockProfileRepository.findOne).toHaveBeenCalledWith({
      where: {
        clinifyId: 'clinify-id',
      },
    });
    expect(entityManagerMock.withRepository().save).toHaveBeenCalledWith({
      ...input,
      profile,
      hospital,
      creatorName: 'John Doe',
      createdBy: mutator,
    });
    expect(entityManagerMock.save).toHaveBeenCalled();
    expect(entityManagerMock.createQueryBuilder).toHaveBeenCalled();
  });

  it('updateRequestPackage(): should call updateRequestPackage repository method', async () => {
    const mutator = profileFactory.build();
    const input = requestPackageFactory.build();

    await service.updateRequestPackage(mutator, input, 'request-package-id');

    expect(
      mockRequestPackageRepository.updateRequestPackage,
    ).toHaveBeenCalledWith(mutator, input, 'request-package-id');
  });

  it('deleteRequestPackages(): should delete requested package and bill', async () => {
    entityManagerMock.createQueryBuilder.mockClear();

    const mutator = {
      ...profileFactory.build(),
      type: UserType.OrganizationDoctor,
    };
    const bill = billFactory.build();
    const deletedItem = { ...requestPackageFactory.build(), bill };

    entityManagerMock.withRepository().deleteRequestPackages = jest.fn(() =>
      Promise.resolve([deletedItem]),
    );

    await service.deleteRequestPackages(mutator, [deletedItem.id]);

    expect(entityManagerMock.createQueryBuilder).toHaveBeenCalledTimes(1);
    expect(
      entityManagerMock.withRepository().deleteRequestPackages,
    ).toHaveBeenCalled();
    expect(entityManagerMock.delete).toHaveBeenCalledWith(BillModel, [bill.id]);
  });

  it('archiveRequestPackages(): should call archiveRequestPackages repository method', async () => {
    const mutator = profileFactory.build();

    await service.archiveRequestPackages(
      mutator,
      ['request-package-id'],
      false,
    );

    expect(
      mockRequestPackageRepository.archiveRequestPackages,
    ).toHaveBeenCalledWith(mutator, ['request-package-id'], false);
  });
});
