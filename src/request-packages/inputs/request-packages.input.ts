import { Field, InputType } from '@nestjs/graphql';
import { IsOptional, IsUUID } from 'class-validator';
import { PackageServiceInput } from '@clinify/packages/inputs/package.input';

@InputType()
export class NewRequestPackageInput {
  @Field({ nullable: true })
  @IsUUID('4')
  @IsOptional()
  id?: string;

  @Field(() => String, { nullable: false })
  clinifyId: string;

  @Field(() => Date, { nullable: false })
  requestDate: Date;

  @Field(() => String, { nullable: false })
  packageName: string;

  @Field(() => String, { nullable: true })
  priority: string;

  @Field(() => String, { nullable: true })
  category: string;

  @Field(() => [PackageServiceInput], { nullable: true })
  serviceDetails?: PackageServiceInput[];

  @Field(() => String, { nullable: false })
  price: string;

  @Field(() => String, { nullable: true })
  orderedBy: string;

  @Field(() => String, { nullable: true })
  specialty: string;

  @Field(() => String, { nullable: true })
  rank: string;

  @Field(() => String, { nullable: true })
  department: string;

  @Field(() => String, { nullable: true })
  patientType?: string;

  @Field(() => String, { nullable: true })
  paymentType?: string;

  @Field(() => String, { nullable: true })
  facilityName: string;

  @Field(() => String, { nullable: true })
  facilityAddress: string;

  @Field(() => String, { nullable: true })
  additionalNote: string;
}

@InputType()
export class RequestPackageInput extends NewRequestPackageInput {}
