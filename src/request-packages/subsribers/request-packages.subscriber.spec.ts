import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { RequestPackagesSubscriber } from './request-packages.subscriber';
import { RequestPackageModel } from '../models/request-packages.model';
import { hospitalFactory } from '@clinify/__mocks__/factories/hospital.factory';
import { packageFactory } from '@clinify/__mocks__/factories/package.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { TestDataSourceOptions } from '@clinify/data-source';
import { NotificationsService } from '@clinify/notifications/services/notifications.service';

const chance = new Chance();

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

const mockNotificationsService = {
  handleNoticationEvent: jest.fn(),
};

describe('RequestPackagesSubscriber', () => {
  let subscriber: RequestPackagesSubscriber;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        RequestPackagesSubscriber,
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
        {
          provide: NotificationsService,
          useValue: mockNotificationsService,
        },
      ],
    }).compile();

    subscriber = module.get<RequestPackagesSubscriber>(
      RequestPackagesSubscriber,
    );
  });

  it('should return Request Package Model class', () => {
    expect(subscriber.listenTo()).toBe(RequestPackageModel);
  });

  it('afterInsert(): should create records in package requested', async () => {
    const entityId = chance.guid({ version: 4 });
    const profile = profileFactory.build();
    const createdBy = profileFactory.build();
    const hospital = hospitalFactory.build();

    const eventMock = {
      connection: {},
      queryRunner: {},
      metadata: {},
      entity: {
        ...packageFactory.build(),
        id: entityId,
        clinifyId: 'clinify-id',
        serviceDetails: [
          {
            serviceType: 'Admission',
            serviceName: 'Private Room',
          },
          {
            serviceType: 'Antenatal',
            serviceName: 'First Visit',
          },
          {
            serviceType: 'Consultation',
            serviceName: 'General Consultation',
          },
          {
            serviceType: 'Consumable',
            serviceName: 'Forcept',
          },
          {
            serviceType: 'Immunization',
            serviceName: 'Vaccination Name',
          },
          {
            serviceType: 'Laboratory',
            serviceName: 'Full Blood Count',
          },
          {
            serviceType: 'Labour And Delivery',
            serviceName: 'CS',
          },
          {
            serviceType: 'Medication',
            serviceName: 'Panadol',
          },
          {
            serviceType: 'Nursing Services',
            serviceName: 'Circumcision',
          },
          {
            serviceType: 'Oncology',
            serviceName: 'Oncology Consultation',
          },
          {
            serviceType: 'Radiology',
            serviceName: 'Chext X-Ray',
          },
          {
            serviceType: 'Postnatal',
            serviceName: 'After Labour Care',
          },
          {
            serviceType: 'Procedure',
            serviceName: 'ENT Surgery',
          },
          {
            serviceType: 'Consultation Procedure',
            serviceName: 'ENT Surgery',
          },
        ],
        profile,
        createdBy,
        hospital,
      },
      manager: {
        save: jest.fn(() =>
          Promise.resolve({
            profile,
            createdBy,
          }),
        ),
        getRepository: jest.fn().mockReturnValue({
          find: jest.fn(),
          findOne: jest.fn(),
          createQueryBuilder: jest.fn().mockReturnValue({
            where: jest.fn().mockReturnThis(),
            leftJoinAndSelect: jest.fn().mockReturnThis(),
            getOne: jest.fn().mockReturnValue([]),
          }),
        }),
        createQueryBuilder: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnThis(),
          from: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getRawOne: jest.fn().mockReturnThis(),
        }),
      },
    } as any;

    await subscriber.afterInsert({ ...eventMock });

    expect(eventMock.manager.save).toHaveBeenCalledTimes(15);
    expect(
      mockNotificationsService.handleNoticationEvent,
    ).toHaveBeenCalledTimes(13);
    expect(pubSubMock.publish).toHaveBeenCalledTimes(32);
  });
});
