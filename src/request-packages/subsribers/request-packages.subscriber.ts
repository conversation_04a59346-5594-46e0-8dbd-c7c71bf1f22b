/* eslint-disable max-lines */
/* eslint-disable brace-style */
import { Inject, Injectable } from '@nestjs/common';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import {
  DataSource,
  EntityManager,
  EntitySubscriberInterface,
  InsertEvent,
} from 'typeorm';
import { v4 as generateUUID } from 'uuid';
import { RequestPackageModel } from '../models/request-packages.model';
import { AdmissionModel } from '@clinify/admissions/models/admission.model';
import { ConsultationModel } from '@clinify/consultations/models/consultation.model';
import { FacilityPreferenceModel } from '@clinify/facility-preferences/models/facility-preference.model';
import { ImmunizationDetailModel } from '@clinify/immunizations/models/immunization-details.model';
import { ImmunizationModel } from '@clinify/immunizations/models/immunization.model';
import { InvestigationModel } from '@clinify/investigation/models/investigation.model';
import { DispenseRegistersModel } from '@clinify/medications/models/dispense-register.model';
import { MedicationModel } from '@clinify/medications/models/medication.model';
import { MedicationDetailsModel } from '@clinify/medications/models/medication_details.model';
import { MedicationConsumable } from '@clinify/medications/validators/medication-details.input';
import { NotificationsService } from '@clinify/notifications/services/notifications.service';
import { NursingServiceDetailModel } from '@clinify/nursing-services/models/nursing-services-details.model';
import { NursingServiceModel } from '@clinify/nursing-services/models/nursing-services.model';
import { OncologyConsultationHistoryModel } from '@clinify/oncology-consultation-history/models/oncology-consultation-history.model';
import { OncologyConsultationRegisterModel } from '@clinify/oncology-consultation-history/models/oncology-consultation-register.model';
import { PackageServiceInput } from '@clinify/packages/inputs/package.input';
import { AntenatalDetailsModel } from '@clinify/pregnancy-care/models/antenatal-details.model';
import { AntenatalModel } from '@clinify/pregnancy-care/models/antenatal.model';
import { LabourDeliveryModel } from '@clinify/pregnancy-care/models/labour-delivery.model';
import { PostnatalModel } from '@clinify/pregnancy-care/models/postnatal.model';
import { RequestProcedureModel } from '@clinify/request-procedures/models/request-procedures.model';
import { LAB_TEST_CATEGORY } from '@clinify/shared/constants';
import { EventType } from '@clinify/shared/enums/events';
import { InvestigationRequestType } from '@clinify/shared/enums/investigation';
import {
  BankType,
  MedicationOptionType,
} from '@clinify/shared/enums/medication';
import {
  DashboardIcon,
  NotificationTag,
} from '@clinify/shared/enums/notifications';
import {
  makeDispenseRoster,
  makeRosterName,
} from '@clinify/shared/helper/medication/dispense-roster';
import { SurgeryModel } from '@clinify/surgeries/models/surgery.model';
import { ProcedureTypeInput } from '@clinify/surgeries/validators/surgery.input';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const {
  AdmissionAdded,
  AdmissionEvent,
  InPatientEvent,
  AntenatalAdded,
  AntenatalEvent,
  PostnatalAdded,
  LabourAndDeliveryAdded,
  ConsultationAdded,
  ConsultationEvent,
  ImmunizationAdded,
  ImmunizationEvent,
  LaboratoryAdded,
  LaboratoryEvent,
  RadiologyAdded,
  RadiologyEvent,
  InvestigationAdded,
  InvestigationEvent,
  MedicationAdded,
  MedicationEvent,
  PrescriptionEvent,
  NursingServiceAdded,
  NursingServiceEvent,
  OncologyConsultationAdded,
  ProcedureAdded,
  ProcedureEvent,
  RequestProcedureAdded,
} = SubscriptionTypes;

type ServiceType =
  | 'admServiceDetails'
  | 'antServiceDetails'
  | 'consulServiceDetails'
  | 'consultProcedureServiceDetails'
  | 'immServiceDetails'
  | 'labServiceDetails'
  | 'labDelServiceDetails'
  | 'medServiceDetails'
  | 'nusServiceDetails'
  | 'oncologyServiceDetails'
  | 'radServiceDetails'
  | 'postServiceDetails'
  | 'proServiceDetails';

interface IRecords {
  admissions: AdmissionModel[];
  antenatal: AntenatalModel;
  consultations: ConsultationModel[];
  consultProcedure: RequestProcedureModel;
  immunization: ImmunizationModel;
  laboratory: InvestigationModel;
  labourAndDeliveries: LabourDeliveryModel[];
  medication: MedicationModel;
  nursingService: NursingServiceModel;
  oncology: OncologyConsultationHistoryModel[];
  radiology: InvestigationModel;
  postnatals: PostnatalModel[];
  procedure: SurgeryModel;
}

@Injectable()
export class RequestPackagesSubscriber
  implements EntitySubscriberInterface<RequestPackageModel>
{
  constructor(
    private dataSource: DataSource,
    @Inject(PUB_SUB) private pubSub: RedisPubSub,
    private notificationService: NotificationsService,
  ) {
    this.dataSource.subscribers.push(this);
  }

  listenTo(): typeof RequestPackageModel {
    return RequestPackageModel;
  }

  private generateServiceList(
    serviceDetails: PackageServiceInput[],
  ): Record<ServiceType, PackageServiceInput[]> {
    const admServiceDetails: PackageServiceInput[] = [];
    const antServiceDetails: PackageServiceInput[] = [];
    const consulServiceDetails: PackageServiceInput[] = [];
    const consultProcedureServiceDetails: PackageServiceInput[] = [];
    const ConsumServiceDetails: PackageServiceInput[] = [];
    const immServiceDetails: PackageServiceInput[] = [];
    const labServiceDetails: PackageServiceInput[] = [];
    const labDelServiceDetails: PackageServiceInput[] = [];
    const medServiceDetails: PackageServiceInput[] = [];
    const nusServiceDetails: PackageServiceInput[] = [];
    const oncologyServiceDetails: PackageServiceInput[] = [];
    const radServiceDetails: PackageServiceInput[] = [];
    const postServiceDetails: PackageServiceInput[] = [];
    const proServiceDetails: PackageServiceInput[] = [];

    const SERVICE_TYPE_CONTAINER_MAP = {
      Admission: admServiceDetails,
      Antenatal: antServiceDetails,
      Consultation: consulServiceDetails,
      'Consultation Procedure': consultProcedureServiceDetails,
      Consumable: ConsumServiceDetails,
      Immunization: immServiceDetails,
      Laboratory: labServiceDetails,
      'Labour And Delivery': labDelServiceDetails,
      Medication: medServiceDetails,
      'Nursing Services': nusServiceDetails,
      Oncology: oncologyServiceDetails,
      Radiology: radServiceDetails,
      Postnatal: postServiceDetails,
      Procedure: proServiceDetails,
    };

    serviceDetails.forEach((item) => {
      if (SERVICE_TYPE_CONTAINER_MAP.hasOwnProperty(item.serviceType)) {
        SERVICE_TYPE_CONTAINER_MAP[item.serviceType].push(item);
      }
    });

    if (ConsumServiceDetails.length) {
      medServiceDetails.push({
        serviceType: 'Consumable',
        ConsumServiceDetails,
      } as any);
    }

    return {
      admServiceDetails,
      antServiceDetails,
      consulServiceDetails,
      consultProcedureServiceDetails,
      immServiceDetails,
      labServiceDetails,
      labDelServiceDetails,
      medServiceDetails,
      nusServiceDetails,
      oncologyServiceDetails,
      radServiceDetails,
      postServiceDetails,
      proServiceDetails,
    };
  }

  private async createRecods(
    manager: EntityManager,
    entityRecord: RequestPackageModel,
    serviceList: Record<ServiceType, PackageServiceInput[]>,
  ): Promise<IRecords> {
    const {
      admServiceDetails,
      antServiceDetails,
      consulServiceDetails,
      consultProcedureServiceDetails,
      immServiceDetails,
      labServiceDetails,
      labDelServiceDetails,
      medServiceDetails,
      nusServiceDetails,
      oncologyServiceDetails,
      radServiceDetails,
      postServiceDetails,
      proServiceDetails,
    } = serviceList;

    const {
      requestDate,
      orderedBy,
      priority,
      category,
      rank,
      specialty,
      department,
      facilityName,
      facilityAddress,
      profile,
      hospital,
      createdBy,
    } = entityRecord;
    const { fullName } = createdBy;
    const clinifyId = ({ ...entityRecord } as any).clinifyId;

    let admissions: AdmissionModel[] = [];
    let antenatal: AntenatalModel;
    let consultations: ConsultationModel[] = [];
    let consultProcedure: RequestProcedureModel;
    let immunization: ImmunizationModel;
    let laboratory: InvestigationModel;
    let labourAndDeliveries: LabourDeliveryModel[] = [];
    let medication: MedicationModel;
    let nursingService: NursingServiceModel;
    let oncology: OncologyConsultationHistoryModel[] = [];
    let radiology: InvestigationModel;
    let postnatals: PostnatalModel[] = [];
    let procedure: SurgeryModel;

    if (admServiceDetails.length) {
      admissions = await Promise.all(
        admServiceDetails.map(async () => {
          return manager.save(
            AdmissionModel,
            new AdmissionModel({
              admissionDate: requestDate,
              priority,
              category,
              admittedBy: orderedBy,
              serviceDetails: null,
              specialty,
              rank,
              department,
              clinicName: facilityName,
              clinicAddress: facilityAddress,
              profile,
              hospital,
              createdBy,
              creatorName: fullName,
              isPackage: true,
            }),
          );
        }),
      );
    }

    if (antServiceDetails.length) {
      const details = antServiceDetails.map(() => {
        const ref = generateUUID();

        return new AntenatalDetailsModel({
          id: ref,
          priority,
          category,
          visitationDateTime: requestDate,
          seenBy: orderedBy,
          specialty,
          rank,
          department,
          createdBy,
          creatorName: fullName,
        });
      });

      antenatal = await manager.save(
        AntenatalModel,
        new AntenatalModel({
          details,
          serviceDetails: null,
          hospitalName: facilityName,
          hospitalAddress: facilityAddress,
          profile,
          hospital,
          createdBy,
          creatorName: fullName,
          isPackage: true,
        }),
      );
    }

    if (consulServiceDetails.length) {
      consultations = await Promise.all(
        consulServiceDetails.map(async () => {
          return manager.save(
            ConsultationModel,
            new ConsultationModel({
              consultationDateTime: requestDate,
              doctorName: orderedBy,
              priority,
              specialty,
              class: rank,
              department,
              category,
              serviceDetails: null,
              clinicName: facilityName,
              clinicAddress: facilityAddress,
              profile,
              hospital,
              createdBy,
              creatorName: fullName,
              isPackage: true,
            }),
          );
        }),
      );
    }

    if (consultProcedureServiceDetails.length) {
      const procedureType = consultProcedureServiceDetails.map((item) => {
        const { serviceName } = item;

        return {
          type: serviceName,
          priority,
        } as ProcedureTypeInput;
      });

      consultProcedure = await manager.save(
        RequestProcedureModel,
        new RequestProcedureModel({
          surgeryDate: requestDate,
          procedureType,
          operatedBy: orderedBy,
          specialty,
          rank,
          department,
          serviceDetails: null,
          facilityAddress,
          profile,
          hospital,
          createdBy,
          creatorName: fullName,
          isPackage: true,
        }),
      );
    }

    if (immServiceDetails.length) {
      const details = immServiceDetails.map(({ serviceName }) => {
        const ref = generateUUID();

        return new ImmunizationDetailModel({
          id: ref,
          administeredDate: requestDate,
          priority,
          class: rank,
          immunizationName: serviceName,
          administratorName: orderedBy,
          createdBy,
          creatorName: fullName,
        });
      });

      immunization = await manager.save(
        ImmunizationModel,
        new ImmunizationModel({
          details,
          serviceDetails: null,
          hospitalName: facilityName,
          hospitalAddress: facilityAddress,
          profile,
          hospital,
          createdBy,
          creatorName: fullName,
          isPackage: true,
        }),
      );
    }

    if (labServiceDetails.length) {
      const testInfo = labServiceDetails.map((item) => {
        const { serviceName } = item;
        const testCategory = LAB_TEST_CATEGORY[serviceName];

        return {
          testName: serviceName,
          testCategory,
          priority,
        };
      });

      laboratory = await manager.save(
        InvestigationModel,
        new InvestigationModel({
          requestType: InvestigationRequestType.Laboratory,
          requestDate,
          testInfo,
          priority,
          rank,
          department,
          serviceDetails: null,
          orderedBy,
          specialty,
          facilityName,
          facilityAddress,
          external: false,
          clinifyId,
          profile,
          hospital,
          referringHospital: hospital,
          createdBy,
          creatorName: fullName,
          isPackage: true,
        }),
      );
    }

    if (labDelServiceDetails.length) {
      labourAndDeliveries = await Promise.all(
        labDelServiceDetails.map(async () => {
          return manager.save(
            LabourDeliveryModel,
            new LabourDeliveryModel({
              visitationDateTime: requestDate,
              serviceDetails: null,
              facilityName,
              facilityAddress,
              profile,
              hospital,
              createdBy,
              creatorName: fullName,
              isPackage: true,
            }),
          );
        }),
      );
    }

    if (medServiceDetails.length) {
      const medicationDetails = await Promise.all(
        medServiceDetails.map(
          async ({
            serviceType,
            serviceName,
            ConsumServiceDetails,
            extraInformation,
          }: any) => {
            let dispenseRegister;
            const frequency = 'STAT';
            const duration = '1::week';
            const dosage = '1';
            const dosageUnit = '';
            const extra = JSON.parse(extraInformation || '{}');
            if (serviceType === 'Medication') {
              const { freq, roster } = makeDispenseRoster(frequency, duration);

              const newDetail = {
                medicationName: makeRosterName({
                  medicationName: serviceName,
                  frequency,
                  dosage,
                  duration,
                  dosageUnit,
                }),
                periodName: freq?.unit,
                periods: roster,
              };

              dispenseRegister = await manager.save(
                DispenseRegistersModel,
                new DispenseRegistersModel({
                  creator: profile.fullName,
                  details: newDetail,
                }),
              );
            }

            return new MedicationDetailsModel({
              datePrescribed: requestDate,
              bank: BankType.CLINIFY,
              ...(serviceType === 'Consumable'
                ? {
                    option: MedicationOptionType.C,
                    medicationConsumables: ConsumServiceDetails.map(
                      ({ serviceName, extraInformation }) => {
                        const extra = JSON.parse(extraInformation || '{}');
                        return {
                          name: serviceName,
                          quantity: '1',
                          drugInventoryId: extra?.inventoryId || '',
                          ...extra,
                        } as MedicationConsumable;
                      },
                    ),
                  }
                : {
                    option: MedicationOptionType.M,
                    medicationName: serviceName,
                    frequency,
                    dosage,
                    duration,
                    dosageUnit,
                    purpose: '',
                  }),
              ...extra,
              drugInventoryId: extra?.inventoryId || '',
              dispenseRegister,
              hospitalId: hospital.id,
              createdBy,
              creatorName: fullName,
              isPackage: true,
            });
          },
        ),
      );

      medication = await manager.save(
        MedicationModel,
        new MedicationModel({
          clinifyId,
          details: medicationDetails as any,
          totalQuantity: `${medServiceDetails.length}`,
          prescribedBy: orderedBy,
          specialty,
          rank,
          department,
          hospitalName: facilityName,
          hospitalAddress: facilityAddress,
          profile,
          hospital,
          createdBy,
          creatorName: fullName,
        }),
      );
    }

    if (nusServiceDetails.length) {
      const details = nusServiceDetails.map((item) => {
        const { serviceName } = item;

        return new NursingServiceDetailModel({
          procedureDateTime: requestDate,
          procedureType: 'Others',
          procedureName: serviceName,
          priority,
          category,
          profile,
          createdBy,
          creatorName: fullName,
        });
      });

      nursingService = await manager.save(
        NursingServiceModel,
        new NursingServiceModel({
          details,
          nurseName: orderedBy,
          department,
          specialty,
          serviceDetails: null,
          facilityName,
          facilityAddress,
          profile,
          hospital,
          createdBy,
          creatorName: fullName,
          isPackage: true,
        }),
      );
    }

    if (oncologyServiceDetails.length) {
      oncology = await Promise.all(
        oncologyServiceDetails.map(async () => {
          const savedOncologyConst = await manager.save(
            OncologyConsultationHistoryModel,
            new OncologyConsultationHistoryModel({
              consultationDateTime: requestDate,
              doctorName: orderedBy,
              priority,
              specialty,
              rank,
              department,
              category,
              serviceDetails: null,
              facilityName,
              facilityAddress,
              profile,
              hospital,
              createdBy,
              creatorName: fullName,
              isPackage: true,
            }),
          );

          const oncologyRegister = await manager.save(
            new OncologyConsultationRegisterModel({
              treatmentChart: null,
              therapyChart: null,
              oncologyHistory: savedOncologyConst,
              hospital,
              creatorName: fullName,
            }),
          );

          return { ...savedOncologyConst, oncologyRegister };
        }),
      );
    }

    if (radServiceDetails.length) {
      let radiologyContrastConfirmation = false;
      if (hospital?.id) {
        const facilityPreference = await manager
          .createQueryBuilder()
          .select('preference.radiologyContrastConfirmation')
          .from(FacilityPreferenceModel, 'preference')
          .where('preference.hospitalId = :hospitalId', {
            hospitalId: hospital.id,
          })
          .getRawOne();
        radiologyContrastConfirmation =
          facilityPreference?.preference_radiology_contrast_confirmation;
      }

      const examinationType = radServiceDetails.map((item) => {
        const { serviceName } = item;

        return {
          examType: serviceName,
          priority,
        };
      });

      radiology = await manager.save(
        InvestigationModel,
        new InvestigationModel({
          requestType: InvestigationRequestType.Radiology,
          requestDate,
          examinationType,
          priority,
          rank,
          department,
          serviceDetails: null,
          orderedBy,
          specialty,
          facilityName,
          facilityAddress,
          external: false,
          clinifyId,
          profile,
          hospital,
          createdBy,
          referringHospital: hospital,
          creatorName: fullName,
          isPackage: true,
          radiologyContrastConfirmation,
        }),
      );
    }

    if (postServiceDetails.length) {
      postnatals = await Promise.all(
        postServiceDetails.map(async () => {
          return await manager.save(
            PostnatalModel,
            new PostnatalModel({
              visitationDateTime: requestDate,
              seenBy: orderedBy,
              serviceDetails: null,
              rank,
              department,
              facilityName,
              facilityAddress,
              profile,
              hospital,
              createdBy,
              creatorName: fullName,
              isPackage: true,
            }),
          );
        }),
      );
    }

    if (proServiceDetails.length) {
      const procedureType = proServiceDetails.map((item) => {
        const { serviceName } = item;

        return {
          type: serviceName,
          priority,
        } as ProcedureTypeInput;
      });

      procedure = await manager.save(
        SurgeryModel,
        new SurgeryModel({
          surgeryDate: requestDate,
          procedureType,
          operatedBy: orderedBy,
          specialty,
          rank,
          department,
          serviceDetails: null,
          facilityAddress,
          profile,
          hospital,
          createdBy,
          creatorName: fullName,
          isPackage: true,
        }),
      );
    }

    return {
      admissions,
      antenatal,
      consultations,
      consultProcedure,
      immunization,
      laboratory,
      labourAndDeliveries,
      medication,
      nursingService,
      oncology,
      radiology,
      postnatals,
      procedure,
    };
  }

  private async runSubscriptionAndNotification(
    records: IRecords,
  ): Promise<void> {
    const {
      admissions,
      antenatal,
      consultations,
      consultProcedure,
      immunization,
      laboratory,
      labourAndDeliveries,
      medication,
      nursingService,
      oncology,
      radiology,
      postnatals,
      procedure,
    } = records;

    if (admissions) {
      await Promise.all(
        admissions.map(async (item) => {
          const details = {
            modelName: DashboardIcon.Admission,
            action: NotificationTag.Admitted,
            item,
          };
          this.notificationService.handleNoticationEvent({
            profile: item.createdBy,
            details,
          });

          await this.pubSub.publish(AdmissionEvent, {
            admission: item,
            [AdmissionEvent]: item?.creatorId,
          });
          await this.pubSub.publish(InPatientEvent, {
            admission: item,
            [InPatientEvent]: EventType.ADDED,
          });
          await this.pubSub.publish(AdmissionAdded, {
            [AdmissionAdded]: item,
          });

          return item;
        }),
      );
    }

    if (antenatal) {
      const details = {
        modelName: DashboardIcon.Antenatal,
        action: NotificationTag.Created,
        item: antenatal,
      };
      this.notificationService.handleNoticationEvent({
        profile: antenatal.createdBy,
        details,
      });

      await this.pubSub.publish(AntenatalEvent, {
        profileId: antenatal.profileId,
        [AntenatalEvent]: antenatal.creatorId,
      });
      await this.pubSub.publish(AntenatalAdded, {
        [AntenatalAdded]: antenatal,
      });
    }

    if (consultations) {
      await Promise.all(
        consultations.map(async (item) => {
          const details = {
            modelName: DashboardIcon.Consultation,
            action: NotificationTag.Created,
            item,
          };
          this.notificationService.handleNoticationEvent({
            profile: item.createdBy,
            details,
          });

          await this.pubSub.publish(ConsultationEvent, {
            consultation: item,
            [ConsultationEvent]: item?.creatorId,
          });
          await this.pubSub.publish(ConsultationAdded, {
            consultation: {
              hospitalIds: [item?.hospitalId, item?.referredTo?.hospitalId],
            },
            [ConsultationAdded]: item,
          });
          return item;
        }),
      );
    }

    if (consultProcedure) {
      const details = {
        modelName: DashboardIcon.RequestProcedure,
        action: NotificationTag.Requested,
        item: consultProcedure,
      };
      this.notificationService.handleNoticationEvent({
        profile: consultProcedure.createdBy,
        details,
      });

      await this.pubSub.publish(ConsultationEvent, {
        consultation: consultProcedure,
        [ConsultationEvent]: consultProcedure?.creatorId,
      });
      await this.pubSub.publish(RequestProcedureAdded, {
        [RequestProcedureAdded]: procedure,
      });
    }

    if (immunization) {
      const details = {
        modelName: DashboardIcon.Immunization,
        action: NotificationTag.Created,
        item: immunization,
      };
      this.notificationService.handleNoticationEvent({
        profile: immunization.createdBy,
        details,
      });

      await this.pubSub.publish(ImmunizationEvent, {
        immunization,
        [ImmunizationEvent]: immunization?.creatorId,
      });
      await this.pubSub.publish(ImmunizationAdded, {
        [ImmunizationAdded]: immunization,
      });
    }

    if (laboratory) {
      const details = {
        modelName: DashboardIcon.Laboratory,
        action: NotificationTag.Ordered,
        item: laboratory,
      };
      this.notificationService.handleNoticationEvent({
        profile: laboratory.createdBy,
        details,
      });

      await this.pubSub.publish(LaboratoryEvent, {
        laboratory,
        [LaboratoryEvent]: EventType.ADDED,
      });
      await this.pubSub.publish(LaboratoryAdded, {
        [LaboratoryAdded]: laboratory,
      });
      await this.pubSub.publish(InvestigationAdded, {
        [InvestigationAdded]: laboratory,
      });
      await this.pubSub.publish(InvestigationEvent, {
        investigation: laboratory,
        [InvestigationEvent]: EventType.ADDED,
      });
    }

    if (labourAndDeliveries) {
      await Promise.all(
        labourAndDeliveries.map(async (item) => {
          const details = {
            modelName: DashboardIcon.LabourDelivery,
            action: NotificationTag.Created,
            item,
          };
          this.notificationService.handleNoticationEvent({
            profile: item.createdBy,
            details,
          });

          await this.pubSub.publish(AntenatalEvent, {
            profileId: item.profileId,
            [AntenatalEvent]: item?.creatorId,
          });
          await this.pubSub.publish(LabourAndDeliveryAdded, {
            [LabourAndDeliveryAdded]: item,
          });
          return item;
        }),
      );
    }

    if (medication) {
      const details = {
        modelName: DashboardIcon.Medication,
        action: NotificationTag.Prescribed,
        item: medication,
      };
      this.notificationService.handleNoticationEvent({
        profile: medication.createdBy,
        details,
      });

      await this.pubSub.publish(MedicationEvent, {
        medication,
        [MedicationEvent]: medication.creatorId,
      });
      await this.pubSub.publish(PrescriptionEvent, {
        medication,
        [PrescriptionEvent]: EventType.ADDED,
      });
      await this.pubSub.publish(MedicationAdded, {
        [MedicationAdded]: medication,
      });
    }

    if (nursingService) {
      const details = {
        modelName: DashboardIcon.NursingService,
        action: NotificationTag.Created,
        item: nursingService,
      };
      this.notificationService.handleNoticationEvent({
        profile: nursingService.createdBy,
        details,
      });

      await this.pubSub.publish(NursingServiceEvent, {
        nursingService,
        [NursingServiceEvent]: nursingService.creatorId,
      });

      await this.pubSub.publish(NursingServiceAdded, {
        [NursingServiceAdded]: nursingService,
      });
    }

    if (oncology) {
      await Promise.all(
        oncology.map(async (item) => {
          const details = {
            modelName: DashboardIcon.OncologyConsultationHistory,
            action: NotificationTag.Added,
            item,
          };
          this.notificationService.handleNoticationEvent({
            profile: item.createdBy,
            details,
          });

          await this.pubSub.publish(ConsultationEvent, {
            consultation: item,
            [ConsultationEvent]: item?.creatorId,
          });
          await this.pubSub.publish(OncologyConsultationAdded, {
            [OncologyConsultationAdded]: item,
          });
          return item;
        }),
      );
    }

    if (radiology) {
      const details = {
        modelName: DashboardIcon.Radiology,
        action: NotificationTag.Ordered,
        item: radiology,
      };
      this.notificationService.handleNoticationEvent({
        profile: radiology.createdBy,
        details,
      });

      await this.pubSub.publish(RadiologyEvent, {
        radiology,
        [RadiologyEvent]: EventType.ADDED,
      });
      await this.pubSub.publish(RadiologyAdded, {
        [RadiologyAdded]: radiology,
      });
      await this.pubSub.publish(InvestigationAdded, {
        [InvestigationAdded]: radiology,
      });
      await this.pubSub.publish(InvestigationEvent, {
        investigation: radiology,
        [InvestigationEvent]: EventType.ADDED,
      });
    }

    if (postnatals) {
      await Promise.all(
        postnatals.map(async (item) => {
          const details = {
            modelName: DashboardIcon.Postnatal,
            action: NotificationTag.Created,
            item,
          };
          this.notificationService.handleNoticationEvent({
            profile: item.createdBy,
            details,
          });

          await this.pubSub.publish(AntenatalEvent, {
            profileId: item.profileId,
            [AntenatalEvent]: item?.creatorId,
          });
          await this.pubSub.publish(PostnatalAdded, {
            [PostnatalAdded]: item,
          });
          return item;
        }),
      );
    }

    if (procedure) {
      const details = {
        modelName: DashboardIcon.Procedure,
        action: NotificationTag.Ordered,
        item: procedure,
      };
      this.notificationService.handleNoticationEvent({
        profile: procedure.createdBy,
        details,
      });

      await this.pubSub.publish(ProcedureEvent, {
        procedure,
        [ProcedureEvent]: procedure?.creatorId,
      });
      await this.pubSub.publish(ProcedureAdded, {
        [ProcedureAdded]: procedure,
      });
    }
  }

  async afterInsert(event: InsertEvent<RequestPackageModel>): Promise<void> {
    const manager: EntityManager = event.manager;
    const entityRecord: RequestPackageModel = event.entity;

    if (!entityRecord || !manager) return;

    const serviceList = this.generateServiceList(entityRecord.serviceDetails);

    const records = await this.createRecods(manager, entityRecord, serviceList);

    await this.runSubscriptionAndNotification(records);
  }
}
