import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { RequestPackageResolver } from './request-packages.resolver';
import { NewRequestPackageInput } from '../inputs/request-packages.input';
import { RequestPackageModel } from '../models/request-packages.model';
import { RequestPackageService } from '../services/request-packages.service';
import { billFactory } from '@clinify/__mocks__/factories/bill.factory';
import { requestPackageFactory } from '@clinify/__mocks__/factories/package.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { PermissionService } from '@clinify/authorization/services/permission.service';
import { BillModel } from '@clinify/bills/models/bill.model';
import { ProfileModel } from '@clinify/users/models/profile.model';

const mockRequestPackageService = {
  getOneRequestPackage: jest.fn(),
  saveRequestPackage: jest.fn(),
  updateRequestPackage: jest.fn(),
  deleteRequestPackages: jest.fn(),
  archiveRequestPackages: jest.fn(),
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

describe('RequestPackageResolver', () => {
  let resolver: RequestPackageResolver;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RequestPackageResolver,
        RequestPackageService,
        PermissionService,
        {
          provide: RequestPackageService,
          useValue: mockRequestPackageService,
        },
        {
          provide: getRepositoryToken(PermissionModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: {},
        },
        {
          provide: EntityManager,
          useValue: {},
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
      ],
    }).compile();

    resolver = module.get<RequestPackageResolver>(RequestPackageResolver);
  });

  it('requestPackage(): should call getOneRequestPackage service method', async () => {
    const mutator: ProfileModel = profileFactory.build();

    await resolver.requestPackage(mutator, 'request-package-id', 'clinify-id');

    expect(mockRequestPackageService.getOneRequestPackage).toHaveBeenCalledWith(
      mutator,
      'request-package-id',
    );
  });

  it('addRequestPackage(): should call saveRequestPackage service method', async () => {
    const mutator: ProfileModel = profileFactory.build();
    const input: NewRequestPackageInput = requestPackageFactory.build();
    const bill: BillModel = billFactory.build();

    const response = new RequestPackageModel({
      ...input,
      createdBy: mutator,
      creatorName: mutator.fullName,
      bill,
    });

    mockRequestPackageService.saveRequestPackage = jest.fn(() =>
      Promise.resolve(response),
    );

    await resolver.addRequestPackage(mutator, input, 'request-package-id');

    expect(mockRequestPackageService.saveRequestPackage).toHaveBeenCalledWith(
      mutator,
      { ...input, id: 'request-package-id' },
    );

    expect(pubSubMock.publish).toHaveBeenCalledWith('RequestPackageEvent', {
      requestPackage: response,
      RequestPackageEvent: response,
    });

    expect(pubSubMock.publish).toHaveBeenCalledWith('RequestPackageAdded', {
      RequestPackageAdded: response,
    });

    expect(pubSubMock.publish).toHaveBeenCalledWith('BillingEvent', {
      billing: response,
      BillingEvent: 'Added',
    });

    expect(pubSubMock.publish).toHaveBeenCalledWith('OrgBillAdded', {
      OrgBillAdded: bill,
    });
  });

  it('updateRequestPackage(): should call updateRequestPackage service method', async () => {
    const mutator: ProfileModel = profileFactory.build();
    const input: NewRequestPackageInput = requestPackageFactory.build();

    const response = new RequestPackageModel({
      ...input,
      createdBy: mutator,
      creatorName: mutator.fullName,
    });

    mockRequestPackageService.updateRequestPackage = jest.fn(() =>
      Promise.resolve(response),
    );

    await resolver.updateRequestPackage(mutator, input, 'request-package-id');

    expect(mockRequestPackageService.updateRequestPackage).toHaveBeenCalledWith(
      mutator,
      input,
      'request-package-id',
    );

    expect(pubSubMock.publish).toHaveBeenCalledWith('RequestPackageEvent', {
      requestPackage: response,
      RequestPackageEvent: response,
    });

    expect(pubSubMock.publish).toHaveBeenCalledWith('RequestPackageUpdated', {
      RequestPackageUpdated: response,
    });
  });

  it('deleteRequestPackages(): should call deleteRequestPackages service method', async () => {
    const mutator: ProfileModel = profileFactory.build();
    const input: NewRequestPackageInput = requestPackageFactory.build();
    const bill: BillModel = billFactory.build();

    const response = new RequestPackageModel({
      ...input,
      createdBy: mutator,
      creatorName: mutator.fullName,
      bill,
    });

    mockRequestPackageService.deleteRequestPackages = jest.fn(() =>
      Promise.resolve([response]),
    );

    await resolver.deleteRequestPackages(
      mutator,
      ['request-package-id'],
      'clinify-id',
    );

    expect(
      mockRequestPackageService.deleteRequestPackages,
    ).toHaveBeenCalledWith(mutator, ['request-package-id']);

    expect(pubSubMock.publish).toHaveBeenCalledWith('RequestPackageEvent', {
      requestPackage: response,
      RequestPackageEvent: 'Deleted',
    });

    expect(pubSubMock.publish).toHaveBeenCalledWith('RequestPackageRemoved', {
      RequestPackageRemoved: [response],
    });

    expect(pubSubMock.publish).toHaveBeenCalledWith('OrgBillRemoved', {
      OrgBillRemoved: [bill],
    });
  });

  it('archiveRequestPackages(): should call archiveRequestPackages service method with false', async () => {
    const mutator: ProfileModel = profileFactory.build();
    const input: NewRequestPackageInput = requestPackageFactory.build();
    const bill: BillModel = billFactory.build();

    const response = new RequestPackageModel({
      ...input,
      createdBy: mutator,
      creatorName: mutator.fullName,
      bill,
    });

    mockRequestPackageService.archiveRequestPackages = jest.fn(() =>
      Promise.resolve([response]),
    );

    await resolver.archiveRequestPackages(
      mutator,
      ['request-package-id'],
      false,
      'clinify-id',
    );

    expect(
      mockRequestPackageService.archiveRequestPackages,
    ).toHaveBeenCalledWith(mutator, ['request-package-id'], false);

    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'RequestPackageUnarchived',
      {
        RequestPackageUnarchived: [response],
      },
    );
  });

  it('archiveRequestPackages(): should call archiveRequestPackages service method with true', async () => {
    const mutator: ProfileModel = profileFactory.build();
    const input: NewRequestPackageInput = requestPackageFactory.build();
    const bill: BillModel = billFactory.build();

    const response = new RequestPackageModel({
      ...input,
      createdBy: mutator,
      creatorName: mutator.fullName,
      bill,
    });

    mockRequestPackageService.archiveRequestPackages = jest.fn(() =>
      Promise.resolve([response]),
    );

    await resolver.archiveRequestPackages(
      mutator,
      ['request-package-id'],
      true,
      'clinify-id',
    );

    expect(
      mockRequestPackageService.archiveRequestPackages,
    ).toHaveBeenCalledWith(mutator, ['request-package-id'], true);

    expect(pubSubMock.publish).toHaveBeenCalledWith('RequestPackageArchived', {
      RequestPackageArchived: [response],
    });
  });

  it('packageEventHandler(): should call pubsub asyncIterator with RequestPackageEvent', () => {
    resolver.packageEventHandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'RequestPackageEvent',
    );
  });

  it('addPackageSubsHandler(): should call pubsub asyncIterator with RequestPackageAdded', () => {
    resolver.addPackageSubsHandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'RequestPackageAdded',
    );
  });

  it('updatePackageSubsHandler(): should call pubsub asyncIterator with RequestPackageUpdated', () => {
    resolver.updatePackageSubsHandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'RequestPackageUpdated',
    );
  });

  it('removePackageshandler(): should call pubsub asyncIterator with RequestPackageRemoved', () => {
    resolver.removePackageshandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'RequestPackageRemoved',
    );
  });

  it('archivePackageshandler(): should call pubsub asyncIterator with RequestPackageArchived', () => {
    resolver.archivePackageshandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'RequestPackageArchived',
    );
  });

  it('unarchivePackageshandler(): should call pubsub asyncIterator with RequestPackageUnarchived', () => {
    resolver.unarchivePackageshandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'RequestPackageUnarchived',
    );
  });

  it('getBill(): should return bull information', () => {
    const bill = billFactory.build();
    const root = {
      ...requestPackageFactory.build(),
      hospitalId: 'hospital-id',
    };
    const mutator = { ...profileFactory.build(), hospitalId: 'hospital-id' };

    const response = resolver.getBill(mutator, { ...root, bill } as any);

    expect(response).toStrictEqual(bill);
  });

  it('getBill(): should return bull information', () => {
    const bill = billFactory.build();
    const root = {
      ...requestPackageFactory.build(),
      hospitalId: 'hospital-id',
    };
    const mutator = {
      ...profileFactory.build(),
      hospitalId: 'another-hospital-id',
    };

    const response = resolver.getBill(mutator, { ...root, bill } as any);

    expect(response).toStrictEqual(null);
  });
});
