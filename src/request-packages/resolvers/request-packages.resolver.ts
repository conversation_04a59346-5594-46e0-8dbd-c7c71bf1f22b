import { Inject, UseGuards } from '@nestjs/common';
import {
  Args,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
  Subscription,
} from '@nestjs/graphql';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import {
  NewRequestPackageInput,
  RequestPackageInput,
} from '../inputs/request-packages.input';
import { RequestPackageModel } from '../models/request-packages.model';
import { RequestPackageService } from '../services/request-packages.service';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { ProfileDataAccessGuard } from '@clinify/authentication/guards/profile-data-acess.guard';
import { AuthorizationGuard } from '@clinify/authorization/guards/authorization.guard';
import { BillModel } from '@clinify/bills/models/bill.model';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { EventType } from '@clinify/shared/enums/events';
import { AppServices } from '@clinify/shared/enums/services';
import { ProfileModel } from '@clinify/users/models/profile.model';
import {
  filterRequestPackageAdded,
  filterRequestPackageArchived,
  filterRequestPackageEvent,
  filterRequestPackageRemoved,
  filterRequestPackageUnarchived,
  filterRequestPackageUpdated,
} from '@clinify/utils/subscriptions/filters';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const {
  BillingEvent,
  OrgBillAdded,
  OrgBillRemoved,
  RequestPackageEvent,
  RequestPackageAdded,
  RequestPackageUpdated,
  RequestPackageRemoved,
  RequestPackageArchived,
  RequestPackageUnarchived,
} = SubscriptionTypes;

@LogService(AppServices.RequestPackage)
@UseGuards(GqlAuthGuard, AuthorizationGuard)
@Resolver(() => RequestPackageModel)
export class RequestPackageResolver {
  constructor(
    private readonly service: RequestPackageService,
    @Inject(PUB_SUB) private readonly pubSub: RedisPubSub,
  ) {}

  @UseGuards(ProfileDataAccessGuard('clinifyId'))
  @Query(() => RequestPackageModel)
  async requestPackage(
    @CurrentProfile() profile: ProfileModel,
    @Args('id') requestPackageId: string,
    @Args('clinifyId') _clinifyId: string,
  ): Promise<RequestPackageModel> {
    return this.service.getOneRequestPackage(profile, requestPackageId);
  }

  @Subscription(() => String, {
    name: RequestPackageEvent,
    filter: filterRequestPackageEvent,
  })
  packageEventHandler(@Args('profileId') _profileId: string): any {
    return this.pubSub.asyncIterator(RequestPackageEvent);
  }

  @Mutation(() => RequestPackageModel)
  async addRequestPackage(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'requestPackage',
      type: () => NewRequestPackageInput,
      nullable: false,
    })
    input: NewRequestPackageInput,
    @Args({ name: 'id', nullable: true }) id: string,
  ): Promise<any> {
    const item = await this.service.saveRequestPackage(profile, {
      ...input,
      ...(id && { id }),
    });
    await this.pubSub.publish(RequestPackageEvent, {
      requestPackage: item,
      [RequestPackageEvent]: item,
    });
    await this.pubSub.publish(RequestPackageAdded, {
      [RequestPackageAdded]: item,
    });

    await this.pubSub.publish(OrgBillAdded, {
      [OrgBillAdded]: item.bill,
    });
    await this.pubSub.publish(BillingEvent, {
      billing: item,
      [BillingEvent]: EventType.ADDED,
    });

    return item;
  }

  @Subscription(() => RequestPackageModel, {
    name: RequestPackageAdded,
    filter: filterRequestPackageAdded,
  })
  addPackageSubsHandler(@Args('profileId') _profileId: string): any {
    return this.pubSub.asyncIterator(RequestPackageAdded);
  }

  @Mutation(() => RequestPackageModel)
  async updateRequestPackage(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'requestPackage',
      type: () => RequestPackageInput,
      nullable: false,
    })
    input: RequestPackageInput,
    @Args('id') requestPackageId: string,
  ): Promise<RequestPackageModel> {
    const item = await this.service.updateRequestPackage(
      profile,
      input,
      requestPackageId,
    );
    await this.pubSub.publish(RequestPackageEvent, {
      requestPackage: item,
      [RequestPackageEvent]: item,
    });
    await this.pubSub.publish(RequestPackageUpdated, {
      [RequestPackageUpdated]: item,
    });

    return item;
  }

  @Subscription(() => RequestPackageModel, {
    name: RequestPackageUpdated,
    filter: filterRequestPackageUpdated,
  })
  updatePackageSubsHandler(@Args('profileId') _profileId: string): any {
    return this.pubSub.asyncIterator(RequestPackageUpdated);
  }

  @Mutation(() => [RequestPackageModel])
  async deleteRequestPackages(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) requestPackageIds: string[],
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    _clinifyId: string,
  ): Promise<RequestPackageModel[]> {
    const items = await this.service.deleteRequestPackages(
      profile,
      requestPackageIds,
    );
    await this.pubSub.publish(RequestPackageEvent, {
      requestPackage: items[0],
      [RequestPackageEvent]: EventType.DELETED,
    });
    await this.pubSub.publish(RequestPackageRemoved, {
      [RequestPackageRemoved]: items,
    });

    await this.pubSub.publish(OrgBillRemoved, {
      [OrgBillRemoved]: items.map(({ bill }) => bill),
    });

    return items;
  }

  @Subscription(() => [RequestPackageModel], {
    name: RequestPackageRemoved,
    filter: filterRequestPackageRemoved,
  })
  removePackageshandler(@Args('profileId') _profileId: string): any {
    return this.pubSub.asyncIterator(RequestPackageRemoved);
  }

  @Mutation(() => [RequestPackageModel])
  async archiveRequestPackages(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) requestPackageIds: string[],
    @Args({
      name: 'archive',
      type: () => Boolean,
      defaultValue: true,
    })
    archive: boolean,
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    _clinifyId: string,
  ): Promise<RequestPackageModel[]> {
    const items = await this.service.archiveRequestPackages(
      profile,
      requestPackageIds,
      archive,
    );

    const PUB_SUB_TRIGGER = archive
      ? RequestPackageArchived
      : RequestPackageUnarchived;
    await this.pubSub.publish(PUB_SUB_TRIGGER, {
      [PUB_SUB_TRIGGER]: items,
    });

    return items;
  }

  @Subscription(() => [RequestPackageModel], {
    name: RequestPackageArchived,
    filter: filterRequestPackageArchived,
  })
  archivePackageshandler(@Args('profileId') _profileId: string): any {
    return this.pubSub.asyncIterator(RequestPackageArchived);
  }

  @Subscription(() => [RequestPackageModel], {
    name: RequestPackageUnarchived,
    filter: filterRequestPackageUnarchived,
  })
  unarchivePackageshandler(@Args('profileId') _profileId: string): any {
    return this.pubSub.asyncIterator(RequestPackageUnarchived);
  }

  // Hide bill property from external view
  @ResolveField('bill', () => BillModel, { nullable: true })
  getBill(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: RequestPackageModel,
  ): BillModel {
    return profile.hospitalId === root.hospitalId ||
      profile.id === root.profileId
      ? root.bill
      : null;
  }
}
