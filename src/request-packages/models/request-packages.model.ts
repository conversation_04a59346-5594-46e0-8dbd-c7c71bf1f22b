import { Field, ID, ObjectType } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import { IsDate, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AuditEntitiesWithProfile } from './base-audits.entity';
import { BillModel } from '@clinify/bills/models/bill.model';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { PackageServiceInput } from '@clinify/packages/inputs/package.input';

@ObjectType()
@Entity({ name: 'request_packages' })
export class RequestPackageModel extends AuditEntitiesWithProfile {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  @IsUUID('4')
  id?: string;

  @IsDate()
  @Field(() => Date, { nullable: false })
  @CreateDateColumn({ name: 'request_date', nullable: false })
  requestDate: Date;

  @Field(() => String, { nullable: false })
  @Column({ name: 'package_name', nullable: false })
  packageName: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'priority' })
  priority: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'category' })
  category: string;

  @Field(() => [PackageServiceInput], { nullable: true })
  @Column({ name: 'service_details', type: 'jsonb', nullable: true })
  serviceDetails?: PackageServiceInput[];

  @Field(() => String, { nullable: false })
  @Column({ name: 'price', nullable: false })
  price: string;

  @Field({ nullable: true })
  @Column({ name: 'ordered_by', nullable: true })
  orderedBy: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'specialty' })
  specialty: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'rank' })
  rank: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'department' })
  department: string;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'patient_type' })
  patientType?: string;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, name: 'payment_type' })
  paymentType?: string;

  @Field({ nullable: true })
  @Column({ name: 'facility_name', nullable: true })
  facilityName: string;

  @Field({ nullable: true })
  @Column({ name: 'facility_address', nullable: true })
  facilityAddress: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'additional_note' })
  additionalNote: string;

  @Column({ name: 'archived', default: false, nullable: false })
  archived?: boolean;

  @Field(() => HospitalModel, { nullable: true })
  @ManyToOne(() => HospitalModel, (hospital) => hospital.admissions)
  @JoinColumn({ name: 'hospital' })
  @Type(() => HospitalModel)
  hospital: HospitalModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'hospital', nullable: true })
  hospitalId?: string;

  @ManyToOne(() => BillModel, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'bill' })
  bill?: BillModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'bill', nullable: true })
  billId: string;

  constructor(packageBundle: Partial<RequestPackageModel>) {
    super();
    Object.assign(this, packageBundle);
  }
}
