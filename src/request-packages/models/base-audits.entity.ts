import { Field, ObjectType } from '@nestjs/graphql';
import { IsDate } from 'class-validator';
import {
  Column,
  CreateDateC<PERSON>umn,
  JoinColumn,
  ManyToOne,
  UpdateDateColumn,
} from 'typeorm';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
export abstract class BaseAudits {
  @IsDate()
  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate?: Date;

  @IsDate()
  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'updated_by', nullable: true })
  lastModifierId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'last_modifier_name', nullable: true })
  lastModifierName?: string;

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'created_by' })
  createdBy?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'created_by', nullable: true })
  creatorId?: string;

  @Field(() => String, { nullable: true })
  @Column({ name: 'creator_name' })
  creatorName?: string;
}

@ObjectType()
export abstract class AuditEntitiesWithProfile extends BaseAudits {
  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel, (profile) => profile, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn({ name: 'profile' })
  profile?: ProfileModel;

  @Field(() => String, { nullable: true })
  @Column({ name: 'profile', nullable: true })
  profileId?: string;
}
