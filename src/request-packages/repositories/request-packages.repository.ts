import { NotFoundException } from '@nestjs/common';
import { In, Repository } from 'typeorm';
import { RequestPackageFilterInput } from '../inputs/request-packages-filter.input';
import { RequestPackageInput } from '../inputs/request-packages.input';
import { RequestPackageModel } from '../models/request-packages.model';
import { RequestPackageResponse } from '../responses/request-packages.response';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { takePaginatedResponses } from '@clinify/utils/pagination';

export interface IRequestPackageRepository
  extends Repository<RequestPackageModel> {
  this: Repository<RequestPackageModel>;
  findByProfile(
    mutator: ProfileModel,
    profileId: string,
    filterOptions: RequestPackageFilterInput,
  ): Promise<RequestPackageResponse>;
  getOneRequestPackage(
    mutator: ProfileModel,
    requestPackageId: string,
  ): Promise<RequestPackageModel>;
  updateRequestPackage(
    mutator: ProfileModel,
    input: RequestPackageInput,
    requestPackageId: string,
  ): Promise<RequestPackageModel>;
  deleteRequestPackages(
    mutator: ProfileModel,
    requestPackageIds: string[],
  ): Promise<RequestPackageModel[]>;
  archiveRequestPackages(
    mutator: ProfileModel,
    requestPackageIds: string[],
    archive: boolean,
  ): Promise<RequestPackageModel[]>;
}

export const CustomRequestPackageRepoMethods: Pick<
  IRequestPackageRepository,
  | 'findByProfile'
  | 'getOneRequestPackage'
  | 'updateRequestPackage'
  | 'deleteRequestPackages'
  | 'archiveRequestPackages'
> = {
  async findByProfile(
    this: IRequestPackageRepository,
    mutator: ProfileModel,
    profileId: string,
    filterOptions: RequestPackageFilterInput,
  ): Promise<RequestPackageResponse> {
    const { skip, take, dateRange, keyword, archive } = {
      ...filterOptions,
    };

    let query = this.createQueryBuilder('request_packages')
      .leftJoinAndSelect('request_packages.profile', 'profile')
      .leftJoinAndSelect('request_packages.bill', 'requestPackageBill')
      .leftJoinAndSelect('request_packages.hospital', 'hospital')
      .where('request_packages.profile = :profileId', { profileId })
      .andWhere('request_packages.archived = :archived', {
        archived: !!archive,
      });

    if (mutator.branchIds?.length)
      query = query.andWhere(
        `((hospital.facility_created_visibility = false OR hospital.facility_created_visibility IS NULL)
        OR request_packages.hospital IN(:...branchIds))`,
        {
          branchIds: mutator.branchIds,
        },
      );

    if (dateRange?.from) {
      query = query.andWhere('request_packages.requestDate >= :from', {
        from: dateRange.from,
      });
    }

    if (dateRange?.to) {
      query = query.andWhere('request_packages.requestDate < :to', {
        to: dateRange.to,
      });
    }

    if (keyword) {
      query = query.andWhere(
        `(
          request_packages.package_name ILIKE :keyword OR
          request_packages.service_details :: jsonb :: text ILIKE :keyword OR
          request_packages.orderedBy ILIKE :keyword OR
          request_packages.specialty ILIKE :keyword OR
          request_packages.bill::text ILIKE :keyword OR
          request_packages.facilityName ILIKE :keyword
        )`,
        {
          keyword: `%${keyword}%`,
        },
      );
    }

    query = query
      .orderBy('request_packages.createdDate', 'DESC')
      .skip(skip)
      .take(take);
    const response = await query.getManyAndCount();

    return new RequestPackageResponse(
      ...takePaginatedResponses(response, take),
    );
  },

  async getOneRequestPackage(
    this: IRequestPackageRepository,
    mutator: ProfileModel,
    requestPackageId: string,
  ): Promise<RequestPackageModel> {
    return this.findOneOrFail({
      where: { id: requestPackageId },
      relations: ['createdBy', 'updatedBy'],
      withDeleted: true,
    }).catch(() => {
      throw new NotFoundException('Record Not Found');
    });
  },

  async updateRequestPackage(
    this: IRequestPackageRepository,
    mutator: ProfileModel,
    input: RequestPackageInput,
    requestPackageId: string,
  ): Promise<RequestPackageModel> {
    const existingRecord = await this.findOneOrFail({
      where: {
        id: requestPackageId,
        createdBy: { id: mutator.id },
      },
    }).catch(() => {
      throw new NotFoundException('Record Not Found');
    });

    return this.save({
      ...existingRecord,
      requestDate: input.requestDate || existingRecord.requestDate,
      priority: input.priority || existingRecord.priority,
      category: input.category || existingRecord.category,
      additionalNote: input.additionalNote || existingRecord.additionalNote,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  },

  async deleteRequestPackages(
    this: IRequestPackageRepository,
    mutator: ProfileModel,
    requestPackageIds: string[],
  ): Promise<RequestPackageModel[]> {
    const existingRecord = await this.find({
      where: {
        id: In(requestPackageIds),
        createdBy: { id: mutator.id },
      },
      join: {
        alias: 'request_packages',
        leftJoinAndSelect: {
          bill: 'request_packages.bill',
          receiverProfile: 'bill.receiverProfile',
          billDetails: 'bill.details',
          subBills: 'billDetails.subBills',
          billDetailsCreatedBy: 'billDetails.createdBy',
        },
      },
    });

    if (existingRecord.length) {
      await this.delete(existingRecord.map(({ id }) => id));
    }

    return existingRecord;
  },

  async archiveRequestPackages(
    this: IRequestPackageRepository,
    mutator: ProfileModel,
    requestPackageIds: string[],
    archive: boolean,
  ): Promise<RequestPackageModel[]> {
    const existingRecord = await this.find({
      where: {
        id: In(requestPackageIds),
        createdBy: { id: mutator.id },
      },
    });

    const validIds = existingRecord.map(({ id }) => id);
    await this.createQueryBuilder('request_packages')
      .update(RequestPackageModel)
      .set({ archived: archive, updatedDate: () => 'updated_date' })
      .whereInIds(validIds)
      .execute();

    return existingRecord.map((medBundle) => ({
      ...medBundle,
      archived: archive,
    }));
  },
};
