import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import moment from 'moment/moment';
import { DataSource, EntityManager } from 'typeorm';
import {
  CustomRequestPackageRepoMethods,
  IRequestPackageRepository,
} from './request-packages.repository';
import { RequestPackageInput } from '../inputs/request-packages.input';
import { RequestPackageModel } from '../models/request-packages.model';
import { requestPackageFactory } from '@clinify/__mocks__/factories/package.factory';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendDSRepo, extendModel } from '@clinify/database/extendModel';
import { UserType } from '@clinify/shared/enums/users';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { createRequestPackages } from '@clinify/utils/tests/package.fixtures';
import { createUsers } from '@clinify/utils/tests/user.fixtures';

const chance = new Chance();

describe('RequestPackageRepository', () => {
  let repository: IRequestPackageRepository;
  let manager: EntityManager;
  let mutator: ProfileModel;
  let patient: ProfileModel;
  const filterDate = new Date();

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        extendModel(RequestPackageModel, CustomRequestPackageRepoMethods),
      ],
    }).compile();

    const dataSource: DataSource = module.get<DataSource>(DataSource);
    manager = dataSource.manager;
    repository = extendDSRepo<IRequestPackageRepository>(
      dataSource,
      RequestPackageModel,
      CustomRequestPackageRepoMethods,
    );

    const [{ createdBy, profile }] = await createRequestPackages(manager, 3);
    mutator = createdBy;
    patient = profile;
  });

  it('findByProfile(): should return requested packages', async () => {
    const response = await repository.findByProfile(mutator, patient.id, {
      dateRange: {
        from: moment(filterDate).startOf('day').toDate(),
        to: moment(filterDate).endOf('day').toDate(),
      },
    });

    expect(response.totalCount).toBe(3);
  });

  it('findByProfile(): should search by package name', async () => {
    const input: RequestPackageInput[] = [
      {
        ...requestPackageFactory.build(),
        packageName: 'Test Package 1',
      },
      {
        ...requestPackageFactory.build(),
        packageName: 'Test Package 2',
      },
    ];

    const [user] = await createUsers(
      manager,
      1,
      mutator.hospital,
      undefined,
      undefined,
      UserType.Patient,
    );

    const profile = user.defaultProfile;

    await createRequestPackages(manager, input.length, mutator, profile, input);

    const response = await repository.findByProfile(mutator, profile.id, {
      dateRange: {
        from: moment(filterDate).startOf('day').toDate(),
        to: moment(filterDate).endOf('day').toDate(),
      },
      keyword: 'Test Package',
    });

    expect(response.totalCount).toBe(2);

    response.list.forEach(({ packageName }) => {
      expect(packageName.slice(0, 12)).toBe('Test Package');
    });
  });

  it('findByProfile(): should search by service details', async () => {
    const input: RequestPackageInput[] = [
      {
        ...requestPackageFactory.build(),
        serviceDetails: [
          {
            serviceType: 'Consultation',
            serviceName: 'General Consultation',
          },
        ],
      },
      {
        ...requestPackageFactory.build(),
        serviceDetails: [
          {
            serviceType: 'Consultation',
            serviceName: 'Specialist Consultation',
          },
        ],
      },
    ];

    const [user] = await createUsers(
      manager,
      1,
      mutator.hospital,
      undefined,
      undefined,
      UserType.Patient,
    );

    const profile = user.defaultProfile;

    await createRequestPackages(manager, input.length, mutator, profile, input);

    const response = await repository.findByProfile(mutator, profile.id, {
      dateRange: {
        from: moment(filterDate).startOf('day').toDate(),
        to: moment(filterDate).endOf('day').toDate(),
      },
      keyword: 'Consultation',
    });

    expect(response.totalCount).toBe(2);

    response.list.forEach(({ serviceDetails }) => {
      expect(serviceDetails[0].serviceType).toBe('Consultation');
    });

    const response2 = await repository.findByProfile(mutator, profile.id, {
      dateRange: {
        from: moment(filterDate).startOf('day').toDate(),
        to: moment(filterDate).endOf('day').toDate(),
      },
      keyword: 'General',
    });

    expect(response2.totalCount).toBe(1);

    response2.list.forEach(({ serviceDetails }) => {
      expect(serviceDetails[0].serviceName).toBe('General Consultation');
    });

    const response3 = await repository.findByProfile(mutator, profile.id, {
      dateRange: {
        from: moment(filterDate).startOf('day').toDate(),
        to: moment(filterDate).endOf('day').toDate(),
      },
      keyword: 'Specialist',
    });

    expect(response3.totalCount).toBe(1);

    response3.list.forEach(({ serviceDetails }) => {
      expect(serviceDetails[0].serviceName).toBe('Specialist Consultation');
    });
  });

  it('getOneRequestPackage(): should throw error when request package record is not found', async () => {
    await expect(
      repository.getOneRequestPackage(mutator, chance.guid({ version: 4 })),
    ).rejects.toThrow('Record Not Found');
  });

  it('getOneRequestPackage(): should fetch specific request package record', async () => {
    const input: RequestPackageInput = {
      ...requestPackageFactory.build(),
      packageName: 'Test Package 3',
    };

    const [user] = await createUsers(
      manager,
      1,
      mutator.hospital,
      undefined,
      undefined,
      UserType.Patient,
    );

    const profile = user.defaultProfile;

    const [{ id }] = await createRequestPackages(manager, 1, mutator, profile, [
      input,
    ]);

    const { packageName, creatorId, profileId } =
      await repository.getOneRequestPackage(mutator, id);

    expect(packageName).toBe('Test Package 3');
    expect(creatorId).toBe(mutator?.id);
    expect(profileId).toBe(profile?.id);
  });

  it('updateRequestPackage(): should throw error when request package record is not found', async () => {
    const input: RequestPackageInput = requestPackageFactory.build();

    await expect(
      repository.updateRequestPackage(
        mutator,
        input,
        chance.guid({ version: 4 }),
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateRequestPackage(): should update specific request package record', async () => {
    const newInput: RequestPackageInput = {
      ...requestPackageFactory.build(),
      additionalNote: 'First Additional Note',
    };

    const input: RequestPackageInput = {
      ...newInput,
      additionalNote: 'Second Additional Note',
    };

    const [{ id, additionalNote, creatorId }] = await createRequestPackages(
      manager,
      1,
      mutator,
      undefined,
      [newInput],
    );

    expect(additionalNote).toBe('First Additional Note');
    expect(creatorId).toBe(mutator?.id);

    const { additionalNote: updatedNote, lastModifierId } =
      await repository.updateRequestPackage(mutator, input, id);

    expect(updatedNote).toBe('Second Additional Note');
    expect(lastModifierId).toBe(mutator?.id);
  });

  it('deleteRequestPackages(): should delete request package records', async () => {
    const [user] = await createUsers(
      manager,
      1,
      mutator.hospital,
      undefined,
      undefined,
      UserType.OrganizationDoctor,
    );
    const orgProfile = user.defaultProfile;

    const [patient] = await createUsers(
      manager,
      1,
      mutator.hospital,
      undefined,
      undefined,
      UserType.Patient,
    );
    const profile = patient.defaultProfile;

    await createRequestPackages(manager, 5, orgProfile, profile);

    const response = await repository.findByProfile(orgProfile, profile.id, {});

    expect(response.totalCount).toBe(5);

    await repository.deleteRequestPackages(orgProfile, [
      response.list[0].id,
      response.list[1].id,
    ]);

    const response2 = await repository.findByProfile(
      orgProfile,
      profile.id,
      {},
    );

    expect(response2.totalCount).toBe(3);
  });

  it('archiveRequestPackages(): should archive or unarchive request package records', async () => {
    const [user] = await createUsers(
      manager,
      1,
      mutator.hospital,
      undefined,
      undefined,
      UserType.OrganizationDoctor,
    );
    const orgProfile = user.defaultProfile;

    const [patient] = await createUsers(
      manager,
      1,
      mutator.hospital,
      undefined,
      undefined,
      UserType.Patient,
    );
    const profile = patient.defaultProfile;

    await createRequestPackages(manager, 5, orgProfile, profile);

    const response = await repository.findByProfile(orgProfile, profile.id, {});

    expect(response.totalCount).toBe(5);

    await repository.archiveRequestPackages(
      orgProfile,
      [response.list[0].id, response.list[1].id, response.list[2].id],
      true,
    );

    const response2 = await repository.findByProfile(
      orgProfile,
      profile.id,
      {},
    );

    expect(response2.totalCount).toBe(2);

    await repository.archiveRequestPackages(
      orgProfile,
      [response.list[1].id],
      false,
    );

    const response3 = await repository.findByProfile(
      orgProfile,
      profile.id,
      {},
    );

    expect(response3.totalCount).toBe(3);
  });
});
