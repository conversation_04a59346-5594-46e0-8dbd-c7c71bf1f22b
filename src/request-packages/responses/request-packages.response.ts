import { Field, Int, ObjectType } from '@nestjs/graphql';
import { RequestPackageModel } from '../models/request-packages.model';

@ObjectType()
export class RequestPackageResponse {
  constructor(packages: RequestPackageModel[], totalCount: number) {
    this.list = packages;
    this.totalCount = totalCount;
  }

  @Field(() => Int)
  totalCount: number;

  @Field(() => [RequestPackageModel])
  list: RequestPackageModel[];
}
