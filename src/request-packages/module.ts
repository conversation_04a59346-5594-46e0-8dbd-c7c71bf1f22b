import { Lo<PERSON>, Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RequestPackageModel } from './models/request-packages.model';
import { CustomRequestPackageRepoMethods } from './repositories/request-packages.repository';
import { RequestPackageResolver } from './resolvers/request-packages.resolver';
import { RequestPackageService } from './services/request-packages.service';
import { RequestPackagesSubscriber } from './subsribers/request-packages.subscriber';
import { AuthorizationModule } from '@clinify/authorization/authorization.module';
import { extendModel } from '@clinify/database/extendModel';
import { NotificationsModel } from '@clinify/notifications/models/notifications.model';
import { NotificationsService } from '@clinify/notifications/services/notifications.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import PubSub, { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ProfileModel,
      RequestPackageModel,
      NotificationsModel,
    ]),
    AuthorizationModule,
  ],
  providers: [
    extendModel(RequestPackageModel, CustomRequestPackageRepoMethods),
    RequestPackageService,
    RequestPackageResolver,
    RequestPackagesSubscriber,
    NotificationsService,
    Logger,
    { provide: PUB_SUB, useFactory: () => PubSub },
  ],
  exports: [RequestPackageService],
})
export class RequestPackageModule {}
