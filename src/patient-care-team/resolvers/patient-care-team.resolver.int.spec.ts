import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { CustomPermissionRepoMethods } from '@clinify/authorization/repositories/permission.repository';
import { PermissionService } from '@clinify/authorization/services/permission.service';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendModel } from '@clinify/database/extendModel';
import { PatientCareTeamResolver } from '@clinify/patient-care-team/resolvers/patient-care-team.resolver';
import { PatientCareTeamService } from '@clinify/patient-care-team/services/patient-care-team.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { CustomProfileRepoMethods } from '@clinify/users/repositories/profile.repository';
import { mockPatientCareTeam } from '@mocks/factories/patient-care-team.factory';
import { mockProfile } from '@mocks/factories/profile.factory';

const MockPatientCareService = {
  updatePatientCareTeam: jest.fn(() => Promise.resolve(mockPatientCareTeam)),
};

describe('PatientCareTeamResolver', () => {
  let resolver: PatientCareTeamResolver;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        PatientCareTeamResolver,
        PermissionService,
        extendModel(PermissionModel, CustomPermissionRepoMethods),
        extendModel(ProfileModel, CustomProfileRepoMethods),
        {
          provide: PatientCareTeamService,
          useValue: MockPatientCareService,
        },
      ],
    }).compile();

    resolver = module.get(PatientCareTeamResolver);
  });

  afterEach(async () => {
    jest.clearAllMocks();
    await module.close();
  });

  afterAll(() => {
    jest.clearAllMocks();
  });

  it('resolver should be valid', () => {
    expect(resolver).toBeDefined();
  });

  it('updatePatientCareTeam', async () => {
    const input = {
      profileId: 'profile-id',
      team: [],
    };
    expect(await resolver.updatePatientCareTeam(mockProfile, input)).toEqual(
      mockPatientCareTeam,
    );
    expect(MockPatientCareService.updatePatientCareTeam).toHaveBeenCalledWith(
      mockProfile,
      input,
    );
  });
});
