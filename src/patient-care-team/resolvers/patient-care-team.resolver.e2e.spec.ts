import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { GraphQLModule } from '@nestjs/graphql';
import { Test } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import request from 'supertest';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { TestDataSourceOptions } from '@clinify/data-source';
import { PatientCareTeamModule } from '@clinify/patient-care-team/patient-care-team.module';
import { PatientCareTeamRepository } from '@clinify/patient-care-team/repositories/patient-care-team.repository';
import { UserType } from '@clinify/shared/enums/users';
import { mockPatientCareTeam } from '@mocks/factories/patient-care-team.factory';
import gqlAuthGuardMock, { gqlUserMock } from '@mocks/gqlAuthGuard.mock';

const MockPatientCareTeamRepository = {
  findOne: jest.fn(() => Promise.resolve(mockPatientCareTeam)),
  save: jest.fn(() => Promise.resolve(mockPatientCareTeam)),
};

describe('PatientCareTeamController', () => {
  let testHttpServer: request.SuperTest<request.Test>;
  let app: INestApplication;

  beforeAll(async () => {
    const builder = await Test.createTestingModule({
      imports: [
        PatientCareTeamModule,
        TypeOrmModule.forRoot(TestDataSourceOptions),
        EventEmitterModule.forRoot(),
        GraphQLModule.forRoot<ApolloDriverConfig>({
          debug: false,
          playground: false,
          driver: ApolloDriver,
          autoSchemaFile: true,
          installSubscriptionHandlers: true,
          subscriptions: {
            'graphql-ws': {
              onConnect: (connectionParams: Record<string, any>) => {
                return {
                  headers: {
                    ...connectionParams,
                  },
                };
              },
            },
          },
          include: [PatientCareTeamModule],
          context: ({ req }) => ({ req }),
        }),
      ],
    })
      .overrideProvider(PatientCareTeamRepository)
      .useValue(MockPatientCareTeamRepository)
      .overrideGuard(GqlAuthGuard)
      .useValue(gqlAuthGuardMock(UserType.OrganizationAdmin, gqlUserMock))
      .compile();

    app = builder.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    testHttpServer = request(app.getHttpServer());
  });

  afterAll(async () => await app.close());

  it('updatePatientCareTeam', (done) => {
    testHttpServer
      .post('/graphql')
      .set('Authorization', 'Bearer token')
      .send({
        operationName: null,
        query: `
        mutation {
          updatePatientCareTeam(input: {
            profileId: "${mockPatientCareTeam.profileId}",
            team: [
              {
                specialistId: "${mockPatientCareTeam.team[0].specialistId}",
                specialistRole: "${mockPatientCareTeam.team[0].specialistRole}",
                specialistFullName: "${mockPatientCareTeam.team[0].specialistFullName}",
                specialty: "${mockPatientCareTeam.team[0].specialty}",
                specialistTitle: "${mockPatientCareTeam.team[0].specialistTitle}",
              }
            ]
          }) {
            id
          }
        }
        `,
      })
      .expect(({ body }) => {
        const data = body.data.updatePatientCareTeam;
        expect(data).toEqual(
          expect.objectContaining({
            id: mockPatientCareTeam.id,
          }),
        );
      })
      .expect(200)
      .end(done);
  });
});
