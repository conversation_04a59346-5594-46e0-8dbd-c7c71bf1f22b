import { <PERSON><PERSON>, Resolve<PERSON><PERSON>, Resolver } from '@nestjs/graphql';
import { DataSource } from 'typeorm';
import { queryDSWithSlave } from '@clinify/database';
import { PatientCareSpecialist } from '@clinify/patient-care-team/dtos/patient-care-specialist';

@Resolver(() => PatientCareSpecialist)
export class PatientCareSpecialistResolver {
  constructor(private readonly dataSource: DataSource) {}

  @ResolveField(() => String, { nullable: true, name: 'rank' })
  async getRank(@Parent() root: PatientCareSpecialist): Promise<string> {
    if (!root.specialistId) return null;
    const res = await queryDSWithSlave(
      this.dataSource,
      `SELECT rank FROM details
            INNER JOIN profiles ON details.id = profiles.details AND profiles.id = '${root.specialistId}'`,
    );
    return res[0]?.rank;
  }
}
