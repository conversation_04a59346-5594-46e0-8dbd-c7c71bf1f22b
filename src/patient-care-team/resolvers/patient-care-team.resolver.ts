import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { AuthorizationGuard } from '@clinify/authorization/guards/authorization.guard';
import { PatientCareTeamInput } from '@clinify/patient-care-team/inputs/patient-care-team.input';
import { PatientCareTeamModel } from '@clinify/patient-care-team/models/patient-care-team.model';
import { PatientCareTeamService } from '@clinify/patient-care-team/services/patient-care-team.service';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { AppServices } from '@clinify/shared/enums/services';
import { ProfileModel } from '@clinify/users/models/profile.model';

@UseGuards(GqlAuthGuard, AuthorizationGuard)
@Resolver(() => PatientCareTeamModel)
@LogService(AppServices.PatientCareTeam)
export class PatientCareTeamResolver {
  constructor(private readonly service: PatientCareTeamService) {}

  @Mutation(() => PatientCareTeamModel)
  updatePatientCareTeam(
    @CurrentProfile() mutator: ProfileModel,
    @Args('input', { type: () => PatientCareTeamInput })
    input: PatientCareTeamInput,
  ): Promise<PatientCareTeamModel> {
    return this.service.updatePatientCareTeam(mutator, input);
  }
}
