import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { TestDataSourceOptions } from '@clinify/data-source';
import { PatientCareTeamRepository } from '@clinify/patient-care-team/repositories/patient-care-team.repository';

describe('PatientCareTeamRepository', () => {
  let module: TestingModule;
  let repo: PatientCareTeamRepository;
  let ds: DataSource;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeormExtendedModule.forCustomRepository([PatientCareTeamRepository]),
      ],
      providers: [],
    }).compile();

    repo = module.get(PatientCareTeamRepository);
  });

  it('should be defined', () => {
    expect(repo).toBeDefined();
  });
});
