import { Module } from '@nestjs/common';
import { AuthorizationModule } from '@clinify/authorization/authorization.module';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { PatientCareTeamRepository } from '@clinify/patient-care-team/repositories/patient-care-team.repository';
import { PatientCareSpecialistResolver } from '@clinify/patient-care-team/resolvers/patient-care-specialist.resolver';
import { PatientCareTeamResolver } from '@clinify/patient-care-team/resolvers/patient-care-team.resolver';
import { PatientCareTeamService } from '@clinify/patient-care-team/services/patient-care-team.service';

@Module({
  imports: [
    TypeormExtendedModule.forCustomRepository([PatientCareTeamRepository]),
    AuthorizationModule,
  ],
  providers: [
    PatientCareTeamService,
    PatientCareTeamResolver,
    PatientCareSpecialistResolver,
  ],
  exports: [PatientCareTeamService],
})
export class PatientCareTeamModule {}
