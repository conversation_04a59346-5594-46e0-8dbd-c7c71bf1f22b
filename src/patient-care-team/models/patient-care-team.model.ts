import { Field, ID, ObjectType } from '@nestjs/graphql';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { PatientCareSpecialist } from '@clinify/patient-care-team/dtos/patient-care-specialist';
import { ProfileModel } from '@clinify/users/models/profile.model';

@ObjectType()
@Entity({ name: 'patient_care_team' })
export class PatientCareTeamModel {
  constructor(invoice: Partial<PatientCareTeamModel>) {
    Object.assign(this, invoice);
  }

  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field({ nullable: false })
  @CreateDateColumn({ name: 'created_date' })
  createdDate: Date;

  @Field({ nullable: false })
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate: Date;

  @ManyToOne(() => HospitalModel, (hospital) => hospital.patientCareTeams, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'hospital_id' })
  hospital: HospitalModel;

  @Index()
  @Column({ name: 'hospital_id' })
  hospitalId: string;

  @ManyToOne(() => ProfileModel, (profile) => profile.patientCareTeams, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'profile_id' })
  profile: ProfileModel;

  @Index()
  @Column({ name: 'profile_id' })
  profileId: string;

  @Column({ name: 'team', type: 'jsonb' })
  @Field(() => [PatientCareSpecialist], { nullable: 'itemsAndList' })
  team: PatientCareSpecialist[];

  @Field(() => ProfileModel)
  @ManyToOne(() => ProfileModel)
  @JoinColumn({ name: 'creator_id' })
  createdBy: ProfileModel;

  @Field(() => String)
  @Column({ name: 'creator_id' })
  creatorId: string;

  @Field(() => ProfileModel, { nullable: true })
  @ManyToOne(() => ProfileModel)
  @JoinColumn({ name: 'last_modifier_id' })
  updatedBy?: ProfileModel;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'last_modifier_id' })
  lastModifierId?: string;
}
