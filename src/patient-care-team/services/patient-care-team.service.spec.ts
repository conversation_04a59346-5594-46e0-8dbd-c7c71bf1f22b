import { ConflictException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PatientCareTeamRepository } from '@clinify/patient-care-team/repositories/patient-care-team.repository';
import { PatientCareTeamService } from '@clinify/patient-care-team/services/patient-care-team.service';
import { mockPatientCareTeam } from '@mocks/factories/patient-care-team.factory';
import { mockProfile } from '@mocks/factories/profile.factory';

const MockPatientCareTeamRepository = {
  findOne: jest.fn(() => Promise.resolve(mockPatientCareTeam)),
  save: jest.fn(() => Promise.resolve(mockPatientCareTeam)),
};

describe('PatientCareTeamService', () => {
  let service: PatientCareTeamService;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        PatientCareTeamService,
        {
          provide: PatientCareTeamRepository,
          useValue: MockPatientCareTeamRepository,
        },
      ],
    }).compile();

    service = module.get(PatientCareTeamService);
  });

  afterEach(async () => {
    jest.clearAllMocks();
    await module.close();
  });

  afterAll(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('getPatientCareTeam', async () => {
    expect(
      await service.getPatientCareTeam('hospital-id', 'patient-id'),
    ).toEqual(mockPatientCareTeam);
    expect(MockPatientCareTeamRepository.findOne).toHaveBeenCalledWith({
      where: { hospitalId: 'hospital-id', profileId: 'patient-id' },
    });
  });

  it('updatePatientCareTeam', async () => {
    const mutator = { ...mockProfile, hospitalId: 'hospital-id', id: 'id' };
    const input = { profileId: 'profile-id', team: [] };
    expect(await service.updatePatientCareTeam(mutator, input)).toEqual(
      mockPatientCareTeam,
    );
    expect(MockPatientCareTeamRepository.save).toHaveBeenCalledWith(
      expect.objectContaining({
        id: mockPatientCareTeam.id,
        lastUpdatedBy: 'id',
      }),
    );
  });

  it('updatePatientCareTeam to save if it Not Found', async () => {
    MockPatientCareTeamRepository.findOne.mockResolvedValueOnce(null);
    const mutator = { ...mockProfile, hospitalId: 'hospital-id', id: 'id' };
    const input = { profileId: 'profile-id', team: [] };
    expect(await service.updatePatientCareTeam(mutator, input)).toEqual(
      mockPatientCareTeam,
    );
    expect(MockPatientCareTeamRepository.save).toHaveBeenCalledWith(
      expect.objectContaining({
        creatorId: 'id',
        profileId: 'profile-id',
        team: [],
      }),
    );
  });

  it('updatePatientCareTeam should throw if mutator is not associated with a facility', async () => {
    const mutator = { ...mockProfile, hospitalId: null, id: 'id' };
    const input = { profileId: 'profile-id', team: [] };
    await expect(service.updatePatientCareTeam(mutator, input)).rejects.toThrow(
      ConflictException,
    );
  });
});
