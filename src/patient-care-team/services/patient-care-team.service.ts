import { ConflictException, Injectable } from '@nestjs/common';
import { PatientCareTeamInput } from '@clinify/patient-care-team/inputs/patient-care-team.input';
import { PatientCareTeamModel } from '@clinify/patient-care-team/models/patient-care-team.model';
import { PatientCareTeamRepository } from '@clinify/patient-care-team/repositories/patient-care-team.repository';
import { ProfileModel } from '@clinify/users/models/profile.model';

@Injectable()
export class PatientCareTeamService {
  constructor(private readonly repository: PatientCareTeamRepository) {}

  getPatientCareTeam(hospitalId: string, patientId: string) {
    return this.repository.findOne({
      where: { hospitalId, profileId: patientId },
    });
  }

  async updatePatientCareTeam(
    mutator: ProfileModel,
    input: PatientCareTeamInput,
  ): Promise<PatientCareTeamModel> {
    if (!mutator.hospitalId) {
      throw new ConflictException('Facility Not Found');
    }
    const record = await this.repository.findOne({
      where: { hospitalId: mutator.hospitalId, profileId: input.profileId },
    });

    return this.repository.save({
      ...(record
        ? { ...record, lastUpdatedBy: mutator.id }
        : {
            hospitalId: mutator.hospitalId,
            profileId: input.profileId,
            creatorId: mutator.id,
          }),
      team: input.team,
    });
  }
}
