import { Field, InputType, ObjectType } from '@nestjs/graphql';

@ObjectType()
@InputType('PatientCareSpecialistInput')
export class PatientCareSpecialist {
  @Field(() => String, { nullable: true })
  specialistId?: string;

  @Field(() => String, { nullable: true })
  specialty?: string;

  @Field({ nullable: true })
  specialistFullName?: string;

  @Field({ nullable: true })
  specialistTitle?: string;

  @Field({ nullable: true })
  specialistRole?: string;
}
