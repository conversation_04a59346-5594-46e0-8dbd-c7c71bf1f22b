import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsOptional, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  UpdateDateColumn,
} from 'typeorm';
import {
  VirtualAccountProvider,
  VirtualAccountTransactionType,
  VirtualAccountType,
} from '@clinify/banks/enum/virtual-account.enum';
import { BankAccountTransactionModel } from '@clinify/banks/models/bank-account-transaction.model';
import { BillModel } from '@clinify/bills/models/bill.model';
import { VirtualServicesPaymentModel } from '@clinify/bills/models/virtual-services-payment.model';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { InvoiceModel } from '@clinify/invoices/models/invoice.model';
import { Currency } from '@clinify/shared/enums/currency';
import { WalletModel } from '@clinify/wallets/models/wallet.model';

@ObjectType()
@Entity('virtual-bank-accounts')
export class VirtualBankAccountModel {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field()
  @Column({ name: 'account_name' })
  accountName: string;

  @Field()
  @Index()
  @Column({ name: 'account_number', unique: true })
  accountNumber: string;

  @Field(() => VirtualAccountProvider)
  @Column({ name: 'bank', type: 'enum', enum: VirtualAccountProvider })
  bank: VirtualAccountProvider;

  @Field(() => Currency)
  @Column({
    type: 'enum',
    enum: Currency,
    nullable: false,
    default: Currency.KOBO,
  })
  currency: Currency;

  @Field({ nullable: true })
  @Column({
    name: 'total_received',
    nullable: true,
    default: 0,
    type: 'numeric',
  })
  totalReceived?: number;

  @Field({ nullable: true })
  @Column({ name: 'total_sent', nullable: true, default: 0, type: 'numeric' })
  totalSent?: number;

  @OneToMany(
    () => BankAccountTransactionModel,
    (bankTransaction) => bankTransaction.beneficiaryAccount,
  )
  bankTransactions: BankAccountTransactionModel;

  @Field(() => VirtualAccountType)
  @Column({
    name: 'virtual_account_type',
    type: 'enum',
    enum: VirtualAccountType,
  })
  virtualAccountType: VirtualAccountType;

  @Field(() => VirtualAccountTransactionType)
  @Column({
    name: 'transaction_type',
    type: 'enum',
    enum: VirtualAccountTransactionType,
  })
  transactionType: VirtualAccountTransactionType;

  @ManyToOne(() => WalletModel, (wallet) => wallet.virtualBankAccount)
  @JoinColumn({ name: 'wallet_id' })
  wallet?: WalletModel;

  @Field({ nullable: true })
  @Column({ name: 'wallet_id', nullable: true })
  walletId?: string;

  @Field(() => HmoProviderModel, { nullable: true })
  @ManyToOne(() => HmoProviderModel, { nullable: true })
  @JoinColumn({ name: 'hmo_id' })
  hmo?: HmoProviderModel;

  @Field({ nullable: true })
  @Column({ name: 'hmo_id', nullable: true })
  hmoId?: string;

  @Field()
  @CreateDateColumn({ name: 'created_date' })
  createdDate: Date;

  @Field()
  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate?: Date;

  @Field()
  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Field({ nullable: true })
  @Column({ name: 'expiry_date', nullable: true })
  expiryDate?: Date;

  @OneToMany(() => InvoiceModel, (invoice) => invoice.virtualAccount)
  invoices?: InvoiceModel[];

  @Field(() => [String], { nullable: true })
  @RelationId(
    (virtualBankAccount: VirtualBankAccountModel) =>
      virtualBankAccount.invoices,
  )
  invoicesIds?: string[];

  @OneToOne(() => InvoiceModel, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'active_invoice_id' })
  activeInvoice?: InvoiceModel;

  @IsUUID('4')
  @IsOptional()
  @Field({ nullable: true })
  @Column({ nullable: true, name: 'active_invoice_id' })
  activeInvoiceId?: string;

  @OneToMany(
    () => VirtualServicesPaymentModel,
    (virtualServicesPayment) => virtualServicesPayment.virtualBankAccount,
  )
  virtualServicesPayments?: VirtualServicesPaymentModel[];

  @Field(() => [String], { nullable: true })
  @RelationId(
    (virtualBankAccount: VirtualBankAccountModel) =>
      virtualBankAccount.virtualServicesPayments,
  )
  virtualServicesPaymentsIds?: string[];

  @OneToOne(() => BillModel, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'active_bill_id' })
  activeBill?: BillModel;

  @IsUUID('4')
  @IsOptional()
  @Field({ nullable: true })
  @Column({ nullable: true, name: 'active_bill_id' })
  activeBillId?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'phone_number' })
  phoneNumber?: string;

  @Field({ nullable: true })
  @Column({ nullable: true, name: 'bvn' })
  bvn?: string;

  @Column({ nullable: true, name: 'reason_for_deactivation' })
  reasonForDeactivation?: string;

  constructor(virtualBankAccount: Partial<VirtualBankAccountModel>) {
    Object.assign(this, virtualBankAccount);
  }
}
