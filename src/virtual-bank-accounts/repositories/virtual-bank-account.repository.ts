import { NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { CustomRepository } from '@clinify/custom-repository/decorators/custom-repo.decorator';
import { VirtualBankAccountModel } from '@clinify/virtual-bank-accounts/models/virtual-bank-account.model';

@CustomRepository(VirtualBankAccountModel)
export class VirtualBankAccountRepository extends Repository<VirtualBankAccountModel> {
  getVirtualAccount(id: string): Promise<VirtualBankAccountModel> {
    return this.findOneOrFail({ where: { id } }).catch(() => {
      throw new NotFoundException('Account Not Found');
    });
  }
}
