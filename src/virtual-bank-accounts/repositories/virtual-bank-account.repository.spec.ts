import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import { DataSource, EntityManager } from 'typeorm';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { TestDataSourceOptions } from '@clinify/data-source';
import { VirtualBankAccountModel } from '@clinify/virtual-bank-accounts/models/virtual-bank-account.model';
import { VirtualBankAccountRepository } from '@clinify/virtual-bank-accounts/repositories/virtual-bank-account.repository';
import { createVirtualAccount } from '@fixtures/virtual-account.fixture';

const chance = new Chance();

describe('VirtualBankAccountRepository', () => {
  let ds: DataSource;
  let virtualBankAccounts: VirtualBankAccountModel[];
  let virtualBankAccount: VirtualBankAccountModel;
  let manager: EntityManager;
  let repo: VirtualBankAccountRepository;

  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(TestDataSourceOptions),
        TypeormExtendedModule.forCustomRepository([
          VirtualBankAccountRepository,
        ]),
      ],
      providers: [],
    }).compile();

    ds = module.get<DataSource>(DataSource);
    manager = ds.manager;
    repo = module.get<VirtualBankAccountRepository>(
      VirtualBankAccountRepository,
    );
  });

  beforeEach(async () => {
    virtualBankAccounts = await createVirtualAccount(manager, 2);
    virtualBankAccount = virtualBankAccounts[0];
  });

  afterAll(async () => {
    await ds.destroy();
    await module.close();
  });

  it('should be defined', () => {
    expect(repo).toBeDefined();
  });

  it('getVirtualAccount should find one if it exists', async () => {
    const response = await repo.getVirtualAccount(virtualBankAccount.id);
    expect(response).toEqual(
      expect.objectContaining({
        id: virtualBankAccount.id,
        bank: virtualBankAccount.bank,
        accountNumber: virtualBankAccount.accountNumber,
        accountName: virtualBankAccount.accountName,
      }),
    );
  });

  it('getVirtualAccount should throw if id Not Found', () => {
    const response = repo.getVirtualAccount(chance.guid({ version: 4 }));
    expect(response).rejects.toThrow(
      new NotFoundException('Virtual Account Not Found'),
    );
  });
});
