import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { VirtualBankAccountModel } from '@clinify/virtual-bank-accounts/models/virtual-bank-account.model';

export enum VirtualAccountStatus {
  Active = 'Active',
  Inactive = 'Inactive',
}

registerEnumType(VirtualAccountStatus, {
  name: 'VirtualAccountStatus',
});

@ObjectType()
export class VirtualBankAccountListResponse {
  constructor(list: VirtualBankAccountModel[], totalCount: number) {
    this.list = list;
    this.totalCount = totalCount;
  }

  @Field(() => Int)
  totalCount: number;

  @Field(() => [VirtualBankAccountModel])
  list: VirtualBankAccountModel[];
}

@ObjectType()
export class VirtualAccountOwnerResponse {
  constructor(response: VirtualAccountOwnerResponse) {
    this.accountName = response.accountName;
    this.bvn = response.bvn;
    this.phoneNumber = response.phoneNumber;
    this.walletBalance = response.walletBalance;
    this.status = response.status;
  }

  @Field(() => String)
  accountName: string;

  @Field(() => String)
  bvn: string;

  @Field(() => String)
  phoneNumber: string;

  @Field(() => Number, { description: 'Balance in lowest denomination' })
  walletBalance: number;

  @Field(() => VirtualAccountStatus)
  status: VirtualAccountStatus;
}
