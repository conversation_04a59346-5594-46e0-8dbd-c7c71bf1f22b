/* eslint-disable max-lines */
import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { DataSource, EntityManager } from 'typeorm';
import {
  VirtualAccountProvider,
  VirtualAccountTransactionType,
  VirtualAccountType,
} from '@clinify/banks/enum/virtual-account.enum';
import { BankAccountTransactionModel } from '@clinify/banks/models/bank-account-transaction.model';
import { BankAccountTransactionRepository } from '@clinify/banks/repositories/bank-account-transaction.repository';
import { BankService } from '@clinify/banks/services/bank.service';
import { BillModel } from '@clinify/bills/models/bill.model';
import { VirtualServicesPaymentModel } from '@clinify/bills/models/virtual-services-payment.model';
import { BillService } from '@clinify/bills/services/bill.service';
import { customDSSerializeInTransaction } from '@clinify/database';
import { HmoProfileModel } from '@clinify/hmo-profiles/models/hmo-profile.model';
import { InvoicePaymentModel } from '@clinify/invoices/models/invoice-payment.model';
import { InvoiceModel } from '@clinify/invoices/models/invoice.model';
import { InvoiceRepository } from '@clinify/invoices/repositories/invoice.repository';
import { Currency } from '@clinify/shared/enums/currency';
import {
  TransactionStatus,
  TransactionType,
} from '@clinify/shared/enums/transaction';
import { UserType } from '@clinify/shared/enums/users';
import { isVirtualAccountTransfer } from '@clinify/shared/helper';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { TempUserModel } from '@clinify/users/models/temp_user.model';
import { convertToNumber } from '@clinify/utils/dynamic-pdf/generate-enrollee-claim-details-doc';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';
import { VirtualBankAccountKYCInput } from '@clinify/virtual-bank-accounts/inputs/virtual-bank-account.input';
import { IBankTransactionMeta } from '@clinify/virtual-bank-accounts/interfaces/bank-transaction-meta.interface';
import { VirtualBankAccountModel } from '@clinify/virtual-bank-accounts/models/virtual-bank-account.model';
import { VirtualBankAccountRepository } from '@clinify/virtual-bank-accounts/repositories/virtual-bank-account.repository';
import {
  VirtualAccountOwnerResponse,
  VirtualAccountStatus,
} from '@clinify/virtual-bank-accounts/responses/virtual-bank-account.response';
import { WalletTransactionService } from '@clinify/wallet-transactions/services/wallet-transaction.service';
import { WalletModel } from '@clinify/wallets/models/wallet.model';
import { WalletRepository } from '@clinify/wallets/repositories/wallet.repository';
import { WalletService } from '@clinify/wallets/services/wallet.service';

const {
  WalletBalance,
  WalletTransactionEvent,
  InvoicePaymentUpdated,
  TempUserUpdated,
} = SubscriptionTypes;

@Injectable()
export class VirtualBankAccountService {
  constructor(
    private readonly repository: VirtualBankAccountRepository,
    private readonly walletRepository: WalletRepository,
    private readonly bankAccountTransactionRepo: BankAccountTransactionRepository,
    private readonly invoiceRepo: InvoiceRepository,
    private readonly manager: EntityManager,
    private readonly walletTransactionService: WalletTransactionService,
    private readonly dataSource: DataSource,
    private readonly bankService: BankService,
    private readonly walletService: WalletService,
    private readonly billService: BillService,
    @Inject(PUB_SUB) private readonly pubSub: RedisPubSub,
  ) {}

  private async getVirtualAccount(
    accountNumber: string,
    bank: VirtualAccountProvider,
  ): Promise<VirtualBankAccountModel> {
    return this.repository
      .findOneOrFail({
        where: {
          accountNumber,
          bank,
        },
      })
      .catch(() => {
        throw new NotFoundException('Account Not Found');
      });
  }

  getVirtualAccountById(id: string): Promise<VirtualBankAccountModel> {
    return this.repository.getVirtualAccount(id);
  }

  async lookupAccount(
    accountNumber: string,
    bank: VirtualAccountProvider,
  ): Promise<VirtualBankAccountModel> {
    const va = await this.getVirtualAccount(accountNumber, bank);
    if (
      (va.transactionType === VirtualAccountTransactionType.PayInvoice &&
        !va.activeInvoiceId) ||
      !va.isActive
    )
      throw new NotFoundException('Account Not Found');

    return va;
  }

  private async fundWalletTransactionEvent(
    virtualAccount: VirtualBankAccountModel,
    manager: EntityManager,
    meta: IBankTransactionMeta,
  ): Promise<BankAccountTransactionModel> {
    const walletRepo = manager.withRepository(this.walletRepository);
    const bankTransactionRepo = manager.withRepository(
      this.bankAccountTransactionRepo,
    );
    const vaRepo = manager.withRepository(this.repository);
    const wallet = await walletRepo
      .findOneOrFail({ where: { id: virtualAccount.walletId } })
      .catch(() => {
        throw new NotFoundException('Wallet Not Found');
      });
    const walletTransaction =
      await this.walletTransactionService.performWalletTransaction(
        manager,
        meta.paymentReference,
        wallet,
        Number(meta.amount) * 100,
        TransactionType.TOPUP,
        undefined,
        {
          bankName: meta.bankName,
          bankCode: meta.bankCode,
          accountNumber: meta.originatorAccountNumber,
          accountName: meta.originatorName,
        },
      );
    this.pubSub.publish(WalletTransactionEvent, {
      [WalletTransactionEvent]: {
        ...walletTransaction,
        ids: [walletTransaction.receiverWalletId],
      },
    });

    await vaRepo.update(
      { id: virtualAccount.id },
      { totalReceived: () => `total_received + ${Number(meta.amount) * 100}` },
    );
    this.pubSub.publish(WalletBalance, {
      [WalletBalance]: new WalletModel({
        ...wallet,
        totalReceived: wallet.totalReceived + Number(meta.amount) * 100,
      }),
    });

    return bankTransactionRepo.save({
      amount: Number(meta.amount) * 100,
      beneficiaryAccountId: virtualAccount.id,
      transactionType: VirtualAccountTransactionType.FundWallet,
      transactionStatus: TransactionStatus.SUCCESS,
      beneficiaryInitialBalance: (
        Number(virtualAccount.totalReceived) ||
        0 - Number(virtualAccount.totalSent) ||
        0
      ).toString(),
      reference: meta.paymentReference,
      sender: {
        accountNumber: meta.originatorAccountNumber,
        name: meta.originatorName,
        bankName: meta.bankName,
        bankCode: meta.bankCode,
      },
      receiver: {
        accountNumber: meta.creditAccount,
        name: meta.creditAccountName,
        bankName: '',
        bankCode: '',
      },
      description: meta.narration,
      sessionId: meta.sessionId,
      transactionDateTime: new Date(),
      hospitalId: wallet.hospitalId,
    });
  }

  private async payInvoiceTransactionEvent(
    virtualAccount: VirtualBankAccountModel,
    manager: EntityManager,
    meta: IBankTransactionMeta,
  ): Promise<BankAccountTransactionModel> {
    const bankTransactionRepo = manager.withRepository(
      this.bankAccountTransactionRepo,
    );
    const invoiceRepo = manager.withRepository(this.invoiceRepo);
    const walletRepo = manager.withRepository(this.walletRepository);
    if (!virtualAccount.activeInvoiceId)
      throw new NotFoundException('Invoice Not Provided');
    const invoice = await invoiceRepo.findOne({
      where: { id: virtualAccount.activeInvoiceId },
      relations: [
        'invoiceItems',
        'invoicePayments',
        'senderHospital',
        'invoicePayments.createdBy',
        'invoicePayments.updatedBy',
      ],
    });
    const virtualAccountPayment = invoice.invoicePayments.find(
      ({ paymentMethod }) => isVirtualAccountTransfer(paymentMethod),
    );
    if (!virtualAccountPayment)
      throw new UnprocessableEntityException('Bank Transfer Payment Not Found');
    const amountPaid =
      Number(meta.amount) * 100 + virtualAccountPayment.amountPaid;
    const paymentStatus =
      amountPaid >= virtualAccountPayment.amountDue ? 'Paid' : 'Partially Paid';
    await manager.update(InvoicePaymentModel, virtualAccountPayment.id, {
      paymentStatus,
      amountPaid,
    });
    const wallet = await walletRepo
      .findOneOrFail({ where: { hospitalId: invoice.senderHospitalId } })
      .catch(() => {
        throw new NotFoundException('Wallet Not Found');
      });

    const walletTransaction =
      await this.walletTransactionService.performWalletTransaction(
        manager,
        meta.paymentReference,
        wallet,
        Number(meta.amount) * 100,
        TransactionType.PAYMENT,
        undefined,
        {
          accountNumber: meta.originatorAccountNumber,
          accountName: meta.originatorName,
          bankName: meta.bankName,
          bankCode: meta.bankCode,
        },
      );
    this.pubSub.publish(WalletTransactionEvent, {
      ...walletTransaction,
      ids: [walletTransaction.receiverWalletId],
    });
    this.pubSub.publish(InvoicePaymentUpdated, {
      [InvoicePaymentUpdated]: {
        ...virtualAccountPayment,
        amountPaid,
        paymentStatus,
        hospitalId: invoice.senderHospitalId,
      },
    });

    return bankTransactionRepo.save({
      id: meta.transactionReference,
      amount: Number(meta.amount) * 100,
      beneficiaryAccountId: virtualAccount.id,
      transactionType: VirtualAccountTransactionType.PayInvoice,
      transactionStatus: TransactionStatus.SUCCESS,
      reference: meta.paymentReference,
      currency: invoice.currency,
      sender: {
        accountNumber: meta.originatorAccountNumber,
        name: meta.originatorName,
        bankName: meta.bankName,
        bankCode: meta.bankCode,
      },
      receiver: {
        accountNumber: meta.creditAccount,
        name: meta.creditAccountName,
        bankName: '',
        bankCode: '',
      },
      description: meta.narration,
      sessionId: meta.sessionId,
      transactionDateTime: new Date(),
      beneficiaryInitialBalance: (
        Number(virtualAccount.totalReceived) - Number(virtualAccount.totalSent)
      ).toString(),
      invoiceId: invoice.id,
      hospitalId: invoice.senderHospitalId,
    });
  }

  private async payBillTransactionEvent(
    virtualAccount: VirtualBankAccountModel,
    manager: EntityManager,
    meta: IBankTransactionMeta,
    virtualAccountName: VirtualAccountProvider,
  ): Promise<BankAccountTransactionModel> {
    const _walletRepository = manager.withRepository(this.walletRepository);

    const bankTransactionDetails: Partial<BankAccountTransactionModel> = {
      id: meta.transactionReference,
      amount: Number(meta.amount) * 100,
      beneficiaryAccountId: virtualAccount.id,
      transactionType: VirtualAccountTransactionType.PayBill,
      transactionStatus: TransactionStatus.SUCCESS,
      reference: meta.paymentReference,
      currency: Currency.KOBO,
      sender: {
        accountNumber: meta.originatorAccountNumber,
        name: meta.originatorName,
        bankName: meta.bankName,
        bankCode: meta.bankCode,
      },
      receiver: {
        accountNumber: meta.creditAccount,
        name: meta.creditAccountName,
        bankName: '',
        bankCode: '',
      },
      description: meta.narration,
      sessionId: meta.sessionId,
      transactionDateTime: new Date(),
      beneficiaryInitialBalance: (
        Number(virtualAccount.totalReceived) - Number(virtualAccount.totalSent)
      ).toString(),
    };

    const bill = await manager.findOne(BillModel, {
      where: { id: virtualAccount.activeBillId },
      relations: ['details', 'virtualServicesPayment'],
    });
    const virtualServicesPayment = bill.virtualServicesPayment;

    bankTransactionDetails.virtualServicesPaymentId =
      virtualServicesPayment?.id;
    bankTransactionDetails.hospitalId = bill?.senderHospitalId;
    bankTransactionDetails.billId = bill?.id;

    const bankTransaction = await this.bankAccountTransactionRepo.save(
      bankTransactionDetails,
    );
    if (!virtualServicesPayment.id) {
      throw new NotFoundException('Virtual Services Payment Not Provided');
    }
    if (!virtualServicesPayment)
      throw new UnprocessableEntityException('Bill Not Found');
    const amountPaid =
      Number(meta.amount) * 100 + virtualServicesPayment.amountPaid;
    const paymentStatus =
      amountPaid >= virtualServicesPayment.amountDue
        ? 'Paid'
        : 'Partially Paid';
    await manager.update(
      VirtualServicesPaymentModel,
      virtualServicesPayment.id,
      {
        paymentStatus,
        amountPaid,
      },
    );
    const receiverWallet = await _walletRepository.findOne({
      where: { hospitalId: bill.senderHospitalId },
    });
    if (!receiverWallet) throw new NotFoundException('Wallet not found');
    const walletTransaction =
      await this.walletTransactionService.performWalletTransaction(
        manager,
        meta.paymentReference,
        receiverWallet,
        Number(meta.amount) * 100,
        TransactionType.PAYMENT,
        undefined,
        {
          accountNumber: meta.originatorAccountNumber,
          accountName: meta.originatorName,
          bankName: meta.bankName,
          bankCode: meta.bankCode,
        },
      );
    this.pubSub.publish(WalletTransactionEvent, {
      ...walletTransaction,
      ids: [walletTransaction.receiverWalletId],
    });

    await this.billService.updateBillInformationFromPaymentNotification(
      amountPaid,
      bill.id,
      virtualAccountName === VirtualAccountProvider.WEMA
        ? 'WEMA Bank Transfer'
        : null,
      manager,
    );

    return bankTransaction;
  }

  private async remitHmoPaymentEvent(
    virtualAccount: VirtualBankAccountModel,
    manager: EntityManager,
    meta: IBankTransactionMeta,
  ): Promise<BankAccountTransactionModel> {
    const bankTransactionRepo = manager.withRepository(
      this.bankAccountTransactionRepo,
    );
    const walletRepo = manager.withRepository(this.walletRepository);
    const vaRepo = manager.withRepository(this.repository);

    const wallet = await walletRepo
      .findOneOrFail({ where: { id: virtualAccount.walletId } })
      .catch(() => {
        throw new NotFoundException('Wallet Not Found');
      });
    await vaRepo.update(
      { id: virtualAccount.id },
      { totalReceived: () => `total_received + ${Number(meta.amount) * 100}` },
    );
    const walletTransaction =
      await this.walletTransactionService.performWalletTransaction(
        manager,
        meta.paymentReference,
        wallet,
        Number(meta.amount) * 100,
        TransactionType.HMO_REMITTANCE,
        undefined,
        {
          accountNumber: meta.originatorAccountNumber,
          accountName: meta.originatorName,
          bankName: meta.bankName,
          bankCode: meta.bankCode,
        },
      );
    this.pubSub.publish(WalletTransactionEvent, {
      ...walletTransaction,
      ids: [walletTransaction.receiverWalletId],
    });

    return bankTransactionRepo.save({
      amount: Number(meta.amount) * 100,
      beneficiaryAccountId: virtualAccount.id,
      transactionType: VirtualAccountTransactionType.Hmo,
      transactionStatus: TransactionStatus.SUCCESS,
      reference: meta.paymentReference,
      sender: {
        accountNumber: meta.originatorAccountNumber,
        name: meta.originatorName,
        bankName: meta.bankName,
        bankCode: meta.bankCode,
      },
      receiver: {
        accountNumber: meta.creditAccount,
        name: meta.creditAccountName,
        bankName: '',
        bankCode: '',
      },
      description: meta.narration,
      sessionId: meta.sessionId,
      transactionDateTime: new Date(),
      beneficiaryInitialBalance: (
        Number(virtualAccount.totalReceived) ||
        0 - Number(virtualAccount.totalSent) ||
        0
      ).toString(),
      hospitalId: wallet.hospitalId,
    });
  }

  private async payRegistrationFeeTransactionEvent(
    virtualAccount: VirtualBankAccountModel,
    manager: EntityManager,
    meta: IBankTransactionMeta,
  ): Promise<BankAccountTransactionModel> {
    const _walletRepository = manager.withRepository(this.walletRepository);
    const bankTransactionDetails: Partial<BankAccountTransactionModel> = {
      id: meta.transactionReference,
      amount: Number(meta.amount) * 100,
      beneficiaryAccountId: virtualAccount.id,
      transactionType: VirtualAccountTransactionType.Registration,
      transactionStatus: TransactionStatus.SUCCESS,
      reference: meta.paymentReference,
      currency: Currency.KOBO,
      sender: {
        accountNumber: meta.originatorAccountNumber,
        name: meta.originatorName,
        bankName: meta.bankName,
        bankCode: meta.bankCode,
      },
      receiver: {
        accountNumber: meta.creditAccount,
        name: meta.creditAccountName,
        bankName: '',
        bankCode: '',
      },
      description: meta.narration,
      sessionId: meta.sessionId,
      transactionDateTime: new Date(),
      beneficiaryInitialBalance: (
        Number(virtualAccount.totalReceived) - Number(virtualAccount.totalSent)
      ).toString(),
    };
    const tempUser = await manager.findOne(TempUserModel, {
      where: {
        virtualBankAccountId: virtualAccount.id,
      },
      relations: { virtualBankAccount: true, hospital: true },
    });
    bankTransactionDetails.tempUserId = tempUser?.id;
    bankTransactionDetails.hospitalId = tempUser?.hospitalId;
    const bankTransaction = await this.bankAccountTransactionRepo.save(
      bankTransactionDetails,
    );
    await manager.update(TempUserModel, tempUser.id, {
      amountPaid: () => `amount_paid + ${Number(meta.amount) * 100}`,
    });
    const receiverWallet = await _walletRepository.findOne({
      where: { hospitalId: tempUser.hospitalId },
    });
    if (receiverWallet) {
      const walletTransaction =
        await this.walletTransactionService.performWalletTransaction(
          manager,
          meta.paymentReference,
          receiverWallet,
          Number(meta.amount) * 100,
          TransactionType.PAYMENT,
          undefined,
          {
            accountNumber: meta.originatorAccountNumber,
            accountName: meta.originatorName,
            bankName: meta.bankName,
            bankCode: meta.bankCode,
          },
        );
      this.pubSub.publish(WalletTransactionEvent, {
        ...walletTransaction,
        ids: [walletTransaction.receiverWalletId],
      });
    }

    this.pubSub.publish(TempUserUpdated, {
      [TempUserUpdated]: {
        ...tempUser,
        amountPaid: (tempUser.amountPaid || 0) + Number(meta.amount) * 100,
      },
    });

    return bankTransaction;
  }

  private async payHMOEnrollmentTransactionEvent(
    virtualAccount: VirtualBankAccountModel,
    manager: EntityManager,
    meta: IBankTransactionMeta,
  ): Promise<BankAccountTransactionModel> {
    const bankTransactionRepo = manager.withRepository(
      this.bankAccountTransactionRepo,
    );
    const walletRepo = manager.withRepository(this.walletRepository);
    const vaRepo = manager.withRepository(this.repository);

    const virtualServicesPayment = await manager.findOne(
      VirtualServicesPaymentModel,
      {
        where: { virtualBankAccountId: virtualAccount.id },
        relations: { registrationHmoProfile: true },
      },
    );

    const amountInKobo = Number(meta.amount) * 100;

    if (virtualServicesPayment) {
      const hmoProfile = virtualServicesPayment.registrationHmoProfile;
      if (hmoProfile) {
        const newAmountPaid = virtualServicesPayment.amountPaid + amountInKobo;
        await manager.update(
          VirtualServicesPaymentModel,
          virtualServicesPayment.id,
          { amountPaid: newAmountPaid },
        );

        // Check if payment is complete (amountPaid >= premiumOutstanding)
        const premiumOutstanding =
          convertToNumber(hmoProfile.premiumOutstanding) * 100 || 0;
        const isPaymentComplete = newAmountPaid >= premiumOutstanding;

        // If payment is complete, update the HmoProfile
        if (isPaymentComplete) {
          const currentDate = new Date();
          const dueDate = new Date();
          dueDate.setFullYear(dueDate.getFullYear() + 1); // Set due date to a year ahead

          await manager.update(HmoProfileModel, hmoProfile.id, {
            memberStartDate: currentDate,
            memberDueDate: dueDate,
            paymentFrequency: 'Annual',
            paymentDateTime: currentDate,
            activationDatetime: new Date(
              currentDate.getFullYear() +
                (currentDate.getMonth() === 11 ? 1 : 0),
              currentDate.getMonth() === 11 ? 0 : currentDate.getMonth() + 1,
              1,
            ),
          });
        }
      }
    }

    const receiverWallet = virtualServicesPayment
      ? await walletRepo.findOne({
          where: { hospitalId: virtualServicesPayment.senderHospitalId },
        })
      : null;

    if (receiverWallet) {
      await vaRepo.update(
        { id: virtualAccount.id },
        { totalReceived: () => `total_received + ${amountInKobo}` },
      );

      const walletTransaction =
        await this.walletTransactionService.performWalletTransaction(
          manager,
          meta.paymentReference,
          receiverWallet,
          amountInKobo,
          TransactionType.PAYMENT,
          undefined,
          {
            accountNumber: meta.originatorAccountNumber,
            accountName: meta.originatorName,
            bankName: meta.bankName,
            bankCode: meta.bankCode,
          },
        );

      this.pubSub.publish(WalletTransactionEvent, {
        ...walletTransaction,
        ids: [walletTransaction.receiverWalletId],
      });
    }

    const bankTransactionData: any = {
      amount: amountInKobo,
      beneficiaryAccountId: virtualAccount.id,
      transactionType: VirtualAccountTransactionType.ProfileEnrollment,
      transactionStatus: TransactionStatus.SUCCESS,
      reference: meta.paymentReference,
      currency: Currency.KOBO,
      sender: {
        accountNumber: meta.originatorAccountNumber,
        name: meta.originatorName,
        bankName: meta.bankName,
        bankCode: meta.bankCode,
      },
      receiver: {
        accountNumber: meta.creditAccount,
        name: meta.creditAccountName,
        bankName: '',
        bankCode: '',
      },
      description: meta.narration,
      sessionId: meta.sessionId,
      transactionDateTime: new Date(),
      beneficiaryInitialBalance: (
        Number(virtualAccount.totalReceived) ||
        0 - Number(virtualAccount.totalSent) ||
        0
      ).toString(),
    };

    if (virtualServicesPayment) {
      bankTransactionData.virtualServicesPaymentId = virtualServicesPayment.id;
    }

    const bankTransaction = await bankTransactionRepo.save(bankTransactionData);

    return bankTransaction;
  }

  async handleNotificationEvent(
    virtualAccountName: VirtualAccountProvider,
    meta: IBankTransactionMeta,
  ): Promise<BankAccountTransactionModel> {
    const virtualAccount = await this.getVirtualAccount(
      meta.creditAccount,
      virtualAccountName,
    );
    const processedTransaction = await this.manager.findOne(
      BankAccountTransactionModel,
      { where: { reference: meta.paymentReference } },
    );
    if (processedTransaction) return processedTransaction;
    switch (virtualAccount.transactionType) {
      case VirtualAccountTransactionType.FundWallet:
        return customDSSerializeInTransaction(this.dataSource, (_manager) =>
          this.fundWalletTransactionEvent(virtualAccount, _manager, meta),
        );
      case VirtualAccountTransactionType.PayInvoice:
        return customDSSerializeInTransaction(this.dataSource, (_manager) =>
          this.payInvoiceTransactionEvent(virtualAccount, _manager, meta),
        );
      case VirtualAccountTransactionType.Hmo:
        return customDSSerializeInTransaction(this.dataSource, (_manager) =>
          this.remitHmoPaymentEvent(virtualAccount, _manager, meta),
        );
      case VirtualAccountTransactionType.PayBill:
        return customDSSerializeInTransaction(this.dataSource, (_manager) =>
          this.payBillTransactionEvent(
            virtualAccount,
            _manager,
            meta,
            virtualAccountName,
          ),
        );
      case VirtualAccountTransactionType.Registration:
        return customDSSerializeInTransaction(this.dataSource, (_manager) =>
          this.payRegistrationFeeTransactionEvent(
            virtualAccount,
            _manager,
            meta,
          ),
        );
      case VirtualAccountTransactionType.ProfileEnrollment:
        return customDSSerializeInTransaction(this.dataSource, (_manager) =>
          this.payHMOEnrollmentTransactionEvent(virtualAccount, _manager, meta),
        );
    }
  }

  async getInvoice(accountNumber: string): Promise<InvoiceModel> {
    const virtualAccount = await this.repository
      .findOneOrFail({
        where: {
          accountNumber,
        },
        relations: ['activeInvoice'],
      })
      .catch(() => {
        throw new NotFoundException('Account Not Found');
      });

    return virtualAccount.activeInvoice;
  }

  getProfileVirtualAccounts(
    profileId: string,
  ): Promise<VirtualBankAccountModel[]> {
    return this.repository.find({ where: { wallet: { profileId } } });
  }

  getHospitalVirtualAccounts(
    hospitalId: string,
  ): Promise<VirtualBankAccountModel[]> {
    return this.repository.find({
      where: { wallet: { hospitalId } },
      relations: ['hmo'],
    });
  }

  getHospitalWalletVirtualAccount(
    hospitalId: string,
  ): Promise<VirtualBankAccountModel> {
    return this.repository.findOne({
      where: {
        wallet: { hospitalId },
        transactionType: VirtualAccountTransactionType.FundWallet,
      },
    });
  }

  async getOrCreateVirtualAccountForWalletTopup(
    clinifyId: string,
    kycDetails: VirtualBankAccountKYCInput,
  ): Promise<VirtualBankAccountModel> {
    let accountName: string;
    const wallet = await this.walletService.getClinifyUserWallet(clinifyId);
    if (!wallet) {
      throw new BadRequestException('Error Creating Account For Wallet');
    }
    if (wallet.hospital) accountName = wallet.hospital.name;
    else accountName = wallet.profile?.fullName;
    const virtualAccount = await this.repository.findOne({
      where: {
        walletId: wallet.id,
        transactionType: VirtualAccountTransactionType.FundWallet,
      },
    });
    if (!virtualAccount) {
      return this.bankService.generateVirtualAccountNumber(
        {
          virtualAccountType: VirtualAccountType.Permanent,
          wallet,
          vaTransactionType: VirtualAccountTransactionType.FundWallet,
          kycDetails,
        },
        accountName,
      );
    }

    return virtualAccount;
  }

  async getKYCDetails(
    bank: VirtualAccountProvider,
    accountNumber: string,
  ): Promise<VirtualAccountOwnerResponse> {
    const virtualAccount = await this.repository
      .findOneOrFail({
        where: {
          accountNumber,
          bank,
        },
        relations: {
          wallet: true,
        },
      })
      .catch(() => {
        throw new NotFoundException('Account Not Found');
      });

    return new VirtualAccountOwnerResponse({
      phoneNumber: virtualAccount.phoneNumber,
      accountName: virtualAccount.accountName,
      status: virtualAccount.isActive
        ? VirtualAccountStatus.Active
        : VirtualAccountStatus.Inactive,
      bvn: virtualAccount.bvn,
      walletBalance: virtualAccount.wallet.getTotalBalance(),
    });
  }

  async getMiniStatement(
    bank: VirtualAccountProvider,
    accountNumber: string,
  ): Promise<BankAccountTransactionModel[]> {
    const virtualAccount = await this.repository
      .findOneOrFail({
        where: {
          accountNumber,
          bank,
        },
      })
      .catch(() => {
        throw new NotFoundException('Account Not Found');
      });

    return this.manager.find(BankAccountTransactionModel, {
      skip: 0,
      take: 10,
      order: {
        createdDate: 'DESC',
      },
      where: {
        beneficiaryAccountId: virtualAccount.id,
      },
    });
  }

  async deactivateAccount(
    bank: VirtualAccountProvider,
    accountNumber: string,
    reason: string,
  ): Promise<boolean> {
    const virtualAccount = await this.repository
      .findOneOrFail({
        where: {
          accountNumber,
          bank,
        },
        select: ['id', 'isActive'],
      })
      .catch(() => {
        throw new NotFoundException('Account Not Found');
      });

    return this.repository
      .update(virtualAccount.id, {
        isActive: false,
        reasonForDeactivation: reason,
      })
      .then(() => true)
      .catch(() => false);
  }

  async updateKYCDetails(
    mutator: ProfileModel,
    accountNumber: string,
    input: VirtualBankAccountKYCInput,
  ): Promise<VirtualBankAccountModel> {
    const walletWhere =
      mutator.type === UserType.Patient
        ? { profileId: mutator.id }
        : { hospitalId: mutator.hospitalId };
    const virtualAccount = await this.repository
      .findOneOrFail({
        where: {
          accountNumber,
          wallet: walletWhere,
        },
      })
      .catch(() => {
        throw new NotFoundException('Account Not Found');
      });

    return this.repository.save({
      ...virtualAccount,
      phoneNumber: input.phoneNumber,
      bvn: input.bvn,
    });
  }

  async updateHospitalVirtualAccountName(
    hospitalId: string,
    hospitalName: string,
  ): Promise<VirtualBankAccountModel[]> {
    const vaName = `CLINIFY / ${hospitalName}`;
    const hospitalWallet = await this.walletRepository.findOne({
      where: { hospitalId },
    });
    if (!hospitalWallet) return [];
    const va = await this.repository.find({
      where: { walletId: hospitalWallet.id },
    });

    await this.repository.update(
      { walletId: hospitalWallet.id },
      { accountName: vaName },
    );

    return va.map((va) => {
      return {
        ...va,
        accountName: vaName,
      };
    });
  }
}
