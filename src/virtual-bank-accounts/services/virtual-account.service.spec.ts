/* eslint-disable max-lines */
import { NotFoundException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import {
  VirtualAccountProvider,
  VirtualAccountTransactionType,
  VirtualAccountType,
} from '@clinify/banks/enum/virtual-account.enum';
import { BankAccountTransactionModel } from '@clinify/banks/models/bank-account-transaction.model';
import { BankAccountTransactionRepository } from '@clinify/banks/repositories/bank-account-transaction.repository';
import { BankService } from '@clinify/banks/services/bank.service';
import { BillService } from '@clinify/bills/services/bill.service';
import { InvoiceRepository } from '@clinify/invoices/repositories/invoice.repository';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { IBankTransactionMeta } from '@clinify/virtual-bank-accounts/interfaces/bank-transaction-meta.interface';
import { VirtualBankAccountRepository } from '@clinify/virtual-bank-accounts/repositories/virtual-bank-account.repository';
import { VirtualBankAccountService } from '@clinify/virtual-bank-accounts/services/virtual-bank-account.service';
import { WalletTransactionService } from '@clinify/wallet-transactions/services/wallet-transaction.service';
import { WalletRepository } from '@clinify/wallets/repositories/wallet.repository';
import { WalletService } from '@clinify/wallets/services/wallet.service';
import { MockedQueryBuilder } from '@mocks/database.mock';
import { hospitalFactory } from '@mocks/factories/hospital.factory';
import {
  mockInvoice,
  mockInvoicePayment,
} from '@mocks/factories/invoice.factory';
import { mockProfile } from '@mocks/factories/profile.factory';
import {
  mockVirtualAccount,
  virtualAccountFactory,
} from '@mocks/factories/virtual-account.factory';
import { walletTransactionFactory } from '@mocks/factories/wallet-transaction.factory';
import { walletFactory } from '@mocks/factories/wallet.factory';
import { InvoiceRepositoryMock } from '@mocks/invoice.mock';
import { PubSubMock } from '@mocks/pub-sub.mock';

const mockWallet = walletFactory.build();

const managerMock = {
  queryRunner: { isTransactionActive: true },
  withRepository: jest.fn((arg) => ({
    ...arg,
    createQueryBuilder: MockedQueryBuilder,
  })),
  update: jest.fn(),
  findOne: jest.fn(() => Promise.resolve(null)),
  find: jest.fn(() => Promise.resolve([])),
};

const dsMock = {
  getRepository: jest.fn(() => ({
    extend: jest.fn(),
  })),
  transaction: jest.fn((cb) => cb(managerMock)),
  manager: managerMock,
};

const MockVirtualBankAccountRepository = {
  findOneOrFail: jest.fn(() => Promise.resolve(mockVirtualAccount)),
  findOne: jest.fn(() => Promise.resolve(mockVirtualAccount)),
  find: jest.fn(() => Promise.resolve([mockVirtualAccount])),
  getVirtualAccount: jest.fn(() => Promise.resolve(mockVirtualAccount)),
  update: jest.fn(() => Promise.resolve()),
  save: jest.fn(() => Promise.resolve(mockVirtualAccount)),
};

const MockWalletRepository = {
  findOneOrFail: jest.fn(() =>
    Promise.resolve({ id: 'id', getTotalBalance: () => 100 }),
  ),
  update: jest.fn(),
  findOne: jest.fn(() => Promise.resolve(mockWallet)),
};

const MockWalletTransactionService = {
  createWalletTransaction: jest.fn(),
  performWalletTransaction: jest.fn(() =>
    Promise.resolve(walletTransactionFactory.build()),
  ),
};

const MockBankAccountTransactionRepository = {
  save: jest.fn(() => Promise.resolve({})),
};

const MockBankService = {
  generateVirtualAccountNumber: jest.fn(() =>
    Promise.resolve(mockVirtualAccount),
  ),
};
const MockWalletService = {
  getClinifyUserWallet: jest.fn(() => Promise.resolve(mockWallet)),
};

const MockBillService = {
  updateBillInformationFromPaymentNotification: jest.fn(() =>
    Promise.resolve(),
  ),
};

describe('VirtualAccountService', () => {
  let service: VirtualBankAccountService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        VirtualBankAccountService,
        {
          provide: VirtualBankAccountRepository,
          useValue: MockVirtualBankAccountRepository,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: {},
        },
        {
          provide: WalletRepository,
          useValue: MockWalletRepository,
        },
        {
          provide: BankAccountTransactionRepository,
          useValue: MockBankAccountTransactionRepository,
        },
        {
          provide: InvoiceRepository,
          useValue: InvoiceRepositoryMock,
        },
        {
          provide: DataSource,
          useValue: dsMock,
        },
        {
          provide: EntityManager,
          useValue: managerMock,
        },
        {
          provide: WalletTransactionService,
          useValue: MockWalletTransactionService,
        },
        {
          provide: BankService,
          useValue: MockBankService,
        },
        {
          provide: WalletService,
          useValue: MockWalletService,
        },
        {
          provide: BillService,
          useValue: MockBillService,
        },
        {
          provide: 'PUB_SUB',
          useValue: PubSubMock,
        },
      ],
    }).compile();

    service = module.get(VirtualBankAccountService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('getVirtualAccountById() should return virtual account', async () => {
    const response = await service.getVirtualAccountById('id');
    expect(response).toEqual(mockVirtualAccount);
  });

  it('lookupAccount() should get virtual account', async () => {
    const va = { ...mockVirtualAccount, activeInvoiceId: mockInvoice };
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    const response = await service.lookupAccount(
      '**********',
      VirtualAccountProvider.WEMA,
    );
    expect(response).toEqual(va);
  });

  it('lookupAccount() should throw for missing active invoice', () => {
    const va = {
      ...mockVirtualAccount,
      transactionType: VirtualAccountTransactionType.PayInvoice,
    };
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    const response = service.lookupAccount(
      '**********',
      VirtualAccountProvider.WEMA,
    );
    expect(response).rejects.toThrow(
      new NotFoundException('Account Not Found'),
    );
  });

  it('lookupAccount() should throw for missing active invoice', () => {
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.reject(),
    );
    const response = service.lookupAccount(
      '**********',
      VirtualAccountProvider.WEMA,
    );
    expect(response).rejects.toThrow(
      new NotFoundException('Account Not Found'),
    );
  });

  it('handleNotificationEvent for Pay Invoice', async () => {
    const va = { ...mockVirtualAccount, activeInvoiceId: mockInvoice.id };
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'Narration',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
    };
    await service.handleNotificationEvent(VirtualAccountProvider.WEMA, meta);
    expect(
      MockWalletTransactionService.performWalletTransaction,
    ).toHaveBeenCalled();
  });

  it('handleNotificationEvent for Pay Invoice should fail if receiving wallet is not found', async () => {
    const va = { ...mockVirtualAccount, activeInvoiceId: mockInvoice.id };
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    MockWalletRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.reject(),
    );
    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'Narration',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
    };
    const res = service.handleNotificationEvent(
      VirtualAccountProvider.WEMA,
      meta,
    );
    await expect(res).rejects.toThrow(
      new NotFoundException('Wallet Not Found'),
    );
  });

  it('handleNotificationEvent for Pay Invoice should throw if invoice is not provided', async () => {
    const va = { ...mockVirtualAccount };
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'Narration',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
    };
    const response = service.handleNotificationEvent(
      VirtualAccountProvider.WEMA,
      meta,
    );
    await expect(response).rejects.toThrow(
      new NotFoundException('Invoice Not Provided'),
    );
  });

  it('handleNotificationEvent for Pay Invoice should throw if invoice does not contain any bank transfer payment', async () => {
    const va = { ...mockVirtualAccount, activeInvoiceId: mockInvoice.id };
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    InvoiceRepositoryMock.findOne.mockImplementationOnce(() =>
      Promise.resolve({
        ...mockInvoice,
        invoicePayments: [{ ...mockInvoicePayment, paymentMethod: 'Cash' }],
      }),
    );
    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'Narration',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
    };
    const response = service.handleNotificationEvent(
      VirtualAccountProvider.WEMA,
      meta,
    );
    await expect(response).rejects.toThrow(
      new NotFoundException('Bank Transfer Payment Not Found'),
    );
  });

  it('handleNotificationEvent for Fund Wallet', async () => {
    const va = virtualAccountFactory.build({
      walletId: 'wallet-id',
      transactionType: VirtualAccountTransactionType.FundWallet,
    });
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'Fund Wallet',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
    };
    await service.handleNotificationEvent(VirtualAccountProvider.WEMA, meta);
    expect(
      MockWalletTransactionService.performWalletTransaction,
    ).toHaveBeenCalled();
  });

  it('handleNotificationEvent for Fund Wallet should throw if wallet Not Found', async () => {
    const va = virtualAccountFactory.build({
      walletId: 'wallet-id',
      transactionType: VirtualAccountTransactionType.FundWallet,
    });
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    MockWalletRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.reject(),
    );
    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'Fund Wallet',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
    };
    const res = service.handleNotificationEvent(
      VirtualAccountProvider.WEMA,
      meta,
    );
    await expect(res).rejects.toThrow(
      new NotFoundException('Wallet Not Found'),
    );
  });

  it('handleNotificationEvent for HMO Claim Remittance', async () => {
    const va = virtualAccountFactory.build({
      walletId: 'wallet-id',
      transactionType: VirtualAccountTransactionType.Hmo,
    });

    // Add these mocks
    MockWalletRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(mockWallet),
    );

    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );

    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'Claim Payment',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
    };

    await service.handleNotificationEvent(VirtualAccountProvider.WEMA, meta);

    expect(
      MockWalletTransactionService.performWalletTransaction,
    ).toHaveBeenCalled();
  });

  it('handleNotificationEvent for HMO Claim Remittance should throw if wallet Not Found', () => {
    const va = virtualAccountFactory.build({
      walletId: 'wallet-id',
      transactionType: VirtualAccountTransactionType.Hmo,
    });
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    MockWalletRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.reject(),
    );
    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'Claim Payment',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
    };
    const response = service.handleNotificationEvent(
      VirtualAccountProvider.WEMA,
      meta,
    );
    expect(response).rejects.toThrow(new NotFoundException('Wallet Not Found'));
  });

  it('handleNotificationEvent for Pay Bill', async () => {
    const mockBill = {
      id: 'bill-id',
      senderHospitalId: 'hospital-id',
      virtualServicesPayment: {
        id: 'payment-id',
        amountPaid: 0,
        amountDue: 5000 * 100,
      },
      details: [],
    };
    const va = virtualAccountFactory.build({
      walletId: 'wallet-id',
      transactionType: VirtualAccountTransactionType.PayBill,
      activeBillId: mockBill.id,
      isActive: true,
    });
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    managerMock.findOne
      .mockImplementationOnce(() => Promise.resolve(null))
      .mockImplementationOnce(() => Promise.resolve(mockBill));
    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'Bill Payment',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
      transactionReference: 'transaction-reference',
    };
    await service.handleNotificationEvent(VirtualAccountProvider.WEMA, meta);
    expect(
      MockWalletTransactionService.performWalletTransaction,
    ).toHaveBeenCalled();
    expect(
      MockBillService.updateBillInformationFromPaymentNotification,
    ).toHaveBeenCalledWith(
      5000 * 100,
      mockBill.id,
      'WEMA Bank Transfer',
      expect.anything(),
    );
  });

  it('handleNotificationEvent for Pay Bill should throw if wallet Not Found', async () => {
    const mockBill = {
      id: 'bill-id',
      senderHospitalId: 'hospital-id',
      virtualServicesPayment: {
        id: 'payment-id',
        amountPaid: 0,
        amountDue: 5000 * 100,
      },
      details: [],
    };
    const va = virtualAccountFactory.build({
      walletId: 'wallet-id',
      transactionType: VirtualAccountTransactionType.PayBill,
      activeBillId: mockBill.id,
    });
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    managerMock.findOne
      .mockImplementationOnce(() => Promise.resolve(null))
      .mockImplementationOnce(() => Promise.resolve(mockBill));
    /* MockWalletRepository.findOne.mockImplementationOnce(() =>
      Promise.resolve(null),
    ); */
    managerMock.withRepository.mockImplementationOnce(() => ({
      findOne: jest.fn(() => Promise.resolve(null)),
    }));
    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'Bill Payment',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
      transactionReference: 'transaction-reference',
    };
    const response = service.handleNotificationEvent(
      VirtualAccountProvider.WEMA,
      meta,
    );
    await expect(response).rejects.toThrow(
      new NotFoundException('Wallet not found'),
    );
  });

  it('handleNotificationEvent for Pay Bill should throw if virtual services payment is not provided', async () => {
    const mockBill = {
      id: 'bill-id',
      senderHospitalId: 'hospital-id',
      virtualServicesPayment: {
        id: null,
        amountPaid: 0,
        amountDue: 5000 * 100,
      },
      details: [],
    };
    const va = virtualAccountFactory.build({
      walletId: 'wallet-id',
      transactionType: VirtualAccountTransactionType.PayBill,
      activeBillId: mockBill.id,
    });
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    managerMock.findOne
      .mockImplementationOnce(() => Promise.resolve(null))
      .mockImplementationOnce(() => Promise.resolve(mockBill));
    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'Bill Payment',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
      transactionReference: 'transaction-reference',
    };
    const response = service.handleNotificationEvent(
      VirtualAccountProvider.WEMA,
      meta,
    );
    await expect(response).rejects.toThrow(
      new NotFoundException('Virtual Services Payment Not Provided'),
    );
  });

  it('handleNotificationEvent for Registration Fee', async () => {
    const mockTempUser = {
      id: 'temp-user-id',
      hospitalId: 'hospital-id',
      amountPaid: 0,
      virtualBankAccount: mockVirtualAccount,
      hospital: hospitalFactory.build(),
    };
    const va = virtualAccountFactory.build({
      walletId: 'wallet-id',
      transactionType: VirtualAccountTransactionType.Registration,
      isActive: true,
    });
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );
    managerMock.findOne
      .mockImplementationOnce(() => Promise.resolve(null))
      .mockImplementationOnce(() => Promise.resolve(mockTempUser));
    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'Registration Fee',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
      transactionReference: 'transaction-reference',
    };
    await service.handleNotificationEvent(VirtualAccountProvider.WEMA, meta);
    expect(managerMock.update).toHaveBeenCalledWith(
      expect.anything(),
      mockTempUser.id,
      expect.objectContaining({
        amountPaid: expect.any(Function),
      }),
    );
    expect(
      MockWalletTransactionService.performWalletTransaction,
    ).toHaveBeenCalled();
  });

  it('handleNotificationEvent for Profile Enrollment', async () => {
    const mockHmoProfile = {
      id: 'hmo-profile-id',
      memberStatus: 'Inactive',
      premiumOutstanding: '10000',
    };

    const mockVirtualServicesPayment = {
      id: 'virtual-services-payment-id',
      amountPaid: 0,
      amountDue: 10000,
      senderHospitalId: 'hospital-id',
      registrationHmoProfile: mockHmoProfile,
    };

    const va = virtualAccountFactory.build({
      walletId: 'wallet-id',
      transactionType: VirtualAccountTransactionType.ProfileEnrollment,
      isActive: true,
    });

    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );

    managerMock.findOne
      .mockImplementationOnce(() => Promise.resolve(null))
      .mockImplementationOnce(() =>
        Promise.resolve(mockVirtualServicesPayment),
      );

    MockWalletRepository.findOne.mockImplementationOnce(() =>
      Promise.resolve(mockWallet),
    );

    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000', // Partial payment
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'HMO Profile Enrollment',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
      transactionReference: 'transaction-reference',
    };

    await service.handleNotificationEvent(VirtualAccountProvider.WEMA, meta);

    expect(managerMock.update).toHaveBeenCalledWith(
      expect.anything(),
      mockVirtualServicesPayment.id,
      expect.objectContaining({
        amountPaid: 500000,
      }),
    );

    expect(
      MockWalletTransactionService.performWalletTransaction,
    ).toHaveBeenCalled();

    expect(MockBankAccountTransactionRepository.save).toHaveBeenCalledWith(
      expect.objectContaining({
        amount: 500000,
        beneficiaryAccountId: va.id,
        transactionType: VirtualAccountTransactionType.ProfileEnrollment,
        transactionStatus: 'SUCCESS',
        virtualServicesPaymentId: mockVirtualServicesPayment.id,
      }),
    );
  });

  it('handleNotificationEvent for Profile Enrollment with complete payment', async () => {
    const mockHmoProfile = {
      id: 'hmo-profile-id',
      memberStatus: 'Inactive',
      premiumOutstanding: '10000',
    };

    const mockVirtualServicesPayment = {
      id: 'virtual-services-payment-id',
      amountPaid: 0,
      amountDue: 10000,
      senderHospitalId: 'hospital-id',
      registrationHmoProfile: mockHmoProfile,
    };

    const va = virtualAccountFactory.build({
      walletId: 'wallet-id',
      transactionType: VirtualAccountTransactionType.ProfileEnrollment,
      isActive: true,
    });

    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );

    managerMock.findOne
      .mockImplementationOnce(() => Promise.resolve(null))
      .mockImplementationOnce(() =>
        Promise.resolve(mockVirtualServicesPayment),
      );

    MockWalletRepository.findOne.mockImplementationOnce(() =>
      Promise.resolve(mockWallet),
    );

    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '10000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'HMO Profile Enrollment',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
      transactionReference: 'transaction-reference',
    };

    await service.handleNotificationEvent(VirtualAccountProvider.WEMA, meta);

    expect(managerMock.update).toHaveBeenCalledWith(
      expect.anything(),
      mockVirtualServicesPayment.id,
      expect.objectContaining({
        amountPaid: 1000000,
      }),
    );

    expect(managerMock.update).toHaveBeenCalledWith(
      expect.anything(),
      mockHmoProfile.id,
      expect.objectContaining({
        memberStartDate: expect.any(Date),
        memberDueDate: expect.any(Date),
        paymentFrequency: 'Annual',
        paymentDateTime: expect.any(Date),
        activationDatetime: expect.any(Date),
      }),
    );

    expect(
      MockWalletTransactionService.performWalletTransaction,
    ).toHaveBeenCalled();

    expect(MockBankAccountTransactionRepository.save).toHaveBeenCalledWith(
      expect.objectContaining({
        amount: 1000000,
        beneficiaryAccountId: va.id,
        transactionType: VirtualAccountTransactionType.ProfileEnrollment,
        transactionStatus: 'SUCCESS',
        virtualServicesPaymentId: mockVirtualServicesPayment.id,
      }),
    );
  });

  it('handleNotificationEvent for Profile Enrollment should use existing transaction if found', async () => {
    const va = virtualAccountFactory.build({
      walletId: 'wallet-id',
      transactionType: VirtualAccountTransactionType.ProfileEnrollment,
      isActive: true,
    });

    const existingTransaction = {
      id: 'existing-transaction-id',
      reference: 'reference',
    };

    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );

    managerMock.findOne.mockImplementationOnce(() =>
      Promise.resolve(existingTransaction),
    );

    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'HMO Profile Enrollment',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
      transactionReference: 'transaction-reference',
    };

    const result = await service.handleNotificationEvent(
      VirtualAccountProvider.WEMA,
      meta,
    );

    expect(result).toEqual(existingTransaction);
    expect(managerMock.update).not.toHaveBeenCalled();
    expect(
      MockWalletTransactionService.performWalletTransaction,
    ).not.toHaveBeenCalled();
  });

  it('handleNotificationEvent for Profile Enrollment should handle missing virtual services payment', async () => {
    const va = virtualAccountFactory.build({
      walletId: 'wallet-id',
      transactionType: VirtualAccountTransactionType.ProfileEnrollment,
      isActive: true,
    });

    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(va),
    );

    managerMock.findOne
      .mockImplementationOnce(() => Promise.resolve(null))
      .mockImplementationOnce(() => Promise.resolve(null));

    MockWalletRepository.findOne.mockImplementationOnce(() =>
      Promise.resolve(null),
    );

    const meta: IBankTransactionMeta = {
      bankCode: '035',
      sessionId: 'session-id',
      bankName: 'Wema',
      amount: '5000',
      creditAccountName: 'Account Name',
      originatorName: 'Originator Name',
      narration: 'HMO Profile Enrollment',
      originatorAccountNumber: '**********',
      paymentReference: 'reference',
      creditAccount: '**********',
      transactionReference: 'transaction-reference',
    };

    await service.handleNotificationEvent(VirtualAccountProvider.WEMA, meta);

    expect(MockBankAccountTransactionRepository.save).toHaveBeenCalledWith(
      expect.objectContaining({
        amount: 500000,
        beneficiaryAccountId: va.id,
        transactionType: VirtualAccountTransactionType.ProfileEnrollment,
        transactionStatus: 'SUCCESS',
      }),
    );

    expect(managerMock.update).not.toHaveBeenCalledWith(
      expect.anything(),
      expect.any(String),
      expect.objectContaining({
        amountPaid: expect.any(Number),
      }),
    );
  });

  it('getInvoice() get active invoice from virtual account', async () => {
    const response = await service.getInvoice('**********');
    expect(response).toEqual(undefined);
  });

  it('getInvoice() should throw if virtual account is not found', () => {
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.reject(),
    );
    const response = service.getInvoice('**********');
    expect(response).rejects.toThrow(
      new NotFoundException('Account Not Found'),
    );
  });

  it('getProfileVirtualAccounts', async () => {
    const res = await service.getProfileVirtualAccounts('id');
    expect(MockVirtualBankAccountRepository.find).toHaveBeenCalledWith({
      where: { wallet: { profileId: 'id' } },
    });
    expect(res).toEqual([mockVirtualAccount]);
  });

  it('getHospitalVirtualAccounts', async () => {
    const res = await service.getHospitalVirtualAccounts('id');
    expect(MockVirtualBankAccountRepository.find).toHaveBeenCalledWith({
      where: { wallet: { hospitalId: 'id' } },
      relations: ['hmo'],
    });
    expect(res).toEqual([mockVirtualAccount]);
  });

  it('getHospitalWalletVirtualAccount', async () => {
    const res = await service.getHospitalWalletVirtualAccount('id');
    expect(MockVirtualBankAccountRepository.findOne).toHaveBeenCalledWith({
      where: {
        wallet: { hospitalId: 'id' },
        transactionType: VirtualAccountTransactionType.FundWallet,
      },
    });
    expect(res).toEqual(mockVirtualAccount);
  });

  it('getOrCreateVirtualAccountForWalletTopup', async () => {
    const res = await service.getOrCreateVirtualAccountForWalletTopup(
      'clinifyid',
      { bvn: '123', phoneNumber: '123' },
    );
    expect(res).toEqual(mockVirtualAccount);
  });

  it('getOrCreateVirtualAccountForWalletTopup - Create new if none exist', async () => {
    MockVirtualBankAccountRepository.findOne.mockImplementationOnce(() =>
      Promise.resolve(null),
    );
    const res = await service.getOrCreateVirtualAccountForWalletTopup(
      'clinifyid',
      { bvn: '123', phoneNumber: '123' },
    );
    expect(MockBankService.generateVirtualAccountNumber).toHaveBeenCalledWith(
      expect.objectContaining({
        virtualAccountType: VirtualAccountType.Permanent,
        vaTransactionType: VirtualAccountTransactionType.FundWallet,
      }),
      expect.anything(),
    );
    expect(res).toEqual(mockVirtualAccount);
  });

  it('getOrCreateVirtualAccountForWalletTopup - Create new if none exist for hospital', async () => {
    MockVirtualBankAccountRepository.findOne.mockImplementationOnce(() =>
      Promise.resolve(null),
    );
    MockWalletService.getClinifyUserWallet.mockImplementationOnce(() =>
      Promise.resolve({
        ...walletFactory.build({ hospital: hospitalFactory.build() }),
      }),
    );
    const res = await service.getOrCreateVirtualAccountForWalletTopup(
      'clinifyid',
      { bvn: '123', phoneNumber: '123' },
    );
    expect(MockBankService.generateVirtualAccountNumber).toHaveBeenCalledWith(
      expect.objectContaining({
        virtualAccountType: VirtualAccountType.Permanent,
        vaTransactionType: VirtualAccountTransactionType.FundWallet,
      }),
      expect.anything(),
    );
    expect(res).toEqual(mockVirtualAccount);
  });

  it('getOrCreateVirtualAccountForWalletTopup should throw if wallet Not Found', async () => {
    MockWalletService.getClinifyUserWallet.mockImplementationOnce(() =>
      Promise.resolve(null),
    );
    await expect(
      service.getOrCreateVirtualAccountForWalletTopup('clinifyid', {
        bvn: '123',
        phoneNumber: '123',
      }),
    ).rejects.toThrow('Error Creating Account For Wallet');
  });

  it('getKYCDetails should return KYC details', async () => {
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...mockVirtualAccount,
        accountName: 'CLINIFY / Account Name',
        bvn: '**********',
        phoneNumber: '***********',
        isActive: true,
        wallet: {
          getTotalBalance: jest.fn(() => 100),
        },
      }),
    );
    const res = await service.getKYCDetails(
      VirtualAccountProvider.WEMA,
      '**********',
    );
    expect(res).toEqual(
      expect.objectContaining({
        accountName: 'CLINIFY / Account Name',
        bvn: '**********',
        phoneNumber: '***********',
        walletBalance: 100,
        status: 'Active',
      }),
    );
  });

  it('getKYCDetails should fail gracefully if no account is found', async () => {
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.reject(),
    );
    const res = service.getKYCDetails(
      VirtualAccountProvider.WEMA,
      '**********',
    );
    await expect(res).rejects.toThrow(
      new NotFoundException('Account Not Found'),
    );
  });

  it('deactivateAccount should deactivate account', async () => {
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...mockVirtualAccount,
        accountName: 'CLINIFY / Account Name',
        bvn: '**********',
        phoneNumber: '***********',
        isActive: true,
      }),
    );
    const res = await service.deactivateAccount(
      VirtualAccountProvider.WEMA,
      '**********',
      'reason',
    );
    expect(MockVirtualBankAccountRepository.update).toHaveBeenCalledWith(
      mockVirtualAccount.id,
      {
        isActive: false,
        reasonForDeactivation: 'reason',
      },
    );
    expect(res).toBe(true);
  });

  it('deactivateAccount should fail gracefully if no account is found', async () => {
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.reject(),
    );
    const res = service.deactivateAccount(
      VirtualAccountProvider.WEMA,
      '**********',
      'reason',
    );
    await expect(res).rejects.toThrow(
      new NotFoundException('Account Not Found'),
    );
  });

  it('getMiniStatement should return mini statement', async () => {
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...mockVirtualAccount,
        accountName: 'CLINIFY / Account Name',
        bvn: '**********',
        phoneNumber: '***********',
        isActive: true,
      }),
    );
    const res = await service.getMiniStatement(
      VirtualAccountProvider.WEMA,
      '**********',
    );
    expect(managerMock.find).toHaveBeenCalledWith(
      BankAccountTransactionModel,
      expect.objectContaining({
        skip: 0,
        take: 10,
        order: {
          createdDate: 'DESC',
        },
      }),
    );
    expect(res).toEqual([]);
  });

  it('getMiniStatement should fail gracefully if no account is found', async () => {
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.reject(),
    );
    const res = service.getMiniStatement(
      VirtualAccountProvider.WEMA,
      '**********',
    );
    await expect(res).rejects.toThrow(
      new NotFoundException('Account Not Found'),
    );
  });

  it('updateKYCDetails', async () => {
    const res = await service.updateKYCDetails(mockProfile, '**********', {
      bvn: '123',
      phoneNumber: '123',
    });
    expect(MockVirtualBankAccountRepository.findOneOrFail).toHaveBeenCalledWith(
      {
        where: {
          accountNumber: '**********',
          wallet: { profileId: mockProfile.id },
        },
      },
    );
    expect(res).toEqual(mockVirtualAccount);
  });

  it('updateKYCDetails - Org User', async () => {
    const res = await service.updateKYCDetails(
      { ...mockProfile, type: 'OrganizationAdmin', hospitalId: 'id' },
      '**********',
      {
        bvn: '123',
        phoneNumber: '123',
      },
    );
    expect(MockVirtualBankAccountRepository.findOneOrFail).toHaveBeenCalledWith(
      {
        where: {
          accountNumber: '**********',
          wallet: { hospitalId: 'id' },
        },
      },
    );
    expect(res).toEqual(mockVirtualAccount);
  });

  it('updateKYCDetails should fail gracefully if no account is found', async () => {
    MockVirtualBankAccountRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.reject(),
    );
    const res = service.updateKYCDetails(mockProfile, '**********', {
      bvn: '123',
      phoneNumber: '123',
    });
    await expect(res).rejects.toThrow(
      new NotFoundException('Account Not Found'),
    );
  });
});
