import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { CustomPermissionRepoMethods } from '@clinify/authorization/repositories/permission.repository';
import { PermissionService } from '@clinify/authorization/services/permission.service';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendModel } from '@clinify/database/extendModel';
import { InvoiceService } from '@clinify/invoices/services/invoice.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { CustomProfileRepoMethods } from '@clinify/users/repositories/profile.repository';
import { VirtualBankAccountResolver } from '@clinify/virtual-bank-accounts/resolvers/virtual-bank-account.resolver';
import { VirtualBankAccountService } from '@clinify/virtual-bank-accounts/services/virtual-bank-account.service';
import { mockInvoice } from '@mocks/factories/invoice.factory';
import { mockProfile } from '@mocks/factories/profile.factory';
import { mockVirtualAccount } from '@mocks/factories/virtual-account.factory';
import { InvoiceServiceMock } from '@mocks/invoice.mock';

const MockVirtualBankAccountService = {
  getVirtualAccountById: jest.fn(() => Promise.resolve(mockVirtualAccount)),
  getOrCreateVirtualAccountForWalletTopup: jest.fn(() =>
    Promise.resolve(mockVirtualAccount),
  ),
  updateKYCDetails: jest.fn(() => Promise.resolve(mockVirtualAccount)),
};

describe('VirtualBankAccountResolver', () => {
  let resolver: VirtualBankAccountResolver;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        VirtualBankAccountResolver,
        PermissionService,
        extendModel(PermissionModel, CustomPermissionRepoMethods),
        extendModel(ProfileModel, CustomProfileRepoMethods),
        {
          provide: VirtualBankAccountService,
          useValue: MockVirtualBankAccountService,
        },
        {
          provide: InvoiceService,
          useValue: InvoiceServiceMock,
        },
      ],
    }).compile();

    resolver = module.get(VirtualBankAccountResolver);
    jest.clearAllMocks();
  });

  afterEach(() => {
    module.close();
  });

  afterAll(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  it('getVirtualAccount() should call getVirtualAccountById from service and return value', async () => {
    const response = await resolver.getVirtualAccount(mockProfile, 'id');
    expect(
      MockVirtualBankAccountService.getVirtualAccountById,
    ).toHaveBeenCalledWith('id');
    expect(response).toEqual(mockVirtualAccount);
  });

  it('getInvoices() should call getInvoices from invoice service and return value', async () => {
    const response = await resolver.getInvoices(mockVirtualAccount);
    expect(InvoiceServiceMock.getInvoices).toHaveBeenCalledWith(
      mockVirtualAccount.invoiceIds,
    );
    expect(response).toEqual([mockInvoice]);
  });

  it('getVirtualAccountForWalletTopup', async () => {
    const res = await resolver.getVirtualAccountForWalletTopup('clinifyid', {
      bvn: '123',
      phoneNumber: '123',
    });
    expect(
      MockVirtualBankAccountService.getOrCreateVirtualAccountForWalletTopup,
    ).toHaveBeenCalledWith('clinifyid', { bvn: '123', phoneNumber: '123' });
    expect(res).toEqual(mockVirtualAccount);
  });

  it('updateKYCDetails', async () => {
    const res = await resolver.updateKYCDetails(mockProfile, '1234', {
      phoneNumber: '123',
      bvn: '123',
    });
    expect(MockVirtualBankAccountService.updateKYCDetails).toHaveBeenCalledWith(
      mockProfile,
      '1234',
      { phoneNumber: '123', bvn: '123' },
    );
    expect(res).toEqual(mockVirtualAccount);
  });
});
