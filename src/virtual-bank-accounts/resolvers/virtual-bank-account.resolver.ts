import { UseGuards } from '@nestjs/common';
import {
  Args,
  Mu<PERSON>,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { InvoiceModel } from '@clinify/invoices/models/invoice.model';
import { InvoiceService } from '@clinify/invoices/services/invoice.service';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { VirtualBankAccountKYCInput } from '@clinify/virtual-bank-accounts/inputs/virtual-bank-account.input';
import { VirtualBankAccountModel } from '@clinify/virtual-bank-accounts/models/virtual-bank-account.model';
import { VirtualBankAccountService } from '@clinify/virtual-bank-accounts/services/virtual-bank-account.service';

@Resolver(() => VirtualBankAccountModel)
@UseGuards(GqlAuthGuard)
export class VirtualBankAccountResolver {
  constructor(
    private readonly service: VirtualBankAccountService,
    private readonly invoiceService: InvoiceService,
  ) {}

  @Query(() => VirtualBankAccountModel)
  getVirtualAccount(
    @CurrentProfile() mutator: ProfileModel,
    @Args('id') id: string,
  ): Promise<VirtualBankAccountModel> {
    return this.service.getVirtualAccountById(id);
  }

  @Query(() => VirtualBankAccountModel)
  getVirtualAccountForWalletTopup(
    @Args('clinifyId') clinifyId: string,
    @Args('input', { type: () => VirtualBankAccountKYCInput, nullable: true })
    input: VirtualBankAccountKYCInput,
  ): Promise<VirtualBankAccountModel> {
    return this.service.getOrCreateVirtualAccountForWalletTopup(
      clinifyId,
      input,
    );
  }

  @Mutation(() => VirtualBankAccountModel)
  updateKYCDetails(
    @CurrentProfile() mutator: ProfileModel,
    @Args('accountNumber') accountNumber: string,
    @Args('input', { type: () => VirtualBankAccountKYCInput })
    input: VirtualBankAccountKYCInput,
  ): Promise<VirtualBankAccountModel> {
    return this.service.updateKYCDetails(mutator, accountNumber, input);
  }

  @ResolveField(() => [InvoiceModel], { name: 'invoices' })
  getInvoices(
    @Parent() root: VirtualBankAccountModel,
  ): Promise<InvoiceModel[]> {
    return this.invoiceService.getInvoices(root.invoicesIds);
  }
}
