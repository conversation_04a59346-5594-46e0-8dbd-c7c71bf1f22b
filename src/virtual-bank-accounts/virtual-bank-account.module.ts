import { forwardRef, Module } from '@nestjs/common';
import { AuthorizationModule } from '@clinify/authorization/authorization.module';
import { BankModule } from '@clinify/banks/bank.module';
import { BankAccountTransactionRepository } from '@clinify/banks/repositories/bank-account-transaction.repository';
import { BillModule } from '@clinify/bills/bill.module';
import { TypeormExtendedModule } from '@clinify/custom-repository/typeorm-extended.module';
import { InvoiceModule } from '@clinify/invoices/invoice.module';
import { InvoiceRepository } from '@clinify/invoices/repositories/invoice.repository';
import PubSub, { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { VirtualBankAccountRepository } from '@clinify/virtual-bank-accounts/repositories/virtual-bank-account.repository';
import { VirtualBankAccountResolver } from '@clinify/virtual-bank-accounts/resolvers/virtual-bank-account.resolver';
import { VirtualBankAccountService } from '@clinify/virtual-bank-accounts/services/virtual-bank-account.service';
import { WalletTransactionModule } from '@clinify/wallet-transactions/wallet-transaction.module';
import { WalletRepository } from '@clinify/wallets/repositories/wallet.repository';
import { WalletModule } from '@clinify/wallets/wallet.module';

@Module({
  imports: [
    TypeormExtendedModule.forCustomRepository([
      VirtualBankAccountRepository,
      WalletRepository,
      BankAccountTransactionRepository,
      InvoiceRepository,
    ]),
    AuthorizationModule,
    WalletTransactionModule,
    forwardRef(() => BillModule),
    forwardRef(() => InvoiceModule),
    forwardRef(() => WalletModule),
    forwardRef(() => BankModule),
  ],
  providers: [
    VirtualBankAccountResolver,
    VirtualBankAccountService,
    { provide: PUB_SUB, useFactory: () => PubSub },
  ],
  exports: [VirtualBankAccountService],
})
export class VirtualBankAccountModule {}
