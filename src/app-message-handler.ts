import { Injectable, Logger } from '@nestjs/common';
import AWS from 'aws-sdk';
import { VirtualAccountProvider } from '@clinify/banks/enum/virtual-account.enum';
import { IWemaNotificationAPIRequest } from '@clinify/partners-integration/banks/wema/interfaces/wema.interface';
import { WEMA_TRANSACTION_NOTIFICATION_QUEUE_NAME } from '@clinify/shared/constants';
import {
  SqsConsumerEventHandler,
  SqsMessageHandler,
} from '@clinify/sqs-consumer/decorators';
import { VirtualBankAccountService } from '@clinify/virtual-bank-accounts/services/virtual-bank-account.service';

@Injectable()
export class AppMessageHandler {
  constructor(
    private readonly virtualBankAccountService: VirtualBankAccountService,
    private readonly logger: Logger,
  ) {}

  @SqsMessageHandler(WEMA_TRANSACTION_NOTIFICATION_QUEUE_NAME, false)
  public async handleMessage(message: AWS.SQS.Message) {
    this.logger.log(
      `Running handle message: ${message.Body}`,
      WEMA_TRANSACTION_NOTIFICATION_QUEUE_NAME,
    );
    const queueMessage: IWemaNotificationAPIRequest = JSON.parse(message.Body);
    await this.virtualBankAccountService.handleNotificationEvent(
      VirtualAccountProvider.WEMA,
      {
        originatorAccountNumber: queueMessage.originatoraccountnumber,
        amount: queueMessage.amount,
        originatorName: queueMessage.originatorname,
        narration: queueMessage.narration,
        creditAccountName: queueMessage.craccountname,
        creditAccount: queueMessage.craccount,
        paymentReference: queueMessage.paymentreference,
        bankName: queueMessage.bankname,
        sessionId: queueMessage.sessionid,
        bankCode: queueMessage.bankcode,
        transactionReference: queueMessage.transactionreference,
      },
    );
  }

  @SqsConsumerEventHandler(
    WEMA_TRANSACTION_NOTIFICATION_QUEUE_NAME,
    'processing_error',
  )
  public onProcessingError(error: Error, message: AWS.SQS.Message) {
    this.logger.error(
      `Error Processing Message: ${message.Body}`,
      error.stack,
      WEMA_TRANSACTION_NOTIFICATION_QUEUE_NAME,
    );
  }
}
